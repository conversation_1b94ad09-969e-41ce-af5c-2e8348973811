// @since 2024-01-25 10:28:21
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "moego/api/appointment/v1/appointment_view.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/appointment_models.proto";
import "moego/models/appointment/v1/appointment_note_models.proto";
import "moego/models/appointment/v1/invoice_deposit_models.proto";
import "moego/models/appointment/v1/overview_enums.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/appointment/v1/wait_list_models.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/business_customer/v1/business_pet_code_models.proto";
import "moego/models/business_customer/v1/business_pet_evaluation_models.proto";
import "moego/models/business_customer/v1/business_pet_incident_report_models.proto";
import "moego/models/business_customer/v1/business_pet_note_models.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/order/v1/invoice_models.proto";
import "moego/models/payment/v1/pre_auth_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// get overview list params
message GetOverviewListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // date
  string date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // key word
  optional string keyword = 3 [(validate.rules).string = {max_len: 50}];
  // service item type
  repeated models.offering.v1.ServiceItemType service_item_types = 4 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // date type
  models.appointment.v1.OverviewDateType date_type = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // filter, only effective when selected_status is set
  optional Filter filter = 6;

  // filter
  message Filter {
    // report card status, empty for not filtering
    optional models.appointment.v1.OverviewReportStatus report_status = 1 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
    // appointment status, empty for not filtering
    repeated models.appointment.v1.AppointmentStatus appointment_statuses = 2 [(validate.rules).repeated = {
      max_items: 6
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
  }

  // sort, only effective when selected_status is set
  repeated moego.utils.v2.OrderBy order_bys = 7;

  // selected status tab
  optional models.appointment.v1.OverviewStatus selected_status = 8 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// overview item
message OverviewItem {
  // Appointment detail
  models.appointment.v1.AppointmentOverview appointment = 1;
  // pet and services
  repeated ServiceDetailOverview service_detail = 2;
  // notes
  repeated models.appointment.v1.AppointmentNoteModel notes = 3;
  // customer
  CustomerCompositeOverview customer = 4;
  // wait list
  models.appointment.v1.WaitListCalendarView wait_list = 5;
  // service type list
  repeated models.offering.v1.ServiceItemType service_item_types = 6;
  // pre auth
  models.payment.v1.PreAuthCalendarView pre_auth = 21;
  // deposit
  models.appointment.v1.InvoiceDepositModel deposits = 22;
  // invoice
  models.order.v1.InvoiceCalendarView invoice = 23;
  // no show invoice
  models.order.v1.NoShowInvoiceCalendarView no_show_invoice = 24;
  // report status
  repeated ReportStatus report_statuses = 25;
  // memberships
  moego.models.membership.v1.MembershipSubscriptionListModel membership_subscriptions = 26;
}

// report status
message ReportStatus {
  // pet id
  int64 pet_id = 2;
  // report status
  models.appointment.v1.OverviewReportStatus status = 1;
}

// pet detail
message ServiceDetailOverview {
  // pet
  models.business_customer.v1.BusinessCustomerPetModelOverview pet = 1;
  // services
  repeated ServiceCompositeOverview services = 2;
  // add-ons
  repeated AddOnCompositeOverview add_ons = 3;
  // evaluation
  repeated EvaluationServiceOverview evaluations = 4;
  // pet codes
  repeated moego.models.business_customer.v1.BusinessPetCodeModel codes = 5;
  // pet notes
  repeated moego.models.business_customer.v1.BusinessPetNoteModel notes = 6;
  // pet vaccines
  repeated VaccineComposite vaccines = 7;

  // pet codes bindings
  message Binding {
    // pet id
    int64 pet_id = 1;
    // code id
    int64 code_id = 2;
    // comment
    string comment = 3;
    // binding time
    google.protobuf.Timestamp binding_time = 4;
  }
  // pet bindings
  repeated Binding bindings = 8;

  // pet incident reports
  repeated moego.models.business_customer.v1.BusinessPetIncidentReportModel incident_reports = 9;
  // pet evaluation
  repeated models.business_customer.v1.PetEvaluationModel pet_evaluations = 10;
}

// pet vaccine
message VaccineComposite {
  // vaccine binding id
  int64 vaccine_binding_id = 1;
  // vaccine id
  int64 vaccine_id = 3;
  // expiration date, may not exist
  optional google.type.Date expiration_date = 4;
  // vaccine document urls
  repeated string document_urls = 5;
  // vaccine name
  string vaccine_name = 6;
}

// pet detail service composite
message ServiceCompositeOverview {
  // pet detail id
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // staff id
  int64 staff_id = 4;
  // service id
  int64 service_id = 5;
  // enable operation
  bool enable_operation = 21;
  // work mode, 0-parallel, 1-sequence
  models.appointment.v1.WorkMode work_mode = 22;
  // service color code
  string service_color_code = 23;
  // service start date, in yyyy-MM-dd format, for boarding or daycare service
  string start_date = 24;
  // service end date, in yyyy-MM-dd format, for boarding or daycare service
  string end_date = 25;
  // service item type, different from service type, it includes grooming, boarding, daycare or other services.
  models.offering.v1.ServiceItemType service_item_type = 26;
  // lodging id, only for boarding service item type
  int64 lodging_id = 27;
  // service name
  string service_name = 28;
  // lodging unit name
  string lodging_unit_name = 29;
  // lodging type name
  string lodging_type_name = 30;
  // staff name
  string staff_name = 31;
  // max duration (only for daycare service)
  int32 max_duration = 32;
  // price unit
  moego.models.offering.v1.ServicePriceUnit price_unit = 33;
  // split lodging unit name
  repeated LodgingInfo lodging_infos = 34;

  // lodging info
  message LodgingInfo {
    // lodging unit name
    string lodging_unit_name = 1;
    // lodging type name
    string lodging_type_name = 2;
  }
}

// pet detail add on composite
message AddOnCompositeOverview {
  // pet detail id
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // staff id
  int64 staff_id = 4;
  // service id
  int64 service_id = 5;
  // enable operation
  bool enable_operation = 21;
  // work mode, 0-parallel, 1-sequence
  models.appointment.v1.WorkMode work_mode = 22;
  // service color code
  string service_color_code = 23;
  // service start date, in yyyy-MM-dd format, for boarding or daycare service
  string start_date = 24;
  // service end date, in yyyy-MM-dd format, for boarding or daycare service
  string end_date = 25;
  // service item type, different from service type, it includes grooming, boarding, daycare or other services.
  models.offering.v1.ServiceItemType service_item_type = 26;
  // service name
  string service_name = 27;
  // staff name
  string staff_name = 28;
  // date type
  models.appointment.v1.PetDetailDateType date_type = 29;
  // specific dates
  repeated string specific_dates = 30;
}

// pet evaluation service composite
message EvaluationServiceOverview {
  // pet evaluation detail id
  int64 id = 1;
  // appointment id
  int64 appointment_id = 2;
  // pet id
  int64 pet_id = 3;
  // service id
  int64 service_id = 4;
  // service start date, in yyyy-MM-dd format, for boarding or daycare service
  string start_date = 5;
  // service end date, in yyyy-MM-dd format, for boarding or daycare service
  string end_date = 6;
  // service item type, always be evaluation.
  models.offering.v1.ServiceItemType service_item_type = 7;
  // service name
  string service_name = 8;
  // staff id
  optional int64 staff_id = 9;
  // staff name
  optional string staff_name = 10;
  // lodging id
  optional int64 lodging_id = 11;
  // lodging unit name
  optional string lodging_unit_name = 12;
  // lodging type name
  optional string lodging_type_name = 13;
  // start time, in minutes
  int32 start_time = 14;
  // end time, in minutes
  int32 end_time = 15;
}

// customer composite
message CustomerCompositeOverview {
  // customer
  models.business_customer.v1.BusinessCustomerCalendarView customer_profile = 1;
  // is new customer
  bool is_new_customer = 2;
  // required sign
  bool required_sign = 3;
  // review booster sent
  bool review_booster_sent = 4;
  // customer package info
  repeated CustomerPackageView customer_packages = 5;
  // unpaid amount
  google.type.Money unpaid_amount = 6;
  // cof status
  enum COFStatus {
    // unspecified
    COF_STATUS_UNSPECIFIED = 0;
    // authorized
    AUTHORIZED = 1;
    // pending
    PENDING = 2;
    // failed
    FAILED = 3;
    // no card on file
    NO_CARD_ON_FILE = 4;
  }
  // cof status
  COFStatus cof_status = 7;
}

// overview status entry
message OverviewEntry {
  // status
  models.appointment.v1.OverviewStatus status = 1;
  // count
  int64 count = 2;
  // overview items
  repeated OverviewItem items = 3;
  // appointment count by service item type
  repeated AppointmentCountByServiceItemType appointment_count_by_service_item_types = 4;
  // pet count by service item type
  repeated PetCountByServiceItemType pet_count_by_service_item_types = 5;
}

// appointment count by service item type
message AppointmentCountByServiceItemType {
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 1;
  // appt count
  int64 count = 2;
}

// pet count by service item type
message PetCountByServiceItemType {
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 1;
  // pet count
  int64 count = 2;
}

// get overview count result
message GetOverviewListResult {
  // entries
  repeated OverviewEntry entries = 1;
}

// get overview list page params
message ListOverviewAppointmentParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];
  // date type
  models.appointment.v1.OverviewDateType date_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // overview status
  models.appointment.v1.OverviewStatus overview_status = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // filter
  optional Filter filter = 5;

  // filter
  message Filter {
    // service item type, empty for not filtering
    repeated models.offering.v1.ServiceItemType service_item_types = 1 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];

    // appointment status, empty for not filtering
    repeated models.appointment.v1.AppointmentStatus appointment_statuses = 2 [(validate.rules).repeated = {
      max_items: 6
      unique: true
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
    // key word
    optional string keyword = 3 [(validate.rules).string = {max_len: 50}];
    // report card status, empty for not filtering
    optional models.appointment.v1.OverviewReportStatus report_status = 4 [(validate.rules).enum = {
      defined_only: true
      not_in: [0]
    }];
  }

  // pagination
  moego.utils.v2.PaginationRequest pagination = 9;

  // sort
  repeated moego.utils.v2.OrderBy order_bys = 10;
}

// get overview list page result
message ListOverviewAppointmentResult {
  // overview items
  repeated OverviewItem items = 1;
  // pagination
  // deprecated by Freeman since 2025/4/14, 这个字段返回的 count 是 pet count，实际应该返回 appointment count，使用 pagination_v2 替换
  moego.utils.v2.PaginationResponse pagination = 2 [deprecated = true];
  // pagination v2
  moego.utils.v2.PaginationResponse pagination_v2 = 6;
  // appointment count by service item type
  repeated AppointmentCountByServiceItemType appointment_count_by_service_item_types = 3;
  // pet count by service item type
  repeated PetCountByServiceItemType pet_count_by_service_item_types = 4;
  // total pet count, fixes https://moego.atlassian.net/browse/MER-4159
  int32 pet_count = 5;
}

// count overview appointment params
message CountOverviewAppointmentParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];
  // date type
  models.appointment.v1.OverviewDateType date_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // overview status, deprecated, please use overview_statuses instead
  optional models.appointment.v1.OverviewStatus overview_status = 4 [
    (validate.rules).enum = {
      defined_only: true
      not_in: [0]
    },
    deprecated = true
  ];

  // filter
  optional Filter filter = 5;

  // overview statuses, if not set, we will calculate the status with date&time
  repeated models.appointment.v1.OverviewStatus overview_statuses = 6 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // filter
  message Filter {
    // service item type, empty for not filtering
    repeated models.offering.v1.ServiceItemType service_item_types = 1 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
    // key word
    optional string keyword = 2 [(validate.rules).string = {max_len: 50}];
  }
}

// count overview appointment result
message CountOverviewAppointmentResult {
  // count
  int64 count = 1 [deprecated = true];
  // count by overview status
  message CountWithOverviewStatus {
    // overview status
    models.appointment.v1.OverviewStatus overview_status = 1;
    // count
    int64 count = 2;
  }
  // count by overview status
  repeated CountWithOverviewStatus count_with_overview_status = 2;
}

// the overview service
service OverviewService {
  // get overview list
  rpc GetOverviewList(GetOverviewListParams) returns (GetOverviewListResult);
  // list overview
  rpc ListOverviewAppointment(ListOverviewAppointmentParams) returns (ListOverviewAppointmentResult);
  // count overview
  rpc CountOverviewAppointment(CountOverviewAppointmentParams) returns (CountOverviewAppointmentResult);
}
