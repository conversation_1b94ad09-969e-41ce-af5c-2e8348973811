syntax = "proto3";

package moego.api.appointment.v1;

import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// Save or update appointment pet detail params
message SaveOrUpdatePetDetailsParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // DO NOT use this field, use `pet_details` instead.
  optional models.appointment.v1.PetDetailDef pet_detail = 2 [deprecated = true];

  // Multi pets update details
  // It contains all the services and add-on information selected for a single pet.
  // If it already exists, it will be overwritten with the latest data.
  // If it does not exist, the latest data will be directly inserted.
  repeated models.appointment.v1.PetDetailDef pet_details = 4 [(validate.rules).repeated = {max_items: 100}];

  // Repeat appointment modify scope
  optional models.appointment.v1.RepeatAppointmentModifyScope repeat_appointment_modify_scope = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Save or update appointment pet detail result
message SaveOrUpdatePetDetailsResult {}

// Delete appointment selected pet params
message DeletePetParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // selected pet id
  int64 pet_id = 2 [(validate.rules).int64.gt = 0];

  // Repeat appointment modify scope
  optional models.appointment.v1.RepeatAppointmentModifyScope repeat_appointment_modify_scope = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Delete appointment selected pet result
message DeletePetResult {}

// Delete appointment selected pet evaluation param
message DeletePetEvaluationParams {
  // Appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];

  // selected evaluation service detail id
  int64 evaluation_service_detail_id = 2 [(validate.rules).int64.gt = 0];
}

// Delete appointment selected pet result
message DeletePetEvaluationResult {}

// pre check params for create evaluation service
message PreCreateEvaluationServiceCheckParams {
  // business id
  optional int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // appointment date
  optional string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// pre check result for create evaluation service
message PreCreateEvaluationServiceCheckResult {
  // pet num for evaluation on the same day
  optional int32 pet_num_for_evaluation = 1;
}

// Count pet detail params
message CountPetDetailParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // start date, format: yyyy-MM-dd
  string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date, format: yyyy-MM-dd
  string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // service item type, SERVICE_ITEM_TYPE_UNSPECIFIED (0) means all service item types
  optional moego.models.offering.v1.ServiceItemType service_item_type = 4 [(validate.rules).enum = {defined_only: true}];
}

// Count pet detail result
message CountPetDetailResult {
  // Pet detail count
  int32 count = 1;
}

// Appointment pet detail service
service PetDetailService {
  // Save or update pet's selected services
  // If there is no pet, the selected services will be saved directly.
  // If there is a pet, it will delete the original services selected for the pet, then save the newly selected services again.
  rpc SaveOrUpdatePetDetails(SaveOrUpdatePetDetailsParams) returns (SaveOrUpdatePetDetailsResult);

  // Delete selected pet
  rpc DeletePet(DeletePetParams) returns (DeletePetResult);

  // Delete selected pet evaluation service detail
  rpc DeletePetEvaluation(DeletePetEvaluationParams) returns (DeletePetEvaluationResult);

  // pre check for create evaluation service
  rpc PreCreateEvaluationServiceCheck(PreCreateEvaluationServiceCheckParams) returns (PreCreateEvaluationServiceCheckResult);

  // Count pet detail
  rpc CountPetDetail(CountPetDetailParams) returns (CountPetDetailResult);
}
