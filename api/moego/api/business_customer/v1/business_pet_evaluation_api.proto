syntax = "proto3";

package moego.api.business_customer.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/business_customer/v1/business_pet_evaluation_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/evaluation_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.business_customer.v1";

// List evaluation history params
message ListPetEvaluationHistoryParams {
  //pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
}

// List evaluation history result
message ListPetEvaluationHistoryResult {
  // evaluation history view
  repeated EvaluationHistoryView evaluation_histories = 1;
  //evaluation view
  repeated moego.models.offering.v1.EvaluationView evaluations = 2;
}

// evaluation history view
message EvaluationHistoryView {
  // action type
  moego.models.business_customer.v1.PetEvaluationHistoryModel.ActionType action_type = 1;
  // operator
  Operator operator = 2;
  // operate time
  google.protobuf.Timestamp operate_date = 3;
  // evaluation extra
  EvaluationExtra evaluation_extra = 4;
  // evaluation id
  int64 evaluation_id = 5 [(validate.rules).int64.gt = 0];
}

// evaluation extra
message EvaluationExtra {
  // reset interval days
  int32 reset_interval_days = 1;
  // original value
  models.customer.v1.EvaluationStatus original_status = 2;
  // new value
  models.customer.v1.EvaluationStatus new_status = 3;
}

// Operator
message Operator {
  // operator id
  int64 id = 1;
  // operator name
  string name = 2;
}

// ListPetEvaluationParams
message ListPetEvaluationParams {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
}

// ListPetEvaluationResult
message ListPetEvaluationResult {
  // pet evaluations
  repeated moego.models.business_customer.v1.PetEvaluationModel pet_evaluations = 1;
  //evaluation view
  repeated moego.models.offering.v1.EvaluationView evaluations = 2;
}

// UpdatePetEvaluationParams
message UpdatePetEvaluationParams {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
  // evaluation id
  int64 evaluation_id = 3 [(validate.rules).int64.gt = 0];
  // evaluation status
  models.customer.v1.EvaluationStatus evaluation_status = 4;
  // action type
  moego.models.business_customer.v1.PetEvaluationHistoryModel.ActionType action_type = 5;
}

// UpdatePetEvaluationResult
message UpdatePetEvaluationResult {}

// ListEvaluationStatusByPetServiceParams
message ListEvaluationStatusByPetServiceParams {
  // pet id and service id
  repeated PetServiceIdParams pet_service_ids = 1 [(validate.rules).repeated = {min_items: 1}];
  // pet id and service id
  message PetServiceIdParams {
    // pet id
    int64 pet_id = 1 [(validate.rules).int64.gt = 0];
    // service id
    int64 service_id = 2 [(validate.rules).int64.gt = 0];
  }
}

// ListEvaluationStatusByPetServiceResult
message ListEvaluationStatusByPetServiceResult {
  // evaluation status result
  repeated EvaluationStatusResult evaluation_status_results = 1;

  // evaluation status result
  message EvaluationStatusResult {
    // pet id
    int64 pet_id = 1;
    // service id
    int64 service_id = 2;
    // pet evaluation status
    models.customer.v1.EvaluationStatus final_evaluation_status = 3;
    // whether evaluation is required
    bool is_evaluation_required = 6;
    // whether evaluation is required before online booking
    bool is_evaluation_required_for_ob = 7;
    // evaluation id
    int64 evaluation_id = 8;
  }
}

// API for business pet evaluation
service BusinessPetEvaluationService {
  // List pet evaluation history
  rpc ListPetEvaluationHistory(ListPetEvaluationHistoryParams) returns (ListPetEvaluationHistoryResult);
  // List pet evaluation
  rpc ListPetEvaluation(ListPetEvaluationParams) returns (ListPetEvaluationResult);
  // update pet evaluation
  rpc UpdatePetEvaluation(UpdatePetEvaluationParams) returns (UpdatePetEvaluationResult);
  // List pet service 的 evaluation status
  rpc ListEvaluationStatusByPetService(ListEvaluationStatusByPetServiceParams) returns (ListEvaluationStatusByPetServiceResult);
}
