// @since 2024-03-12 11:33:59
// <AUTHOR> <z<PERSON><PERSON>@moego.pet>

syntax = "proto3";

package moego.api.offering.v1;

import "moego/models/offering/v1/evaluation_defs.proto";
import "moego/models/offering/v1/evaluation_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/organization/v1/location_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

// create evaluation params
message CreateEvaluationParams {
  // the evaluation def
  moego.models.offering.v1.EvaluationDef evaluation_def = 1 [(validate.rules).message = {required: true}];
}

// create evaluation result
message CreateEvaluationResult {
  // the evaluation model
  moego.models.offering.v1.EvaluationModel evaluation_model = 1;
}

// update evaluation params
message UpdateEvaluationParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // the evaluation def
  moego.models.offering.v1.EvaluationDef evaluation_def = 2 [(validate.rules).message = {required: true}];
}

// update evaluation result
message UpdateEvaluationResult {
  // the evaluation model
  moego.models.offering.v1.EvaluationModel evaluation_model = 1;
}

// delete evaluation params
message DeleteEvaluationParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete evaluation result
message DeleteEvaluationResult {}

// get evaluation
message GetEvaluationParams {
  // evaluation
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get evaluation
message GetEvaluationResult {
  // the evaluation model
  moego.models.offering.v1.EvaluationModel evaluation = 1;
}

// get evaluation list
message GetEvaluationListParams {}

// get evaluation list result
message GetEvaluationListResult {
  // the evaluation model list
  repeated moego.models.offering.v1.EvaluationModel evaluations = 1;
}

// get applicable evaluation list
message GetApplicableEvaluationListParams {
  // business id
  optional int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service item type
  optional models.offering.v1.ServiceItemType service_item_type = 2;
}

// get applicable evaluation list result
message GetApplicableEvaluationListResult {
  // the evaluation model list
  repeated moego.models.offering.v1.EvaluationBriefView evaluations = 1;
}

// get business list with applicable evaluation params
message GetBusinessListWithApplicableEvaluationParams {}

// get business list with applicable evaluation result
message GetBusinessListWithApplicableEvaluationResult {
  // the business list
  repeated moego.models.organization.v1.LocationBriefView businesses = 1;
}

// the evaluation service
service EvaluationService {
  // create evaluation
  rpc CreateEvaluation(CreateEvaluationParams) returns (CreateEvaluationResult) {}
  // update evaluation
  rpc UpdateEvaluation(UpdateEvaluationParams) returns (UpdateEvaluationResult) {}
  // delete evaluation
  rpc DeleteEvaluation(DeleteEvaluationParams) returns (DeleteEvaluationResult) {}
  // get evaluation
  rpc GetEvaluation(GetEvaluationParams) returns (GetEvaluationResult) {}
  // get evaluation list
  rpc GetEvaluationList(GetEvaluationListParams) returns (GetEvaluationListResult) {}
  // get applicable evaluation list
  rpc GetApplicableEvaluationList(GetApplicableEvaluationListParams) returns (GetApplicableEvaluationListResult) {}
  // get business list with applicable evaluation
  rpc GetBusinessListWithApplicableEvaluation(GetBusinessListWithApplicableEvaluationParams) returns (GetBusinessListWithApplicableEvaluationResult) {}
}
