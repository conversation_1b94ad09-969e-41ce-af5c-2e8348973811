// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/api/appointment/v1/appointment_view.proto";
import "moego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/offering/v1/evaluation_models.proto";
import "moego/models/offering/v1/group_class_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/models/online_booking/v1/boarding_add_on_detail_models.proto";
import "moego/models/online_booking/v1/boarding_service_detail_models.proto";
import "moego/models/online_booking/v1/booking_request_enums.proto";
import "moego/models/online_booking/v1/booking_request_models.proto";
import "moego/models/online_booking/v1/daycare_add_on_detail_models.proto";
import "moego/models/online_booking/v1/daycare_service_detail_models.proto";
import "moego/models/online_booking/v1/evaluation_test_detail_models.proto";
import "moego/models/online_booking/v1/feeding_models.proto";
import "moego/models/online_booking/v1/grooming_add_on_detail_models.proto";
import "moego/models/online_booking/v1/grooming_service_detail_models.proto";
import "moego/models/online_booking/v1/medication_models.proto";
import "moego/service/online_booking/v1/booking_request_service.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// Get booking request list request
message GetBookingRequestListRequest {
  // The business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Pagination
  utils.v2.PaginationRequest pagination = 2 [(validate.rules).message = {required: true}];
  // Keyword
  optional string keyword = 3 [(validate.rules).string = {max_len: 50}];
  // order by (support multi fields), optional
  repeated moego.utils.v2.OrderBy order_bys = 4;
  // Service item type
  //  repeated models.offering.v1.ServiceItemType service_item_types = 5 [(validate.rules).repeated = {
  //    items: {
  //      enum: {
  //        defined_only: true,
  //        not_in: [0]
  //      }
  //    }
  //  }];
}

// customer composite
message CustomerDetail {
  // first name
  string first_name = 1;
  // last name
  string last_name = 2;
  // phone number
  string phone_number = 3;
  // business id
  int64 business_id = 4;
  // customer id
  int64 customer_id = 5;
  // email
  string email = 6;
  // avatar path
  string avatar_path = 7;
  // client color
  string client_color = 8;
  // referral source id
  int32 referral_source_id = 9;
  // preferred groomer id
  int32 preferred_groomer_id = 10;
  // preferred frequency type
  int32 preferred_frequency_type = 11;
  // frequency day
  int32 preferred_frequency_day = 12;
  // preferred day
  repeated int32 preferred_day = 13;
  // preferred time
  repeated int32 preferred_time = 14;
  // is new customer
  bool is_new_customer = 15;
  // primary address
  optional AddressDetail primary_address = 16;
  // new address
  repeated AddressDetail new_address = 17;
  // question answers
  repeated QuestionAnswerDetail question_answers = 18;
  // last alert note
  optional string last_alert_note = 19;
  // out of service area
  bool out_of_service_area = 20;
  // has Pet Parent App account
  bool has_pet_parent_app_account = 21;

  // CRM-3555 customer emergency contact
  message EmergencyContact {
    // first name
    string first_name = 1;
    // last name
    string last_name = 2;
    // phone number
    string phone_number = 3;
  }
  // emergency contact
  EmergencyContact emergency_contact = 22;

  // 复用这个结构了，偷懒，嘻嘻
  EmergencyContact pickup_contact = 23;
}

// Get booking request list response
message GetBookingRequestListResponse {
  // The booking request item
  repeated GetBookingRequestItem booking_request_items = 1;
  // Pagination
  utils.v2.PaginationResponse pagination = 2;
}

// Get booking request request
message GetBookingRequestRequest {
  // The booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Get booking request item
message GetBookingRequestItem {
  // booking request
  BookingRequestDetail booking_request = 1;
  // service
  repeated ServiceDetail service_details = 2;
  // customer
  CustomerDetail customer_detail = 3;
  // pay
  PayBookingRequestView pay = 4;
  // profile has request update
  bool has_request_update = 5;
  // auto assign
  // deprecated by Freeman since 2025/3/4, use incomplete_details instead
  AssignRequire assign_require = 6 [deprecated = true];
  // active memberships for the customer
  moego.models.membership.v1.MembershipSubscriptionListModel membership_subscriptions = 7;
  // customer package info
  repeated moego.api.appointment.v1.CustomerPackageView customer_packages = 8;
  // 还需要前端填充数据的 details，包括了：
  // - grooming/boarding/daycare/evaluation service details
  // - grooming/boarding/daycare addon details
  IncompleteDetails incomplete_details = 9;
  // related membership, only contains source membership now
  repeated moego.models.membership.v1.MembershipModelPublicView related_memberships = 10;
}

// Service detail
message ServiceDetail {
  // pet detail
  PetDetail pet_detail = 1;
  // the services
  repeated Service services = 2;

  // The booking request service
  message Service {
    // the service type
    oneof service {
      // grooming service
      GroomingService grooming = 1;
      // boarding service
      BoardingService boarding = 2;
      // daycare service
      DaycareService daycare = 3;
      // evaluation service
      EvaluationService evaluation = 4;
      // dog walking service
      DogWalkingService dog_walking = 6;
      // group class service
      GroupClassService group_class = 7;
    }
    // service item type, different from service type, it includes grooming, boarding, daycare or other services.
    models.offering.v1.ServiceItemType service_item_type = 5;
  }
}

// The booking request detail
message BookingRequestDetail {
  // unique id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // appointment id, generated after the booking request is scheduled
  int64 appointment_id = 4;
  // start date, format: yyyy-mm-dd
  string start_date = 5;
  // start time, the minutes from 00:00
  int32 start_time = 6;
  // end date, format: yyyy-mm-dd
  string end_date = 7;
  // end time, the minutes from 00:00
  int32 end_time = 8;
  // status, 0: pending, 1: confirmed, 2: canceled
  models.online_booking.v1.BookingRequestStatus status = 9;
  // is prepaid
  bool is_prepaid = 10;
  // additional note
  string additional_note = 11;
  // source platform
  models.online_booking.v1.BookingRequestSourcePlatform source_platform = 12;
  // service item type include, bitmap value
  int32 service_type_include = 13;
  // the create time
  google.protobuf.Timestamp created_at = 20;
  // no start time
  bool no_start_time = 21;
  // service type list
  repeated models.offering.v1.ServiceItemType service_item_types = 22;
  // staff name
  optional string staff_name = 23;
  // staff id
  optional int64 staff_id = 24;
  // specific dates only for daycare appointment
  repeated string specific_dates = 25;
  // source
  // Note: if source is MEMBERSHIP, frontend will display as "Branded app"
  models.online_booking.v1.BookingRequestModel.Source source = 26;
  // source id
  int64 source_id = 27;
}

// Get booking request response
message GetBookingRequestResponse {
  // booking request
  BookingRequestDetail booking_request = 1;
  // service
  repeated ServiceDetail service_details = 2;
  // customer
  CustomerDetail customer_detail = 3;
  // pay
  PayBookingRequestView pay = 4;
  // profile has request update
  bool has_request_update = 5;
  // address
  AddressDetail address = 6;
  // assign require
  // deprecated by Freeman since 2025/3/4, use incomplete_details instead
  AssignRequire assign_require = 7 [deprecated = true];
  // order
  OrderBookingRequestView order = 8;
  // 还需要前端填充数据的 details，包括了：
  // - grooming/boarding/daycare/evaluation service details
  // - grooming/boarding/daycare addon details
  IncompleteDetails incomplete_details = 9;
}

// The grooming service
message GroomingService {
  // the grooming service detail
  GroomingServiceDetail service = 1;
  // the addons
  repeated GroomingAddOnDetail addons = 2;
  // auto assign
  optional GroomingAutoAssignDetail auto_assign = 6;
}

// The grooming service or add-on detail
message GroomingServiceDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current service
  int64 pet_id = 3;
  // The id of staff, associated with the current service
  int64 staff_id = 4;
  // The id of current service
  int64 service_id = 5;
  // The time of current service, unit minute
  int32 service_time = 6;
  // The price of current service
  double service_price = 7;
  // The start date of the service, yyyy-MM-dd
  string start_date = 10;
  // The start time of the service, unit minute, 540 means 09:00
  int32 start_time = 11;
  // The end date of the service, yyyy-MM-dd
  string end_date = 12;
  // The end time of the service, unit minute, 540 means 09:00
  int32 end_time = 13;
  // service name
  string service_name = 16;
  // staff name
  string staff_name = 17;
  // price override type
  moego.models.offering.v1.ServiceOverrideType price_override_type = 18;
  // duration override type
  moego.models.offering.v1.ServiceOverrideType duration_override_type = 19;
}

// The grooming add-on detail
message GroomingAddOnDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of service detail
  int64 service_detail_id = 3;
  // The id of pet, associated with the current service
  int64 pet_id = 4;
  // The id of staff, associated with the current service
  int64 staff_id = 5;
  // The id of add-on service, aka. grooming service id
  int64 add_on_id = 6;
  // The time of current service, unit minute
  int32 service_time = 7;
  // The price of current service
  double service_price = 8;
  // The start date of the service, yyyy-MM-dd
  string start_date = 9;
  // The start time of the service, unit minute, 540 means 09:00
  int32 start_time = 10;
  // The end date of the service, yyyy-MM-dd
  string end_date = 11;
  // The end time of the service, unit minute, 540 means 09:00
  int32 end_time = 12;
  // service name
  string service_name = 16;
  // staff name
  string staff_name = 17;
}

// The boarding service
message BoardingService {
  // the boarding service detail
  BoardingServiceDetail service = 1;
  // the addons
  repeated BoardingAddOnDetail addons = 2;
  // the feeding. Deprecated, use feedings instead
  FeedingDetail feeding = 3 [deprecated = true];
  // the medication. Deprecated, use medications instead
  MedicationDetail medication = 4 [deprecated = true];
  // auto assign
  optional BoardingAutoAssignDetail auto_assign = 6 [deprecated = true];
  // the feedings
  repeated FeedingDetail feedings = 7;
  // the medications
  repeated MedicationDetail medications = 8;
}

// The boarding service detail
message BoardingServiceDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current service
  int64 pet_id = 3;
  // The id of lodging, associated with the current service
  int64 lodging_id = 4 [deprecated = true];
  // The id of current service
  int64 service_id = 5;
  // specific dates
  repeated string specific_dates = 6;
  // The price of current service
  double service_price = 7;
  // taxId
  int64 tax_id = 8;
  // The pet arrival date of the service, yyyy-MM-dd
  string start_date = 9;
  // The pet arrival time of the service, unit minute, 540 means 09:00
  int32 start_time = 10;
  // The pet pickup date of the service, yyyy-MM-dd
  string end_date = 11;
  // The pet pickup time of the service, unit minute, 540 means 09:00
  int32 end_time = 12;
  // service name
  string service_name = 16;
  // lodging unit name
  string lodging_unit_name = 17 [deprecated = true];
  // lodging type name
  string lodging_type_name = 18 [deprecated = true];
  // price unit
  moego.models.offering.v1.ServicePriceUnit price_unit = 19;
}

// The boarding add-on detail
message BoardingAddOnDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of boarding service detail, associated with the current add-on
  int64 service_detail_id = 3;
  // The id of pet, associated with the current add-on
  int64 pet_id = 4;
  // The id of current add-on service
  int64 add_on_id = 5;
  // The specific dates of the add-on service
  repeated string specific_dates = 6;
  // whether the add-on service is everyday, not include checkout day
  // deprecated. use date_type instead
  bool is_everyday = 7 [deprecated = true];
  // The price of current add-on service
  double service_price = 8;
  // taxId
  int64 tax_id = 9;
  // duration
  int32 duration = 10;
  // service name
  string service_name = 16;
  // quantity per day
  int32 quantity_per_day = 17;
  // require dedicated staff
  bool require_dedicated_staff = 18;
  // date type
  optional models.appointment.v1.PetDetailDateType date_type = 13;
  // start date
  // Use start_date when date_type is PET_DETAIL_DATE_DATE_POINT
  optional string start_date = 14;
}

//// The service detail type
//enum ServiceDetailType {
//  // Unspecified service detail type.
//  SERVICE_DETAIL_TYPE_UNSPECIFIED = 0;
//  // Boarding service detail type.
//  BOARDING = 1;
//  // Daycare service detail type.
//  DAYCARE = 2;
//}

// Stores information about feedings.
message FeedingDetail {
  // The primary key identifier for each feeding.
  int64 id = 1;
  // The booking request identifier.
  int64 booking_request_id = 2;
  // The service detail identifier.
  int64 service_detail_id = 3;
  // service detail type, 1: boarding, 2: daycare
  //  ServiceDetailType service_detail_type = 4;
  // Feeding time.
  repeated models.online_booking.v1.FeedingModel.FeedingSchedule time = 5;
  // Feeding amount, must be greater than 0.
  double amount = 6 [deprecated = true];
  // Feeding unit.
  string unit = 7;
  // Food type.
  repeated string food_type = 8;
  // Food source.
  string food_source = 9;
  // Feeding instructions.
  string instruction = 10;
  // Feeding note
  string note = 11;
  // Feeding amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 12;
}

// Stores information about medication events.
message MedicationDetail {
  // The primary key identifier for each medication event.
  int64 id = 1;
  // The booking request identifier.
  int64 booking_request_id = 2;
  // The service detail identifier.
  int64 service_detail_id = 3;
  // service detail type, 1: boarding, 2: daycare
  //  ServiceDetailType service_detail_type = 4;
  // Medication time.
  repeated models.online_booking.v1.MedicationModel.MedicationSchedule time = 5;
  // Medication amount, must be greater than 0.
  double amount = 6 [deprecated = true];
  // Medication unit.
  string unit = 7;
  // Medication name.
  string medication_name = 8;
  // Additional notes about the medication.
  string notes = 9;
  // Medication amount, such as 1.2, 1/2, 1 etc.
  optional string amount_str = 12;
  // Medication select date
  optional moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef selected_date = 13;
}

// The daycare service
message DaycareService {
  // the daycare service detail
  DaycareServiceDetail service = 1;
  // the addons
  repeated DaycareAddOnDetail addons = 2;
  // the feeding. Deprecated, use feedings instead
  FeedingDetail feeding = 3 [deprecated = true];
  // the medication. Deprecated, use medications instead
  MedicationDetail medication = 4 [deprecated = true];
  // the feedings
  repeated FeedingDetail feedings = 5;
  // the medications
  repeated MedicationDetail medications = 6;
}

// The daycare service detail
message DaycareServiceDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current service
  int64 pet_id = 3;
  // The id of current service
  int64 service_id = 4;
  // The specific dates of the daycare service
  repeated string specific_dates = 5;
  // The price of current service
  double service_price = 6;
  // taxId
  int64 tax_id = 7;
  // The max duration of the daycare service, unit minute
  int32 max_duration = 8;
  // The pet arrival date of the service, yyyy-MM-dd
  string start_date = 9;
  // The pet arrival time of the service, unit minute, 540 means 09:00
  int32 start_time = 10;
  // The pet latest pickup date of the service, yyyy-MM-dd
  string end_date = 11;
  // The pet latest pickup time of the service, unit minute, 540 means 09:00
  int32 end_time = 12;
  // service name
  string service_name = 16;
}

// The daycare add-on detail
message DaycareAddOnDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of daycare service detail, associated with the current add-on
  int64 service_detail_id = 3;
  // The id of pet, associated with the current add-on
  int64 pet_id = 4;
  // The id of current add-on service
  int64 add_on_id = 5;
  // The specific dates of add-on service
  repeated string specific_dates = 6;
  // The flag to indicate if the add-on service is everyday
  bool is_everyday = 7;
  // The price of current add-on service
  double service_price = 8;
  // The id of tax, associated with the current add-on
  int64 tax_id = 9;
  // The duration of current add-on service
  int32 duration = 10;
  // service name
  string service_name = 16;
  // quantity per day
  int32 quantity_per_day = 17;
  // require dedicated staff
  bool require_dedicated_staff = 18;
}

// The evaluation service
message EvaluationService {
  // the evaluation service detail
  EvaluationTestDetail service = 1;
}

// The evaluation test detail
message EvaluationTestDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current evaluation test
  int64 pet_id = 3;
  // The id of current evaluation test
  int64 evaluation_id = 4;
  // The price of current evaluation test
  double service_price = 5;
  // The duration of current evaluation test, unit minute
  int32 duration = 6;
  // The start date of the evaluation test, yyyy-MM-dd
  string start_date = 7;
  // The start time of the evaluation test, unit minute, 540 means 09:00
  int32 start_time = 8;
  // The end date of the evaluation test, yyyy-MM-dd
  string end_date = 9;
  // The end time of the evaluation test, unit minute, 540 means 09:00
  int32 end_time = 10;
  // service name
  // deprecated by Freeman since 2025/5/16, 避免和 service 名字混淆，use evaluation_name instead
  string service_name = 16 [deprecated = true];
  // evaluation name
  string evaluation_name = 17;
  // service id, evaluation 绑定的 service id
  // 0 表示没有绑定 service
  int64 service_id = 18;
}

// The dog walking service
message DogWalkingService {
  // the dog walking service detail
  DogWalkingServiceDetail service = 1;
}

// The dog walking service detail
message DogWalkingServiceDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current service
  int64 pet_id = 3;
  // The id of staff, associated with the current service
  int64 staff_id = 4;
  // The id of current service
  int64 service_id = 5;
  // The time of current service, unit minute
  int32 service_time = 6;
  // The price of current service
  double service_price = 7;
  // The start date of the service, yyyy-MM-dd
  string start_date = 10;
  // The start time of the service, unit minute, 540 means 09:00
  int32 start_time = 11;
  // The end date of the service, yyyy-MM-dd
  string end_date = 12;
  // The end time of the service, unit minute, 540 means 09:00
  int32 end_time = 13;
  // service name
  string service_name = 16;
  // staff name
  string staff_name = 17;
  // price override type
  moego.models.offering.v1.ServiceOverrideType price_override_type = 18;
  // duration override type
  moego.models.offering.v1.ServiceOverrideType duration_override_type = 19;
}

// The group class service
message GroupClassService {
  // The group class service detail
  GroupClassServiceDetail service = 1;
}

// The group class service detail
message GroupClassServiceDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // The id of pet, associated with the current service
  int64 pet_id = 3;
  // The id of group class instance,
  int64 class_instance_id = 4;
  // The id of staff, associated with the current service
  int64 staff_id = 5;
  // The id of current service
  int64 service_id = 6;
  // The time of current service, unit minute
  repeated string specific_dates = 7;
  // The start time of the service, unit minute, 540 means 09:00
  int32 start_time = 8;
  // The end time of the service, unit minute, 540 means 09:00
  int32 end_time = 9;
  // Duration of each session in minutes
  int32 duration_per_session = 10;
  // service name
  string service_name = 11;
  // staff name
  string staff_name = 12;
  // instance name
  string instance_name = 13;
  // Number of sessions
  int32 num_sessions = 14;
  // Group class instance occurrence
  models.offering.v1.GroupClassInstance.Occurrence occurrence = 15;
}

// pet detail
message PetDetail {
  // id
  int64 id = 1;
  // pet name
  string pet_name = 4;
  // avatar path
  string avatar_path = 5;
  // pet type id
  moego.models.customer.v1.PetType pet_type = 6;
  // breed
  string breed = 7;
  // breed mixed
  bool breed_mixed = 10;
  // gender
  moego.models.customer.v1.PetGender gender = 11;
  // weight
  string weight = 8;
  // coat type
  string coat_type = 9;
  // fixed
  string fixed = 12;
  // behavior
  string behavior = 13;
  // birthday, may not exist
  optional google.type.Date birthday = 15;
  // passed away
  bool passed_away = 16;
  // deleted
  bool deleted = 17;
  // expiry notification
  bool expiry_notification = 20;
  // vet name
  string vet_name = 30;
  // vet phone number
  string vet_phone_number = 31;
  // vet address
  string vet_address = 32;
  // emergency contact name
  string emergency_contact_name = 33;
  // emergency contact phone number
  string emergency_contact_phone_number = 34;
  // health issues
  string health_issues = 35;
  // evaluation status
  models.customer.v1.EvaluationStatus evaluation_status = 36;
  // pet codes
  repeated PetCodeComposite pet_codes = 37;
  // question answers
  repeated QuestionAnswerDetail question_answers = 38;
  // pet vaccines
  repeated PetVaccineComposite pet_vaccines = 39;
}

// pet vaccine
message PetVaccineComposite {
  // vaccine record id
  int64 vaccine_binding_id = 1;
  // vaccine id
  int64 vaccine_id = 3;
  // expiration date, may not exist
  optional google.type.Date expiration_date = 4;
  // vaccine document urls
  repeated string document_urls = 5;
  // vaccine name
  string vaccine_name = 6;
}

// pet code model
message PetCodeComposite {
  // pet code id
  int64 id = 1;
  // pet code abbreviation
  string abbreviation = 2;
  // pet code description
  string description = 4;
  // pet code color
  string color = 5;
  // pet code sort. The larger the sort number, the higher the priority.
  int32 sort = 6;
  // if the pet code is deleted
  bool deleted = 7;
}

// The Pay for Booking Request
message PayBookingRequestView {
  // paid amount
  optional double paid_amount = 1;
  // pre pay amount
  optional double pre_pay_amount = 2;
  // refund amount
  optional double refund_amount = 3;
  // pre pay status
  optional int32 pre_pay_status = 4;
  // pre pay rate
  optional double pre_pay_rate = 5;
  // pre auth enable
  optional bool pre_auth_enable = 6;
}

// Address detail
message AddressDetail {
  // address id
  int32 address_id = 1;
  // address 1
  string address1 = 2;
  // address 2
  string address2 = 3;
  // city
  string city = 4;
  // state
  string state = 5;
  // zip code
  string zipcode = 6;
  // country
  string country = 7;
  // latitude
  string lat = 8;
  // longitude
  string lng = 9;
  // boolean
  bool is_primary = 10;
  // is profile request address
  optional bool is_profile_request_address = 11;
  // customer id
  int64 customer_id = 12;
}

// question answer detail
message QuestionAnswerDetail {
  // question key
  string key = 1;
  // question
  string question = 2;
  // answer
  string answer = 3;
}

// grooming auto assign detail
message GroomingAutoAssignDetail {
  // id
  int32 id = 1;
  // appointment id
  int32 appointment_id = 2;
  // staff id
  int32 staff_id = 3;
  // appointment time
  int32 appointment_time = 4;
}

// boarding auto assign detail
message BoardingAutoAssignDetail {
  // id
  int64 id = 1;
  // The id of booking request
  int64 booking_request_id = 2;
  // boardingServiceDetailId
  int64 boarding_service_detail_id = 3;
  // The id of lodging
  int64 lodging_id = 4;
}

// assign require
message AssignRequire {
  // room
  repeated RoomAssignRequire room_assign_requires = 6 [deprecated = true];
  // staff
  repeated StaffAssignRequire staff_assign_require = 7;
}

// assign require
message IncompleteDetails {
  // 还需要分配 staff/time 的 grooming services
  repeated GroomingService grooming_services = 1;
  // 还需要分配 loading/evaluation 的 boarding services
  repeated BoardingService boarding_services = 2;
  // 还需要分配 evaluation 的 daycare services
  repeated DaycareService daycare_services = 3;
  // 还需要分配 staff 的 evaluation services
  repeated EvaluationService evaluation_services = 4;

  // 还需要分配 staff/time 的 grooming addons
  repeated GroomingAddon grooming_addons = 5;
  // 还需要分配 staff/time 的 boarding addons
  repeated BoardingAddon boarding_addons = 6;
  // 还需要分配 staff/time 的 daycare addons
  repeated DaycareAddon daycare_addons = 7;

  // Grooming service
  message GroomingService {
    // grooming service detail
    moego.models.online_booking.v1.GroomingServiceDetailModel service_detail = 1;
    // service info
    moego.models.offering.v1.ServiceBriefView service = 2;
  }

  // Boarding service
  message BoardingService {
    // boarding service detail
    moego.models.online_booking.v1.BoardingServiceDetailModel service_detail = 1;
    // service info
    moego.models.offering.v1.ServiceBriefView service = 2;
    // pet 对应 boarding service 所缺失的 evaluation
    optional moego.models.offering.v1.EvaluationBriefView missing_evaluation = 3;
  }

  // Daycare service
  message DaycareService {
    // daycare service detail
    moego.models.online_booking.v1.DaycareServiceDetailModel service_detail = 1;
    // service info
    moego.models.offering.v1.ServiceBriefView service = 2;
    // pet 对应 daycare service 所缺失的 evaluation
    optional moego.models.offering.v1.EvaluationBriefView missing_evaluation = 3;
  }

  // Evaluation service
  message EvaluationService {
    // evaluation service detail
    moego.models.online_booking.v1.EvaluationTestDetailModel service_detail = 1;
    // evaluation info
    moego.models.offering.v1.EvaluationBriefView evaluation = 2;
  }

  // Grooming addon
  message GroomingAddon {
    // grooming addon detail
    moego.models.online_booking.v1.GroomingAddOnDetailModel addon_detail = 1;
    // service info
    moego.models.offering.v1.ServiceBriefView service = 2;
  }

  // Boarding addon
  message BoardingAddon {
    // boarding addon detail
    moego.models.online_booking.v1.BoardingAddOnDetailModel addon_detail = 1;
    // service info
    moego.models.offering.v1.ServiceBriefView service = 2;
  }

  // Daycare addon
  message DaycareAddon {
    // daycare addon detail
    moego.models.online_booking.v1.DaycareAddOnDetailModel addon_detail = 1;
    // service info
    moego.models.offering.v1.ServiceBriefView service = 2;
  }
}

// pet detail
message RoomAssignRequire {
  option deprecated = true;
  // pet id
  int64 pet_id = 1;
  // pet name
  string pet_name = 2;
  // service id
  int64 service_id = 3;
  // start date
  string start_date = 4;
  // end date
  string end_date = 5;
}

// pet detail
message StaffAssignRequire {
  // pet id
  int64 pet_id = 1;
  // pet name
  string pet_name = 2;
  // service id
  int64 service_id = 3;
  // duration
  int32 duration = 6;
  // service name
  string service_name = 7;
  // specific dates, indicates that staff needs to be specified for multiple days
  repeated string specific_dates = 8;
}

// order booking request view
message OrderBookingRequestView {
  // discount code name
  string discount_code_name = 1;
}

// Accept booking request request
message AcceptBookingRequestRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // assign lodging unit id
  repeated PetToLodging pet_to_lodgings = 2;
  // assign staff
  repeated PetToStaff pet_to_staffs = 3;
  // assign service
  repeated PetToService pet_to_services = 4;
  // assign evaluation staff
  repeated PetToStaff evaluation_pet_to_staffs = 5;
}

// Pet to lodging
message PetToLodging {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // lodging unit id
  int64 lodging_unit_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// Pet to staff
message PetToStaff {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service id
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
  // start time
  int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
}

// Pet to service
message PetToService {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64 = {gt: 0}];
  // old evaluation service id
  optional int64 from_evaluation_service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // new evaluation service id
  optional int64 to_evaluation_service_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// Auto assign request
message AutoAssignRequest {
  // booking request id
  int64 booking_request_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Auto assign response
message AutoAssignResponse {
  // boarding assign info require
  repeated AssignRequire boarding_assign_requires = 1;
  // assign result. pet to lodging unit id
  repeated PetToLodging pet_to_lodgings = 2;
  // evaluation assign info require
  repeated AssignRequire evaluation_assign_requires = 3;
  // assign result. pet to staff
  repeated PetToStaff evaluation_pet_to_staffs = 4;

  // lodging details for auto assign
  repeated LodgingDetail lodgings = 11;
  // staff details for auto assign
  repeated StaffDetail staffs = 12;

  // lodging assign info require
  message AssignRequire {
    // pet id
    int64 pet_id = 1;
    // service id
    int64 service_id = 2;
    // start date
    optional string start_date = 3;
    // end date
    optional string end_date = 4;
  }

  // lodging detail for auto assigned
  message LodgingDetail {
    // lodging id
    int64 lodging_id = 1;
    // lodging unit name
    string lodging_unit_name = 2;
    // lodging type name
    string lodging_type_name = 3;
  }

  // staff detail for auto assign
  message StaffDetail {
    // staff id
    int64 id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
  }
}

// Accept booking request response
message AcceptBookingRequestResponse {
  // result
  bool result = 1;
  // need to send notification appointment id
  int64 appointment_id = 2;
}

// Decline booking request request
message DeclineBookingRequestRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Decline booking request response
message DeclineBookingRequestResponse {
  // result
  bool result = 1;
  // need to send notification appointment id
  int64 appointment_id = 2;
}

// AcceptBookingRequestV2 request
message AcceptBookingRequestV2Request {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // grooming services
  repeated moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingService grooming_services = 2;
  // boarding services
  repeated moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingService boarding_services = 3;
  // daycare services
  repeated moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareService daycare_services = 4;
  // evaluation services
  repeated moego.service.online_booking.v1.AcceptBookingRequestV2Request.EvaluationService evaluation_services = 5;

  // grooming addons
  repeated moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingAddon grooming_addons = 15;
  // boarding addons
  repeated moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingAddon boarding_addons = 16;
  // daycare addons
  repeated moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareAddon daycare_addons = 17;

  // Create evaluation requests
  repeated moego.service.online_booking.v1.AcceptBookingRequestV2Request.CreateEvaluationRequest create_evaluation_requests = 18;
}

// AcceptBookingRequestV2 response
message AcceptBookingRequestV2Response {
  // result
  bool result = 1;
  // appointment id
  int64 appointment_id = 2;
}

// AutoAssignV2 request
message AutoAssignV2Request {
  // booking request id
  int64 booking_request_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// AutoAssignV2 response
message AutoAssignV2Response {
  // boarding services
  repeated moego.service.online_booking.v1.AutoAssignResponse.BoardingService boarding_services = 1;
  // evaluation services
  repeated moego.service.online_booking.v1.AutoAssignResponse.EvaluationService evaluation_services = 2;
}

// MoveBookingRequestToWaitlist params
message MoveBookingRequestToWaitlistParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // The booking request id
  int64 booking_request_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// MoveBookingRequestToWaitlist result
message MoveBookingRequestToWaitlistResult {}

// the booking_request service
service BookingRequestService {
  // Get booking request list
  rpc GetBookingRequestList(GetBookingRequestListRequest) returns (GetBookingRequestListResponse);

  // Get booking request by id
  rpc GetBookingRequest(GetBookingRequestRequest) returns (GetBookingRequestResponse);

  // Get auto assign room
  // deprecated by Freeman since 2025/3/4, use AutoAssignV2 instead
  rpc AutoAssign(AutoAssignRequest) returns (AutoAssignResponse) {
    option deprecated = true;
  }

  // Auto assign v2
  // 根据 booking request id 返回自动分配的信息。
  rpc AutoAssignV2(AutoAssignV2Request) returns (AutoAssignV2Response);

  // Accept booking request
  // deprecated by Freeman since 2025/3/5, 这个接口的参数设计和实现都很难看懂，难以扩展，use AcceptBookingRequestV2 instead
  rpc AcceptBookingRequest(AcceptBookingRequestRequest) returns (AcceptBookingRequestResponse) {
    option deprecated = true;
  }

  // Accept booking request v2
  // 没放在 v2 包下面的原因是这个接口会大量复用 AcceptBookingRequest 的逻辑，需要在一个类里面
  rpc AcceptBookingRequestV2(AcceptBookingRequestV2Request) returns (AcceptBookingRequestV2Response);

  // Decline booking request
  rpc DeclineBookingRequest(DeclineBookingRequestRequest) returns (DeclineBookingRequestResponse);

  // Move booking request to waitlist
  rpc MoveBookingRequestToWaitlist(MoveBookingRequestToWaitlistParams) returns (MoveBookingRequestToWaitlistResult);
}
