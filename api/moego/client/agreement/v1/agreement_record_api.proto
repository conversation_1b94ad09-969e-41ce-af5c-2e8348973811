syntax = "proto3";

package moego.client.agreement.v1;

import "moego/models/agreement/v1/agreement_enums.proto";
import "moego/models/agreement/v1/agreement_models.proto";
import "moego/models/agreement/v1/agreement_record_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/agreement/v1;agreementapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.agreement.v1";

// get agreement record list for customer params
message GetRecordListParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // sign status
  optional moego.models.agreement.v1.SignedStatus signed_status = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // page info
  moego.utils.v2.PaginationRequest pagination = 4 [(validate.rules).message = {required: true}];
  // if true, the agreement record will be listed in the company, otherwise only the records of the business
  // default is false
  optional bool list_company_record = 5;
}

// get agreement record list for customer result
message GetRecordListResult {
  // record list
  repeated moego.models.agreement.v1.AgreementRecordSimpleView agreement_record_simple_view = 1;
  // page info
  moego.utils.v2.PaginationResponse pagination = 2;
  // agreement list
  repeated moego.models.agreement.v1.AgreementModelSimpleView agreement_simple_view = 3;
}

// Agreement Record API
service AgreementRecordService {
  // get agreement record list for customer
  rpc GetRecordList(GetRecordListParams) returns (GetRecordListResult);
}
