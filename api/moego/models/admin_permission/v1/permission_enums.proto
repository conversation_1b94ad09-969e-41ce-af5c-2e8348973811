// @since 2023-05-27 21:48:13
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.admin_permission.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1;adminpermissionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.admin_permission.v1";

// 此枚举不应存在于任何接口定义中
// 权限点
// 值以十进制, 约定:
// 1. 3 位为分组
// 2. 5 位为子分组
// 3. 8 位为权限点
// 没有互斥的概念, 通过 filter 来实现
enum Permission {
  // unspecified
  PERMISSION_UNSPECIFIED = 0;

  // admin permission group
  PERMISSION_ADMIN_PERMISSION = 100;

  // role group
  PERMISSION_ADMIN_PERMISSION_ROLE = 10000;

  // role list
  PERMISSION_ADMIN_PERMISSION_ROLE_LIST = ********;
  // role detail (permissions)
  PERMISSION_ADMIN_PERMISSION_ROLE_VIEW = ********;
  // role edit (update/delete)
  PERMISSION_ADMIN_PERMISSION_ROLE_EDIT = ********;
  // role add
  PERMISSION_ADMIN_PERMISSION_ROLE_ADD = ********;

  // admin permission binding
  PERMISSION_ADMIN_PERMISSION_BINDING = 10001;
  // add/del role permission,
  // add/del account role binding
  PERMISSION_ADMIN_PERMISSION_BINDING_EDIT = ********;
  // list account roles
  PERMISSION_ADMIN_PERMISSION_BINDING_LIST = ********;
  // view account permissions
  PERMISSION_ADMIN_PERMISSION_BINDING_VIEW = ********;

  // metadata group
  PERMISSION_METADATA = 101;
  // list keys
  PERMISSION_METADATA_LIST = ********;
  // view values
  PERMISSION_METADATA_VIEW = ********;
  // edit keys/values
  PERMISSION_METADATA_EDIT = ********;
  // add keys
  PERMISSION_METADATA_ADD = ********;

  // account group
  PERMISSION_ACCOUNT = 102;
  // impersonate an user
  PERMISSION_ACCOUNT_IMPERSONATE = ********;
  // search account
  PERMISSION_ACCOUNT_SEARCH = ********;
  // view account info, sensitive fields will be desensitized when querying account list without this permission
  PERMISSION_ACCOUNT_INFO_VIEW = ********;
  // impersonate log view
  PERMISSION_ACCOUNT_IMPERSONATE_LOG_VIEW = ********;

  // demo account
  PERMISSION_ACCOUNT_DEMO = 10201;
  // list demo accounts
  PERMISSION_ACCOUNT_DEMO_LIST = ********;
  // view demo account
  PERMISSION_ACCOUNT_DEMO_VIEW = ********;
  // edit demo account
  PERMISSION_ACCOUNT_DEMO_EDIT = ********;
  // add demo account
  PERMISSION_ACCOUNT_DEMO_ADD = ********;
  // impersonate demo account
  PERMISSION_ACCOUNT_DEMO_IMPERSONATE = ********;

  // account
  PERMISSION_ACCOUNT_ACCOUNT = 10202;
  // list accounts
  PERMISSION_ACCOUNT_ACCOUNT_LIST = ********;
  // edit account
  PERMISSION_ACCOUNT_ACCOUNT_EDIT = ********;

  // session
  PERMISSION_ACCOUNT_SESSION = 10203;
  // list sessions
  PERMISSION_ACCOUNT_SESSION_LIST = ********;
  // edit sessions
  PERMISSION_ACCOUNT_SESSION_EDIT = ********;

  // profile
  PERMISSION_ACCOUNT_PROFILE = 10204;
  // view profile
  PERMISSION_ACCOUNT_PROFILE_VIEW = ********;

  // business module
  PERMISSION_BUSINESS = 103;
  // business list
  PERMISSION_BUSINESS_LIST = ********;
  // business search
  PERMISSION_BUSINESS_SEARCH = ********;

  // staff module
  PERMISSION_STAFF = 104;
  // staff list
  PERMISSION_STAFF_LIST = ********;
  // staff view
  PERMISSION_STAFF_VIEW = ********;
  // staff edit
  PERMISSION_STAFF_EDIT = ********;
  // staff add
  PERMISSION_STAFF_ADD = ********;
  // staff delete
  PERMISSION_STAFF_DELETE = ********;

  // ai assistant module
  PERMISSION_AI_ASSISTANT = 105;
  // ai assistant conversation template sub module
  PERMISSION_AI_ASSISTANT_CONVERSATION_TEMPLATE = 10500;
  // ai assistant conversation template list
  PERMISSION_AI_ASSISTANT_CONVERSATION_TEMPLATE_LIST = ********;
  // ai assistant conversation template edit
  PERMISSION_AI_ASSISTANT_CONVERSATION_TEMPLATE_EDIT = ********;
  // ai assistant conversation summary sub module
  PERMISSION_AI_ASSISTANT_CONVERSATION_SUMMARY = 10501;
  // ai assistant conversation summary list
  PERMISSION_AI_ASSISTANT_CONVERSATION_SUMMARY_LIST = 10501000;
  // ai assistant conversation sub module
  PERMISSION_AI_ASSISTANT_CONVERSATION = 10502;
  // ai assistant conversation list
  PERMISSION_AI_ASSISTANT_CONVERSATION_LIST = 10502000;

  // price checker module
  PERMISSION_PRICE_CHECKER = 106;
  // price checker data query
  PERMISSION_PRICE_CHECKER_DATA_QUERY = 10601;
  // price checker data query view
  PERMISSION_PRICE_CHECKER_DATA_QUERY_VIEW = 10601001;

  // message module
  PERMISSION_MESSAGE = 107;
  // mass text sub module
  PERMISSION_MESSAGE_MASS_TEXT = 10700;
  // mass text list
  PERMISSION_MESSAGE_MASS_TEXT_LIST = 10700000;
  // message sub module
  PERMISSION_MESSAGE_MESSAGE = 10701;
  // message thread list
  PERMISSION_MESSAGE_MESSAGE_THREAD_LIST = 10701000;
  // message list
  PERMISSION_MESSAGE_MESSAGE_MESSAGE_LIST = 10701001;
  // message summary module
  PERMISSION_MESSAGE_SUMMARY = 10702;
  // message summary list
  PERMISSION_MESSAGE_SUMMARY_EXPORT = 10702000;

  // activity log module
  PERMISSION_ACTIVITY_LOG = 108;
  // activity log sub module
  PERMISSION_ACTIVITY_LOG_ACTIVITY_LOG = 10800;
  // activity log list
  PERMISSION_ACTIVITY_LOG_ACTIVITY_LOG_LIST = 10800000;
  // activity log view
  PERMISSION_ACTIVITY_LOG_ACTIVITY_LOG_VIEW = 10800001;

  // pay ops module
  PERMISSION_PAY_OPS = 109;
  // pay ops payout sub module
  PERMISSION_PAY_OPS_PAYOUT = 10900;
  // pay ops payout edit
  PERMISSION_PAY_OPS_PAYOUT_EDIT = 10900000;
  // pay ops payout list
  PERMISSION_PAY_OPS_PAYOUT_LIST = 10900001;
  // pay ops custom fee sub module
  PERMISSION_PAY_OPS_CUSTOM_FEE = 10901;
  // pay ops custom fee edit
  PERMISSION_PAY_OPS_CUSTOM_FEE_EDIT = 10901000;
  // pay ops custom fee list
  PERMISSION_PAY_OPS_CUSTOM_FEE_LIST = 10901001;
  // pay ops custom fee add
  PERMISSION_PAY_OPS_CUSTOM_FEE_ADD = 10901002;
  // pay ops custom fee delete
  PERMISSION_PAY_OPS_CUSTOM_FEE_DELETE = 10901003;
  // pay ops custom fee approval
  PERMISSION_PAY_OPS_CUSTOM_FEE_APPROVAL = 10901004;
  // pay ops payout t+1 white list sub module
  PERMISSION_PAY_OPS_PAYOUT_T1_WHITE_LIST = 10902;
  // pay ops payout t+1 white list list
  PERMISSION_PAY_OPS_PAYOUT_T1_WHITE_LIST_LIST = 10902000;
  // pay ops payout t+1 white list add
  PERMISSION_PAY_OPS_PAYOUT_T1_WHITE_LIST_ADD = 10902001;
  // pay ops payout t+1 white list delete
  PERMISSION_PAY_OPS_PAYOUT_T1_WHITE_LIST_DELETE = 10902002;
  // pay ops pay admin
  // pay admin is a large module,
  // and company customer balance is one part of it
  // 10903 defines the permissions for company customer balance and does not affect other parts related to pay admin
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE = 10903;
  // pay ops company customer balance list:
  // this permission point is only used to view company customer balance list
  // if you want to query details, because billing\ stripe is involved,
  // view permission is also required
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_LIST = 10903000;
  // pay ops company customer balance view
  // This permission point is used to view the details of billing subscription
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_VIEW = 10903001;
  // pay ops company customer balance edit
  // This permission point is used to edit the details of billing subscription
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_EDIT = 10903002;
  // pay ops company customer balance subscription
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_SUBSCRIPTION = 10904;
  // pay ops company customer balance subscription view
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_SUBSCRIPTION_VIEW = 10904000;
  // pay ops company customer balance subscription edit
  // This permission point is used for modifying coupons in the balance subscription module
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_SUBSCRIPTION_EDIT = 10904001;
  // pay ops company customer balance subscription delete
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_SUBSCRIPTION_DELETE = 10904002;
  // pay ops company customer balance subscription add
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_SUBSCRIPTION_ADD = 10904003;
  // pay ops company customer balance refund
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_REFUND = 10905;
  // pay ops company customer balance refund view
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_REFUND_VIEW = 10905000;
  // pay ops company customer dispute
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_DISPUTE = 10906;
  // pay ops company customer dispute business list
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_DISPUTE_BUSINESS_LIST = 10906000;
  // pay ops company customer dispute business edit
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_DISPUTE_BUSINESS_EDIT = 10906001;
  // pay ops company customer dispute business view
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_DISPUTE_BUSINESS_VIEW = 10906002;

  // The following permission points are not currently in use,
  // reserved for disputes related to subscription fees initiated by merchants on the platform.
  // pay ops company customer dispute platform list
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_DISPUTE_PLATFORM_LIST = 10906003;
  // pay ops company customer dispute platform edit
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_DISPUTE_PLATFORM_EDIT = 10906004;
  // pay ops company customer dispute platform view
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_DISPUTE_PLATFORM_VIEW = 10906005;

  // company customer balance billing charge
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_BILLING_CHARGE = 10908;
  // company customer balance billing charge list
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_BILLING_CHARGE_LIST = 10908000;
  // company customer balance billing charge refund
  PERMISSION_PAY_OPS_COMPANY_CUSTOMER_BALANCE_BILLING_CHARGE_REFUND = 10908001;

  // pay ops refund
  PERMISSION_PAY_OPS_CLIENT_REFUND = 10907;
  // pay ops refund client refund search
  PERMISSION_PAY_OPS_CLIENT_REFUND_SEARCH = 10907000;
  // pay ops refund client refund view
  PERMISSION_PAY_OPS_CLIENT_REFUND_VIEW = 10907001;
  // pay ops refund client refund refund
  PERMISSION_PAY_OPS_CLIENT_REFUND_REFUND = 10907002;

  // pay ops verification
  PERMISSION_PAY_OPS_VERIFICATION = 10909;
  // pay ops verification search
  PERMISSION_PAY_OPS_VERIFICATION_SEARCH = 10909000;
  // pay ops verification update
  PERMISSION_PAY_OPS_VERIFICATION_UPDATE = 10909001;

  // pay ops traffic switch
  PERMISSION_PAY_OPS_TRAFFIC_SWITCH = 10910;
  // pay ops all split payment traffic switch view
  PERMISSION_PAY_OPS_ALL_SPLIT_PAYMENT_TRAFFIC_SWITCH_VIEW = 10910000;
  // pay ops add to split payment traffic switch whitelist
  PERMISSION_PAY_OPS_SPLIT_PAYMENT_TRAFFIC_SWITCH_ADD_TO_WHITELIST = 10910001;
  // pay ops delete from split payment traffic switch whitelist
  PERMISSION_PAY_OPS_SPLIT_PAYMENT_TRAFFIC_SWITCH_DELETE_FROM_WHITELIST = 10910002;
  // pay ops add to split payment traffic switch blacklist
  PERMISSION_PAY_OPS_SPLIT_PAYMENT_TRAFFIC_SWITCH_ADD_TO_BLACKLIST = 10910003;
  // pay ops delete from split payment traffic switch blacklist
  PERMISSION_PAY_OPS_SPLIT_PAYMENT_TRAFFIC_SWITCH_DELETE_FROM_BLACKLIST = 10910004;
  // pay ops set split payment traffic switch ratio
  PERMISSION_PAY_OPS_SPLIT_PAYMENT_TRAFFIC_SET_RATIO = 10910005;
  // pay ops set split payment traffic switch loan
  PERMISSION_PAY_OPS_SPLIT_PAYMENT_TRAFFIC_SET_LOAN_SWITCH = 10910006;
  // pay ops add to invoice reinvent traffic switch whitelist
  PERMISSION_PAY_OPS_INVOICE_REINVENT_TRAFFIC_SWITCH_ADD_TO_WHITELIST = 10910007;
  // pay ops all invoice reinvent traffic switch view
  PERMISSION_PAY_OPS_ALL_INVOICE_REINVENT_TRAFFIC_SWITCH_VIEW = 10910008;

  // pay ops enterprise custom fee sub module
  PERMISSION_PAY_OPS_ENTERPRISE_CUSTOM_FEE = 10911;
  // pay ops enterprise custom fee edit
  PERMISSION_PAY_OPS_ENTERPRISE_CUSTOM_FEE_EDIT = ********;
  // pay ops enterprise custom fee list
  PERMISSION_PAY_OPS_ENTERPRISE_CUSTOM_FEE_LIST = ********;
  // pay ops enterprise custom fee add
  PERMISSION_PAY_OPS_ENTERPRISE_CUSTOM_FEE_ADD = ********;
  // pay ops enterprise custom fee delete
  PERMISSION_PAY_OPS_ENTERPRISE_CUSTOM_FEE_DELETE = ********;

  // pay ops accounting
  PERMISSION_PAY_OPS_ACCOUNTING = 10912;
  // pay ops accounting page view
  PERMISSION_PAY_OPS_ACCOUNTING_PAGE_VIEW = ********;
  // pay ops accounting compensate
  PERMISSION_PAY_OPS_ACCOUNTING_COMPENSATE = ********;

  // company module
  PERMISSION_COMPANY = 110;
  // company list
  PERMISSION_COMPANY_LIST = ********;
  // company search
  PERMISSION_COMPANY_SEARCH = ********;
  // reactive free company
  PERMISSION_COMPANY_REACTIVATE_FREE = ********;
  // remove free company
  PERMISSION_COMPANY_REMOVE_FREE = ********;
  // deactivate company
  PERMISSION_COMPANY_DEACTIVATE = ********;
  // company normal edit
  PERMISSION_COMPANY_NORMAL_EDIT = ********;
  // company plan change edit
  PERMISSION_COMPANY_PLAN_EDIT = ********;
  //company sms amount edit
  PERMISSION_COMPANY_SMS_EDIT = ********;

  // platform care module
  PERMISSION_PLATFORM_CARE = 111;
  // platform care create link
  PERMISSION_PLATFORM_CARE_CREATE_LINK = ********;
  // platform care list
  PERMISSION_PLATFORM_CARE_LIST = ********;
  // platform care delete
  PERMISSION_PLATFORM_CARE_DELETE = ********;
  // platform care edit
  PERMISSION_PLATFORM_CARE_EDIT = ********;

  // platform plan
  PERMISSION_PLATFORM_CARE_PLAN = 11101;
  // platform plan list
  PERMISSION_PLATFORM_CARE_PLAN_LIST = 11101000;
  // platform plan edit
  PERMISSION_PLATFORM_CARE_PLAN_EDIT = 11101001;

  // file module
  PERMISSION_FILE = 112;
  // file list sub module
  PERMISSION_FILE_LIST = 11200;
  // file list platform
  PERMISSION_FILE_LIST_PLATFORM = 11200000;
  // file list tenant
  PERMISSION_FILE_LIST_TENANT = 11200001;
  // file list own
  PERMISSION_FILE_LIST_OWN = 11200002;
  // file upload sub module
  PERMISSION_FILE_UPLOAD = 11201;
  // file upload sub platform
  PERMISSION_FILE_UPLOAD_PLATFORM = 11201000;
  // file upload sub tenant
  PERMISSION_FILE_UPLOAD_TENANT = 11201001;

  // notification module
  PERMISSION_NOTIFICATION = 113;
  // notification campaign sub module
  PERMISSION_NOTIFICATION_CAMPAIGN = 11300;
  // notification campaign start
  PERMISSION_NOTIFICATION_CAMPAIGN_START = 11300000;
  // notification campaign targets search
  PERMISSION_NOTIFICATION_CAMPAIGN_TARGETS_SEARCH = 11300001;
  // notification template sub module
  PERMISSION_NOTIFICATION_TEMPLATES = 11301;
  // notification templates create
  PERMISSION_NOTIFICATION_TEMPLATES_CREATE = 11301000;
  // notification templates search
  PERMISSION_NOTIFICATION_TEMPLATES_SEARCH = 11301001;
  // notification templates update
  PERMISSION_NOTIFICATION_TEMPLATES_UPDATE = 11301002;
  // notification templates delete
  PERMISSION_NOTIFICATION_TEMPLATES_DELETE = 11301003;
  // notification metrics sub module
  PERMISSION_NOTIFICATION_METRICS = 11302;
  // notification metrics search
  PERMISSION_NOTIFICATION_METRICS_SEARCH = 11302000;
  // notification metrics reports search
  PERMISSION_NOTIFICATION_METRICS_REPORTS_SEARCH = 11302001;

  // agreement management module
  PERMISSION_AGREEMENT_MANAGEMENT = 114;
  // agreement management platform agreement
  PERMISSION_AGREEMENT_MANAGEMENT_PLATFORM_AGREEMENT = 11400;
  // agreement management platform agreement list
  PERMISSION_AGREEMENT_MANAGEMENT_PLATFORM_AGREEMENT_LIST = 11400000;
  // agreement management platform agreement delete
  PERMISSION_AGREEMENT_MANAGEMENT_PLATFORM_AGREEMENT_DELETE = 11400001;
  // agreement management platform agreement edit
  PERMISSION_AGREEMENT_MANAGEMENT_PLATFORM_AGREEMENT_EDIT = 11400002;
  // agreement management platform agreement view
  PERMISSION_AGREEMENT_MANAGEMENT_PLATFORM_AGREEMENT_VIEW = 11400003;
  // agreement management platform agreement add
  PERMISSION_AGREEMENT_MANAGEMENT_PLATFORM_AGREEMENT_ADD = 11400004;

  // agreement management business agreement
  PERMISSION_AGREEMENT_MANAGEMENT_BUSINESS_AGREEMENT = 11401;
  // agreement management business agreement list
  PERMISSION_AGREEMENT_MANAGEMENT_BUSINESS_AGREEMENT_LIST = 11401000;
  // agreement management business agreement delete
  PERMISSION_AGREEMENT_MANAGEMENT_BUSINESS_AGREEMENT_DELETE = 11401001;
  // agreement management business agreement edit
  PERMISSION_AGREEMENT_MANAGEMENT_BUSINESS_AGREEMENT_EDIT = 11401002;
  // agreement management business agreement view
  PERMISSION_AGREEMENT_MANAGEMENT_BUSINESS_AGREEMENT_VIEW = 11401003;
  // agreement management business agreement add
  PERMISSION_AGREEMENT_MANAGEMENT_BUSINESS_AGREEMENT_ADD = 11401004;

  // agreement management agreement record
  PERMISSION_AGREEMENT_MANAGEMENT_AGREEMENT_RECORD = 11402;
  // agreement management agreement record list
  PERMISSION_AGREEMENT_MANAGEMENT_AGREEMENT_RECORD_LIST = 11402000;
  // agreement management agreement record list
  PERMISSION_AGREEMENT_MANAGEMENT_AGREEMENT_RECORD_VIEW = 11402001;

  // coupon management
  PERMISSION_COUPON_MANAGEMENT = 115;
  // coupon management coupon
  PERMISSION_COUPON_MANAGEMENT_COUPON = 11500;
  // coupon management coupon list
  PERMISSION_COUPON_MANAGEMENT_COUPON_LIST = 11500000;
  // coupon management coupon add
  PERMISSION_COUPON_MANAGEMENT_COUPON_ADD = 11500001;
  // coupon management coupon view
  PERMISSION_COUPON_MANAGEMENT_COUPON_VIEW = 11500002;
  // coupon management coupon deleted
  PERMISSION_COUPON_MANAGEMENT_COUPON_DELETED = 11500003;

  // platform sales module
  PERMISSION_PLATFORM_SALES = 116;
  // platform sales create link
  PERMISSION_PLATFORM_SALES_CREATE_LINK = 11600000;
  // platform sales list
  PERMISSION_PLATFORM_SALES_LIST = 11600001;
  // platform sales delete
  PERMISSION_PLATFORM_SALES_DELETE = 11600002;
  // platform sales edit
  PERMISSION_PLATFORM_SALES_EDIT = 11600003;

  // appointment module
  PERMISSION_APPOINTMENT = 117;
  // appointment list
  PERMISSION_APPOINTMENT_LIST = 11700001;
  // appointment edit
  PERMISSION_APPOINTMENT_EDIT = 11700002;

  // basic exceptions
  PERMISSION_BASIC_EXCEPTIONS = 118;
  //basic exceptions list
  PERMISSION_BASIC_EXCEPTIONS_LIST = 11800001;

  // review booster module
  PERMISSION_REVIEW_BOOSTER = 119;
  // review booster list
  PERMISSION_REVIEW_BOOSTER_RECORD_LIST = 11900001;
  // review booster edit
  PERMISSION_REVIEW_BOOSTER_RECORD_EDIT = 11900002;

  // quick book
  PERMISSION_QUICK_BOOK = 120;
  // quick book setting page
  PERMISSION_QUICK_BOOK_SETTING = 12000;
  // quick book setting page view
  PERMISSION_QUICK_BOOK_SETTING_VIEW = 12000000;
  // quick book setting page edit
  PERMISSION_QUICK_BOOK_SETTING_EDIT = 12000001;
  // quick book view page
  PERMISSION_QUICK_BOOK_VIEW = 12001;
  // quick book view page search
  PERMISSION_QUICK_BOOK_VIEW_SEARCH = 12001001;
  // quick book view page compensation
  PERMISSION_QUICK_BOOK_VIEW_COMPENSATION = 12001002;

  // enterprise module
  PERMISSION_ENTERPRISE = 121;
  // enterprise search
  PERMISSION_ENTERPRISE_SEARCH = 12100000;
  // link franchisee
  PERMISSION_ENTERPRISE_LINK_FRANCHISEE = 12100001;
  // unlink franchisee
  PERMISSION_ENTERPRISE_UNLINK_FRANCHISEE = 12100002;
}
