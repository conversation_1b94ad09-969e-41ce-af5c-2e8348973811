syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "moego/models/agreement/v1/agreement_models.proto";
import "moego/models/appointment/v1/appointment_note_models.proto";
import "moego/models/business_customer/v1/business_customer_tag_models.proto";
import "moego/models/business_customer/v1/business_pet_code_models.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// ClientPetsMapping
message ClientPetsMapping {
  // customer id
  int64 customer_id = 1;
  // list of pet ids
  repeated int64 pet_ids = 2 [(validate.rules).repeated = {
    max_items: 100
    unique: true
    items: {
      int64: {gte: 0}
    }
  }];
}

// AlertDetail
message AlertDetail {
  // client alert
  optional ClientAlert client_alert = 1;
  // list of pet alert
  repeated PetAlert pet_alerts = 2;
}

// ClientAlert
message ClientAlert {
  // customer id
  int64 customer_id = 1;
  // client tag alerts
  optional ClientTagAlert client_tag_alert = 5;
  // membership alert
  optional MembershipAlert membership_alert = 6;
  // package alert
  optional PackageAlert package_alert = 7;
  // note alert
  optional NoteAlert note_alert = 8;
  // unsigned agreement alert
  optional UnsignedAgreementAlert unsigned_agreement_alert = 9;
  // card on file alert
  optional CardOnFileAlert card_on_file_alert = 10;
  // unpaid balance alert
  optional UnpaidBalanceAlert unpaid_balance_alert = 11;
  // pickup person alert
  // optional PickupPersonAlert pickup_person_alert = 12;
}

// PetAlert
message PetAlert {
  // pet id
  int64 pet_id = 1;
  // pet code alerts
  optional PetCodeAlert pet_code_alert = 5;
  // vaccine alert
  optional VaccineAlert vaccine_alert = 6;
  // incident alert
  optional IncidentAlert incident_alert = 7;
}

// ClientTagAlert
message ClientTagAlert {
  // client tags
  repeated moego.models.business_customer.v1.BusinessCustomerTagModel client_tags = 1;
}

// PetCodeAlert
message PetCodeAlert {
  // pet codes
  repeated moego.models.business_customer.v1.BusinessPetCodeView pet_codes = 1;
}

// VaccineAlert
message VaccineAlert {
  // list of vaccines
  repeated VaccineView vaccines = 1;

  // VaccineView
  message VaccineView {
    // vaccine id
    int64 vaccine_id = 1;
    // vaccine name
    string vaccine_name = 2;
    // vaccine binding id
    int64 vaccine_binding_id = 3;
    // vaccine document urls
    repeated string document_urls = 4;
    // expiration date, may not exist
    optional google.type.Date expiration_date = 5;
    // is missing
    bool is_missing = 6;
  }
}

// IncidentAlert
message IncidentAlert {
  // list of incidents
  repeated IncidentView incidents = 1;

  // IncidentView
  message IncidentView {
    // business id
    int64 business_id = 1;
    // list of pet ids
    repeated int64 pet_ids = 2;
    // incident report id
    int64 incident_report_id = 3;
    // incident type id
    int64 incident_type_id = 4;
    // incident type name
    string incident_type_name = 5;
    // incident description
    string description = 6;
    // incident timestamp
    google.protobuf.Timestamp incident_time = 7;
  }
}

// MembershipAlert
message MembershipAlert {
  // list of membership subscriptions
  repeated moego.models.membership.v1.MembershipSubscriptionModel subscriptions = 1;
}

// CardOnFileAlert
message CardOnFileAlert {
  // card on file status
  optional COFStatus cof_status = 1;

  // COFStatus
  enum COFStatus {
    // unspecified
    COF_STATUS_UNSPECIFIED = 0;
    // authorized
    AUTHORIZED = 1;
    // pending
    PENDING = 2;
    // failed
    FAILED = 3;
    // no card on file
    NO_CARD_ON_FILE = 4;
  }
}

// PackageAlert
message PackageAlert {
  // list of packages
  repeated PackageView packages = 1;

  // PackageView
  message PackageView {
    // customer id
    int64 customer_id = 1;
    // package id
    int64 package_id = 2;
    // package name
    string package_name = 3;
    // valid start date
    google.type.Date start_date = 4;
    // expiration date
    optional google.type.Date expiration_date = 5;
  }
}

// NoteAlert
message NoteAlert {
  // last alert notes
  optional models.appointment.v1.AppointmentNoteModel alert_note = 1;
}

// UnsignedAgreementAlert
message UnsignedAgreementAlert {
  // list of unsigned agreements
  repeated moego.models.agreement.v1.AgreementModelSimpleView agreements = 1;
}

// UnpaidBalanceAlert
message UnpaidBalanceAlert {
  // unpaid amount
  optional google.type.Money unpaid_amount = 1;
}
