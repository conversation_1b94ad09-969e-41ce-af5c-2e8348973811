// @since 2024-06-24 17:19:30
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// question type
enum QuestionType {
  // unspecified value
  QUESTION_TYPE_UNSPECIFIED = 0;
  // single_choice
  SINGLE_CHOICE = 1;
  // multi_choice
  MULTI_CHOICE = 2;
  // text_input( long_text_input )
  TEXT_INPUT = 3;
  // body_view
  BODY_VIEW = 4;
  // short_text_input
  SHORT_TEXT_INPUT = 5;
  // tag_choice
  TAG_CHOICE = 6;
}

// question category type
enum QuestionCategoryType {
  // unspecified value
  QUESTION_CATEGORY_TYPE_UNSPECIFIED = 0;
  // feedback
  FEEDBACK = 1;
  // pet condition
  PET_CONDITION = 2;
  // customize feedback
  CUSTOMIZE_FEEDBACK = 3;
}

// report status
enum ReportCardStatus {
  // unspecified value
  REPORT_CARD_STATUS_UNSPECIFIED = 0;
  // created
  REPORT_CARD_CREATED = 1;
  // draft
  REPORT_CARD_DRAFT = 2;
  // ready
  REPORT_CARD_READY = 3;
  // sent
  REPORT_CARD_SENT = 4;
}

// send method
enum SendMethod {
  // unspecified value
  SEND_METHOD_UNSPECIFIED = 0;
  // email
  EMAIL = 1;
  // sms
  SMS = 2;
}
