syntax = "proto3";

package moego.models.appointment.v1;

import "moego/models/appointment/v1/appointment_pet_feeding_schedule_defs.proto";
import "moego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto";
import "moego/models/appointment/v1/boarding_split_lodging_defs.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/appointment/v1/service_operation_defs.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// the appointment pet detail definitions, contains the selected pets and services.
message PetDetailDef {
  // selected pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];

  // Boarding service: start date, start time -> end date, end time
  // Daycare service: start date, start time -> end time
  // Grooming service: start date, start time -> end time, staff id
  repeated SelectedServiceDef services = 2 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Boarding add-on: every day / certain dates, start time, staff id
  // Daycare add-on: every day / certain dates, start time, staff id
  // Grooming add-on: start date, start time -> end time, staff id
  repeated SelectedAddOnDef add_ons = 3 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Evaluation: start date, start time -> end time
  repeated SelectedEvaluationDef evaluations = 4 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Selected service definition
message SelectedServiceDef {
  // selected service id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];

  // service start date, in yyyy-MM-dd format
  // Drop off date for boarding or daycare service
  // Grooming date for grooming service
  // empty for daycare associated to boarding with specific days or every days
  string start_date = 2 [(validate.rules).string = {
    pattern: "^\\d{4}-\\d{2}-\\d{2}$"
    ignore_empty: true
  }];

  // service end date, in yyyy-MM-dd format
  // Pick up date for boarding or daycare service
  // Do not pass the grooming service field.
  optional string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  // Grooming & Boarding & Daycare service required
  optional int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // end time, in minutes
  // Boarding & Daycare service required
  optional int32 end_time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // lodging id
  // Boarding service required, Daycare service is optional
  optional int64 lodging_id = 7 [(validate.rules).int64.gte = 0];

  // selected staff id, only for grooming service
  // Grooming service required
  optional int64 staff_id = 8 [(validate.rules).int64.gte = 0];

  // Service time, Can be manually modified when created.
  // If the scope type is 2, directly take the customized service time
  // If it is not 2, directly take this value and overwrite it.
  optional int32 service_time = 9 [(validate.rules).int32 = {gte: 0}];

  // Service price, Can be manually modified when created.
  // If the scope type is 2, directly take the customized service price
  // If it is not 2, directly take this value and overwrite it.
  optional double service_price = 10;

  // scope type price, apply to other services
  optional models.offering.v1.ServiceScopeType scope_type_price = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // scope type time, apply to other services
  optional models.offering.v1.ServiceScopeType scope_type_time = 12 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // enable operation
  optional bool enable_operation = 13;

  // work mode, 0-parallel, 1-sequence
  optional models.appointment.v1.WorkMode work_mode = 14 [(validate.rules).enum = {defined_only: true}];

  // operation list, multi-staff work for grooming service
  repeated models.appointment.v1.ServiceOperationDef operations = 15 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Pet's feedings
  repeated models.appointment.v1.AppointmentPetFeedingScheduleDef feedings = 16 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Pet's medications
  repeated models.appointment.v1.AppointmentPetMedicationScheduleDef medications = 17 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Pet detail date type
  // default for PET_DETAIL_DATE_DATE_POINT
  // support daycare associated to boarding
  optional PetDetailDateType date_type = 18 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // Certain dates
  // support date_type = PET_DETAIL_DATE_SPECIFIC_DATE
  repeated string specific_dates = 19 [(validate.rules).repeated = {
    max_items: 100
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];

  // price override type
  optional moego.models.offering.v1.ServiceOverrideType price_override_type = 21 [(validate.rules).enum = {defined_only: true}];

  // price override type
  optional moego.models.offering.v1.ServiceOverrideType duration_override_type = 22 [(validate.rules).enum = {defined_only: true}];

  // split lodgings
  repeated models.appointment.v1.BoardingSplitLodgingScheduleDef split_lodgings = 23 [(validate.rules).repeated = {max_items: 100}];
}

// Selected add-on service definition
message SelectedAddOnDef {
  // selected add-on id
  int64 add_on_id = 1 [(validate.rules).int64.gt = 0];

  // Add on date type, deprecated, use addon_date_type instead
  optional AddOnDateType date_type = 2 [deprecated = true];

  // service start date, in yyyy-MM-dd format
  // Grooming add-on & Require staff add-on required
  optional string start_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // Certain dates
  // No require staff add-on required
  repeated string specific_dates = 4 [(validate.rules).repeated = {
    max_items: 100
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];

  // start time, in minutes
  // Grooming add-on & Require staff add-on required
  optional int32 start_time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // selected staff id, only for grooming service
  // Grooming add-on & Require staff add-on required
  optional int64 staff_id = 6 [(validate.rules).int64.gte = 0];

  // Service time, Can be manually modified when created.
  // If the scope type is 2, directly take the customized service time
  // If it is not 2, directly take this value and overwrite it.
  optional int32 service_time = 7 [(validate.rules).int32 = {gte: 0}];

  // Service price, Can be manually modified when created.
  // If the scope type is 2, directly take the customized service price
  // If it is not 2, directly take this value and overwrite it.
  optional double service_price = 8;

  // scope type price, apply to other services
  optional models.offering.v1.ServiceScopeType scope_type_price = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // scope type time, apply to other services
  optional models.offering.v1.ServiceScopeType scope_type_time = 10 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // enable operation
  optional bool enable_operation = 11;

  // work mode, 0-parallel, 1-sequence
  optional models.appointment.v1.WorkMode work_mode = 12 [(validate.rules).enum = {defined_only: true}];

  // operation list, multi-staff work for grooming service
  repeated models.appointment.v1.ServiceOperationDef operations = 13 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Associated service id
  // Grooming only not required
  optional int64 associated_service_id = 14 [(validate.rules).int64.gte = 0];
  // price override type
  optional moego.models.offering.v1.ServiceOverrideType price_override_type = 15 [(validate.rules).enum = {defined_only: true}];
  // duration override type
  optional moego.models.offering.v1.ServiceOverrideType duration_override_type = 16 [(validate.rules).enum = {defined_only: true}];

  // Quantity per day
  // Non-required staff add-on need this parameter
  optional int32 quantity_per_day = 17 [(validate.rules).int32 = {gt: 0}];

  // Add on date type
  optional PetDetailDateType addon_date_type = 18 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Selected evaluation service definition
message SelectedEvaluationDef {
  // selected evaluation id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];

  // service start date, in yyyy-MM-dd format
  string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  optional int32 start_time = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // Service time, Can be manually modified when created.
  optional int32 service_time = 4 [(validate.rules).int32 = {gte: 0}];

  // Service price, Can be manually modified when created.
  optional double service_price = 5 [(validate.rules).double = {gte: 0}];

  // staff id
  optional int64 staff_id = 6 [(validate.rules).int64.gt = 0];

  // lodging id
  optional int64 lodging_id = 7 [(validate.rules).int64.gte = 0];

  // end time, in minutes
  optional int32 end_time = 8 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
}

// Block time definition
message BlockTimeDef {
  // selected staff id, only for grooming service
  // Non-required staff do not need to pass this parameter.
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];

  // block date, in yyyy-MM-dd format
  string start_date = 11 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  int32 start_time = 13 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // end time, in minutes
  int32 end_time = 14 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// Pet service schedule definition
message PetServiceScheduleDef {
  // selected pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];

  // Boarding service: start date, start time -> end date, end time
  // Daycare service: start date, start time -> end time
  // Grooming service: start date, start time -> end time, staff id
  repeated ServiceScheduleDef service_schedules = 2 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // Boarding add-on: every day / certain dates, start time, staff id
  // Daycare add-on: every day / certain dates, start time, staff id
  // Grooming add-on: start date, start time -> end time, staff id
  repeated AddOnScheduleDef add_on_schedules = 3 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Service schedule definition
message ServiceScheduleDef {
  // selected service id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];

  // service start date, in yyyy-MM-dd format
  string start_date = 2 [(validate.rules).string = {
    pattern: "^\\d{4}-\\d{2}-\\d{2}$"
    ignore_empty: true
  }];

  // service end date, in yyyy-MM-dd format
  optional string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  optional int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // end time, in minutes
  optional int32 end_time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // service order
  // Grooming only service required
  optional int32 order = 6 [(validate.rules).int32 = {gte: 0}];

  // service time, in minutes
  // If not specified, the default time will be used
  optional int32 service_time = 7 [(validate.rules).int32 = {gte: 0}];

  // Pet detail date type
  // default for PET_DETAIL_DATE_DATE_POINT
  // support daycare associated to boarding
  optional PetDetailDateType date_type = 8 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // Certain dates
  // support date_type = PET_DETAIL_DATE_SPECIFIC_DATE
  repeated string specific_dates = 9 [(validate.rules).repeated = {
    max_items: 100
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];
}

// Add-on service schedule definition
message AddOnScheduleDef {
  // selected add-on id
  int64 add_on_id = 1 [(validate.rules).int64.gt = 0];

  // Add on date type, deprecated, use addon_date_type instead
  optional AddOnDateType date_type = 2 [deprecated = true];

  // service start date, in yyyy-MM-dd format
  // Grooming date for grooming add-on
  optional string start_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // Certain dates
  // Boarding & Daycare service required
  repeated string specific_dates = 4 [(validate.rules).repeated = {
    max_items: 100
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];

  // start time, in minutes
  // Boarding & Daycare service required
  optional int32 start_time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // service order
  // Grooming only service required
  optional int32 order = 6 [(validate.rules).int32 = {gte: 0}];

  // service end date, in yyyy-MM-dd format
  optional string end_date = 7 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // end time, in minutes
  optional int32 end_time = 8 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // service time, in minutes
  // If not specified, the default time will be used
  optional int32 service_time = 9 [(validate.rules).int32 = {gte: 0}];

  // Associated service id
  // Grooming only not required
  optional int64 associated_service_id = 10 [(validate.rules).int64.gte = 0];

  // Add on date type
  optional PetDetailDateType addon_date_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// service lodging assign definition
message ServiceLodgingAssignDef {
  // selected pet service detail id / pet evaluation detail id
  int64 pet_service_detail_id = 1 [(validate.rules).int64.gt = 0];
  // selected lodging id
  int64 lodging_id = 2 [(validate.rules).int64.gte = 0];
}

// Boarding service schedule definition
message BoardingServiceScheduleDef {
  // selected pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];

  // selected lodging id
  int64 lodging_id = 4 [(validate.rules).int64.gt = 0];

  // service start date, in yyyy-MM-dd format
  string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // service end date, in yyyy-MM-dd format
  string end_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  int32 start_time = 7 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // end time, in minutes
  int32 end_time = 8 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// Evaluation service schedule definition
message EvaluationServiceScheduleDef {
  // selected evaluation id
  int64 evaluation_id = 1 [(validate.rules).int64.gt = 0];

  // service start date, in yyyy-MM-dd format
  string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  int32 start_time = 3 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // staff id
  optional int64 staff_id = 4 [(validate.rules).int64.gt = 0];

  // end time, can be stretched using this parameter
  optional int32 end_time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // lodging id
  optional int64 lodging_id = 6 [(validate.rules).int64 = {gte: 0}];
}

// Pet service calendar definition
message PetServiceCalendarDef {
  // selected pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];

  // Selected grooming service or add-on definitions
  repeated GroomingServiceCalendarDef grooming_services = 2 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Grooming service calendar definition
message GroomingServiceCalendarDef {
  // Selected service id or add-on id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];

  // Selected staff id
  // Single staff or main staff for multi-staff
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];

  // Service time, in minutes
  int32 service_time = 3 [(validate.rules).int32 = {gte: 0}];

  // Service type
  models.offering.v1.ServiceType service_type = 4 [(validate.rules).enum = {defined_only: true}];

  // Associated service id, add-on only
  optional int64 associated_service_id = 5 [(validate.rules).int64.gt = 0];

  // enable operation
  // Assign to multi-staff
  optional bool enable_operation = 6;

  // Work mode, 0-parallel, 1-sequence
  optional models.appointment.v1.WorkMode work_mode = 7 [(validate.rules).enum = {defined_only: true}];

  // Operation list, multi-staff work for grooming service
  repeated models.appointment.v1.ServiceOperationCalendarDef operations = 8 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Pet service calendar schedule definition
message PetServiceCalendarScheduleDef {
  // selected pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];

  // Selected grooming service or add-on schedules
  repeated GroomingServiceCalendarScheduleDef grooming_service_schedules = 2 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// Grooming service calendar schedule definition
message GroomingServiceCalendarScheduleDef {
  // Selected service id or add-on id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];

  // Selected staff id
  // Single staff or main staff for multi-staff
  int64 staff_id = 2 [(validate.rules).int64.gt = 0];

  // Service time, in minutes
  int32 service_time = 3 [(validate.rules).int32 = {gte: 0}];

  // Service type
  models.offering.v1.ServiceType service_type = 4 [(validate.rules).enum = {defined_only: true}];

  // Associated service id, add-on only
  optional int64 associated_service_id = 5 [(validate.rules).int64.gt = 0];

  // enable operation
  // Assign to multi-staff
  optional bool enable_operation = 6;

  // Work mode, 0-parallel, 1-sequence
  optional models.appointment.v1.WorkMode work_mode = 7 [(validate.rules).enum = {defined_only: true}];

  // Operation list, multi-staff work for grooming service
  repeated models.appointment.v1.ServiceOperationCalendarScheduleDef operation_schedules = 8 [(validate.rules).repeated = {
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // service start date, in yyyy-MM-dd format
  string start_date = 11 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // service end date, in yyyy-MM-dd format
  string end_date = 12 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, in minutes
  int32 start_time = 13 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];

  // end time, in minutes
  int32 end_time = 14 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
}

// The message of pet detail schedule, required params for care type
// Boarding service: start date, start time, end date, end time
// Daycare service: start date, start time, end time
// Grooming service: start date, start time
// Require staff add-on: start date, start time
// Non-require staff add-on: date_type, specific_dates, quantity_per_day
message PetDetailScheduleDef {
  // The id of pet detail
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // The start date
  optional string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The end date
  optional string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The start time
  optional int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // The end time
  optional int32 end_time = 5 [(validate.rules).int32 = {
    gte: 0
    lte: 1440
  }];
  // Pet detail date type
  // default for PET_DETAIL_DATE_DATE_POINT
  // support daycare associated to boarding
  optional moego.models.appointment.v1.PetDetailDateType date_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Certain dates
  // support date_type = PET_DETAIL_DATE_SPECIFIC_DATE
  repeated string specific_dates = 7 [(validate.rules).repeated = {
    max_items: 100
    items: {
      string: {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}
    }
  }];
  // Quantity per day
  // Non-required staff add-on need this parameter
  optional int32 quantity_per_day = 8 [(validate.rules).int32 = {gt: 0}];
}

// Update pet detail request
message UpdatePetDetailDef {
  // pet detail id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet detail date type. It is required for update date/time
  optional models.appointment.v1.PetDetailDateType date_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // service start date, in yyyy-MM-dd format, for boarding or daycare service
  optional string start_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // start time, in minutes
  optional int32 start_time = 4 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // service end date, in yyyy-MM-dd format, for boarding or daycare service
  optional string end_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end time, in minutes
  optional int32 end_time = 6 [(validate.rules).int32 = {
    gte: 0
    lt: 1440
  }];
  // add-on specific dates, yyyy-MM-dd
  repeated string specific_dates = 7 [(validate.rules).repeated = {
    max_items: 100
    unique: true
  }];
  // quantity per day
  optional int32 quantity_per_day = 8 [(validate.rules).int32 = {gt: 0}];
}
