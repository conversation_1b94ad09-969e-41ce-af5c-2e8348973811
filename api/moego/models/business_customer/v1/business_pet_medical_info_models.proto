syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet medical info model
message BusinessPetMedicalInfoModel {
  // pet id
  int64 pet_id = 1;
  // vet name
  string vet_name = 2;
  // vet phone number
  string vet_phone_number = 3;
  // vet address
  string vet_address = 4;
  // emergency contact name
  string emergency_contact_name = 5;
  // emergency contact phone number
  string emergency_contact_phone_number = 6;
  // health issues
  string health_issues = 7;
}
