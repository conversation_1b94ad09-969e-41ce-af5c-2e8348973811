// @since 2025-03-31 15:46:22
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/fulfillment/v1/fulfillment_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/order/v1/service_charge_enums.proto";
import "moego/models/organization/v1/tax_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.fulfillment.v1";

// The Fulfillment create definition
message FulfillmentCreateDef {
  // The ID of company
  int64 company_id = 2 [(validate.rules).int64.gt = 0];

  // The ID of business
  int64 business_id = 3 [(validate.rules).int64.gt = 0];

  // The ID of customer
  int64 customer_id = 4 [(validate.rules).int64.gt = 0];

  // The ID of booking request
  optional int64 booking_request_id = 5 [(validate.rules).int64.gt = 0];

  // The start datetime of fulfillment
  google.protobuf.Timestamp start_date_time = 6;

  // The end datetime of fulfillment
  google.protobuf.Timestamp end_date_time = 7;

  // The end datetime of fulfillment
  Status status = 8 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // color code
  optional string color_code = 9 [(validate.rules).string.pattern = "^#([A-Fa-f0-9]{6})$"];

  // source
  Source source = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// The Fulfillment update definition
message FulfillmentUpdateDef {
  // The fulfillment ID
  int64 id = 1 [(validate.rules).int64.gt = 0];

  // The ID of booking request
  optional int64 booking_request_id = 5 [(validate.rules).int64.gt = 0];

  // The start datetime of fulfillment
  optional google.protobuf.Timestamp start_date_time = 6;

  // The end datetime of fulfillment
  optional google.protobuf.Timestamp end_date_time = 7;

  // The end datetime of fulfillment
  optional Status status = 8 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // color code
  optional string color_code = 9 [(validate.rules).string.pattern = "^#([A-Fa-f0-9]{6})$"];
}

// The surcharge details
message SurchargeItem {
  // The surcharge ID
  int64 service_charge_id = 1;
  // The surcharge fee name
  string name = 2;
  // The surcharge fee type
  models.order.v1.SurchargeType type = 3;
  // The description of the service charge
  string description = 4;
  // The service charge detail ID, used to prepay for invoice
  int64 service_charge_detail_id = 5;
  // The unit price of the surcharge
  google.type.Money unit_price = 6;
  // The quantity
  int32 quantity = 7;
  // The total price of the surcharge
  google.type.Money total_price = 8;
  // External ID, example: sc_xxxx
  string external_id = 9;
}

// The pet service details
message PetService {
  // The pet info
  models.business_customer.v1.BusinessCustomerPetInfoModel pet = 1;
  // The service details
  repeated ServiceItem services = 2;
}

// The service item details
message ServiceItem {
  // The service ID
  int64 service_id = 1;
  // The service name
  string name = 2;
  // The service item type
  models.offering.v1.ServiceItemType service_item_type = 3;
  // The service type
  models.offering.v1.ServiceType service_type = 4;
  // The pet detail ID or evaluation service detail ID, used to prepay for invoice
  int64 detail_id = 5;
  // The unit price of the boarding service
  google.type.Money unit_price = 6;
  // The quantity
  int32 quantity = 7;
  // The total price of the service
  google.type.Money total_price = 8;
  // The staff ID for grooming service
  int64 staff_id = 9;
  // The description of the service
  string description = 10;
  // The line item details, Including original price, saved price and pricing rules
  repeated LineItem line_items = 11;
  // The order line item ID, if value is not 0, it means the line item is created
  int64 order_line_item_id = 12;
  // The service related tax
  models.organization.v1.TaxRuleModel tax = 13;
  // External ID, example: pd_xxx, ev_xxx
  string external_id = 14;
}

// The price adjustment
message LineItem {
  // The item name, e.g., "Service - Deluxe(D001)", "Service - Leslie M", "Multiple pets", "Peak dates"
  string item_name = 1;
  // The unit price of the item, Can be negative for discounts, e.g., -$5.00, or positive like $80.00
  google.type.Money unit_price = 2;
  // The quantity, e.g., 3, 2, 5
  int32 quantity = 3;
  // The line total, e.g., $240.00, -$25.00
  google.type.Money line_total = 4;
  // Type discriminator
  LineItemType item_type = 5;

  // The line item type
  enum LineItemType {
    // unspecified
    LINE_ITEM_TYPE_UNSPECIFIED = 0;
    // service price
    SERVICE_PRICE = 1;
    // pricing rule adjustment
    PRICING_RULE_ADJUSTMENT = 2;
  }
}
