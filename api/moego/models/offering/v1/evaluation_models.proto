// @since 2024-03-12 11:36:30
// <AUTHOR> <z<PERSON><PERSON>@moego.pet>

syntax = "proto3";

package moego.models.offering.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// The Evaluation model
message EvaluationModel {
  // the unique id
  int64 id = 1;
  // whether the evaluation is available for all business
  bool available_for_all_business = 2;
  // available business ids (if available_for_all_business is false)
  repeated int64 available_business_ids = 3;
  // service item types that require evaluation
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 4;
  // price
  double price = 5;
  // duration
  int32 duration = 6;
  // color code
  string color_code = 7;
  // name
  string name = 8;
  // is active
  bool is_active = 9;
  // whether the service is available for all lodging
  bool lodging_filter = 10;
  // available lodging ids(only if available_for_all_lodgings is false)
  repeated int64 customized_lodging_ids = 11;
  // description
  string description = 12;
  // name shown in ob flow
  string alias_for_online_booking = 13;
  // is available for all staff. default is true
  bool is_all_staff = 14;
  // available staff ids(only if is_all_staff is false)
  repeated int64 allowed_staff_list = 15;
  // is allow staff auto assign. default is false
  bool allow_staff_auto_assign = 16;
  // is resettable
  bool is_resettable = 17;
  // reset interval days
  int32 reset_interval_days = 18;
  // company_id
  int64 company_id = 19;
  // breed filter
  bool breed_filter = 20;
  // pet type breed filter
  repeated PetBreedFilter breed_filters = 21;
  // tax_id
  int64 tax_id = 22;
  // create time
  google.protobuf.Timestamp created_at = 23;
  // update time
  google.protobuf.Timestamp updated_at = 24;
  // update time
  google.protobuf.Timestamp deleted_at = 25;
}

// evaluation view
message EvaluationView {
  // the unique id
  int64 id = 1;
  // service item types that require evaluation
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 4;
  // price
  double price = 5;
  // duration
  int32 duration = 6;
  // color code
  string color_code = 7;
  // name
  string name = 8;
  // is active
  bool is_active = 9;
  // description
  string description = 12;
  // reset interval days
  int32 reset_interval_days = 18;
  // tax_id
  optional int64 tax_id = 22;
}

// The message for pet type & breed name filter
message PetBreedFilter {
  // pet type id
  models.customer.v1.PetType pet_type = 1;
  // allow all breeds
  bool is_all_breed = 2;
  // pet breed names
  repeated string breed_names = 3;
}

// the evaluation brief view
message EvaluationBriefView {
  // the unique id
  int64 id = 1;
  // price
  double price = 2;
  // duration
  int32 duration = 3;
  // color code
  string color_code = 4;
  // name
  string name = 5;
  // description
  string description = 6;
  // name shown in ob flow
  string alias_for_online_booking = 7;
  // is online booking available
  bool is_online_booking_available = 8;
  // is available for all staff. default is true
  bool is_all_staff = 9;
  // available staff ids(only if is_all_staff is false)
  repeated int64 allowed_staff_list = 10;
  // is allow staff auto assign. default is false
  bool allow_staff_auto_assign = 11;
  // is active
  bool is_active = 12;
  // breed filter
  bool breed_filter = 20;
  // customized breed filter
  repeated PetBreedFilter breed_filters = 21;
  // tax_id
  int64 tax_id = 22;
  // create time
  google.protobuf.Timestamp created_at = 35;
  // update time
  google.protobuf.Timestamp updated_at = 36;
}

// evaluation service client view
message EvaluationServiceClientView {
  // the unique id
  int64 id = 1;
  // whether the evaluation is available for all business
  bool available_for_all_business = 2;
  // available business ids (if available_for_all_business is false)
  repeated int64 available_business_ids = 3;
  // service item types that require evaluation
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 4;
  // price
  double price = 5;
  // duration
  int32 duration = 6;
  // color code
  string color_code = 7;
  // name
  string name = 8;
  // is active
  bool is_active = 9;
  // whether the service is available for all lodging
  bool lodging_filter = 10;
  // available lodging ids(only if available_for_all_lodgings is false)
  repeated int64 customized_lodging_ids = 11;
  // description
  string description = 12;
  // name shown in ob flow
  string alias_for_online_booking = 13;
  // is available for all staff. default is true
  bool is_all_staff = 14;
  // available staff ids(only if is_all_staff is false)
  repeated int64 allowed_staff_list = 15;
  // is allow staff auto assign. default is false
  bool allow_staff_auto_assign = 16;
  // is resettable
  bool is_resettable = 17;
  // reset interval days
  int32 reset_interval_days = 18;
  // company_id
  int64 company_id = 19;
}
