syntax = "proto3";

package moego.models.offering.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// service category model
message ServiceCategoryModel {
  // category id
  int64 category_id = 1;
  // category name
  string name = 2;
  // service list
  repeated ServiceModel services = 3;
}

// service model
message ServiceModel {
  reserved "is_all_staff"; // due to the type compatibility issue
  reserved 41;
  // id
  int64 service_id = 1;
  // name
  string name = 2;
  // description
  string description = 3;
  // inactive
  bool inactive = 4;
  // images
  repeated string images = 5;
  // category id
  int64 category_id = 6;
  // color code
  string color_code = 7;
  // location override rules
  repeated LocationOverrideRule location_override_list = 8 [deprecated = true];
  // require dedicated staff
  bool require_dedicated_staff = 9;
  // service type
  ServiceItemType service_item_type = 10;
  // price
  double price = 11;
  // price unit
  ServicePriceUnit price_unit = 12;
  // tax id
  int64 tax_id = 13;
  // duration
  int32 duration = 14;
  // whether add to commission base
  bool add_to_commission_base = 15;
  // whether can tip
  bool can_tip = 16;
  // whether the service is available for all locations
  bool is_all_location = 17;
  // available locations (only if is_available_in_all_locations is false)
  repeated int64 available_business_id_list = 18;
  // whether the service is available for all pet type & breed
  bool breed_filter = 19;
  // available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
  repeated CustomizedBreed customized_breed = 20;
  // available for all pet size
  bool pet_size_filter = 21;
  // available pet size (only if is_available_for_all_pet_size is false)
  repeated int64 customized_pet_sizes = 22;
  // weight filter only for compatible with old version, use pet_size in new version
  bool weight_filter = 23 [deprecated = true];
  // weight range (only if weight_filter is true)
  repeated double weight_range = 24 [deprecated = true];
  // available for all pet coat type
  bool coat_filter = 25;
  // available pet coat type (only if is_available_for_all_pet_coat_type is false)
  repeated int64 customized_coat = 26;
  // required dedicated lodging
  bool require_dedicated_lodging = 27;
  // whether the service is available for all lodging(only if require_dedicated_lodging is true)
  bool lodging_filter = 28;
  // available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
  repeated int64 customized_lodgings = 29;
  // whether the add on is available for all services(only for add on)
  bool service_filter = 30;
  // service filters(only for add on)
  repeated ServiceFilter service_filter_list = 31;
  // service type
  ServiceType type = 32;
  // max duration (only for daycare service)
  int32 max_duration = 33;
  // auto rollover rule
  AutoRolloverRule auto_rollover_rule = 34;
  // create time
  google.protobuf.Timestamp create_time = 35;
  // update time
  google.protobuf.Timestamp update_time = 36;
  // is deleted
  bool is_deleted = 37;
  // company id
  int64 company_id = 38;
  // available staffs
  repeated int64 available_staff_id_list = 39;
  // location staff override rules
  repeated LocationStaffOverrideRule location_staff_override_list = 40;
  // whether the service is available for all staff
  bool available_for_all_staff = 42;

  // pet code filter
  message PetCodeFilter {
    // whether to filter by white list or black list
    bool is_white_list = 1;
    // whether it applies to all pet codes.
    bool is_all_pet_code = 2;
    // pet code list, only valid when is_all_pet_code is false
    repeated int64 pet_code_ids = 3;
  }
  // pet code filter
  PetCodeFilter pet_code_filter = 43;

  // bundle services
  repeated int64 bundle_service_ids = 44;

  // source
  Source source = 45;

  // source
  enum Source {
    // source is not set
    SOURCE_UNSPECIFIED = 0;
    // source is from MoeGo platform (e.x. b web/app)
    MOEGO_PLATFORM = 1;
    // source is from Enterprise Hub
    ENTERPRISE_HUB = 2;
  }

  // number of sessions, only for training
  int32 num_sessions = 46;

  // duration of each session in minutes, only for training
  int32 duration_session_min = 47;

  // capacity of group class, zero means unlimited, only for training
  int32 capacity = 48;

  // whether it require a prerequisite class
  bool is_require_prerequisite_class = 50;

  // prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture
  repeated int64 prerequisite_class_ids = 51;

  // whether evaluation is required
  bool is_evaluation_required = 52;

  // whether evaluation is required before online booking
  bool is_evaluation_required_for_ob = 53;

  // evaluation id
  int64 evaluation_id = 54;
  // additional service rule
  optional AdditionalServiceRule additional_service_rule = 55;
}

// location override rules model
message LocationOverrideRule {
  // business id
  int64 business_id = 1;
  // price(null for not override)
  optional double price = 2;
  // tax(null for not override)
  optional int64 tax_id = 3;
  // duration(null for not override)
  optional int32 duration = 4;
  // max duration(only for daycare service)
  optional int32 max_duration = 5;
}

// override rule
message ServiceOverrideRule {
  // pet override rules
  repeated PetOverrideRule pet_override_list = 1;
}

// pet override rule
message PetOverrideRule {
  // pet id
  int64 pet_id = 1;
  // price(null for not override)
  optional double price = 2;
  // duration(null for not override)
  optional int32 duration = 3;
}

// auto roll over rule
message AutoRolloverRule {
  // auto rollover enabled
  bool enabled = 1;
  // auto rollover after x minutes
  int32 after_minute = 2;
  // auto rollover target service id
  int64 target_service_id = 3;
}

// service availability model
message ServiceAvailability {
  // whether the service is available for all locations
  bool is_all_location = 2;
  // available locations (only if is_available_in_all_locations is false)
  repeated int64 available_business_id_list = 3;
  // whether the service is available for all pet type & breed
  bool breed_filter = 4;
  // available pet type with pet breed (only if is_available_for_all_pet_type_and_breed is false)
  repeated CustomizedBreed customized_breed = 5;
  // available for all pet size
  bool pet_size_filter = 6;
  // available pet size (only if is_available_for_all_pet_size is false)
  repeated int64 customized_pet_sizes = 7;
  // weight filter only for compatible with old version, use pet_size in new version
  bool weight_filter = 8 [deprecated = true];
  // weight range (only if weight_filter is true)
  repeated double weight_range = 9 [deprecated = true];
  // available for all pet coat type
  bool coat_filter = 10;
  // available pet coat type (only if is_available_for_all_pet_coat_type is false)
  repeated int64 customized_coat = 11;
  // required dedicated lodging
  bool require_dedicated_lodging = 12;
  // whether the service is available for all lodging(only if require_dedicated_lodging is true)
  bool lodging_filter = 13;
  // available lodging ids(only if require_dedicated_lodging is true and available_for_all_lodgings is false)
  repeated int64 customized_lodgings = 14;
}

// customized breed
message CustomizedBreed {
  // pet type id
  int64 pet_type_id = 1;
  // pet breed ids
  repeated string breeds = 2;
  // allow all breeds
  optional bool is_all = 3;
}

// service filter
message ServiceFilter {
  // service item type
  ServiceItemType service_item_type = 1;
  // whether the addon is available for all services
  optional bool available_for_all_services = 2;
  // available service ids (only if available_for_all_services is false)
  repeated int64 available_service_id_list = 3;
}

// service category brief view
message CustomizedServiceCategoryView {
  // category id
  int64 category_id = 1;
  // category name
  string name = 2;
  // service list
  repeated CustomizedServiceView services = 3;
}

// service brief view
message CustomizedServiceView {
  // service id
  int64 id = 1;
  // name
  string name = 2;
  // price
  double price = 3;
  // price unit
  ServicePriceUnit price_unit = 4;
  // duration
  optional int32 duration = 5;
  // type
  ServiceType type = 6;
  // category id
  int64 category_id = 7;
  // price override type
  ServiceOverrideType price_override_type = 8;
  // duration override type
  ServiceOverrideType duration_override_type = 9;
  // tax id
  int64 tax_id = 10;
  // service item type
  ServiceItemType service_item_type = 11;
  // description
  string description = 12;
  // require dedicated staff
  bool require_dedicated_staff = 13;
  // require dedicated lodging
  bool require_dedicated_lodging = 14;
  // inactive
  bool inactive = 15;
  // max duration
  int32 max_duration = 16;
  // images
  repeated string images = 17;
  // staff overridden list
  repeated StaffOverrideRule staff_override_list = 18;
  // available staff list
  AvailableStaffs available_staffs = 19;
  // whether the service is available for all lodging
  bool lodging_filter = 20;
  // available lodging ids(only if lodging_filter is true)
  repeated int64 customized_lodgings = 21;

  // Available staffs
  message AvailableStaffs {
    // is all available for this service
    bool is_all_available = 1;
    // available staff ids
    repeated int64 ids = 2;
  }

  // bundle services
  repeated int64 bundle_service_ids = 22;

  // additional service rule
  AdditionalServiceRule additional_service_rule = 23;
}

// customized service view list
message CustomizedServiceViewList {
  // service list
  repeated CustomizedServiceView services = 1;
}

// service brief view
message ServiceBriefView {
  // service id
  int64 id = 1;
  // category id
  int64 category_id = 2;
  // service type
  ServiceType type = 3;
  // service item type
  ServiceItemType service_item_type = 4;
  // name
  string name = 5;
  // description
  string description = 6;
  // price
  double price = 7;
  // price unit
  ServicePriceUnit price_unit = 8;
  // duration
  int32 duration = 9;
  // create time
  google.protobuf.Timestamp create_time = 10;
  // update time
  google.protobuf.Timestamp update_time = 11;
  // inactive
  bool inactive = 12;
  // is deleted
  bool is_deleted = 13;
  // color code
  string color_code = 14;
  // max duration (only for daycare service)
  int32 max_duration = 15;
  // require dedicated staff (only for add on)
  bool require_dedicated_staff = 16;
  // whether the service is available for all lodging
  bool lodging_filter = 17;
  // available lodging ids(only if lodging_filter is true)
  repeated int64 customized_lodgings = 18;
  // number of sessions, only for training
  int32 num_sessions = 19;
  // duration of each session in minutes, only for training
  int32 duration_session_min = 20;
  // capacity of group class, zero means unlimited, only for training
  int32 capacity = 21;
  // whether it require a prerequisite class
  bool is_require_prerequisite_class = 22;
  // prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture
  repeated int64 prerequisite_class_ids = 23;
  // whether evaluation is required
  bool is_evaluation_required = 24;
  // whether evaluation is required before online booking
  bool is_evaluation_required_for_ob = 25;
  // evaluation id
  int64 evaluation_id = 26;
  // tax id
  int64 tax_id = 27;
}

// the grooming service in c app appt detail view
message ServiceClientView {
  // service id
  int64 id = 1;
  // service name
  string name = 4;
  // service type
  ServiceType type = 6;
  // is inactive
  bool inactive = 10;
  // service item type
  ServiceItemType service_item_type = 11;
  // is deleted
  bool is_deleted = 13;
}

// service with available staff customized info
message StaffOverrideRule {
  // staff id
  int64 staff_id = 1;
  // price(null for not override)
  optional double price = 2;
  // tax(null for not override)
  optional int32 duration = 3;
}

// service with available location/staff customized info
message LocationStaffOverrideRule {
  // business override rule
  LocationOverrideRule location_override = 1;
  // staff override rules
  repeated StaffOverrideRule staff_override_list = 2;
}

// service bundle sale view for online booking
message ServiceBundleSaleView {
  // service id
  int64 id = 1;
  // service name
  string name = 2;
  // price
  double price = 3;
  // price unit
  ServicePriceUnit price_unit = 4;
}

// additional service rule
message AdditionalServiceRule {
  // enable
  bool enable = 1;
  // min stay length
  int32 min_stay_length = 2;
  // apply rule
  message ApplyRule {
    // service id
    int64 service_id = 1;
    // date type
    DateType date_type = 2;
    // quantity per day
    int32 quantity_per_day = 3;
  }
  // apply rules
  repeated ApplyRule apply_rules = 3;
}
