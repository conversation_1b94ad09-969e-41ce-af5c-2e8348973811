syntax = "proto3";

package moego.models.promotion.v1;

import "google/type/decimal.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/promotion/v1/subject.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/utils/v1/time_period.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1;promotionpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.promotion.v1";

// source
message Source {
  // type
  enum Type {
    // unspecified
    TYPE_UNSPECIFIED = 0;
    // discount
    DISCOUNT = 1;
    // package, 此 type 对应的id 是 package service id
    PACKAGE = 2;
    // membership discount, 此 type 对应的id是benefit id
    MEMBERSHIP_DISCOUNT = 3;
    // membership quantity, 此 type 对应的id是benefit id
    MEMBERSHIP_QUANTITY = 4;
  }
  // id
  int64 id = 1;
  // type
  Type type = 2;
  // subject 主体，根据不同的 type 对应不同的主体
  oneof subject {
    // discount 当 type 为 DISCOUNT 时，表示这个优惠属于哪个折扣
    DiscountSubject discount = 3;
    // package 当 type 为 PACKAGE 时，表示这个优惠属于哪个套餐服务
    PackageSubject package = 4;
    // membership 当 type 为 MEMBERSHIP_DISCOUNT 或 MEMBERSHIP_QUANTITY 时，表示这个优惠属于哪个会员权益
    MembershipSubject membership = 5;
  }
}

// promotion 作为优惠本身的配置
message Promotion {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // description
  string description = 3;
  // source 来源
  Source source = 4;
  // discount 折扣内容
  Discount discount = 5;
  // restrictions 使用限制
  Restrictions restrictions = 6;
  // validity period 是配置本身的有效期
  Duration validity_period = 7;
  // available period 转化为 coupon 之后的可用期限
  Duration available_period = 8;
  // redemptions 使用次数限制
  Redemptions redemptions = 9;
}

// restrictions
message Restrictions {
  // targets 适用对象限制
  repeated Targets targets = 1;
  // user filters 使用对象限制
  repeated Filter user_filters = 2;
}

// TargetType 目标类型
enum TargetType {
  // unspecified
  TARGET_TYPE_UNSPECIFIED = 0;
  // service 服务
  SERVICE = 1;
  // product 产品
  PRODUCT = 2;
  // service_charge 服务费
  SERVICE_CHARGE = 3;
}

// Targets
message Targets {
  // type 目标类型
  TargetType type = 1;
  // ids 可用对象的 id
  repeated int64 id = 2;
  // all 是否全体对象可用
  bool all = 3; // TODO: 调用方无感
}

// CouponApplicationTargeaet 优惠券目标用目标
message CouponApplicationTarget {
  // target_type 目标类型
  TargetType target_type = 1;
  // target_id 目标ID
  int64 target_id = 2;
  // unit_price 单价
  google.type.Money unit_price = 3;
  // quantity 数量
  int32 target_quantity = 4;
  // order_item_id
  optional int64 order_item_id = 5;
}

// Duration
message Duration {
  // duration
  oneof duration {
    // relative period 相对时间
    moego.utils.v1.TimePeriod relative_period = 1;
    // absolute period 绝对时间
    google.type.Interval absolute_period = 2;
    // never expire 永不过期
    bool never_expire = 3;
  }
}

// Filter
message Filter {
  // key
  string key = 1;
  // value
  string value = 2;
  // operator
  reporting.v2.Operator operator = 3;
}

// discount 折扣内容
message Discount {
  // mode
  oneof mode {
    // fixed amount 固定金额折扣
    google.type.Money fixed_amount = 1;
    // percentage 百分比金额折扣
    google.type.Decimal percentage = 2;
    // deduction 固定数量抵扣
    int64 deduction = 3;
  }
}

// redemptions
message Redemptions {
  // 已扣减次数
  int64 redeemed_times = 1;
  // 最大可扣减次数
  int64 max_redeem_times = 2;
  // 是否可无限次使用
  bool unlimited = 5;
  // 是否可退还，某些国内无良平台会提供此选项
  bool refundable = 6;
}
