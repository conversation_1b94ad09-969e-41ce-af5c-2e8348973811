syntax = "proto3";

package moego.models.reporting.v2;

import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/field_model.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// The operator of the filter parameter
message FilterGroup {
  // The group name
  string group_name = 1;
  // The filters
  repeated Filter filters = 2;
  // group type
  enum FilterGroupType {
    // Unspecified filter group type
    FILTER_GROUP_TYPE_UNSPECIFIED = 0;

    // SINGLE FILTER
    SINGLE_FILTER = 1;
    // normal filter group
    LEVEL_TREE = 2;
    // date input
    DATE_INPUT = 3;
    // checkbox + relative date filter
    CHECKBOX_RELATIVE_DATE = 4;
    // checkbox + checkbox filter
    CHECKBOX_CHECKBOX = 5;
  }
  // group type
  FilterGroupType group_type = 3;
  // The description of the filter group
  optional string description = 4;
  // category name
  optional string category_name = 5;
  // category sort
  optional int32 category_sort = 6;
  // filter group sort
  optional int32 sort = 7;
}

// A filter
message Filter {
  // A filter component
  enum ComponentType {
    // Unspecified filter component
    COMPONENT_TYPE_UNSPECIFIED = 0;
    // A text filter component
    CHECKBOX = 1;
    // A radio filter component
    RADIO = 2;
    // A select
    SELECT = 3;
    // A date filter component
    DATE = 4;
    // A radio filter component with customized options
    RADIO_WITH_CUSTOMIZED = 5;
    // A radio filter component with input option
    RADIO_INPUT = 6;
    // A input filter component
    INPUT = 7;
    // preserved 8-20 for normal filter component

    // RELATIVE DATE FOR PASS DATE
    RELATIVE_DATE = 8;
    // LEVEL TREE SUB NODE
    LEVEL_TREE_NODE = 9;
    // RELATIVE DATE FOR FUTURE DATE
    RELATIVE_DATE_FUTURE = 10;

    // customized filter component: client tag
    CUSTOMIZED_CLIENT_TAG = 21;
    // customized filter component: pet type and breed
    CUSTOMIZED_PET_TYPE_AND_BREED = 22;
    // customized filter component: pet code
    CUSTOMIZED_PET_CODE = 23;
    // customized filter component: zipcode
    CUSTOMIZED_ZIPCODE = 24;
    // customized filter component: payment method
    CUSTOMIZED_PAYMENT_METHOD = 25;
    // customized filter component: service area
    CUSTOMIZED_SERVICE_AREA = 26;
    // customized filter component: workflow
    CUSTOMIZED_WORKFLOW = 27;
  }
  // The field key of the filter parameter
  string field_key = 1;
  // The name of the filter parameter
  optional string name = 2;
  // The options of the filter
  repeated FilterOption options = 3;
  // The component type of the filter parameter
  ComponentType component_type = 4;
  // Current filter's required permission code
  string permission_code = 5;
  // The operator of the filter parameter, key is the [label] of parent filter options.
  map<string, LevelFilterOptions> level_options = 6;
  // Field type of the filter
  Field.Type field_type = 7;
  // The default operator of this filter
  Operator operator = 8;
}

// Level filter options
message LevelFilterOptions {
  // The operator of the filter parameter
  repeated FilterOption options = 1;
}

// Filter option definition
message FilterOption {
  // The operator of the filter parameter
  Operator operator = 1;
  // The label of the filter option
  optional string label = 2;
  // The label of the filter option
  optional string description = 3;
  // option values
  repeated Value values = 4;
}
