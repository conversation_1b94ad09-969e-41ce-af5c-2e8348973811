syntax = "proto3";

package moego.service.agreement.v1;

import "moego/models/agreement/v1/agreement_enums.proto";
import "moego/models/agreement/v1/agreement_record_models.proto";
import "moego/models/message/v1/message_enums.proto";
import "moego/utils/v1/pagination_messages.proto";
import "moego/utils/v1/status_messages.proto";
import "moego/utils/v2/condition_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/agreement/v1;agreementsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.agreement.v1";

/**
 * check the legitimacy of agreement record.
 * where at least one of both id or uuid is specified, or specify both.
 * if both are specified, the record will be queried by id, and the uuid will be checked for correctness.
 * otherwise the record will be queried by one of them(id or uuid).
 * other fields will be checked if specified, ignored if not specified.
 */
message CheckRecordRequest {
  // agreement record id
  optional int64 id = 1;
  // agreement record uuid
  optional string uuid = 2 [(validate.rules).string.pattern = "^[0-9a-f]{32}$"];
  // business id
  optional int64 business_id = 3;
  // customer id
  optional int64 customer_id = 4;
  // agreement id
  optional int64 agreement_id = 5;
  // target id
  optional int64 target_id = 6;
}

// CheckRecordResponse
message CheckRecordResponse {
  // result of check
  bool result = 1;
  // message
  string msg = 2;
}

// get agreement record list for customer request
message GetRecordListRequest {
  // business id
  int64 business_id = 1;
  // customer id
  optional int64 customer_id = 2;
  // agreement id
  optional int64 agreement_id = 3;
  // target id by service type
  optional int64 target_id = 4;
  // associated service type: see definition in ServiceType
  optional int32 service_types = 5 [(validate.rules).int32 = {gt: 0}];
  // status: normal, deleted
  optional moego.utils.v1.Status status = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // signed status
  optional moego.models.agreement.v1.SignedStatus signed_status = 7 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // signed type
  optional moego.models.agreement.v1.SignedType signed_type = 8 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // source type
  optional moego.models.agreement.v1.SourceType source_type = 9 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // the page info
  moego.utils.v1.PaginationRequest pagination = 10;
  // sort
  repeated moego.utils.v2.OrderBy order_bys = 11;
}

// query record simple view response
message GetRecordListResponse {
  // record list
  repeated moego.models.agreement.v1.AgreementRecordSimpleView agreement_record_simple_view = 1;
  // page info
  moego.utils.v1.PaginationResponse pagination = 2;
}

// get agreement record list by company for customer request
message GetRecordListByCompanyRequest {
  // company id
  int64 company_id = 1;
  // customer id
  optional int64 customer_id = 2;
  // agreement id
  optional int64 agreement_id = 3;
  // target id by service type
  optional int64 target_id = 4;
  // associated service type: see definition in ServiceType
  optional int32 service_types = 5 [(validate.rules).int32 = {gt: 0}];
  // status: normal, deleted
  optional moego.utils.v1.Status status = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // signed status
  optional moego.models.agreement.v1.SignedStatus signed_status = 7 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // signed type
  optional moego.models.agreement.v1.SignedType signed_type = 8 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // source type
  optional moego.models.agreement.v1.SourceType source_type = 9 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // the page info
  moego.utils.v1.PaginationRequest pagination = 10;
  // target id list
  repeated int64 target_ids = 11 [(validate.rules).repeated = {
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // sort
  repeated moego.utils.v2.OrderBy order_bys = 12;
}

// query record simple view response
message GetRecordListByCompanyResponse {
  // record list
  repeated moego.models.agreement.v1.AgreementRecordSimpleView agreement_record_simple_view = 1;
  // page info
  moego.utils.v1.PaginationResponse pagination = 2;
}

// get record input
message GetRecordRequest {
  // business id
  optional int64 business_id = 1;
  // agreement record id
  optional int64 id = 2;
  // agreement record uuid
  optional string uuid = 3 [(validate.rules).string.pattern = "^[0-9a-f]{32}$"];
  // company id,if set will be used to check the agreement's company id instead of checking the business id
  optional int64 company_id = 4;
}

// GetRecentSignedAgreementListRequest
message GetRecentSignedAgreementListRequest {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // sign type
  optional moego.models.agreement.v1.SignedType signed_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // associated service type: see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
  // is valid: related agreement last_required_time queries
  optional bool is_valid = 5;
}

// GetRecentSignedAgreementListResponse
message GetRecentSignedAgreementListResponse {
  // agreement with recent signed record list
  repeated moego.models.agreement.v1.AgreementWithRecentRecordsView agreement_recent_view = 1;
}

// BatchGetRecentSignedAgreementListRequest
message BatchGetRecentSignedAgreementListRequest {
  // business id
  int64 business_id = 1;
  // customer id list
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // sign type
  repeated moego.models.agreement.v1.SignedType signed_type = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // associated service type: see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
}

// BatchGetRecentSignedAgreementListResponse
message BatchGetRecentSignedAgreementListResponse {
  // customer id to agreement with recent signed record list
  map<int64, moego.models.agreement.v1.AgreementWithRecentRecordsViewList> customer_recent_agreement = 1;
}

// GetRecentSignedAgreementListByCompanyRequest
message GetRecentSignedAgreementListByCompanyRequest {
  // company id
  int64 company_id = 1;
  // customer id
  int64 customer_id = 2;
  // sign type
  optional moego.models.agreement.v1.SignedType signed_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // associated service type: see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
  // is valid: related agreement last_required_time queries
  optional bool is_valid = 5;
}

// GetRecentSignedAgreementListByCompanyResponse
message GetRecentSignedAgreementListByCompanyResponse {
  // agreement with recent signed record list
  repeated moego.models.agreement.v1.AgreementWithRecentRecordsView agreement_recent_view = 1;
}

// delete record input
message DeleteRecordRequest {
  // agreement record id
  int64 id = 1;
  // business id
  int64 business_id = 2;
}

// DeleteRecordResponse
message DeleteRecordResponse {
  // number of delete
  int32 number = 1;
}

// AddRecordRequest
message AddRecordRequest {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // agreement id
  int64 agreement_id = 3;
  // associated target id
  optional int64 target_id = 4;
  // associated service type: see definition in ServiceType
  optional int32 service_types = 5 [(validate.rules).int32 = {gt: 0}];
  // associated source type: see definition in SourceType
  optional moego.models.agreement.v1.SourceType source_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // company id, if set will be used to check the agreement's company id instead of checking the business id
  optional int64 company_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// SignAgreementRequest
message SignAgreementRequest {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // agreement id
  int64 agreement_id = 3;
  // customer signature
  string signature = 4 [(validate.rules).string = {
    uri: true
    max_len: 1024
  }];
  // associated target id
  optional int64 target_id = 5;
  // associated service type: see definition in ServiceType
  optional int32 service_types = 6 [(validate.rules).int32 = {gt: 0}];
  // associated source type: see definition in SourceType
  optional moego.models.agreement.v1.SourceType source_type = 7 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // input values
  repeated string inputs = 8;
}

// SignRecordRequest
message SignRecordRequest {
  // agreement record id
  optional int64 id = 1;
  // agreement record uuid
  optional string uuid = 2 [(validate.rules).string.pattern = "^[0-9a-f]{32}$"];
  // business id
  optional int64 business_id = 3;
  // customer signature
  string signature = 4 [(validate.rules).string = {
    uri: true
    max_len: 1024
  }];
  // input values
  repeated string inputs = 5;
}

// UploadSignedFileRequest
message UploadSignedFileRequest {
  // business id
  int64 business_id = 1;
  // customer id
  int64 customer_id = 2;
  // agreement title
  string agreement_title = 3 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
  // associated service type: see definition in ServiceType
  optional int32 service_types = 4 [(validate.rules).int32 = {gt: 0}];
  // upload files
  repeated string upload_files = 5 [(validate.rules).repeated = {
    min_items: 1
    max_items: 64
    unique: true
    items: {
      string: {
        uri: true
        max_len: 1024
      }
    }
  }];
  // associated source type: see definition in SourceType
  optional moego.models.agreement.v1.SourceType source_type = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // company id
  int64 company_id = 7;
}

// SendAgreementRecordRequest
message SendSignRequestRequest {
  // agreement record id
  int64 id = 1;
  // business_id
  int64 business_id = 2;
  // customer id
  int64 customer_id = 3;
  // staff_id
  int64 staff_id = 4;
  // message channel type
  moego.models.message.v1.MessageType send_message_type = 5 [(validate.rules).enum = {
    in: [
      1,
      2
    ]
  }];
}

// SendSignRequestResponse
message SendSignRequestResponse {
  // message id
  int64 msg_id = 1;
}

// AgreementRecordService
service AgreementRecordService {
  // check agreement record
  rpc CheckRecord(CheckRecordRequest) returns (CheckRecordResponse);

  // add an agreement record
  rpc AddRecord(AddRecordRequest) returns (moego.models.agreement.v1.AgreementRecordModel);

  // upload signed agreement files
  rpc UploadSignedFile(UploadSignedFileRequest) returns (moego.models.agreement.v1.AgreementRecordModel);

  // get agreement with recent signed record list
  rpc GetRecentSignedAgreementList(GetRecentSignedAgreementListRequest) returns (GetRecentSignedAgreementListResponse);

  // batch get agreement with recent signed record list
  rpc BatchGetRecentSignedAgreementList(BatchGetRecentSignedAgreementListRequest) returns (BatchGetRecentSignedAgreementListResponse);

  // query agreement record list
  rpc GetRecordList(GetRecordListRequest) returns (GetRecordListResponse);

  // get an agreement record detail by id
  rpc GetRecord(GetRecordRequest) returns (moego.models.agreement.v1.AgreementRecordModel);

  // get an agreement record by id
  rpc GetRecordSimpleView(GetRecordRequest) returns (moego.models.agreement.v1.AgreementRecordSimpleView);

  // direct sign a record from an agreement
  rpc SignAgreement(SignAgreementRequest) returns (moego.models.agreement.v1.AgreementRecordSimpleView);

  // sign a created agreement record
  rpc SignRecord(SignRecordRequest) returns (moego.models.agreement.v1.AgreementRecordSimpleView);

  // send an already created agreement record to sign
  rpc SendSignRequest(SendSignRequestRequest) returns (SendSignRequestResponse);

  // delete an agreement record
  rpc DeleteRecord(DeleteRecordRequest) returns (DeleteRecordResponse);

  // get agreement with recent signed record list by company
  rpc GetRecentSignedAgreementListByCompany(GetRecentSignedAgreementListByCompanyRequest) returns (GetRecentSignedAgreementListByCompanyResponse);

  // get agreement record list by company
  rpc GetRecordListByCompany(GetRecordListByCompanyRequest) returns (GetRecordListByCompanyResponse);
}
