// @since 2024-02-22 15:55:04
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// get appointment count request
message GetAppointmentCountByServiceRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service id
  int64 service_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // appointment type: empty for all, 1 - upcoming, 2 - past(not supported yet), 3 - pending(not supported yet)
  optional models.appointment.v1.AppointmentType appointment_type = 4;
}

// get appointment count response
message GetAppointmentCountByServiceResponse {
  // count
  int64 count = 1;
}

// get appointment count by date request
message GetAppointmentCountByDateRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // appointment start date, in yyyy-MM-dd format
  optional string start_date_gte = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // appointment end date, in yyyy-MM-dd format
  optional string end_date_lt = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // service item type
  repeated models.offering.v1.ServiceItemType service_item_type = 5;
}

// get appointment count by date response
message GetAppointmentCountByDateResponse {
  // count
  int32 count = 1;
  // evaluation pet count
  int32 evaluation_pet_count = 2;
}

// batch get appointment total request
message BatchGetTotalAppointmentCountRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer ids
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {max_items: 100}];
}

//batch get appointment total response
message BatchGetTotalAppointmentCountResponse {
  // key is customer_id, value is total_appointment_count
  map<int64, int32> count = 1;
}

// batch get appointment upcoming request
message BatchGetUpcomingAppointmentCountRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer ids
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {max_items: 100}];
  // evaluation id
  repeated int64 evaluation_ids = 3 [(validate.rules).repeated = {max_items: 100}];
}

//batch get appointment upcoming response
message BatchGetUpcomingAppointmentCountResponse {
  // key is customer_id, value is upcoming_appointment_count
  map<int64, int32> count = 1;
  // key is evaluation_id, value is upcoming_appointment_count
  map<int64, int32> evaluation_count = 2;
}

// the appointment count service
service AppointmentCountService {
  // get appointment_count
  rpc GetAppointmentCountByService(GetAppointmentCountByServiceRequest) returns (GetAppointmentCountByServiceResponse);
  // get appointment_count by date
  rpc GetAppointmentCountByDate(GetAppointmentCountByDateRequest) returns (GetAppointmentCountByDateResponse);
  // batch get appointment total
  rpc BatchGetTotalAppointmentCount(BatchGetTotalAppointmentCountRequest) returns (BatchGetTotalAppointmentCountResponse);
  //batch get appointment upcoming
  rpc BatchGetUpcomingAppointmentCount(BatchGetUpcomingAppointmentCountRequest) returns (BatchGetUpcomingAppointmentCountResponse);
}
