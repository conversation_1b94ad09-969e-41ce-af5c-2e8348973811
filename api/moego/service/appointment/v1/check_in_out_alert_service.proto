syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/check_in_out_alert_defs.proto";
import "moego/models/appointment/v1/check_in_out_alert_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// CheckInOutAlertService
service CheckInOutAlertService {
  // get check-in-out alter settings
  rpc GetAlertSettings(GetAlertSettingsRequest) returns (GetAlertSettingsResponse);

  // save check-in-out alter settings
  rpc SaveAlertSettings(SaveAlertSettingsRequest) returns (SaveAlertSettingsResponse);

  // batch get alter detail for check in
  rpc BatchGetAlertsForCheckIn(BatchGetAlertsForCheckInRequest) returns (BatchGetAlertsForCheckInResponse);

  // get alter detail for check in
  rpc GetAlertsForCheckIn(GetAlertsForCheckInRequest) returns (GetAlertsForCheckInResponse);

  // get alter detail for check out
  rpc GetAlertsForCheckOut(GetAlertsForCheckOutRequest) returns (GetAlertsForCheckOutResponse);
}

// GetAlertSettingRequest
message GetAlertSettingsRequest {
  // company id
  int64 company_id = 1;
}

// GetAlertSettingResponse
message GetAlertSettingsResponse {
  // check in/out alerts settings
  moego.models.appointment.v1.CheckInOutAlertSettings settings = 1;
}

// SaveAlertSettingRequest
message SaveAlertSettingsRequest {
  // company id
  int64 company_id = 1;
  // check in alert settings
  moego.models.appointment.v1.CheckInAlertSettings check_in_settings = 2;
  // check out alert settings
  moego.models.appointment.v1.CheckOutAlertSettings check_out_settings = 3;
}

// SaveAlertSettingResponse
message SaveAlertSettingsResponse {
  // check in/out alerts settings
  moego.models.appointment.v1.CheckInOutAlertSettings settings = 1;
}

// GetAlertsForCheckInRequest
message GetAlertsForCheckInRequest {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // appointment id
  int64 appointment_id = 3;
}

// GetAlertsForCheckInResponse
message GetAlertsForCheckInResponse {
  // check in alert
  optional moego.models.appointment.v1.AlertDetail alert = 1;
}

// GetAlertsForCheckOutRequest
message GetAlertsForCheckOutRequest {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // appointment id
  int64 appointment_id = 3;
}

// GetAlertsForCheckOutResponse
message GetAlertsForCheckOutResponse {
  // check out alert
  optional moego.models.appointment.v1.AlertDetail alert = 1;
}

// BatchGetAlertsForCheckInRequest
message BatchGetAlertsForCheckInRequest {
  // company id
  int64 company_id = 1;
  // business id
  int64 business_id = 2;
  // client and pet ids
  repeated moego.models.appointment.v1.ClientPetsMapping client_pets = 3;
}

// BatchGetAlertsForCheckInResponse
message BatchGetAlertsForCheckInResponse {
  // client and pet alerts
  repeated moego.models.appointment.v1.AlertDetail alerts = 1;
}
