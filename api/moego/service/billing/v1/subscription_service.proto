// @since 2024-06-06
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.billing.v1;

import "google/type/interval.proto";
import "moego/models/billing/v1/subscription_defs.proto";
import "moego/models/billing/v1/subscription_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/billing/v1;billingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.billing.v1";

// create subscription request
message CreateSubscriptionRequest {
  // subscription items
  repeated models.billing.v1.PlanUnit plan_units = 1;
  // payment method
  string payment_method = 2 [(validate.rules).string = {max_len: 64}];
  // coupon relation id
  optional int64 coupon_id = 3;
  // application fee percent
  // A non-negative decimal between 0 and 10, with at most two decimal places. (stripe is 【0，100】
  // This represents the percentage of the subscription invoice total that will be transferred to the application owner’s Stripe account.
  // The request must be made by a platform account on a connected account in order to set an application fee percentage.
  optional double application_fee_percent = 4 [(validate.rules).double = {
    gte: 0
    lte: 10
  }];
  // on behalf of: The account on behalf of which to charge, for each of the subscription’s invoices.
  optional string on_behalf_of = 5 [(validate.rules).string = {max_len: 64}];
  // connected account only
  optional models.billing.v1.TransferData transfer_data = 6;
  // customer relation id, TODO: not impl currently
  optional int64 customer_id = 7;
  // payment behavior
  models.billing.v1.SubscriptionModel.PaymentBehavior payment_behavior = 8;
  // metadata
  map<string, string> metadata = 10;
  // description
  optional string description = 11;
  // vendor customer id
  string vendor_customer_id = 100 [(validate.rules).string = {max_len: 64}];
  // idempotency key
  optional string idempotency_key = 12;
}

// create subscription response
message CreateSubscriptionResponse {
  // subscription relation ID
  int64 id = 1;
  // client secret
  string client_secret = 2;
  // vendor subscription id
  string vendor_subscription_id = 3;
  // vendor invoice id
  string vendor_invoice_id = 4;
  // vendor payment intent id
  string vendor_payment_intent_id = 5;
}

// update subscription request
message UpdateSubscriptionRequest {
  // proration behavior enum, ref: https://docs.stripe.com/api/subscriptions/update#update_subscription-proration_behavior
  enum ProrationBehavior {
    // Unknown proration behavior.
    PRORATION_BEHAVIOR_UNSPECIFIED = 0;
    // No proration. Disable creating prorations in this request.
    NO_PRORATION = 1;
    // Prorates the price based on the time remaining in the current cycle.
    CREATE_PRORATIONS = 2;
    // Always invoice immediately for prorations.
    ALWAYS_INVOICE = 3;
  }
  // subscription relation ID
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // subscription items
  repeated models.billing.v1.PlanUnit plan_units = 2;
  // payment method
  string payment_method = 3;
  // proration behavior
  ProrationBehavior proration_behavior = 4;
  // application fee percent
  optional double application_fee_percent = 5 [(validate.rules).double = {
    gte: 0
    lte: 10
  }];
  // payment behavior
  models.billing.v1.SubscriptionModel.PaymentBehavior payment_behavior = 6;
  //
  // metadata
  map<string, string> metadata = 10;
  // pause collection，不传就不修改，传空结构是 resume
  models.billing.v1.PauseCollection pause_collection = 11;
}

// update subscription response
message UpdateSubscriptionResponse {
  // subscription relation ID
  int64 id = 1;
  // vendor subscription id
  string vendor_subscription_id = 2;
  // vendor invoice id
  string vendor_invoice_id = 3;
  // vendor payment intent id
  string vendor_payment_intent_id = 4;
}

// cancel subscription request
message CancelSubscriptionRequest {
  // cancel type
  enum CancelType {
    // cancel type unspecified
    CANCEL_TYPE_UNSPECIFIED = 0;
    // cancel immediately
    IMMEDIATELY = 1;
    // cancel at the end of the billing cycle
    END_OF_CYCLE = 2;
  }

  // subscription relation ID
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // cancel type, default end of cycle
  CancelType cancel_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // revert cancel if not yet applied for cancel_at_period_end
  optional bool revert = 3;
  // prorate refund
  optional bool refund = 4;
}

// cancel subscription response
message CancelSubscriptionResponse {
  // subscription relation ID
  int64 id = 1;
}

// get subscription request
message ConvertVendorSubscriptionIdsRequest {
  // subscription relation ID
  repeated string vendor_subscription_ids = 1;
}

// get subscription response
message ConvertVendorSubscriptionIdsResponse {
  // subscription relation ID
  repeated int64 subscription_ids = 1;
}

// get subscription request
message GetSubscriptionRequest {
  // subscription relation ID
  int64 id = 1;
}

// get subscription response
message GetSubscriptionResponse {
  // subscription object
  models.billing.v1.SubscriptionModel subscription = 1;
}

// schedule next billing cycle request
message ScheduleNextBillingCycleRequest {
  // subscription ID
  int64 subscription_id = 1;
  // plan units
  repeated models.billing.v1.PlanUnit plan_units = 2;
  // current billing cycle
  google.type.Interval current_billing_cycle = 3;
}

// schedule next billing cycle response
message ScheduleNextBillingCycleResponse {
  // subscription schedule object
  models.billing.v1.SubscriptionScheduleModel subscription_schedule = 1;
}

// ListSubscriptionsRequest
message ListSubscriptionsRequest {
  // filter
  message Filter {
    // vendor_customer_ids
    repeated string vendor_customer_ids = 1;
  }
  // filter
  Filter filter = 1;
}

// ListCustomerSubscriptionsResponse
message ListSubscriptionsResponse {
  // subscriptions
  repeated models.billing.v1.SubscriptionModel subscriptions = 1;
}

// subscription service
service SubscriptionService {
  // create subscription
  rpc CreateSubscription(CreateSubscriptionRequest) returns (CreateSubscriptionResponse);
  // update subscription
  rpc UpdateSubscription(UpdateSubscriptionRequest) returns (UpdateSubscriptionResponse);
  // cancel subscription
  rpc CancelSubscription(CancelSubscriptionRequest) returns (CancelSubscriptionResponse);
  // convert vendor subscription ids
  rpc ConvertVendorSubscriptionIds(ConvertVendorSubscriptionIdsRequest) returns (ConvertVendorSubscriptionIdsResponse);
  // get subscription
  rpc GetSubscription(GetSubscriptionRequest) returns (GetSubscriptionResponse);
  // schedule next billing cycle, it will automatically handle existing schedule and idempotency
  rpc ScheduleNextBillingCycle(ScheduleNextBillingCycleRequest) returns (ScheduleNextBillingCycleResponse);
  // list subscriptions
  rpc ListSubscriptions(ListSubscriptionsRequest) returns (ListSubscriptionsResponse);
}
