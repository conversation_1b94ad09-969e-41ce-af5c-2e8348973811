syntax = "proto3";

package moego.service.enterprise.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// setting service
service SettingService {
  // copy intake form
  rpc CopyIntakeForms(CopyIntakeFormsRequest) returns (CopyIntakeFormsResponse);
  // copy discount code
  rpc CopyDiscountCodes(CopyDiscountCodesRequest) returns (CopyDiscountCodesResponse);
  // copy roles
  rpc CopyRoles(CopyRolesRequest) returns (CopyRolesResponse);
  // copy packages
  rpc CopyPackages(CopyPackagesRequest) returns (CopyPackagesResponse);
  // copy membership
  rpc CopyMemberships(CopyMembershipsRequest) returns (CopyMembershipsResponse);
}

// copy intake form
message CopyIntakeFormsRequest {
  // source company id
  int64 source_company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target company ids
  repeated int64 target_company_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// response of copy intake form
message CopyIntakeFormsResponse {}

// copy discount code
message CopyDiscountCodesRequest {
  // source company id
  int64 source_company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target company ids
  repeated int64 target_company_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// response of copy discount code
message CopyDiscountCodesResponse {}

// copy roles
message CopyRolesRequest {
  // source company id
  int64 source_company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target company ids
  repeated int64 target_company_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// response of copy roles
message CopyRolesResponse {}

// copy packages
message CopyPackagesRequest {
  // source company id
  int64 source_company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target company ids
  repeated int64 target_company_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// response of copy packages
message CopyPackagesResponse {}

// copy membership
message CopyMembershipsRequest {
  // source company id
  int64 source_company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // target company ids
  repeated int64 target_company_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// response of copy membership
message CopyMembershipsResponse {}
