// @since 2024-03-21 14:16:42
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/date.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/offering/v1/evaluation_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/models/online_booking/v1/booking_request_defs.proto";
import "moego/models/online_booking/v1/booking_request_enums.proto";
import "moego/models/online_booking/v1/booking_request_models.proto";
import "moego/models/online_booking/v1/grooming_service_detail_defs.proto";
import "moego/models/organization/v1/tenant.proto";
import "moego/service/online_booking/v1/boarding_add_on_detail_service.proto";
import "moego/service/online_booking/v1/boarding_service_detail_service.proto";
import "moego/service/online_booking/v1/daycare_add_on_detail_service.proto";
import "moego/service/online_booking/v1/daycare_service_detail_service.proto";
import "moego/service/online_booking/v1/dog_walking_detail_service.proto";
import "moego/service/online_booking/v1/evaluation_test_detail_service.proto";
import "moego/service/online_booking/v1/feeding_service.proto";
import "moego/service/online_booking/v1/grooming_add_on_detail_service.proto";
import "moego/service/online_booking/v1/grooming_auto_assign_service.proto";
import "moego/service/online_booking/v1/grooming_service_detail_service.proto";
import "moego/service/online_booking/v1/group_class_service_detail_service.proto";
import "moego/service/online_booking/v1/medication_service.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/list.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create BookingRequest request
message CreateBookingRequestRequest {
  // businessId
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customerId
  int64 customer_id = 2;
  // appointmentId
  optional int64 appointment_id = 3;
  // startDate
  optional string start_date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // startTime
  optional int32 start_time = 5 [(validate.rules).int32 = {gte: 0}];
  // endDate
  optional string end_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // endTime
  optional int32 end_time = 7;
  // status
  optional int32 status = 8;
  // isPrepaid
  optional bool is_prepaid = 9;
  // additionalNote
  optional string additional_note = 10 [(validate.rules).string = {max_len: 2048}];
  // sourcePlatform
  optional string source_platform = 11 [(validate.rules).string = {max_len: 2048}];
  // serviceTypeInclude
  //  optional int32 service_type_include = 12;
  // createdAt
  optional google.protobuf.Timestamp created_at = 13;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 14;
  // Company id
  optional int64 company_id = 15;

  // Services
  repeated Service services = 16 [(validate.rules).repeated = {min_items: 1}];

  // Additional attributes
  optional moego.models.online_booking.v1.BookingRequestModel.Attr attr = 17;

  // payment status, use NO_PAYMENT if not set
  optional moego.models.online_booking.v1.BookingRequestModel.PaymentStatus payment_status = 18 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // need create order, default false
  bool need_create_order = 19;
  // member ship apply info
  optional Membership membership = 20;

  // comment business private note
  optional string comment = 21 [(validate.rules).string = {max_len: 2048}];
  // booking request source
  optional moego.models.online_booking.v1.BookingRequestModel.Source source = 22;
  // source id
  optional int64 source_id = 23;

  // Service
  message Service {
    // Service type
    oneof service {
      // Grooming
      GroomingService grooming = 1;
      // Boarding
      BoardingService boarding = 2;
      // Daycare
      DaycareService daycare = 3;
      // Evaluation
      EvaluationService evaluation = 4;
      // Dog walking
      DogWalkingService dog_walking = 5;
      // Group class
      GroupClassService group_class = 6;
    }
  }

  // Grooming service
  message GroomingService {
    // Grooming service detail
    CreateGroomingServiceDetailRequest service = 1;
    // Grooming service addons
    // 参数类型用错了，使用 addons_v2 替换
    repeated CreateGroomingServiceDetailRequest addons = 2 [deprecated = true];
    // Grooming service addons
    repeated CreateGroomingAddOnDetailRequest addons_v2 = 3;
  }

  // Boarding service
  message BoardingService {
    // Boarding service detail
    CreateBoardingServiceDetailRequest service = 1;
    // Boarding service addons
    repeated CreateBoardingAddOnDetailRequest addons = 2;
    // Feeding. Deprecated, use feedings instead
    optional CreateFeedingRequest feeding = 3 [deprecated = true];
    // Medication. Deprecated, use medications instead
    optional CreateMedicationRequest medication = 4 [deprecated = true];
    // Feedings
    repeated CreateFeedingRequest feedings = 5;
    // Medications
    repeated CreateMedicationRequest medications = 6;
    // Boarding waitlist
    optional CreateBoardingServiceWaitlistRequest waitlist = 7;
  }

  // Daycare service
  message DaycareService {
    // Daycare service detail
    CreateDaycareServiceDetailRequest service = 1;
    // Daycare service addons
    repeated CreateDaycareAddOnDetailRequest addons = 2;
    // Feeding. Deprecated, use feedings instead
    optional CreateFeedingRequest feeding = 3 [deprecated = true];
    // Medication. Deprecated, use medications instead
    optional CreateMedicationRequest medication = 4 [deprecated = true];
    // Feedings
    repeated CreateFeedingRequest feedings = 5;
    // Medications
    repeated CreateMedicationRequest medications = 6;
    // Daycare waitlist
    optional CreateDaycareServiceWaitlistRequest waitlist = 7;
  }

  // Evaluation service
  message EvaluationService {
    // Evaluation service detail
    CreateEvaluationTestDetailRequest service = 1;
  }

  // Dog walking service
  message DogWalkingService {
    // Dog walking service detail
    CreateDogWalkingServiceDetailRequest service = 1;
  }
  // Group class service
  message GroupClassService {
    // Group class service detail
    CreateGroupClassServiceDetailRequest service = 1;
  }

  // Membership apply Info
  message Membership {
    // membership id
    repeated int64 membership_ids = 1 [(validate.rules).repeated = {
      unique: true
      max_items: 1000
      items: {
        int64: {gt: 0}
      }
    }];
  }
}

// Get Booking Request detail request
message GetBookingRequestRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // associated models
  repeated models.online_booking.v1.BookingRequestAssociatedModel associated_models = 2 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // payment statuses
  repeated moego.models.online_booking.v1.BookingRequestModel.PaymentStatus payment_statuses = 3 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
}

// Get BookingRequest response
message GetBookingRequestResponse {
  // Existing record
  models.online_booking.v1.BookingRequestModel booking_request = 1;
  //waitlist extra
  WaitlistExtra waitlist_extras = 2;
}

// List BookingRequest request
message ListBookingRequestsRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {
    gt: 0
    ignore_empty: true
  }];

  // associated models
  repeated models.online_booking.v1.BookingRequestAssociatedModel associated_models = 2 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // contains statuses
  repeated models.online_booking.v1.BookingRequestStatus statuses = 3 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // business ids, optional
  repeated int64 business_ids = 4 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // start date filter, used to filter start date
  optional google.type.Date start_date = 5;

  // end date filter, used to filter start date
  optional google.type.Date end_date = 6;

  // order by (support multi fields), optional
  repeated moego.utils.v2.OrderBy order_bys = 7;

  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 8;

  // search keywords
  optional string keywords = 9 [(validate.rules).string = {max_len: 50}];

  // service item type, If empty, it means all service item types
  repeated models.offering.v1.ServiceItemType service_items = 10 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // service type include, If empty, it means all service item types
  repeated int32 service_type_includes = 11 [(validate.rules).repeated = {
    min_items: 0
    unique: true
  }];

  // company id
  optional int64 company_id = 12 [(validate.rules).int64 = {gt: 0}];

  // customer ids
  repeated int64 customer_id = 13 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // appointment ids
  repeated int64 appointment_ids = 14 [(validate.rules).repeated = {
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // payment status
  repeated moego.models.online_booking.v1.BookingRequestModel.PaymentStatus payment_statuses = 15 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // Booking request source
  optional moego.models.online_booking.v1.BookingRequestModel.Source source = 16;
  // waitlist expired
  optional bool is_waitlist_expired = 17;
  // waitlist expired
  optional bool is_waitlist_available = 18;
  // waitlist order by price desc
  optional bool order_price_desc = 19;
  // latest end date
  optional google.type.Date latest_end_date = 20;
  // Booking request source list
  repeated moego.models.online_booking.v1.BookingRequestModel.Source sources = 21;
}

// List BookingRequest response
message ListBookingRequestsResponse {
  // booking request list
  repeated models.online_booking.v1.BookingRequestModel booking_requests = 1;
  //waitlist extra
  repeated WaitlistExtra waitlist_extras = 3;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// List Waitlists request
message ListWaitlistsRequest {
  // associated models
  repeated models.online_booking.v1.BookingRequestAssociatedModel associated_models = 2 [(validate.rules).repeated = {
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // business ids, optional
  repeated int64 business_ids = 4 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // start date filter, used to filter start date
  optional google.type.Date start_date = 5;

  // end date filter, used to filter start date
  optional google.type.Date end_date = 6;

  // order by (support multi fields), optional
  repeated moego.utils.v2.OrderBy order_bys = 7;

  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 8;

  // search keywords
  optional string keywords = 9 [(validate.rules).string = {max_len: 50}];

  // service item type, If empty, it means all service item types
  repeated models.offering.v1.ServiceItemType service_items = 10 [(validate.rules).repeated = {
    min_items: 0
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];

  // service type include, If empty, it means all service item types
  repeated int32 service_type_includes = 11 [(validate.rules).repeated = {
    min_items: 0
    unique: true
  }];

  // company id
  optional int64 company_id = 12 [(validate.rules).int64 = {gt: 0}];

  // customer ids
  repeated int64 customer_id = 13 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // Booking request source
  optional moego.models.online_booking.v1.BookingRequestModel.Source source = 16;
  // waitlist expired
  optional bool is_waitlist_expired = 17;
  // waitlist expired
  optional bool is_waitlist_available = 18;
  // waitlist order by price desc
  optional bool order_price_desc = 19;
  // latest end date
  optional google.type.Date latest_end_date = 20;
  // Booking request source list
  repeated moego.models.online_booking.v1.BookingRequestModel.Source sources = 21;
}

// List waitlist response
message ListWaitlistsResponse {
  // booking request list
  repeated models.online_booking.v1.BookingRequestModel booking_requests = 1;
  //waitlist extra
  repeated WaitlistExtra waitlist_extras = 3;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

//waitlist extra
message WaitlistExtra {
  //booking request id
  int64 id = 1;
  // is available
  bool is_available = 2;
  // is expired
  bool is_expired = 3;
}

// Create a grooming only request
message CreateGroomingOnlyRequest {
  // booking request
  moego.models.online_booking.v1.BookingRequestDef booking_request = 1 [(validate.rules).message.required = true];

  // Grooming service detail
  repeated moego.models.online_booking.v1.GroomingServiceDetailDef grooming_service_details = 2 [(validate.rules).repeated = {
    min_items: 1
    items: {
      message: {required: true}
    }
  }];
}

// Create a grooming only response
message CreateGroomingOnlyResponse {}

// Update booking request status request
message UpdateBookingRequestStatusRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // updated status
  models.online_booking.v1.BookingRequestStatus status = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// Update booking request status response
message UpdateBookingRequestStatusResponse {
  // updated result
  bool updated_result = 1;
}

// retry failed events request
message RetryFailedEventsRequest {}

// retry failed events response
message RetryFailedEventsResponse {}

// Update booking request request
message UpdateBookingRequestRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // appointment id, generated after the booking request is scheduled
  optional int64 appointment_id = 2 [(validate.rules).int64 = {gt: 0}];

  // start date, format: yyyy-mm-dd
  optional string start_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, the minutes from 00:00
  optional int32 start_time = 4 [(validate.rules).int32 = {gte: 0}];

  // end date, format: yyyy-mm-dd
  optional string end_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // end time, the minutes from 00:00
  optional int32 end_time = 6 [(validate.rules).int32 = {gte: 0}];

  // updated status
  optional models.online_booking.v1.BookingRequestStatus status = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // the services, If this parameter is not passed, it will not be updated
  repeated Service services = 10;

  // Additional attributes
  // NOTE：这个操作会全量更新 attr 字段，如果只需要更新部分字段，需要先获取原始数据，然后合并更新字段
  optional moego.models.online_booking.v1.BookingRequestModel.Attr attr = 11;

  // payment status
  optional moego.models.online_booking.v1.BookingRequestModel.PaymentStatus payment_status = 12 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // comment business private note
  optional string comment = 13 [(validate.rules).string = {max_len: 2048}];

  // Service
  message Service {
    // Service type
    oneof service {
      // Grooming
      GroomingService grooming = 1;
      // Boarding
      BoardingService boarding = 2;
      // Daycare
      DaycareService daycare = 3;
      // TODO add other service item types
    }
  }

  // Grooming service
  message GroomingService {
    // Grooming service detail
    UpdateGroomingServiceDetailRequest service = 1;
    // Grooming service addons
    repeated UpdateGroomingAddOnDetailRequest addons = 2;
    // Grooming auto assign detail
    optional UpsertGroomingAutoAssignRequest auto_assign = 3;
  }

  // Boarding service
  message BoardingService {
    // Boarding service detail
    UpdateBoardingServiceDetailRequest service = 1;
    // Feedings
    optional CreateFeedingRequestList feedings_upsert = 2;
    // Medications
    optional CreateMedicationRequestList medications_upsert = 3;
    // Boarding service addons
    repeated UpdateBoardingAddOnDetailRequest addons = 4;
    // Boarding waitlist
    optional UpdateBoardingServiceWaitlistRequest waitlist = 7;
  }

  // Daycare service
  message DaycareService {
    // Daycare service detail
    UpdateDaycareServiceDetailRequest service = 1;
    // Feedings
    optional CreateFeedingRequestList feedings_upsert = 2;
    // Medications
    optional CreateMedicationRequestList medications_upsert = 3;
    // Daycare service addons
    repeated UpdateDaycareAddOnDetailRequest addons = 4;
    // Daycare waitlist
    optional UpdateDaycareServiceWaitlistRequest waitlist = 7;
  }
}

// Update booking request response
message UpdateBookingRequestResponse {}

// replace booking request request
message ReplaceBookingRequestRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // start date, format: yyyy-mm-dd
  optional string start_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // start time, the minutes from 00:00
  optional int32 start_time = 4 [(validate.rules).int32 = {gte: 0}];

  // end date, format: yyyy-mm-dd
  optional string end_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];

  // end time, the minutes from 00:00
  optional int32 end_time = 6 [(validate.rules).int32 = {gte: 0}];

  // updated status
  optional models.online_booking.v1.BookingRequestStatus status = 9 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // the services, If this parameter is not passed, it will not be updated
  repeated Service services = 10;

  // comment business private note
  optional string comment = 13 [(validate.rules).string = {max_len: 2048}];

  // Service
  message Service {
    // Service type
    oneof service {
      // Boarding
      BoardingService boarding = 2;
      // Daycare
      DaycareService daycare = 3;
      // TODO add other service item types
    }
  }

  // Boarding service
  message BoardingService {
    // Boarding service detail
    CreateBoardingServiceDetailRequest service = 1;
    // Boarding waitlist
    optional CreateBoardingServiceWaitlistRequest waitlist = 7;
  }

  // Daycare service
  message DaycareService {
    // Daycare service detail
    CreateDaycareServiceDetailRequest service = 1;
    // Daycare waitlist
    optional CreateDaycareServiceWaitlistRequest waitlist = 7;
  }
}

// replace booking request response
message ReplaceBookingRequestResponse {}

// get auto assign request
message GetAutoAssignRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// get auto assign response
message GetAutoAssignResponse {
  // boarding assign info require
  repeated AssignRequire boarding_assign_requires = 1;
  // assign result. pet to lodging unit id
  repeated models.online_booking.v1.PetToLodgingDef pet_to_lodgings = 2;
  // lodging details for auto assign
  repeated LodgingDetail lodgings = 3;
  // evaluation assign info require
  repeated AssignRequire evaluation_assign_requires = 4;
  // assign result. evaluation pet to staff
  repeated models.online_booking.v1.PetToStaffDef evaluation_pet_to_staffs = 5;
  // daycare assign info require
  repeated AssignRequire daycare_assign_requires = 6;

  // lodging assign info require
  message AssignRequire {
    // pet id
    int64 pet_id = 1;
    // service id
    int64 service_id = 2;
    // start date
    optional string start_date = 3;
    // end date
    optional string end_date = 4;
    // specific dates
    repeated string specific_dates = 5;
  }

  // lodging detail for auto assigned
  message LodgingDetail {
    // lodging id
    int64 lodging_id = 1;
    // lodging unit name
    string lodging_unit_name = 2;
    // lodging type name
    string lodging_type_name = 3;
  }
}

// Accept booking request request
message AcceptBookingRequestRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // assign lodging unit id
  repeated models.online_booking.v1.PetToLodgingDef pet_to_lodgings = 2;
  // assign staff
  repeated models.online_booking.v1.PetToStaffDef pet_to_staffs = 3;
  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
  // staff id
  optional int64 staff_id = 6 [(validate.rules).int64 = {gt: 0}];
  // assign service
  repeated models.online_booking.v1.PetToServiceDef pet_to_services = 7;
  // assign evaluation staff
  repeated models.online_booking.v1.PetToStaffDef evaluation_pet_to_staffs = 9;
}

// Accept booking request response
message AcceptBookingRequestResponse {
  // result
  bool result = 1;
  // need to send notification appointment id
  int64 appointment_id = 2;
}

// Decline booking request request
message DeclineBookingRequestRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // staff id
  optional int64 staff_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// Decline booking request response
message DeclineBookingRequestResponse {
  // result
  bool result = 1;
  // need to send notification appointment id
  int64 appointment_id = 2;
}

// Count booking requests request
message CountBookingRequestsRequest {
  // tenant
  moego.models.organization.v1.Tenant tenant = 1 [(validate.rules).message.required = true];

  // filters
  message Filters {
    // created time range
    optional google.type.Interval created_time_range = 1;
    // statuses, used to filter status, if empty, it means all statuses
    repeated models.online_booking.v1.BookingRequestStatus statuses = 2 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
    // payment statuses
    repeated moego.models.online_booking.v1.BookingRequestModel.PaymentStatus payment_statuses = 3 [(validate.rules).repeated = {
      items: {
        enum: {
          defined_only: true
          not_in: [0]
        }
      }
    }];
  }
  // filters
  optional Filters filters = 2;
}

// Count booking requests response
message CountBookingRequestsResponse {
  // count
  int32 count = 1;
}

// ListBookingRequestId request
message ListBookingRequestIdRequest {
  // appointment ids
  repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
    max_items: 10000
    items: {
      int64: {gt: 0}
    }
  }];
}

// ListBookingRequestId response
message ListBookingRequestIdResponse {
  // appointment id to booking request id map
  map<int64, int64> appointment_id_to_booking_request_id = 1;
}

// Sync BookingRequest from appointment request
message SyncBookingRequestFromAppointmentRequest {
  // appointment id
  repeated int64 appointment_id = 1 [(validate.rules).repeated = {
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
}

// Sync BookingRequest from appointment response
message SyncBookingRequestFromAppointmentResponse {}

// TriggerBookingRequestAutoAccepted request
message TriggerBookingRequestAutoAcceptedRequest {
  // booking request id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// TriggerBookingRequestAutoAccepted response
message TriggerBookingRequestAutoAcceptedResponse {
  // whether the booking request is auto accepted
  bool is_auto_accepted = 1;
}

// AcceptBookingRequestV2 request
message AcceptBookingRequestV2Request {
  // company id, optional
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];

  // booking request id
  int64 id = 2 [(validate.rules).int64 = {gt: 0}];

  // staff id, operator
  optional int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];

  // grooming services
  repeated GroomingService grooming_services = 10;
  // boarding services
  repeated BoardingService boarding_services = 11;
  // daycare services
  repeated DaycareService daycare_services = 12;
  // evaluation services
  repeated EvaluationService evaluation_services = 13;
  // grooming addons
  repeated GroomingAddon grooming_addons = 14;
  // boarding addons
  repeated BoardingAddon boarding_addons = 15;
  // daycare addons
  repeated DaycareAddon daycare_addons = 16;

  // 新增场景的参数

  // 新增 evaluation
  repeated CreateEvaluationRequest create_evaluation_requests = 17;

  // Grooming service
  message GroomingService {
    // id
    int64 id = 1;
    // staff id
    optional int64 staff_id = 2;
    // start time
    optional int32 start_time = 3;
  }

  // Boarding service
  message BoardingService {
    // id
    int64 id = 1;
    // lodging unit id
    // 使用 lodging id 是为了和 boarding_service_detail 保持一致
    optional int64 lodging_id = 4;
  }

  // Daycare service
  message DaycareService {
    // id
    int64 id = 1;
    // lodging unit id
    optional int64 lodging_id = 4;
  }

  // Evaluation service
  message EvaluationService {
    // id
    int64 id = 1;
    // staff id
    optional int64 staff_id = 2;
    // evaluation id
    optional int64 evaluation_id = 3;
  }

  // Grooming addon
  message GroomingAddon {
    // id
    int64 id = 1;
    // staff id
    optional int64 staff_id = 2;
    // start time
    optional int32 start_time = 3;
  }

  // Boarding addon
  message BoardingAddon {
    // id
    int64 id = 1;
    // staff id
    optional int64 staff_id = 2;
    // start time
    optional int32 start_time = 3;
  }

  // Daycare addon
  message DaycareAddon {
    // id
    int64 id = 1;
    // staff id
    optional int64 staff_id = 2;
    // start time
    optional int32 start_time = 3;
  }

  // Create evaluation request
  message CreateEvaluationRequest {
    // evaluation id
    int64 evaluation_id = 1 [(validate.rules).int64 = {gt: 0}];
    // pet id
    int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
    // start date
    google.type.Date start_date = 3 [(validate.rules).message = {required: true}];
    // start time
    int32 start_time = 4 [(validate.rules).int32 = {gte: 0}];
    // staff id
    optional int64 staff_id = 5;
    // lodging id
    optional int64 lodging_id = 6;
  }
}

// AcceptBookingRequestV2 response
message AcceptBookingRequestV2Response {
  // result
  bool result = 1;
  // appointment id
  int64 appointment_id = 2;
}

// AutoAssign request
message AutoAssignRequest {
  // booking request id
  int64 booking_request_id = 1 [(validate.rules).int64 = {gt: 0}];

  // company id, optional
  optional int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// AutoAssign response
message AutoAssignResponse {
  // boarding services
  repeated BoardingService boarding_services = 1;
  // evaluation services
  repeated EvaluationService evaluation_services = 2;
  // daycare services
  repeated DaycareService daycare_services = 3;

  // boarding service
  message BoardingService {
    // boarding_service_detail id
    int64 id = 1;
    // lodging id
    // 没有返回值表示不能 auto assign 到 lodging
    optional int64 lodging_id = 2;
    // 这个 pet 选择的 boarding service 所缺失的 evaluation
    optional moego.models.offering.v1.EvaluationBriefView missing_evaluation = 3;
  }

  // evaluation service
  message EvaluationService {
    // evaluation_test_detail id
    int64 id = 1;
    // staff id
    // 没有返回值表示不能 auto assign 到 staff
    optional int64 staff_id = 2;
    // 选择的 evaluation 信息
    moego.models.offering.v1.EvaluationBriefView selected_evaluation = 3;
    // 所有的 evaluation 信息
    repeated moego.models.offering.v1.EvaluationBriefView all_evaluations = 4;
  }

  // daycare service
  message DaycareService {
    // daycare_service_detail id
    int64 id = 1;
    // 这个 pet 选择的 daycare service 所缺失的 evaluation
    optional moego.models.offering.v1.EvaluationBriefView missing_evaluation = 2;
  }
}

// CreateBoardingServiceWaitlist request
message CreateBoardingServiceWaitlistRequest {
  // start date
  google.type.Date start_date = 1 [(validate.rules).message = {required: true}];
  // end date
  google.type.Date end_date = 2 [(validate.rules).message = {required: true}];
}

// CreateDaycareServiceWaitlist request
message CreateDaycareServiceWaitlistRequest {
  // specific dates
  repeated google.type.Date specific_dates = 1 [(validate.rules).repeated = {min_items: 1}];
}

// UpdateBoardingServiceWaitlist request
message UpdateBoardingServiceWaitlistRequest {
  // start date
  optional google.type.Date start_date = 1;
  // end date
  optional google.type.Date end_date = 2;
}

// UpdateDaycareServiceWaitlist request
message UpdateDaycareServiceWaitlistRequest {
  // specific dates
  optional moego.utils.v2.DateList specific_dates = 1;
}

// check waitlist available request
message CheckWaitlistAvailableTaskRequest {}

// check waitlist available Response
message CheckWaitlistAvailableTaskResponse {}

// MoveBookingRequestToWaitlist request
message MoveBookingRequestToWaitlistRequest {
  // company id, optional
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id, optional
  optional int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];

  // The booking request id
  int64 booking_request_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// MoveBookingRequestToWaitlist response
message MoveBookingRequestToWaitlistResponse {}

// The request to preview pricing of booking request
message PreviewBookingRequestPricingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // exising customer id, optional
  optional int64 customer_id = 3 [(validate.rules).int64 = {gt: 0}];
  // selected pet and services
  repeated models.online_booking.v1.PetServiceDetails pet_services = 4 [(validate.rules).repeated = {min_items: 1}];
}

// The response of preview pricing of booking request
message PreviewBookingRequestPricingResponse {
  // Line items of the booking request
  repeated LineItem line_items = 1;

  // Line item
  message LineItem {
    // pet id, virtual pet id or real pet id
    int64 pet_id = 1;
    // service id
    models.offering.v1.CustomizedServiceView service = 2;
    // unit price
    google.type.Money unit_price = 3;
    // quantity
    int32 quantity = 4;
    // total price
    google.type.Money total_price = 5;
  }
}

// count booking request by filter
message CountBookingRequestByFilterRequest {
  // company
  int64 company_id = 1;
  // evaluation_ids
  repeated int64 evaluation_ids = 2;
}

// check service in use response
message CountBookingRequestByFilterResponse {
  // evaluation in use
  map<int64, int32> evaluation_in_use = 1;
}

// the booking_request service
service BookingRequestService {
  // Create a record, return inserted id.
  rpc CreateBookingRequest(CreateBookingRequestRequest) returns (google.protobuf.Int64Value) {}

  // Get a record by id, not include deleted record.
  rpc GetBookingRequest(GetBookingRequestRequest) returns (GetBookingRequestResponse) {}

  // List booking requests<br>
  // Supports filtering, sorting, paging, and returning data by associated model array<br>
  // filters: business_id, customer_id, status, start_date, end_date, created_at<br>
  // sorts: id, start_date, created_at<br>
  // associated model array: services, add-on, feeding, medication
  rpc ListBookingRequests(ListBookingRequestsRequest) returns (ListBookingRequestsResponse) {}

  // list waitlists
  rpc ListWaitlists(ListWaitlistsRequest) returns (ListWaitlistsResponse) {}

  // Create a grooming only booking request
  rpc CreateGroomingOnly(CreateGroomingOnlyRequest) returns (CreateGroomingOnlyResponse);

  // Update booking request status
  // submitted -> wait_list / scheduled / declined / payment_failed
  // wait_list -> scheduled / deleted
  rpc UpdateBookingRequestStatus(UpdateBookingRequestStatusRequest) returns (UpdateBookingRequestStatusResponse);

  // Update booking request
  rpc UpdateBookingRequest(UpdateBookingRequestRequest) returns (UpdateBookingRequestResponse);

  // replace booking reqeust
  rpc ReplaceBookingRequest(ReplaceBookingRequestRequest) returns (ReplaceBookingRequestResponse);

  // Retry failed events
  rpc RetryFailedEvents(RetryFailedEventsRequest) returns (RetryFailedEventsResponse);

  // Get auto assign room
  // deprecated by Freeman since 2025/3/6, use AutoAssign instead
  rpc GetAutoAssign(GetAutoAssignRequest) returns (GetAutoAssignResponse) {
    option deprecated = true;
  }

  // Auto assign
  rpc AutoAssign(AutoAssignRequest) returns (AutoAssignResponse);

  // Accept booking request
  // deprecated by Freeman since 2025/3/5, 这个接口的参数设计和实现都很难看懂，难以扩展，use AcceptBookingRequestV2 instead
  rpc AcceptBookingRequest(AcceptBookingRequestRequest) returns (AcceptBookingRequestResponse) {
    option deprecated = true;
  }

  // Accept booking request v2
  // 没放在 v2 包下面的原因是这个接口会大量复用 AcceptBookingRequest 的逻辑，需要在一个类里面
  rpc AcceptBookingRequestV2(AcceptBookingRequestV2Request) returns (AcceptBookingRequestV2Response);

  // Decline booking request
  rpc DeclineBookingRequest(DeclineBookingRequestRequest) returns (DeclineBookingRequestResponse);

  // Count booking requests
  rpc CountBookingRequests(CountBookingRequestsRequest) returns (CountBookingRequestsResponse);

  // List booking request ids by appointment id
  rpc ListBookingRequestId(ListBookingRequestIdRequest) returns (ListBookingRequestIdResponse);

  // Sync BookingRequest from appointment
  // 这个接口用于同步老的 BookingRequest 数据（保存在 moe_grooming_appointment 表）到新的 booking_request 表
  rpc SyncBookingRequestFromAppointment(SyncBookingRequestFromAppointmentRequest) returns (SyncBookingRequestFromAppointmentResponse);

  // online booking 的 auto accept 逻辑，这个接口会处理 accept BookingRequest，capture PaymentIntent，send notification 等逻辑。
  // 这个接口的调用场景有两个：
  // 1. 在不需要 payment 的 submit BookingRequest 场景里，在 client-api-v1 直接调用。
  // 2. 在需要 payment 的 submit BookingRequest 场景里，在 confirm PaymentIntent 的回调里调用。
  rpc TriggerBookingRequestAutoAccepted(TriggerBookingRequestAutoAcceptedRequest) returns (TriggerBookingRequestAutoAcceptedResponse);

  // waitlist available check task
  rpc CheckWaitlistAvailableTask(CheckWaitlistAvailableTaskRequest) returns (CheckWaitlistAvailableTaskResponse);

  // Move booking request to waitlist
  rpc MoveBookingRequestToWaitlist(MoveBookingRequestToWaitlistRequest) returns (MoveBookingRequestToWaitlistResponse);

  // check service id is use
  rpc CountBookingRequestByFilter(CountBookingRequestByFilterRequest) returns (CountBookingRequestByFilterResponse);

  // Preview pricing of booking request
  rpc PreviewBookingRequestPricing(PreviewBookingRequestPricingRequest) returns (PreviewBookingRequestPricingResponse);
}
