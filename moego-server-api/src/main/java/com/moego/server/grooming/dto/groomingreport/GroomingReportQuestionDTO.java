package com.moego.server.grooming.dto.groomingreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * Grooming report question 记录，moe_grooming_report_question 表对象
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroomingReportQuestionDTO {

    private Integer id;
    private Integer businessId;

    @Schema(description = "category name")
    private Byte category;

    @Schema(description = "question type")
    private String type;

    @Schema(description = "system default question key")
    private String key;

    @Schema(description = "title")
    private String title;

    @Schema(description = "is default question")
    private Boolean isDefault;

    @Schema(description = "required to fill in: true/false")
    private Boolean required;

    @Schema(description = "type editable: true/false")
    private Boolean typeEditable;

    @Schema(description = "title editable: true/false")
    private Boolean titleEditable;

    @Schema(description = "options editable: true/false")
    private Boolean optionsEditable;

    @Schema(description = "sort value, in descending order")
    private Integer sort;

    @Schema(description = "status, 0-normal, 1-deleted")
    private Byte status;

    private Long createTime;
    private Long updateTime;

    // extra json 解析字段
    @Schema(description = "required = false 时，true 直接展示在填写列表，false 放在 select more 中")
    private Boolean show;

    @Schema(description = "default options list, not allow to change")
    private List<String> buildInOptions;

    @Schema(description = "single_choice/multi_choice question's options list")
    private List<String> options;

    @Schema(description = "single_choice/multi_choice question's selected choice list")
    private List<String> choices;

    @Schema(description = "single_choice/multi_choice question's custom options list")
    private List<String> customOptions;

    @Schema(description = "text_input question's input text")
    private String text;

    @Schema(description = "text_input question's placeholder")
    private String placeholder;

    @Schema(description = "body_view question's saved urls")
    private BodyViewUrl urls;
}
