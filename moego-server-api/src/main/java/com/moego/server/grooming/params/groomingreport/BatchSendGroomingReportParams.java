package com.moego.server.grooming.params.groomingreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BatchSendGroomingReportParams {

    @JsonIgnore
    private Integer companyId;

    @JsonIgnore
    private Integer businessId;

    @Schema(description = "need to send report ids")
    private List<Integer> reportIds;

    @Schema(description = "send method: 1-sms, 2-email")
    private Byte sendMethod;

    @Schema(description = "staff id")
    private Integer staffId;
}
