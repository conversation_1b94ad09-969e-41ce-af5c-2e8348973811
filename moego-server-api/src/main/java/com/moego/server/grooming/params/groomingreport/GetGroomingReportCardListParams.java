package com.moego.server.grooming.params.groomingreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.type.Date;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class GetGroomingReportCardListParams {

    @JsonIgnore
    private Long companyId;

    @JsonIgnore
    private Long businessId;

    @Schema(description = "start date")
    private String startDate;

    @Schema(description = "end date")
    private String endDate;

    @Schema(description = "grooming report status, created/draft/submitted/sent")
    private GroomingReportStatusEnum status;

    @Schema(description = "pet id")
    private Long petId;
}
