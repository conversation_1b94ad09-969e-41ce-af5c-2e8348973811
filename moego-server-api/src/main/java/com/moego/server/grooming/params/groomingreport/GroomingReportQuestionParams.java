package com.moego.server.grooming.params.groomingreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.moego.server.grooming.dto.groomingreport.BodyViewUrl;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroomingReportQuestionParams {

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Long companyId;

    @Schema(description = "question id, not need for creating a new question")
    @Positive
    private Integer id;

    @Schema(description = "category, 1-feedback, 2-pet condition, 3-customized feedback")
    private Byte category;

    @Schema(description = "question type: single_choice/multi_choice/text_input/body_view/tag_choice/short_text_input")
    private String type;

    @Schema(description = "system default question key")
    private String key;

    @Schema(description = "question title")
    @Size(max = 50)
    private String title;

    @Schema(description = "is required to fill in")
    private Boolean required;

    @Schema(description = "question type editable")
    private Boolean typeEditable;

    @Schema(description = "question title editable")
    private Boolean titleEditable;

    @Schema(description = "question options editable")
    private Boolean optionsEditable;

    @Schema(description = "sort value, in descending order")
    private Integer sort;

    @Schema(description = "status: 0-normal, 1-deleted")
    @Max(1)
    @Min(0)
    private Byte status;

    // extra json 解析字段
    @Schema(description = "default options list, not allow to change")
    private List<String> buildInOptions;

    @Schema(description = "single_choice/multi_choice, options list")
    private List<@Size(max = 30) String> options;

    @Schema(description = "single_choice/multi_choice, custom options list")
    private List<@Size(max = 30) String> customOptions;

    @Schema(description = "default selected options")
    private List<String> choices;

    @Schema(description = "non required question show at question list")
    private Boolean show;

    @Schema(description = "text_input question default value")
    private String text;

    @Schema(description = "text_input question placeholder")
    private String placeholder;

    @Schema(description = "body_view question marked result url")
    private BodyViewUrl urls;
}
