package com.moego.server.message.dto;

import java.util.Date;
import lombok.Data;

@Data
public class GroomingReportSendLogDTO {

    private Integer id;
    private Integer businessId;
    private Integer groomingId;
    private Integer petId;
    private Integer reportId;
    private Integer sendingType;
    private Byte sendingMethod;
    private Date sentTime;
    private Integer sentBy;
    private Byte status;
    private Integer errorCode;
    private String errorMsg;
    private Date createTime;
    private Date updateTime;
}
