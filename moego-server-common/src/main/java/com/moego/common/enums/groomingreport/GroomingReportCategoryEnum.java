package com.moego.common.enums.groomingreport;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum GroomingReportCategoryEnum {
    CATEGORY_FEEDBACK((byte) 1, "Professional feedback"),
    CATEGORY_PET_CONDITION((byte) 2, "Pet condition"),
    CATEGORY_CUSTOMIZED_FEEDBACK((byte) 3, "Customized feedback");

    final byte type;
    final String name;
}
