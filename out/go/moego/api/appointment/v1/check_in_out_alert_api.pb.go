// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/check_in_out_alert_api.proto

package appointmentapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetAlertSettingsParams
type GetAlertSettingsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetAlertSettingsParams) Reset() {
	*x = GetAlertSettingsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertSettingsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertSettingsParams) ProtoMessage() {}

func (x *GetAlertSettingsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertSettingsParams.ProtoReflect.Descriptor instead.
func (*GetAlertSettingsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{0}
}

// GetAlertSettingsResult
type GetAlertSettingsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check in/out alerts settings
	Settings *v1.CheckInOutAlertSettings `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *GetAlertSettingsResult) Reset() {
	*x = GetAlertSettingsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertSettingsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertSettingsResult) ProtoMessage() {}

func (x *GetAlertSettingsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertSettingsResult.ProtoReflect.Descriptor instead.
func (*GetAlertSettingsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetAlertSettingsResult) GetSettings() *v1.CheckInOutAlertSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

// SaveAlertSettingsParams
type SaveAlertSettingsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check in alert settings
	CheckInSettings *v1.CheckInAlertSettings `protobuf:"bytes,1,opt,name=check_in_settings,json=checkInSettings,proto3,oneof" json:"check_in_settings,omitempty"`
	// check out alert settings
	CheckOutSettings *v1.CheckOutAlertSettings `protobuf:"bytes,2,opt,name=check_out_settings,json=checkOutSettings,proto3,oneof" json:"check_out_settings,omitempty"`
}

func (x *SaveAlertSettingsParams) Reset() {
	*x = SaveAlertSettingsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAlertSettingsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAlertSettingsParams) ProtoMessage() {}

func (x *SaveAlertSettingsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAlertSettingsParams.ProtoReflect.Descriptor instead.
func (*SaveAlertSettingsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{2}
}

func (x *SaveAlertSettingsParams) GetCheckInSettings() *v1.CheckInAlertSettings {
	if x != nil {
		return x.CheckInSettings
	}
	return nil
}

func (x *SaveAlertSettingsParams) GetCheckOutSettings() *v1.CheckOutAlertSettings {
	if x != nil {
		return x.CheckOutSettings
	}
	return nil
}

// SaveAlertSettingsResult
type SaveAlertSettingsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check in/out alerts settings
	Settings *v1.CheckInOutAlertSettings `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *SaveAlertSettingsResult) Reset() {
	*x = SaveAlertSettingsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAlertSettingsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAlertSettingsResult) ProtoMessage() {}

func (x *SaveAlertSettingsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAlertSettingsResult.ProtoReflect.Descriptor instead.
func (*SaveAlertSettingsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{3}
}

func (x *SaveAlertSettingsResult) GetSettings() *v1.CheckInOutAlertSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

// GetAlertsForCheckInParams
type GetAlertsForCheckInParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetAlertsForCheckInParams) Reset() {
	*x = GetAlertsForCheckInParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsForCheckInParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsForCheckInParams) ProtoMessage() {}

func (x *GetAlertsForCheckInParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsForCheckInParams.ProtoReflect.Descriptor instead.
func (*GetAlertsForCheckInParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetAlertsForCheckInParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAlertsForCheckInParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// GetAlertsForCheckInResult
type GetAlertsForCheckInResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check in alert
	Alert *v1.AlertDetail `protobuf:"bytes,1,opt,name=alert,proto3,oneof" json:"alert,omitempty"`
}

func (x *GetAlertsForCheckInResult) Reset() {
	*x = GetAlertsForCheckInResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsForCheckInResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsForCheckInResult) ProtoMessage() {}

func (x *GetAlertsForCheckInResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsForCheckInResult.ProtoReflect.Descriptor instead.
func (*GetAlertsForCheckInResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{5}
}

func (x *GetAlertsForCheckInResult) GetAlert() *v1.AlertDetail {
	if x != nil {
		return x.Alert
	}
	return nil
}

// GetAlertsForCheckOutParams
type GetAlertsForCheckOutParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetAlertsForCheckOutParams) Reset() {
	*x = GetAlertsForCheckOutParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsForCheckOutParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsForCheckOutParams) ProtoMessage() {}

func (x *GetAlertsForCheckOutParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsForCheckOutParams.ProtoReflect.Descriptor instead.
func (*GetAlertsForCheckOutParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{6}
}

func (x *GetAlertsForCheckOutParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAlertsForCheckOutParams) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// GetAlertsForCheckOutResult
type GetAlertsForCheckOutResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check out alert
	Alert *v1.AlertDetail `protobuf:"bytes,1,opt,name=alert,proto3,oneof" json:"alert,omitempty"`
}

func (x *GetAlertsForCheckOutResult) Reset() {
	*x = GetAlertsForCheckOutResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsForCheckOutResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsForCheckOutResult) ProtoMessage() {}

func (x *GetAlertsForCheckOutResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsForCheckOutResult.ProtoReflect.Descriptor instead.
func (*GetAlertsForCheckOutResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{7}
}

func (x *GetAlertsForCheckOutResult) GetAlert() *v1.AlertDetail {
	if x != nil {
		return x.Alert
	}
	return nil
}

// BatchGetAlertsForCheckInParams
type BatchGetAlertsForCheckInParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// client and pet ids
	ClientPets []*v1.ClientPetsMapping `protobuf:"bytes,2,rep,name=client_pets,json=clientPets,proto3" json:"client_pets,omitempty"`
}

func (x *BatchGetAlertsForCheckInParams) Reset() {
	*x = BatchGetAlertsForCheckInParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAlertsForCheckInParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAlertsForCheckInParams) ProtoMessage() {}

func (x *BatchGetAlertsForCheckInParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAlertsForCheckInParams.ProtoReflect.Descriptor instead.
func (*BatchGetAlertsForCheckInParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{8}
}

func (x *BatchGetAlertsForCheckInParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchGetAlertsForCheckInParams) GetClientPets() []*v1.ClientPetsMapping {
	if x != nil {
		return x.ClientPets
	}
	return nil
}

// BatchGetAlertsForCheckInResult
type BatchGetAlertsForCheckInResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client and pet alerts
	Alerts []*v1.AlertDetail `protobuf:"bytes,1,rep,name=alerts,proto3" json:"alerts,omitempty"`
}

func (x *BatchGetAlertsForCheckInResult) Reset() {
	*x = BatchGetAlertsForCheckInResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAlertsForCheckInResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAlertsForCheckInResult) ProtoMessage() {}

func (x *BatchGetAlertsForCheckInResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAlertsForCheckInResult.ProtoReflect.Descriptor instead.
func (*BatchGetAlertsForCheckInResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP(), []int{9}
}

func (x *BatchGetAlertsForCheckInResult) GetAlerts() []*v1.AlertDetail {
	if x != nil {
		return x.Alerts
	}
	return nil
}

var File_moego_api_appointment_v1_check_in_out_alert_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x1a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x18, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x6a, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x08,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x91, 0x02, 0x0a, 0x17, 0x53, 0x61, 0x76,
	0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x62, 0x0a, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x48, 0x00, 0x52, 0x0f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x88, 0x01, 0x01, 0x12, 0x65, 0x0a, 0x12, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x48, 0x01, 0x52, 0x10, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x4f, 0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x88, 0x01, 0x01, 0x42,
	0x14, 0x0a, 0x12, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x6b, 0x0a, 0x17,
	0x53, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x4f,
	0x75, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52,
	0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x75, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x6a, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x43, 0x0a,
	0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x88,
	0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x22, 0x76, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4f, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x43, 0x0a, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x05, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x22, 0xa7, 0x01, 0x0a, 0x1e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x5b,
	0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x73, 0x4d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x64, 0x52,
	0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x73, 0x22, 0x62, 0x0a, 0x1e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a,
	0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x32,
	0xa2, 0x05, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x76, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x79, 0x0a, 0x11, 0x53, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8e, 0x01,
	0x0a, 0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73,
	0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f,
	0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7f,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6e, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46,
	0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x82, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescData = file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDesc
)

func file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDescData
}

var file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_api_appointment_v1_check_in_out_alert_api_proto_goTypes = []interface{}{
	(*GetAlertSettingsParams)(nil),         // 0: moego.api.appointment.v1.GetAlertSettingsParams
	(*GetAlertSettingsResult)(nil),         // 1: moego.api.appointment.v1.GetAlertSettingsResult
	(*SaveAlertSettingsParams)(nil),        // 2: moego.api.appointment.v1.SaveAlertSettingsParams
	(*SaveAlertSettingsResult)(nil),        // 3: moego.api.appointment.v1.SaveAlertSettingsResult
	(*GetAlertsForCheckInParams)(nil),      // 4: moego.api.appointment.v1.GetAlertsForCheckInParams
	(*GetAlertsForCheckInResult)(nil),      // 5: moego.api.appointment.v1.GetAlertsForCheckInResult
	(*GetAlertsForCheckOutParams)(nil),     // 6: moego.api.appointment.v1.GetAlertsForCheckOutParams
	(*GetAlertsForCheckOutResult)(nil),     // 7: moego.api.appointment.v1.GetAlertsForCheckOutResult
	(*BatchGetAlertsForCheckInParams)(nil), // 8: moego.api.appointment.v1.BatchGetAlertsForCheckInParams
	(*BatchGetAlertsForCheckInResult)(nil), // 9: moego.api.appointment.v1.BatchGetAlertsForCheckInResult
	(*v1.CheckInOutAlertSettings)(nil),     // 10: moego.models.appointment.v1.CheckInOutAlertSettings
	(*v1.CheckInAlertSettings)(nil),        // 11: moego.models.appointment.v1.CheckInAlertSettings
	(*v1.CheckOutAlertSettings)(nil),       // 12: moego.models.appointment.v1.CheckOutAlertSettings
	(*v1.AlertDetail)(nil),                 // 13: moego.models.appointment.v1.AlertDetail
	(*v1.ClientPetsMapping)(nil),           // 14: moego.models.appointment.v1.ClientPetsMapping
}
var file_moego_api_appointment_v1_check_in_out_alert_api_proto_depIdxs = []int32{
	10, // 0: moego.api.appointment.v1.GetAlertSettingsResult.settings:type_name -> moego.models.appointment.v1.CheckInOutAlertSettings
	11, // 1: moego.api.appointment.v1.SaveAlertSettingsParams.check_in_settings:type_name -> moego.models.appointment.v1.CheckInAlertSettings
	12, // 2: moego.api.appointment.v1.SaveAlertSettingsParams.check_out_settings:type_name -> moego.models.appointment.v1.CheckOutAlertSettings
	10, // 3: moego.api.appointment.v1.SaveAlertSettingsResult.settings:type_name -> moego.models.appointment.v1.CheckInOutAlertSettings
	13, // 4: moego.api.appointment.v1.GetAlertsForCheckInResult.alert:type_name -> moego.models.appointment.v1.AlertDetail
	13, // 5: moego.api.appointment.v1.GetAlertsForCheckOutResult.alert:type_name -> moego.models.appointment.v1.AlertDetail
	14, // 6: moego.api.appointment.v1.BatchGetAlertsForCheckInParams.client_pets:type_name -> moego.models.appointment.v1.ClientPetsMapping
	13, // 7: moego.api.appointment.v1.BatchGetAlertsForCheckInResult.alerts:type_name -> moego.models.appointment.v1.AlertDetail
	0,  // 8: moego.api.appointment.v1.CheckInOutAlertService.GetAlertSettings:input_type -> moego.api.appointment.v1.GetAlertSettingsParams
	2,  // 9: moego.api.appointment.v1.CheckInOutAlertService.SaveAlertSettings:input_type -> moego.api.appointment.v1.SaveAlertSettingsParams
	8,  // 10: moego.api.appointment.v1.CheckInOutAlertService.BatchGetAlertsForCheckIn:input_type -> moego.api.appointment.v1.BatchGetAlertsForCheckInParams
	4,  // 11: moego.api.appointment.v1.CheckInOutAlertService.GetAlertsForCheckIn:input_type -> moego.api.appointment.v1.GetAlertsForCheckInParams
	6,  // 12: moego.api.appointment.v1.CheckInOutAlertService.GetAlertsForCheckOut:input_type -> moego.api.appointment.v1.GetAlertsForCheckOutParams
	1,  // 13: moego.api.appointment.v1.CheckInOutAlertService.GetAlertSettings:output_type -> moego.api.appointment.v1.GetAlertSettingsResult
	3,  // 14: moego.api.appointment.v1.CheckInOutAlertService.SaveAlertSettings:output_type -> moego.api.appointment.v1.SaveAlertSettingsResult
	9,  // 15: moego.api.appointment.v1.CheckInOutAlertService.BatchGetAlertsForCheckIn:output_type -> moego.api.appointment.v1.BatchGetAlertsForCheckInResult
	5,  // 16: moego.api.appointment.v1.CheckInOutAlertService.GetAlertsForCheckIn:output_type -> moego.api.appointment.v1.GetAlertsForCheckInResult
	7,  // 17: moego.api.appointment.v1.CheckInOutAlertService.GetAlertsForCheckOut:output_type -> moego.api.appointment.v1.GetAlertsForCheckOutResult
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_check_in_out_alert_api_proto_init() }
func file_moego_api_appointment_v1_check_in_out_alert_api_proto_init() {
	if File_moego_api_appointment_v1_check_in_out_alert_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertSettingsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertSettingsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAlertSettingsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAlertSettingsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsForCheckInParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsForCheckInResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsForCheckOutParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsForCheckOutResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAlertsForCheckInParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAlertsForCheckInResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_check_in_out_alert_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_check_in_out_alert_api_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_check_in_out_alert_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_check_in_out_alert_api_proto = out.File
	file_moego_api_appointment_v1_check_in_out_alert_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_check_in_out_alert_api_proto_goTypes = nil
	file_moego_api_appointment_v1_check_in_out_alert_api_proto_depIdxs = nil
}
