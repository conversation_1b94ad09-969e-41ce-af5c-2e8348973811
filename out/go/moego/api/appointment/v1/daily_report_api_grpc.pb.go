// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/daily_report_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DailyReportServiceClient is the client API for DailyReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DailyReportServiceClient interface {
	// get daily report config
	GetDailyReportConfig(ctx context.Context, in *GetDailyReportConfigParams, opts ...grpc.CallOption) (*GetDailyReportConfigResult, error)
	// get daily report config sent result
	GetDailyReportSentResult(ctx context.Context, in *GetDailyReportSentResultParams, opts ...grpc.CallOption) (*GetDailyReportSentResultResult, error)
	// upsert daily report config
	UpsertDailyReportConfig(ctx context.Context, in *UpsertDailyReportConfigParams, opts ...grpc.CallOption) (*UpsertDailyReportConfigResult, error)
	// get daily report sent history
	GetDailyReportSentHistory(ctx context.Context, in *GetDailyReportSentHistoryParams, opts ...grpc.CallOption) (*GetDailyReportSentHistoryResult, error)
	// get daily report for customer
	GetDailyReportForCustomer(ctx context.Context, in *GetDailyReportForCustomerParams, opts ...grpc.CallOption) (*GetDailyReportForCustomerResult, error)
	// generate message content
	GenerateMessageContent(ctx context.Context, in *GenerateMessageContentParams, opts ...grpc.CallOption) (*GenerateMessageContentResult, error)
	// send message
	SendMessage(ctx context.Context, in *SendMessageParams, opts ...grpc.CallOption) (*SendMessageResult, error)
	// get daily report list
	ListDailyReportConfig(ctx context.Context, in *ListDailyReportConfigParams, opts ...grpc.CallOption) (*ListDailyReportConfigResult, error)
	// batch send daily draft report
	BatchSendDailyDraftReport(ctx context.Context, in *BatchSendDailyDraftReportParams, opts ...grpc.CallOption) (*BatchSendDailyDraftReportResult, error)
	// batch delete daily report config
	BatchDeleteDailyReportConfig(ctx context.Context, in *BatchDeleteDailyReportConfigParams, opts ...grpc.CallOption) (*BatchDeleteDailyReportConfigResult, error)
}

type dailyReportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDailyReportServiceClient(cc grpc.ClientConnInterface) DailyReportServiceClient {
	return &dailyReportServiceClient{cc}
}

func (c *dailyReportServiceClient) GetDailyReportConfig(ctx context.Context, in *GetDailyReportConfigParams, opts ...grpc.CallOption) (*GetDailyReportConfigResult, error) {
	out := new(GetDailyReportConfigResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/GetDailyReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) GetDailyReportSentResult(ctx context.Context, in *GetDailyReportSentResultParams, opts ...grpc.CallOption) (*GetDailyReportSentResultResult, error) {
	out := new(GetDailyReportSentResultResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/GetDailyReportSentResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) UpsertDailyReportConfig(ctx context.Context, in *UpsertDailyReportConfigParams, opts ...grpc.CallOption) (*UpsertDailyReportConfigResult, error) {
	out := new(UpsertDailyReportConfigResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/UpsertDailyReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) GetDailyReportSentHistory(ctx context.Context, in *GetDailyReportSentHistoryParams, opts ...grpc.CallOption) (*GetDailyReportSentHistoryResult, error) {
	out := new(GetDailyReportSentHistoryResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/GetDailyReportSentHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) GetDailyReportForCustomer(ctx context.Context, in *GetDailyReportForCustomerParams, opts ...grpc.CallOption) (*GetDailyReportForCustomerResult, error) {
	out := new(GetDailyReportForCustomerResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/GetDailyReportForCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) GenerateMessageContent(ctx context.Context, in *GenerateMessageContentParams, opts ...grpc.CallOption) (*GenerateMessageContentResult, error) {
	out := new(GenerateMessageContentResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/GenerateMessageContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) SendMessage(ctx context.Context, in *SendMessageParams, opts ...grpc.CallOption) (*SendMessageResult, error) {
	out := new(SendMessageResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/SendMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) ListDailyReportConfig(ctx context.Context, in *ListDailyReportConfigParams, opts ...grpc.CallOption) (*ListDailyReportConfigResult, error) {
	out := new(ListDailyReportConfigResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/ListDailyReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) BatchSendDailyDraftReport(ctx context.Context, in *BatchSendDailyDraftReportParams, opts ...grpc.CallOption) (*BatchSendDailyDraftReportResult, error) {
	out := new(BatchSendDailyDraftReportResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/BatchSendDailyDraftReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dailyReportServiceClient) BatchDeleteDailyReportConfig(ctx context.Context, in *BatchDeleteDailyReportConfigParams, opts ...grpc.CallOption) (*BatchDeleteDailyReportConfigResult, error) {
	out := new(BatchDeleteDailyReportConfigResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.DailyReportService/BatchDeleteDailyReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DailyReportServiceServer is the server API for DailyReportService service.
// All implementations must embed UnimplementedDailyReportServiceServer
// for forward compatibility
type DailyReportServiceServer interface {
	// get daily report config
	GetDailyReportConfig(context.Context, *GetDailyReportConfigParams) (*GetDailyReportConfigResult, error)
	// get daily report config sent result
	GetDailyReportSentResult(context.Context, *GetDailyReportSentResultParams) (*GetDailyReportSentResultResult, error)
	// upsert daily report config
	UpsertDailyReportConfig(context.Context, *UpsertDailyReportConfigParams) (*UpsertDailyReportConfigResult, error)
	// get daily report sent history
	GetDailyReportSentHistory(context.Context, *GetDailyReportSentHistoryParams) (*GetDailyReportSentHistoryResult, error)
	// get daily report for customer
	GetDailyReportForCustomer(context.Context, *GetDailyReportForCustomerParams) (*GetDailyReportForCustomerResult, error)
	// generate message content
	GenerateMessageContent(context.Context, *GenerateMessageContentParams) (*GenerateMessageContentResult, error)
	// send message
	SendMessage(context.Context, *SendMessageParams) (*SendMessageResult, error)
	// get daily report list
	ListDailyReportConfig(context.Context, *ListDailyReportConfigParams) (*ListDailyReportConfigResult, error)
	// batch send daily draft report
	BatchSendDailyDraftReport(context.Context, *BatchSendDailyDraftReportParams) (*BatchSendDailyDraftReportResult, error)
	// batch delete daily report config
	BatchDeleteDailyReportConfig(context.Context, *BatchDeleteDailyReportConfigParams) (*BatchDeleteDailyReportConfigResult, error)
	mustEmbedUnimplementedDailyReportServiceServer()
}

// UnimplementedDailyReportServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDailyReportServiceServer struct {
}

func (UnimplementedDailyReportServiceServer) GetDailyReportConfig(context.Context, *GetDailyReportConfigParams) (*GetDailyReportConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyReportConfig not implemented")
}
func (UnimplementedDailyReportServiceServer) GetDailyReportSentResult(context.Context, *GetDailyReportSentResultParams) (*GetDailyReportSentResultResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyReportSentResult not implemented")
}
func (UnimplementedDailyReportServiceServer) UpsertDailyReportConfig(context.Context, *UpsertDailyReportConfigParams) (*UpsertDailyReportConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertDailyReportConfig not implemented")
}
func (UnimplementedDailyReportServiceServer) GetDailyReportSentHistory(context.Context, *GetDailyReportSentHistoryParams) (*GetDailyReportSentHistoryResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyReportSentHistory not implemented")
}
func (UnimplementedDailyReportServiceServer) GetDailyReportForCustomer(context.Context, *GetDailyReportForCustomerParams) (*GetDailyReportForCustomerResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyReportForCustomer not implemented")
}
func (UnimplementedDailyReportServiceServer) GenerateMessageContent(context.Context, *GenerateMessageContentParams) (*GenerateMessageContentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateMessageContent not implemented")
}
func (UnimplementedDailyReportServiceServer) SendMessage(context.Context, *SendMessageParams) (*SendMessageResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedDailyReportServiceServer) ListDailyReportConfig(context.Context, *ListDailyReportConfigParams) (*ListDailyReportConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDailyReportConfig not implemented")
}
func (UnimplementedDailyReportServiceServer) BatchSendDailyDraftReport(context.Context, *BatchSendDailyDraftReportParams) (*BatchSendDailyDraftReportResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchSendDailyDraftReport not implemented")
}
func (UnimplementedDailyReportServiceServer) BatchDeleteDailyReportConfig(context.Context, *BatchDeleteDailyReportConfigParams) (*BatchDeleteDailyReportConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteDailyReportConfig not implemented")
}
func (UnimplementedDailyReportServiceServer) mustEmbedUnimplementedDailyReportServiceServer() {}

// UnsafeDailyReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DailyReportServiceServer will
// result in compilation errors.
type UnsafeDailyReportServiceServer interface {
	mustEmbedUnimplementedDailyReportServiceServer()
}

func RegisterDailyReportServiceServer(s grpc.ServiceRegistrar, srv DailyReportServiceServer) {
	s.RegisterService(&DailyReportService_ServiceDesc, srv)
}

func _DailyReportService_GetDailyReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyReportConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GetDailyReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/GetDailyReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GetDailyReportConfig(ctx, req.(*GetDailyReportConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_GetDailyReportSentResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyReportSentResultParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GetDailyReportSentResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/GetDailyReportSentResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GetDailyReportSentResult(ctx, req.(*GetDailyReportSentResultParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_UpsertDailyReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertDailyReportConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).UpsertDailyReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/UpsertDailyReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).UpsertDailyReportConfig(ctx, req.(*UpsertDailyReportConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_GetDailyReportSentHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyReportSentHistoryParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GetDailyReportSentHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/GetDailyReportSentHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GetDailyReportSentHistory(ctx, req.(*GetDailyReportSentHistoryParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_GetDailyReportForCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyReportForCustomerParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GetDailyReportForCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/GetDailyReportForCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GetDailyReportForCustomer(ctx, req.(*GetDailyReportForCustomerParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_GenerateMessageContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateMessageContentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).GenerateMessageContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/GenerateMessageContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).GenerateMessageContent(ctx, req.(*GenerateMessageContentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMessageParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/SendMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).SendMessage(ctx, req.(*SendMessageParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_ListDailyReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDailyReportConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).ListDailyReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/ListDailyReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).ListDailyReportConfig(ctx, req.(*ListDailyReportConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_BatchSendDailyDraftReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSendDailyDraftReportParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).BatchSendDailyDraftReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/BatchSendDailyDraftReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).BatchSendDailyDraftReport(ctx, req.(*BatchSendDailyDraftReportParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _DailyReportService_BatchDeleteDailyReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteDailyReportConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DailyReportServiceServer).BatchDeleteDailyReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.DailyReportService/BatchDeleteDailyReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DailyReportServiceServer).BatchDeleteDailyReportConfig(ctx, req.(*BatchDeleteDailyReportConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

// DailyReportService_ServiceDesc is the grpc.ServiceDesc for DailyReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DailyReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.DailyReportService",
	HandlerType: (*DailyReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDailyReportConfig",
			Handler:    _DailyReportService_GetDailyReportConfig_Handler,
		},
		{
			MethodName: "GetDailyReportSentResult",
			Handler:    _DailyReportService_GetDailyReportSentResult_Handler,
		},
		{
			MethodName: "UpsertDailyReportConfig",
			Handler:    _DailyReportService_UpsertDailyReportConfig_Handler,
		},
		{
			MethodName: "GetDailyReportSentHistory",
			Handler:    _DailyReportService_GetDailyReportSentHistory_Handler,
		},
		{
			MethodName: "GetDailyReportForCustomer",
			Handler:    _DailyReportService_GetDailyReportForCustomer_Handler,
		},
		{
			MethodName: "GenerateMessageContent",
			Handler:    _DailyReportService_GenerateMessageContent_Handler,
		},
		{
			MethodName: "SendMessage",
			Handler:    _DailyReportService_SendMessage_Handler,
		},
		{
			MethodName: "ListDailyReportConfig",
			Handler:    _DailyReportService_ListDailyReportConfig_Handler,
		},
		{
			MethodName: "BatchSendDailyDraftReport",
			Handler:    _DailyReportService_BatchSendDailyDraftReport_Handler,
		},
		{
			MethodName: "BatchDeleteDailyReportConfig",
			Handler:    _DailyReportService_BatchDeleteDailyReportConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/daily_report_api.proto",
}
