// @since 2024-01-25 10:28:21
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/overview_api.proto

package appointmentapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v15 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v14 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// cof status
type CustomerCompositeOverview_COFStatus int32

const (
	// unspecified
	CustomerCompositeOverview_COF_STATUS_UNSPECIFIED CustomerCompositeOverview_COFStatus = 0
	// authorized
	CustomerCompositeOverview_AUTHORIZED CustomerCompositeOverview_COFStatus = 1
	// pending
	CustomerCompositeOverview_PENDING CustomerCompositeOverview_COFStatus = 2
	// failed
	CustomerCompositeOverview_FAILED CustomerCompositeOverview_COFStatus = 3
	// no card on file
	CustomerCompositeOverview_NO_CARD_ON_FILE CustomerCompositeOverview_COFStatus = 4
)

// Enum value maps for CustomerCompositeOverview_COFStatus.
var (
	CustomerCompositeOverview_COFStatus_name = map[int32]string{
		0: "COF_STATUS_UNSPECIFIED",
		1: "AUTHORIZED",
		2: "PENDING",
		3: "FAILED",
		4: "NO_CARD_ON_FILE",
	}
	CustomerCompositeOverview_COFStatus_value = map[string]int32{
		"COF_STATUS_UNSPECIFIED": 0,
		"AUTHORIZED":             1,
		"PENDING":                2,
		"FAILED":                 3,
		"NO_CARD_ON_FILE":        4,
	}
)

func (x CustomerCompositeOverview_COFStatus) Enum() *CustomerCompositeOverview_COFStatus {
	p := new(CustomerCompositeOverview_COFStatus)
	*p = x
	return p
}

func (x CustomerCompositeOverview_COFStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerCompositeOverview_COFStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_api_appointment_v1_overview_api_proto_enumTypes[0].Descriptor()
}

func (CustomerCompositeOverview_COFStatus) Type() protoreflect.EnumType {
	return &file_moego_api_appointment_v1_overview_api_proto_enumTypes[0]
}

func (x CustomerCompositeOverview_COFStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerCompositeOverview_COFStatus.Descriptor instead.
func (CustomerCompositeOverview_COFStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{8, 0}
}

// get overview list params
type GetOverviewListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// date
	Date string `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// key word
	Keyword *string `protobuf:"bytes,3,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// service item type
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,4,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// date type
	DateType v11.OverviewDateType `protobuf:"varint,5,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.OverviewDateType" json:"date_type,omitempty"`
	// filter, only effective when selected_status is set
	Filter *GetOverviewListParams_Filter `protobuf:"bytes,6,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// sort, only effective when selected_status is set
	OrderBys []*v2.OrderBy `protobuf:"bytes,7,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// selected status tab
	SelectedStatus *v11.OverviewStatus `protobuf:"varint,8,opt,name=selected_status,json=selectedStatus,proto3,enum=moego.models.appointment.v1.OverviewStatus,oneof" json:"selected_status,omitempty"`
}

func (x *GetOverviewListParams) Reset() {
	*x = GetOverviewListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOverviewListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOverviewListParams) ProtoMessage() {}

func (x *GetOverviewListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOverviewListParams.ProtoReflect.Descriptor instead.
func (*GetOverviewListParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetOverviewListParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetOverviewListParams) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *GetOverviewListParams) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *GetOverviewListParams) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *GetOverviewListParams) GetDateType() v11.OverviewDateType {
	if x != nil {
		return x.DateType
	}
	return v11.OverviewDateType(0)
}

func (x *GetOverviewListParams) GetFilter() *GetOverviewListParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *GetOverviewListParams) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *GetOverviewListParams) GetSelectedStatus() v11.OverviewStatus {
	if x != nil && x.SelectedStatus != nil {
		return *x.SelectedStatus
	}
	return v11.OverviewStatus(0)
}

// overview item
type OverviewItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment detail
	Appointment *v11.AppointmentOverview `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// pet and services
	ServiceDetail []*ServiceDetailOverview `protobuf:"bytes,2,rep,name=service_detail,json=serviceDetail,proto3" json:"service_detail,omitempty"`
	// notes
	Notes []*v11.AppointmentNoteModel `protobuf:"bytes,3,rep,name=notes,proto3" json:"notes,omitempty"`
	// customer
	Customer *CustomerCompositeOverview `protobuf:"bytes,4,opt,name=customer,proto3" json:"customer,omitempty"`
	// wait list
	WaitList *v11.WaitListCalendarView `protobuf:"bytes,5,opt,name=wait_list,json=waitList,proto3" json:"wait_list,omitempty"`
	// service type list
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,6,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// pre auth
	PreAuth *v12.PreAuthCalendarView `protobuf:"bytes,21,opt,name=pre_auth,json=preAuth,proto3" json:"pre_auth,omitempty"`
	// deposit
	Deposits *v11.InvoiceDepositModel `protobuf:"bytes,22,opt,name=deposits,proto3" json:"deposits,omitempty"`
	// invoice
	Invoice *v13.InvoiceCalendarView `protobuf:"bytes,23,opt,name=invoice,proto3" json:"invoice,omitempty"`
	// no show invoice
	NoShowInvoice *v13.NoShowInvoiceCalendarView `protobuf:"bytes,24,opt,name=no_show_invoice,json=noShowInvoice,proto3" json:"no_show_invoice,omitempty"`
	// report status
	ReportStatuses []*ReportStatus `protobuf:"bytes,25,rep,name=report_statuses,json=reportStatuses,proto3" json:"report_statuses,omitempty"`
	// memberships
	MembershipSubscriptions *v14.MembershipSubscriptionListModel `protobuf:"bytes,26,opt,name=membership_subscriptions,json=membershipSubscriptions,proto3" json:"membership_subscriptions,omitempty"`
}

func (x *OverviewItem) Reset() {
	*x = OverviewItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverviewItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverviewItem) ProtoMessage() {}

func (x *OverviewItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverviewItem.ProtoReflect.Descriptor instead.
func (*OverviewItem) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{1}
}

func (x *OverviewItem) GetAppointment() *v11.AppointmentOverview {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *OverviewItem) GetServiceDetail() []*ServiceDetailOverview {
	if x != nil {
		return x.ServiceDetail
	}
	return nil
}

func (x *OverviewItem) GetNotes() []*v11.AppointmentNoteModel {
	if x != nil {
		return x.Notes
	}
	return nil
}

func (x *OverviewItem) GetCustomer() *CustomerCompositeOverview {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *OverviewItem) GetWaitList() *v11.WaitListCalendarView {
	if x != nil {
		return x.WaitList
	}
	return nil
}

func (x *OverviewItem) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *OverviewItem) GetPreAuth() *v12.PreAuthCalendarView {
	if x != nil {
		return x.PreAuth
	}
	return nil
}

func (x *OverviewItem) GetDeposits() *v11.InvoiceDepositModel {
	if x != nil {
		return x.Deposits
	}
	return nil
}

func (x *OverviewItem) GetInvoice() *v13.InvoiceCalendarView {
	if x != nil {
		return x.Invoice
	}
	return nil
}

func (x *OverviewItem) GetNoShowInvoice() *v13.NoShowInvoiceCalendarView {
	if x != nil {
		return x.NoShowInvoice
	}
	return nil
}

func (x *OverviewItem) GetReportStatuses() []*ReportStatus {
	if x != nil {
		return x.ReportStatuses
	}
	return nil
}

func (x *OverviewItem) GetMembershipSubscriptions() *v14.MembershipSubscriptionListModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

// report status
type ReportStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// report status
	Status v11.OverviewReportStatus `protobuf:"varint,1,opt,name=status,proto3,enum=moego.models.appointment.v1.OverviewReportStatus" json:"status,omitempty"`
}

func (x *ReportStatus) Reset() {
	*x = ReportStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportStatus) ProtoMessage() {}

func (x *ReportStatus) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportStatus.ProtoReflect.Descriptor instead.
func (*ReportStatus) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{2}
}

func (x *ReportStatus) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ReportStatus) GetStatus() v11.OverviewReportStatus {
	if x != nil {
		return x.Status
	}
	return v11.OverviewReportStatus(0)
}

// pet detail
type ServiceDetailOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet
	Pet *v15.BusinessCustomerPetModelOverview `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// services
	Services []*ServiceCompositeOverview `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	// add-ons
	AddOns []*AddOnCompositeOverview `protobuf:"bytes,3,rep,name=add_ons,json=addOns,proto3" json:"add_ons,omitempty"`
	// evaluation
	Evaluations []*EvaluationServiceOverview `protobuf:"bytes,4,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
	// pet codes
	Codes []*v15.BusinessPetCodeModel `protobuf:"bytes,5,rep,name=codes,proto3" json:"codes,omitempty"`
	// pet notes
	Notes []*v15.BusinessPetNoteModel `protobuf:"bytes,6,rep,name=notes,proto3" json:"notes,omitempty"`
	// pet vaccines
	Vaccines []*VaccineComposite `protobuf:"bytes,7,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
	// pet bindings
	Bindings []*ServiceDetailOverview_Binding `protobuf:"bytes,8,rep,name=bindings,proto3" json:"bindings,omitempty"`
	// pet incident reports
	IncidentReports []*v15.BusinessPetIncidentReportModel `protobuf:"bytes,9,rep,name=incident_reports,json=incidentReports,proto3" json:"incident_reports,omitempty"`
	// pet evaluation
	PetEvaluations []*v15.PetEvaluationModel `protobuf:"bytes,10,rep,name=pet_evaluations,json=petEvaluations,proto3" json:"pet_evaluations,omitempty"`
}

func (x *ServiceDetailOverview) Reset() {
	*x = ServiceDetailOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceDetailOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDetailOverview) ProtoMessage() {}

func (x *ServiceDetailOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDetailOverview.ProtoReflect.Descriptor instead.
func (*ServiceDetailOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceDetailOverview) GetPet() *v15.BusinessCustomerPetModelOverview {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *ServiceDetailOverview) GetServices() []*ServiceCompositeOverview {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *ServiceDetailOverview) GetAddOns() []*AddOnCompositeOverview {
	if x != nil {
		return x.AddOns
	}
	return nil
}

func (x *ServiceDetailOverview) GetEvaluations() []*EvaluationServiceOverview {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

func (x *ServiceDetailOverview) GetCodes() []*v15.BusinessPetCodeModel {
	if x != nil {
		return x.Codes
	}
	return nil
}

func (x *ServiceDetailOverview) GetNotes() []*v15.BusinessPetNoteModel {
	if x != nil {
		return x.Notes
	}
	return nil
}

func (x *ServiceDetailOverview) GetVaccines() []*VaccineComposite {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

func (x *ServiceDetailOverview) GetBindings() []*ServiceDetailOverview_Binding {
	if x != nil {
		return x.Bindings
	}
	return nil
}

func (x *ServiceDetailOverview) GetIncidentReports() []*v15.BusinessPetIncidentReportModel {
	if x != nil {
		return x.IncidentReports
	}
	return nil
}

func (x *ServiceDetailOverview) GetPetEvaluations() []*v15.PetEvaluationModel {
	if x != nil {
		return x.PetEvaluations
	}
	return nil
}

// pet vaccine
type VaccineComposite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine binding id
	VaccineBindingId int64 `protobuf:"varint,1,opt,name=vaccine_binding_id,json=vaccineBindingId,proto3" json:"vaccine_binding_id,omitempty"`
	// vaccine id
	VaccineId int64 `protobuf:"varint,3,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// expiration date, may not exist
	ExpirationDate *date.Date `protobuf:"bytes,4,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// vaccine document urls
	DocumentUrls []string `protobuf:"bytes,5,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
	// vaccine name
	VaccineName string `protobuf:"bytes,6,opt,name=vaccine_name,json=vaccineName,proto3" json:"vaccine_name,omitempty"`
}

func (x *VaccineComposite) Reset() {
	*x = VaccineComposite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VaccineComposite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VaccineComposite) ProtoMessage() {}

func (x *VaccineComposite) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VaccineComposite.ProtoReflect.Descriptor instead.
func (*VaccineComposite) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{4}
}

func (x *VaccineComposite) GetVaccineBindingId() int64 {
	if x != nil {
		return x.VaccineBindingId
	}
	return 0
}

func (x *VaccineComposite) GetVaccineId() int64 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *VaccineComposite) GetExpirationDate() *date.Date {
	if x != nil {
		return x.ExpirationDate
	}
	return nil
}

func (x *VaccineComposite) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

func (x *VaccineComposite) GetVaccineName() string {
	if x != nil {
		return x.VaccineName
	}
	return ""
}

// pet detail service composite
type ServiceCompositeOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// enable operation
	EnableOperation bool `protobuf:"varint,21,opt,name=enable_operation,json=enableOperation,proto3" json:"enable_operation,omitempty"`
	// work mode, 0-parallel, 1-sequence
	WorkMode v11.WorkMode `protobuf:"varint,22,opt,name=work_mode,json=workMode,proto3,enum=moego.models.appointment.v1.WorkMode" json:"work_mode,omitempty"`
	// service color code
	ServiceColorCode string `protobuf:"bytes,23,opt,name=service_color_code,json=serviceColorCode,proto3" json:"service_color_code,omitempty"`
	// service start date, in yyyy-MM-dd format, for boarding or daycare service
	StartDate string `protobuf:"bytes,24,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format, for boarding or daycare service
	EndDate string `protobuf:"bytes,25,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// service item type, different from service type, it includes grooming, boarding, daycare or other services.
	ServiceItemType v1.ServiceItemType `protobuf:"varint,26,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// lodging id, only for boarding service item type
	LodgingId int64 `protobuf:"varint,27,opt,name=lodging_id,json=lodgingId,proto3" json:"lodging_id,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,28,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// lodging unit name
	LodgingUnitName string `protobuf:"bytes,29,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
	// lodging type name
	LodgingTypeName string `protobuf:"bytes,30,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
	// staff name
	StaffName string `protobuf:"bytes,31,opt,name=staff_name,json=staffName,proto3" json:"staff_name,omitempty"`
	// max duration (only for daycare service)
	MaxDuration int32 `protobuf:"varint,32,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// price unit
	PriceUnit v1.ServicePriceUnit `protobuf:"varint,33,opt,name=price_unit,json=priceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"price_unit,omitempty"`
	// split lodging unit name
	LodgingInfos []*ServiceCompositeOverview_LodgingInfo `protobuf:"bytes,34,rep,name=lodging_infos,json=lodgingInfos,proto3" json:"lodging_infos,omitempty"`
}

func (x *ServiceCompositeOverview) Reset() {
	*x = ServiceCompositeOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCompositeOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCompositeOverview) ProtoMessage() {}

func (x *ServiceCompositeOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCompositeOverview.ProtoReflect.Descriptor instead.
func (*ServiceCompositeOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{5}
}

func (x *ServiceCompositeOverview) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceCompositeOverview) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ServiceCompositeOverview) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ServiceCompositeOverview) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *ServiceCompositeOverview) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ServiceCompositeOverview) GetEnableOperation() bool {
	if x != nil {
		return x.EnableOperation
	}
	return false
}

func (x *ServiceCompositeOverview) GetWorkMode() v11.WorkMode {
	if x != nil {
		return x.WorkMode
	}
	return v11.WorkMode(0)
}

func (x *ServiceCompositeOverview) GetServiceColorCode() string {
	if x != nil {
		return x.ServiceColorCode
	}
	return ""
}

func (x *ServiceCompositeOverview) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *ServiceCompositeOverview) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *ServiceCompositeOverview) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *ServiceCompositeOverview) GetLodgingId() int64 {
	if x != nil {
		return x.LodgingId
	}
	return 0
}

func (x *ServiceCompositeOverview) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServiceCompositeOverview) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

func (x *ServiceCompositeOverview) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

func (x *ServiceCompositeOverview) GetStaffName() string {
	if x != nil {
		return x.StaffName
	}
	return ""
}

func (x *ServiceCompositeOverview) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

func (x *ServiceCompositeOverview) GetPriceUnit() v1.ServicePriceUnit {
	if x != nil {
		return x.PriceUnit
	}
	return v1.ServicePriceUnit(0)
}

func (x *ServiceCompositeOverview) GetLodgingInfos() []*ServiceCompositeOverview_LodgingInfo {
	if x != nil {
		return x.LodgingInfos
	}
	return nil
}

// pet detail add on composite
type AddOnCompositeOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,5,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// enable operation
	EnableOperation bool `protobuf:"varint,21,opt,name=enable_operation,json=enableOperation,proto3" json:"enable_operation,omitempty"`
	// work mode, 0-parallel, 1-sequence
	WorkMode v11.WorkMode `protobuf:"varint,22,opt,name=work_mode,json=workMode,proto3,enum=moego.models.appointment.v1.WorkMode" json:"work_mode,omitempty"`
	// service color code
	ServiceColorCode string `protobuf:"bytes,23,opt,name=service_color_code,json=serviceColorCode,proto3" json:"service_color_code,omitempty"`
	// service start date, in yyyy-MM-dd format, for boarding or daycare service
	StartDate string `protobuf:"bytes,24,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format, for boarding or daycare service
	EndDate string `protobuf:"bytes,25,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// service item type, different from service type, it includes grooming, boarding, daycare or other services.
	ServiceItemType v1.ServiceItemType `protobuf:"varint,26,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,27,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// staff name
	StaffName string `protobuf:"bytes,28,opt,name=staff_name,json=staffName,proto3" json:"staff_name,omitempty"`
	// date type
	DateType v11.PetDetailDateType `protobuf:"varint,29,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType" json:"date_type,omitempty"`
	// specific dates
	SpecificDates []string `protobuf:"bytes,30,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
}

func (x *AddOnCompositeOverview) Reset() {
	*x = AddOnCompositeOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddOnCompositeOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOnCompositeOverview) ProtoMessage() {}

func (x *AddOnCompositeOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOnCompositeOverview.ProtoReflect.Descriptor instead.
func (*AddOnCompositeOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{6}
}

func (x *AddOnCompositeOverview) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddOnCompositeOverview) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *AddOnCompositeOverview) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *AddOnCompositeOverview) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *AddOnCompositeOverview) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *AddOnCompositeOverview) GetEnableOperation() bool {
	if x != nil {
		return x.EnableOperation
	}
	return false
}

func (x *AddOnCompositeOverview) GetWorkMode() v11.WorkMode {
	if x != nil {
		return x.WorkMode
	}
	return v11.WorkMode(0)
}

func (x *AddOnCompositeOverview) GetServiceColorCode() string {
	if x != nil {
		return x.ServiceColorCode
	}
	return ""
}

func (x *AddOnCompositeOverview) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *AddOnCompositeOverview) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *AddOnCompositeOverview) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *AddOnCompositeOverview) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *AddOnCompositeOverview) GetStaffName() string {
	if x != nil {
		return x.StaffName
	}
	return ""
}

func (x *AddOnCompositeOverview) GetDateType() v11.PetDetailDateType {
	if x != nil {
		return x.DateType
	}
	return v11.PetDetailDateType(0)
}

func (x *AddOnCompositeOverview) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

// pet evaluation service composite
type EvaluationServiceOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet evaluation detail id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,4,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// service start date, in yyyy-MM-dd format, for boarding or daycare service
	StartDate string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// service end date, in yyyy-MM-dd format, for boarding or daycare service
	EndDate string `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// service item type, always be evaluation.
	ServiceItemType v1.ServiceItemType `protobuf:"varint,7,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// service name
	ServiceName string `protobuf:"bytes,8,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,9,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// staff name
	StaffName *string `protobuf:"bytes,10,opt,name=staff_name,json=staffName,proto3,oneof" json:"staff_name,omitempty"`
	// lodging id
	LodgingId *int64 `protobuf:"varint,11,opt,name=lodging_id,json=lodgingId,proto3,oneof" json:"lodging_id,omitempty"`
	// lodging unit name
	LodgingUnitName *string `protobuf:"bytes,12,opt,name=lodging_unit_name,json=lodgingUnitName,proto3,oneof" json:"lodging_unit_name,omitempty"`
	// lodging type name
	LodgingTypeName *string `protobuf:"bytes,13,opt,name=lodging_type_name,json=lodgingTypeName,proto3,oneof" json:"lodging_type_name,omitempty"`
	// start time, in minutes
	StartTime int32 `protobuf:"varint,14,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time, in minutes
	EndTime int32 `protobuf:"varint,15,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *EvaluationServiceOverview) Reset() {
	*x = EvaluationServiceOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationServiceOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationServiceOverview) ProtoMessage() {}

func (x *EvaluationServiceOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationServiceOverview.ProtoReflect.Descriptor instead.
func (*EvaluationServiceOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{7}
}

func (x *EvaluationServiceOverview) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EvaluationServiceOverview) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *EvaluationServiceOverview) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *EvaluationServiceOverview) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *EvaluationServiceOverview) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *EvaluationServiceOverview) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *EvaluationServiceOverview) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *EvaluationServiceOverview) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *EvaluationServiceOverview) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *EvaluationServiceOverview) GetStaffName() string {
	if x != nil && x.StaffName != nil {
		return *x.StaffName
	}
	return ""
}

func (x *EvaluationServiceOverview) GetLodgingId() int64 {
	if x != nil && x.LodgingId != nil {
		return *x.LodgingId
	}
	return 0
}

func (x *EvaluationServiceOverview) GetLodgingUnitName() string {
	if x != nil && x.LodgingUnitName != nil {
		return *x.LodgingUnitName
	}
	return ""
}

func (x *EvaluationServiceOverview) GetLodgingTypeName() string {
	if x != nil && x.LodgingTypeName != nil {
		return *x.LodgingTypeName
	}
	return ""
}

func (x *EvaluationServiceOverview) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *EvaluationServiceOverview) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

// customer composite
type CustomerCompositeOverview struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer
	CustomerProfile *v15.BusinessCustomerCalendarView `protobuf:"bytes,1,opt,name=customer_profile,json=customerProfile,proto3" json:"customer_profile,omitempty"`
	// is new customer
	IsNewCustomer bool `protobuf:"varint,2,opt,name=is_new_customer,json=isNewCustomer,proto3" json:"is_new_customer,omitempty"`
	// required sign
	RequiredSign bool `protobuf:"varint,3,opt,name=required_sign,json=requiredSign,proto3" json:"required_sign,omitempty"`
	// review booster sent
	ReviewBoosterSent bool `protobuf:"varint,4,opt,name=review_booster_sent,json=reviewBoosterSent,proto3" json:"review_booster_sent,omitempty"`
	// customer package info
	CustomerPackages []*CustomerPackageView `protobuf:"bytes,5,rep,name=customer_packages,json=customerPackages,proto3" json:"customer_packages,omitempty"`
	// unpaid amount
	UnpaidAmount *money.Money `protobuf:"bytes,6,opt,name=unpaid_amount,json=unpaidAmount,proto3" json:"unpaid_amount,omitempty"`
	// cof status
	CofStatus CustomerCompositeOverview_COFStatus `protobuf:"varint,7,opt,name=cof_status,json=cofStatus,proto3,enum=moego.api.appointment.v1.CustomerCompositeOverview_COFStatus" json:"cof_status,omitempty"`
}

func (x *CustomerCompositeOverview) Reset() {
	*x = CustomerCompositeOverview{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerCompositeOverview) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerCompositeOverview) ProtoMessage() {}

func (x *CustomerCompositeOverview) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerCompositeOverview.ProtoReflect.Descriptor instead.
func (*CustomerCompositeOverview) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{8}
}

func (x *CustomerCompositeOverview) GetCustomerProfile() *v15.BusinessCustomerCalendarView {
	if x != nil {
		return x.CustomerProfile
	}
	return nil
}

func (x *CustomerCompositeOverview) GetIsNewCustomer() bool {
	if x != nil {
		return x.IsNewCustomer
	}
	return false
}

func (x *CustomerCompositeOverview) GetRequiredSign() bool {
	if x != nil {
		return x.RequiredSign
	}
	return false
}

func (x *CustomerCompositeOverview) GetReviewBoosterSent() bool {
	if x != nil {
		return x.ReviewBoosterSent
	}
	return false
}

func (x *CustomerCompositeOverview) GetCustomerPackages() []*CustomerPackageView {
	if x != nil {
		return x.CustomerPackages
	}
	return nil
}

func (x *CustomerCompositeOverview) GetUnpaidAmount() *money.Money {
	if x != nil {
		return x.UnpaidAmount
	}
	return nil
}

func (x *CustomerCompositeOverview) GetCofStatus() CustomerCompositeOverview_COFStatus {
	if x != nil {
		return x.CofStatus
	}
	return CustomerCompositeOverview_COF_STATUS_UNSPECIFIED
}

// overview status entry
type OverviewEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status
	Status v11.OverviewStatus `protobuf:"varint,1,opt,name=status,proto3,enum=moego.models.appointment.v1.OverviewStatus" json:"status,omitempty"`
	// count
	Count int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	// overview items
	Items []*OverviewItem `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	// appointment count by service item type
	AppointmentCountByServiceItemTypes []*AppointmentCountByServiceItemType `protobuf:"bytes,4,rep,name=appointment_count_by_service_item_types,json=appointmentCountByServiceItemTypes,proto3" json:"appointment_count_by_service_item_types,omitempty"`
	// pet count by service item type
	PetCountByServiceItemTypes []*PetCountByServiceItemType `protobuf:"bytes,5,rep,name=pet_count_by_service_item_types,json=petCountByServiceItemTypes,proto3" json:"pet_count_by_service_item_types,omitempty"`
}

func (x *OverviewEntry) Reset() {
	*x = OverviewEntry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OverviewEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverviewEntry) ProtoMessage() {}

func (x *OverviewEntry) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverviewEntry.ProtoReflect.Descriptor instead.
func (*OverviewEntry) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{9}
}

func (x *OverviewEntry) GetStatus() v11.OverviewStatus {
	if x != nil {
		return x.Status
	}
	return v11.OverviewStatus(0)
}

func (x *OverviewEntry) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *OverviewEntry) GetItems() []*OverviewItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *OverviewEntry) GetAppointmentCountByServiceItemTypes() []*AppointmentCountByServiceItemType {
	if x != nil {
		return x.AppointmentCountByServiceItemTypes
	}
	return nil
}

func (x *OverviewEntry) GetPetCountByServiceItemTypes() []*PetCountByServiceItemType {
	if x != nil {
		return x.PetCountByServiceItemTypes
	}
	return nil
}

// appointment count by service item type
type AppointmentCountByServiceItemType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// appt count
	Count int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *AppointmentCountByServiceItemType) Reset() {
	*x = AppointmentCountByServiceItemType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentCountByServiceItemType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentCountByServiceItemType) ProtoMessage() {}

func (x *AppointmentCountByServiceItemType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentCountByServiceItemType.ProtoReflect.Descriptor instead.
func (*AppointmentCountByServiceItemType) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{10}
}

func (x *AppointmentCountByServiceItemType) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *AppointmentCountByServiceItemType) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// pet count by service item type
type PetCountByServiceItemType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,1,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// pet count
	Count int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *PetCountByServiceItemType) Reset() {
	*x = PetCountByServiceItemType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetCountByServiceItemType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetCountByServiceItemType) ProtoMessage() {}

func (x *PetCountByServiceItemType) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetCountByServiceItemType.ProtoReflect.Descriptor instead.
func (*PetCountByServiceItemType) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{11}
}

func (x *PetCountByServiceItemType) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *PetCountByServiceItemType) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// get overview count result
type GetOverviewListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// entries
	Entries []*OverviewEntry `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (x *GetOverviewListResult) Reset() {
	*x = GetOverviewListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOverviewListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOverviewListResult) ProtoMessage() {}

func (x *GetOverviewListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOverviewListResult.ProtoReflect.Descriptor instead.
func (*GetOverviewListResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{12}
}

func (x *GetOverviewListResult) GetEntries() []*OverviewEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

// get overview list page params
type ListOverviewAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// date type
	DateType v11.OverviewDateType `protobuf:"varint,3,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.OverviewDateType" json:"date_type,omitempty"`
	// overview status
	OverviewStatus v11.OverviewStatus `protobuf:"varint,4,opt,name=overview_status,json=overviewStatus,proto3,enum=moego.models.appointment.v1.OverviewStatus" json:"overview_status,omitempty"`
	// filter
	Filter *ListOverviewAppointmentParams_Filter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,9,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// sort
	OrderBys []*v2.OrderBy `protobuf:"bytes,10,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
}

func (x *ListOverviewAppointmentParams) Reset() {
	*x = ListOverviewAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOverviewAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOverviewAppointmentParams) ProtoMessage() {}

func (x *ListOverviewAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOverviewAppointmentParams.ProtoReflect.Descriptor instead.
func (*ListOverviewAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{13}
}

func (x *ListOverviewAppointmentParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListOverviewAppointmentParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ListOverviewAppointmentParams) GetDateType() v11.OverviewDateType {
	if x != nil {
		return x.DateType
	}
	return v11.OverviewDateType(0)
}

func (x *ListOverviewAppointmentParams) GetOverviewStatus() v11.OverviewStatus {
	if x != nil {
		return x.OverviewStatus
	}
	return v11.OverviewStatus(0)
}

func (x *ListOverviewAppointmentParams) GetFilter() *ListOverviewAppointmentParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListOverviewAppointmentParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListOverviewAppointmentParams) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

// get overview list page result
type ListOverviewAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// overview items
	Items []*OverviewItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	// pagination
	// deprecated by Freeman since 2025/4/14, 这个字段返回的 count 是 pet count，实际应该返回 appointment count，使用 pagination_v2 替换
	//
	// Deprecated: Do not use.
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// pagination v2
	PaginationV2 *v2.PaginationResponse `protobuf:"bytes,6,opt,name=pagination_v2,json=paginationV2,proto3" json:"pagination_v2,omitempty"`
	// appointment count by service item type
	AppointmentCountByServiceItemTypes []*AppointmentCountByServiceItemType `protobuf:"bytes,3,rep,name=appointment_count_by_service_item_types,json=appointmentCountByServiceItemTypes,proto3" json:"appointment_count_by_service_item_types,omitempty"`
	// pet count by service item type
	PetCountByServiceItemTypes []*PetCountByServiceItemType `protobuf:"bytes,4,rep,name=pet_count_by_service_item_types,json=petCountByServiceItemTypes,proto3" json:"pet_count_by_service_item_types,omitempty"`
	// total pet count, fixes https://moego.atlassian.net/browse/MER-4159
	PetCount int32 `protobuf:"varint,5,opt,name=pet_count,json=petCount,proto3" json:"pet_count,omitempty"`
}

func (x *ListOverviewAppointmentResult) Reset() {
	*x = ListOverviewAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOverviewAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOverviewAppointmentResult) ProtoMessage() {}

func (x *ListOverviewAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOverviewAppointmentResult.ProtoReflect.Descriptor instead.
func (*ListOverviewAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{14}
}

func (x *ListOverviewAppointmentResult) GetItems() []*OverviewItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// Deprecated: Do not use.
func (x *ListOverviewAppointmentResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListOverviewAppointmentResult) GetPaginationV2() *v2.PaginationResponse {
	if x != nil {
		return x.PaginationV2
	}
	return nil
}

func (x *ListOverviewAppointmentResult) GetAppointmentCountByServiceItemTypes() []*AppointmentCountByServiceItemType {
	if x != nil {
		return x.AppointmentCountByServiceItemTypes
	}
	return nil
}

func (x *ListOverviewAppointmentResult) GetPetCountByServiceItemTypes() []*PetCountByServiceItemType {
	if x != nil {
		return x.PetCountByServiceItemTypes
	}
	return nil
}

func (x *ListOverviewAppointmentResult) GetPetCount() int32 {
	if x != nil {
		return x.PetCount
	}
	return 0
}

// count overview appointment params
type CountOverviewAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// date type
	DateType v11.OverviewDateType `protobuf:"varint,3,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.OverviewDateType" json:"date_type,omitempty"`
	// overview status, deprecated, please use overview_statuses instead
	//
	// Deprecated: Do not use.
	OverviewStatus *v11.OverviewStatus `protobuf:"varint,4,opt,name=overview_status,json=overviewStatus,proto3,enum=moego.models.appointment.v1.OverviewStatus,oneof" json:"overview_status,omitempty"`
	// filter
	Filter *CountOverviewAppointmentParams_Filter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// overview statuses, if not set, we will calculate the status with date&time
	OverviewStatuses []v11.OverviewStatus `protobuf:"varint,6,rep,packed,name=overview_statuses,json=overviewStatuses,proto3,enum=moego.models.appointment.v1.OverviewStatus" json:"overview_statuses,omitempty"`
}

func (x *CountOverviewAppointmentParams) Reset() {
	*x = CountOverviewAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountOverviewAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountOverviewAppointmentParams) ProtoMessage() {}

func (x *CountOverviewAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountOverviewAppointmentParams.ProtoReflect.Descriptor instead.
func (*CountOverviewAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{15}
}

func (x *CountOverviewAppointmentParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CountOverviewAppointmentParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *CountOverviewAppointmentParams) GetDateType() v11.OverviewDateType {
	if x != nil {
		return x.DateType
	}
	return v11.OverviewDateType(0)
}

// Deprecated: Do not use.
func (x *CountOverviewAppointmentParams) GetOverviewStatus() v11.OverviewStatus {
	if x != nil && x.OverviewStatus != nil {
		return *x.OverviewStatus
	}
	return v11.OverviewStatus(0)
}

func (x *CountOverviewAppointmentParams) GetFilter() *CountOverviewAppointmentParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *CountOverviewAppointmentParams) GetOverviewStatuses() []v11.OverviewStatus {
	if x != nil {
		return x.OverviewStatuses
	}
	return nil
}

// count overview appointment result
type CountOverviewAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// count
	//
	// Deprecated: Do not use.
	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	// count by overview status
	CountWithOverviewStatus []*CountOverviewAppointmentResult_CountWithOverviewStatus `protobuf:"bytes,2,rep,name=count_with_overview_status,json=countWithOverviewStatus,proto3" json:"count_with_overview_status,omitempty"`
}

func (x *CountOverviewAppointmentResult) Reset() {
	*x = CountOverviewAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountOverviewAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountOverviewAppointmentResult) ProtoMessage() {}

func (x *CountOverviewAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountOverviewAppointmentResult.ProtoReflect.Descriptor instead.
func (*CountOverviewAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{16}
}

// Deprecated: Do not use.
func (x *CountOverviewAppointmentResult) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *CountOverviewAppointmentResult) GetCountWithOverviewStatus() []*CountOverviewAppointmentResult_CountWithOverviewStatus {
	if x != nil {
		return x.CountWithOverviewStatus
	}
	return nil
}

// filter
type GetOverviewListParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// report card status, empty for not filtering
	ReportStatus *v11.OverviewReportStatus `protobuf:"varint,1,opt,name=report_status,json=reportStatus,proto3,enum=moego.models.appointment.v1.OverviewReportStatus,oneof" json:"report_status,omitempty"`
	// appointment status, empty for not filtering
	AppointmentStatuses []v11.AppointmentStatus `protobuf:"varint,2,rep,packed,name=appointment_statuses,json=appointmentStatuses,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"appointment_statuses,omitempty"`
}

func (x *GetOverviewListParams_Filter) Reset() {
	*x = GetOverviewListParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOverviewListParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOverviewListParams_Filter) ProtoMessage() {}

func (x *GetOverviewListParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOverviewListParams_Filter.ProtoReflect.Descriptor instead.
func (*GetOverviewListParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetOverviewListParams_Filter) GetReportStatus() v11.OverviewReportStatus {
	if x != nil && x.ReportStatus != nil {
		return *x.ReportStatus
	}
	return v11.OverviewReportStatus(0)
}

func (x *GetOverviewListParams_Filter) GetAppointmentStatuses() []v11.AppointmentStatus {
	if x != nil {
		return x.AppointmentStatuses
	}
	return nil
}

// pet codes bindings
type ServiceDetailOverview_Binding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// code id
	CodeId int64 `protobuf:"varint,2,opt,name=code_id,json=codeId,proto3" json:"code_id,omitempty"`
	// comment
	Comment string `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
	// binding time
	BindingTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=binding_time,json=bindingTime,proto3" json:"binding_time,omitempty"`
}

func (x *ServiceDetailOverview_Binding) Reset() {
	*x = ServiceDetailOverview_Binding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceDetailOverview_Binding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceDetailOverview_Binding) ProtoMessage() {}

func (x *ServiceDetailOverview_Binding) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceDetailOverview_Binding.ProtoReflect.Descriptor instead.
func (*ServiceDetailOverview_Binding) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ServiceDetailOverview_Binding) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ServiceDetailOverview_Binding) GetCodeId() int64 {
	if x != nil {
		return x.CodeId
	}
	return 0
}

func (x *ServiceDetailOverview_Binding) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *ServiceDetailOverview_Binding) GetBindingTime() *timestamppb.Timestamp {
	if x != nil {
		return x.BindingTime
	}
	return nil
}

// lodging info
type ServiceCompositeOverview_LodgingInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging unit name
	LodgingUnitName string `protobuf:"bytes,1,opt,name=lodging_unit_name,json=lodgingUnitName,proto3" json:"lodging_unit_name,omitempty"`
	// lodging type name
	LodgingTypeName string `protobuf:"bytes,2,opt,name=lodging_type_name,json=lodgingTypeName,proto3" json:"lodging_type_name,omitempty"`
}

func (x *ServiceCompositeOverview_LodgingInfo) Reset() {
	*x = ServiceCompositeOverview_LodgingInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceCompositeOverview_LodgingInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceCompositeOverview_LodgingInfo) ProtoMessage() {}

func (x *ServiceCompositeOverview_LodgingInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceCompositeOverview_LodgingInfo.ProtoReflect.Descriptor instead.
func (*ServiceCompositeOverview_LodgingInfo) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ServiceCompositeOverview_LodgingInfo) GetLodgingUnitName() string {
	if x != nil {
		return x.LodgingUnitName
	}
	return ""
}

func (x *ServiceCompositeOverview_LodgingInfo) GetLodgingTypeName() string {
	if x != nil {
		return x.LodgingTypeName
	}
	return ""
}

// filter
type ListOverviewAppointmentParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type, empty for not filtering
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,1,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// appointment status, empty for not filtering
	AppointmentStatuses []v11.AppointmentStatus `protobuf:"varint,2,rep,packed,name=appointment_statuses,json=appointmentStatuses,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"appointment_statuses,omitempty"`
	// key word
	Keyword *string `protobuf:"bytes,3,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// report card status, empty for not filtering
	ReportStatus *v11.OverviewReportStatus `protobuf:"varint,4,opt,name=report_status,json=reportStatus,proto3,enum=moego.models.appointment.v1.OverviewReportStatus,oneof" json:"report_status,omitempty"`
}

func (x *ListOverviewAppointmentParams_Filter) Reset() {
	*x = ListOverviewAppointmentParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOverviewAppointmentParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOverviewAppointmentParams_Filter) ProtoMessage() {}

func (x *ListOverviewAppointmentParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOverviewAppointmentParams_Filter.ProtoReflect.Descriptor instead.
func (*ListOverviewAppointmentParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ListOverviewAppointmentParams_Filter) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ListOverviewAppointmentParams_Filter) GetAppointmentStatuses() []v11.AppointmentStatus {
	if x != nil {
		return x.AppointmentStatuses
	}
	return nil
}

func (x *ListOverviewAppointmentParams_Filter) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *ListOverviewAppointmentParams_Filter) GetReportStatus() v11.OverviewReportStatus {
	if x != nil && x.ReportStatus != nil {
		return *x.ReportStatus
	}
	return v11.OverviewReportStatus(0)
}

// filter
type CountOverviewAppointmentParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service item type, empty for not filtering
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,1,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// key word
	Keyword *string `protobuf:"bytes,2,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
}

func (x *CountOverviewAppointmentParams_Filter) Reset() {
	*x = CountOverviewAppointmentParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountOverviewAppointmentParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountOverviewAppointmentParams_Filter) ProtoMessage() {}

func (x *CountOverviewAppointmentParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountOverviewAppointmentParams_Filter.ProtoReflect.Descriptor instead.
func (*CountOverviewAppointmentParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{15, 0}
}

func (x *CountOverviewAppointmentParams_Filter) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *CountOverviewAppointmentParams_Filter) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

// count by overview status
type CountOverviewAppointmentResult_CountWithOverviewStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// overview status
	OverviewStatus v11.OverviewStatus `protobuf:"varint,1,opt,name=overview_status,json=overviewStatus,proto3,enum=moego.models.appointment.v1.OverviewStatus" json:"overview_status,omitempty"`
	// count
	Count int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *CountOverviewAppointmentResult_CountWithOverviewStatus) Reset() {
	*x = CountOverviewAppointmentResult_CountWithOverviewStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountOverviewAppointmentResult_CountWithOverviewStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountOverviewAppointmentResult_CountWithOverviewStatus) ProtoMessage() {}

func (x *CountOverviewAppointmentResult_CountWithOverviewStatus) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_overview_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountOverviewAppointmentResult_CountWithOverviewStatus.ProtoReflect.Descriptor instead.
func (*CountOverviewAppointmentResult_CountWithOverviewStatus) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP(), []int{16, 0}
}

func (x *CountOverviewAppointmentResult_CountWithOverviewStatus) GetOverviewStatus() v11.OverviewStatus {
	if x != nil {
		return x.OverviewStatus
	}
	return v11.OverviewStatus(0)
}

func (x *CountOverviewAppointmentResult_CountWithOverviewStatus) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

var File_moego_api_appointment_v1_overview_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_overview_api_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e,
	0x6f, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61,
	0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x46,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72,
	0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xf6, 0x06, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52,
	0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x68, 0x0a, 0x12, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x44,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88,
	0x01, 0x01, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x65, 0x0a, 0x0f, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x02, 0x52, 0x0e, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x1a,
	0xfb, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x67, 0x0a, 0x0d, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x76, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0x06, 0x18, 0x01, 0x22, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xfd, 0x07, 0x0a, 0x0c, 0x4f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x52, 0x0a, 0x0b, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x56, 0x0a,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x47, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f,
	0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x4f,
	0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x4f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12,
	0x4e, 0x0a, 0x09, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x57, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x5f,
	0x61, 0x75, 0x74, 0x68, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74,
	0x68, 0x12, 0x4c, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x73, 0x12,
	0x44, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x69, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x58, 0x0a, 0x0f, 0x6e, 0x6f, 0x5f, 0x73, 0x68, 0x6f, 0x77,
	0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x0d, 0x6e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12,
	0x4f, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73,
	0x12, 0x76, 0x0a, 0x18, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x17, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x70, 0x0a, 0x0c, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x49, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xfe, 0x07, 0x0a, 0x15, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x55, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x4e, 0x0a, 0x08, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x06,
	0x61, 0x64, 0x64, 0x4f, 0x6e, 0x73, 0x12, 0x55, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4d, 0x0a,
	0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x4d, 0x0a, 0x05,
	0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x08, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x52, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x08, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x08,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x6c, 0x0a, 0x10, 0x69, 0x6e, 0x63, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50,
	0x65, 0x74, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x5e, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x92, 0x01, 0x0a, 0x07, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x0c,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xfc, 0x01, 0x0a, 0x10,
	0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65,
	0x12, 0x2c, 0x0a, 0x12, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x3f, 0x0a,
	0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23,
	0x0a, 0x0d, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55,
	0x72, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x61, 0x63, 0x63, 0x69,
	0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xc3, 0x07, 0x0a, 0x18, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x29, 0x0a, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x09, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61,
	0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x55, 0x6e, 0x69, 0x74, 0x12, 0x63, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x22, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x65, 0x0a, 0x0b, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x84, 0x05, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42,
	0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x73, 0x18, 0x1e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66,
	0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x22, 0x97, 0x05, 0x0a, 0x19, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x55, 0x0a, 0x11,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02,
	0x52, 0x09, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2f,
	0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0f, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x55, 0x6e, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2f, 0x0a, 0x11, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x0f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0xde, 0x04, 0x0a, 0x19, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12,
	0x6a, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69,
	0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x73, 0x69, 0x67, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x62, 0x6f, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x6f, 0x6f,
	0x73, 0x74, 0x65, 0x72, 0x53, 0x65, 0x6e, 0x74, 0x12, 0x5a, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0c, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5c, 0x0a,
	0x0a, 0x63, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x4f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x4f, 0x46, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x09, 0x63, 0x6f, 0x66, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x65, 0x0a, 0x09, 0x43,
	0x4f, 0x46, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x4f, 0x46, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x02, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x13, 0x0a,
	0x0f, 0x4e, 0x4f, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x10, 0x04, 0x22, 0xb5, 0x03, 0x0a, 0x0d, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3c, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x90, 0x01,
	0x0a, 0x27, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x22, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x78, 0x0a, 0x1f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x79,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1a,
	0x70, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x21, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x88, 0x01,
	0x0a, 0x19, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x5a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x41, 0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x22, 0xb1, 0x07, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x56, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x44, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x60, 0x0a, 0x0f, 0x6f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0e, 0x6f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x09, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x73, 0x1a, 0x99, 0x03, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x68, 0x0a, 0x12,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x76, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0x06, 0x18, 0x01,
	0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x26,
	0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x67, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0c,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x42,
	0x0a, 0x0a, 0x08, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x98, 0x04, 0x0a, 0x1d, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x47, 0x0a, 0x0d, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76,
	0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0c, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x12, 0x90, 0x01, 0x0a, 0x27, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x62, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x22, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x78, 0x0a, 0x1f,
	0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x79, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1a, 0x70, 0x65, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x65, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xcf, 0x05, 0x0a, 0x1e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x56, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x44, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x67, 0x0a, 0x0f, 0x6f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x0c, 0x18, 0x01, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x0e, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x5c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x6b, 0x0a, 0x11, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b,
	0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x6f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x1a, 0xa6, 0x01,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x68, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x26, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x00, 0x52, 0x07,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xd2, 0x02, 0x0a, 0x1e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x8d, 0x01, 0x0a, 0x1a, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x77, 0x69, 0x74,
	0x68, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x4f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x17, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x1a, 0x85, 0x01, 0x0a, 0x17, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68,
	0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x54,
	0x0a, 0x0f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x32, 0xa5, 0x03, 0x0a, 0x0f, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x73,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x8e, 0x01, 0x0a, 0x18, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65,
	0x77, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x42, 0x84, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_api_appointment_v1_overview_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_overview_api_proto_rawDescData = file_moego_api_appointment_v1_overview_api_proto_rawDesc
)

func file_moego_api_appointment_v1_overview_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_overview_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_overview_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_overview_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_overview_api_proto_rawDescData
}

var file_moego_api_appointment_v1_overview_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_api_appointment_v1_overview_api_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_moego_api_appointment_v1_overview_api_proto_goTypes = []interface{}{
	(CustomerCompositeOverview_COFStatus)(0),                       // 0: moego.api.appointment.v1.CustomerCompositeOverview.COFStatus
	(*GetOverviewListParams)(nil),                                  // 1: moego.api.appointment.v1.GetOverviewListParams
	(*OverviewItem)(nil),                                           // 2: moego.api.appointment.v1.OverviewItem
	(*ReportStatus)(nil),                                           // 3: moego.api.appointment.v1.ReportStatus
	(*ServiceDetailOverview)(nil),                                  // 4: moego.api.appointment.v1.ServiceDetailOverview
	(*VaccineComposite)(nil),                                       // 5: moego.api.appointment.v1.VaccineComposite
	(*ServiceCompositeOverview)(nil),                               // 6: moego.api.appointment.v1.ServiceCompositeOverview
	(*AddOnCompositeOverview)(nil),                                 // 7: moego.api.appointment.v1.AddOnCompositeOverview
	(*EvaluationServiceOverview)(nil),                              // 8: moego.api.appointment.v1.EvaluationServiceOverview
	(*CustomerCompositeOverview)(nil),                              // 9: moego.api.appointment.v1.CustomerCompositeOverview
	(*OverviewEntry)(nil),                                          // 10: moego.api.appointment.v1.OverviewEntry
	(*AppointmentCountByServiceItemType)(nil),                      // 11: moego.api.appointment.v1.AppointmentCountByServiceItemType
	(*PetCountByServiceItemType)(nil),                              // 12: moego.api.appointment.v1.PetCountByServiceItemType
	(*GetOverviewListResult)(nil),                                  // 13: moego.api.appointment.v1.GetOverviewListResult
	(*ListOverviewAppointmentParams)(nil),                          // 14: moego.api.appointment.v1.ListOverviewAppointmentParams
	(*ListOverviewAppointmentResult)(nil),                          // 15: moego.api.appointment.v1.ListOverviewAppointmentResult
	(*CountOverviewAppointmentParams)(nil),                         // 16: moego.api.appointment.v1.CountOverviewAppointmentParams
	(*CountOverviewAppointmentResult)(nil),                         // 17: moego.api.appointment.v1.CountOverviewAppointmentResult
	(*GetOverviewListParams_Filter)(nil),                           // 18: moego.api.appointment.v1.GetOverviewListParams.Filter
	(*ServiceDetailOverview_Binding)(nil),                          // 19: moego.api.appointment.v1.ServiceDetailOverview.Binding
	(*ServiceCompositeOverview_LodgingInfo)(nil),                   // 20: moego.api.appointment.v1.ServiceCompositeOverview.LodgingInfo
	(*ListOverviewAppointmentParams_Filter)(nil),                   // 21: moego.api.appointment.v1.ListOverviewAppointmentParams.Filter
	(*CountOverviewAppointmentParams_Filter)(nil),                  // 22: moego.api.appointment.v1.CountOverviewAppointmentParams.Filter
	(*CountOverviewAppointmentResult_CountWithOverviewStatus)(nil), // 23: moego.api.appointment.v1.CountOverviewAppointmentResult.CountWithOverviewStatus
	(v1.ServiceItemType)(0),                                        // 24: moego.models.offering.v1.ServiceItemType
	(v11.OverviewDateType)(0),                                      // 25: moego.models.appointment.v1.OverviewDateType
	(*v2.OrderBy)(nil),                                             // 26: moego.utils.v2.OrderBy
	(v11.OverviewStatus)(0),                                        // 27: moego.models.appointment.v1.OverviewStatus
	(*v11.AppointmentOverview)(nil),                                // 28: moego.models.appointment.v1.AppointmentOverview
	(*v11.AppointmentNoteModel)(nil),                               // 29: moego.models.appointment.v1.AppointmentNoteModel
	(*v11.WaitListCalendarView)(nil),                               // 30: moego.models.appointment.v1.WaitListCalendarView
	(*v12.PreAuthCalendarView)(nil),                                // 31: moego.models.payment.v1.PreAuthCalendarView
	(*v11.InvoiceDepositModel)(nil),                                // 32: moego.models.appointment.v1.InvoiceDepositModel
	(*v13.InvoiceCalendarView)(nil),                                // 33: moego.models.order.v1.InvoiceCalendarView
	(*v13.NoShowInvoiceCalendarView)(nil),                          // 34: moego.models.order.v1.NoShowInvoiceCalendarView
	(*v14.MembershipSubscriptionListModel)(nil),                    // 35: moego.models.membership.v1.MembershipSubscriptionListModel
	(v11.OverviewReportStatus)(0),                                  // 36: moego.models.appointment.v1.OverviewReportStatus
	(*v15.BusinessCustomerPetModelOverview)(nil),                   // 37: moego.models.business_customer.v1.BusinessCustomerPetModelOverview
	(*v15.BusinessPetCodeModel)(nil),                               // 38: moego.models.business_customer.v1.BusinessPetCodeModel
	(*v15.BusinessPetNoteModel)(nil),                               // 39: moego.models.business_customer.v1.BusinessPetNoteModel
	(*v15.BusinessPetIncidentReportModel)(nil),                     // 40: moego.models.business_customer.v1.BusinessPetIncidentReportModel
	(*v15.PetEvaluationModel)(nil),                                 // 41: moego.models.business_customer.v1.PetEvaluationModel
	(*date.Date)(nil),                                              // 42: google.type.Date
	(v11.WorkMode)(0),                                              // 43: moego.models.appointment.v1.WorkMode
	(v1.ServicePriceUnit)(0),                                       // 44: moego.models.offering.v1.ServicePriceUnit
	(v11.PetDetailDateType)(0),                                     // 45: moego.models.appointment.v1.PetDetailDateType
	(*v15.BusinessCustomerCalendarView)(nil),                       // 46: moego.models.business_customer.v1.BusinessCustomerCalendarView
	(*CustomerPackageView)(nil),                                    // 47: moego.api.appointment.v1.CustomerPackageView
	(*money.Money)(nil),                                            // 48: google.type.Money
	(*v2.PaginationRequest)(nil),                                   // 49: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                                  // 50: moego.utils.v2.PaginationResponse
	(v11.AppointmentStatus)(0),                                     // 51: moego.models.appointment.v1.AppointmentStatus
	(*timestamppb.Timestamp)(nil),                                  // 52: google.protobuf.Timestamp
}
var file_moego_api_appointment_v1_overview_api_proto_depIdxs = []int32{
	24, // 0: moego.api.appointment.v1.GetOverviewListParams.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	25, // 1: moego.api.appointment.v1.GetOverviewListParams.date_type:type_name -> moego.models.appointment.v1.OverviewDateType
	18, // 2: moego.api.appointment.v1.GetOverviewListParams.filter:type_name -> moego.api.appointment.v1.GetOverviewListParams.Filter
	26, // 3: moego.api.appointment.v1.GetOverviewListParams.order_bys:type_name -> moego.utils.v2.OrderBy
	27, // 4: moego.api.appointment.v1.GetOverviewListParams.selected_status:type_name -> moego.models.appointment.v1.OverviewStatus
	28, // 5: moego.api.appointment.v1.OverviewItem.appointment:type_name -> moego.models.appointment.v1.AppointmentOverview
	4,  // 6: moego.api.appointment.v1.OverviewItem.service_detail:type_name -> moego.api.appointment.v1.ServiceDetailOverview
	29, // 7: moego.api.appointment.v1.OverviewItem.notes:type_name -> moego.models.appointment.v1.AppointmentNoteModel
	9,  // 8: moego.api.appointment.v1.OverviewItem.customer:type_name -> moego.api.appointment.v1.CustomerCompositeOverview
	30, // 9: moego.api.appointment.v1.OverviewItem.wait_list:type_name -> moego.models.appointment.v1.WaitListCalendarView
	24, // 10: moego.api.appointment.v1.OverviewItem.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	31, // 11: moego.api.appointment.v1.OverviewItem.pre_auth:type_name -> moego.models.payment.v1.PreAuthCalendarView
	32, // 12: moego.api.appointment.v1.OverviewItem.deposits:type_name -> moego.models.appointment.v1.InvoiceDepositModel
	33, // 13: moego.api.appointment.v1.OverviewItem.invoice:type_name -> moego.models.order.v1.InvoiceCalendarView
	34, // 14: moego.api.appointment.v1.OverviewItem.no_show_invoice:type_name -> moego.models.order.v1.NoShowInvoiceCalendarView
	3,  // 15: moego.api.appointment.v1.OverviewItem.report_statuses:type_name -> moego.api.appointment.v1.ReportStatus
	35, // 16: moego.api.appointment.v1.OverviewItem.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionListModel
	36, // 17: moego.api.appointment.v1.ReportStatus.status:type_name -> moego.models.appointment.v1.OverviewReportStatus
	37, // 18: moego.api.appointment.v1.ServiceDetailOverview.pet:type_name -> moego.models.business_customer.v1.BusinessCustomerPetModelOverview
	6,  // 19: moego.api.appointment.v1.ServiceDetailOverview.services:type_name -> moego.api.appointment.v1.ServiceCompositeOverview
	7,  // 20: moego.api.appointment.v1.ServiceDetailOverview.add_ons:type_name -> moego.api.appointment.v1.AddOnCompositeOverview
	8,  // 21: moego.api.appointment.v1.ServiceDetailOverview.evaluations:type_name -> moego.api.appointment.v1.EvaluationServiceOverview
	38, // 22: moego.api.appointment.v1.ServiceDetailOverview.codes:type_name -> moego.models.business_customer.v1.BusinessPetCodeModel
	39, // 23: moego.api.appointment.v1.ServiceDetailOverview.notes:type_name -> moego.models.business_customer.v1.BusinessPetNoteModel
	5,  // 24: moego.api.appointment.v1.ServiceDetailOverview.vaccines:type_name -> moego.api.appointment.v1.VaccineComposite
	19, // 25: moego.api.appointment.v1.ServiceDetailOverview.bindings:type_name -> moego.api.appointment.v1.ServiceDetailOverview.Binding
	40, // 26: moego.api.appointment.v1.ServiceDetailOverview.incident_reports:type_name -> moego.models.business_customer.v1.BusinessPetIncidentReportModel
	41, // 27: moego.api.appointment.v1.ServiceDetailOverview.pet_evaluations:type_name -> moego.models.business_customer.v1.PetEvaluationModel
	42, // 28: moego.api.appointment.v1.VaccineComposite.expiration_date:type_name -> google.type.Date
	43, // 29: moego.api.appointment.v1.ServiceCompositeOverview.work_mode:type_name -> moego.models.appointment.v1.WorkMode
	24, // 30: moego.api.appointment.v1.ServiceCompositeOverview.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	44, // 31: moego.api.appointment.v1.ServiceCompositeOverview.price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	20, // 32: moego.api.appointment.v1.ServiceCompositeOverview.lodging_infos:type_name -> moego.api.appointment.v1.ServiceCompositeOverview.LodgingInfo
	43, // 33: moego.api.appointment.v1.AddOnCompositeOverview.work_mode:type_name -> moego.models.appointment.v1.WorkMode
	24, // 34: moego.api.appointment.v1.AddOnCompositeOverview.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	45, // 35: moego.api.appointment.v1.AddOnCompositeOverview.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	24, // 36: moego.api.appointment.v1.EvaluationServiceOverview.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	46, // 37: moego.api.appointment.v1.CustomerCompositeOverview.customer_profile:type_name -> moego.models.business_customer.v1.BusinessCustomerCalendarView
	47, // 38: moego.api.appointment.v1.CustomerCompositeOverview.customer_packages:type_name -> moego.api.appointment.v1.CustomerPackageView
	48, // 39: moego.api.appointment.v1.CustomerCompositeOverview.unpaid_amount:type_name -> google.type.Money
	0,  // 40: moego.api.appointment.v1.CustomerCompositeOverview.cof_status:type_name -> moego.api.appointment.v1.CustomerCompositeOverview.COFStatus
	27, // 41: moego.api.appointment.v1.OverviewEntry.status:type_name -> moego.models.appointment.v1.OverviewStatus
	2,  // 42: moego.api.appointment.v1.OverviewEntry.items:type_name -> moego.api.appointment.v1.OverviewItem
	11, // 43: moego.api.appointment.v1.OverviewEntry.appointment_count_by_service_item_types:type_name -> moego.api.appointment.v1.AppointmentCountByServiceItemType
	12, // 44: moego.api.appointment.v1.OverviewEntry.pet_count_by_service_item_types:type_name -> moego.api.appointment.v1.PetCountByServiceItemType
	24, // 45: moego.api.appointment.v1.AppointmentCountByServiceItemType.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	24, // 46: moego.api.appointment.v1.PetCountByServiceItemType.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	10, // 47: moego.api.appointment.v1.GetOverviewListResult.entries:type_name -> moego.api.appointment.v1.OverviewEntry
	42, // 48: moego.api.appointment.v1.ListOverviewAppointmentParams.date:type_name -> google.type.Date
	25, // 49: moego.api.appointment.v1.ListOverviewAppointmentParams.date_type:type_name -> moego.models.appointment.v1.OverviewDateType
	27, // 50: moego.api.appointment.v1.ListOverviewAppointmentParams.overview_status:type_name -> moego.models.appointment.v1.OverviewStatus
	21, // 51: moego.api.appointment.v1.ListOverviewAppointmentParams.filter:type_name -> moego.api.appointment.v1.ListOverviewAppointmentParams.Filter
	49, // 52: moego.api.appointment.v1.ListOverviewAppointmentParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	26, // 53: moego.api.appointment.v1.ListOverviewAppointmentParams.order_bys:type_name -> moego.utils.v2.OrderBy
	2,  // 54: moego.api.appointment.v1.ListOverviewAppointmentResult.items:type_name -> moego.api.appointment.v1.OverviewItem
	50, // 55: moego.api.appointment.v1.ListOverviewAppointmentResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	50, // 56: moego.api.appointment.v1.ListOverviewAppointmentResult.pagination_v2:type_name -> moego.utils.v2.PaginationResponse
	11, // 57: moego.api.appointment.v1.ListOverviewAppointmentResult.appointment_count_by_service_item_types:type_name -> moego.api.appointment.v1.AppointmentCountByServiceItemType
	12, // 58: moego.api.appointment.v1.ListOverviewAppointmentResult.pet_count_by_service_item_types:type_name -> moego.api.appointment.v1.PetCountByServiceItemType
	42, // 59: moego.api.appointment.v1.CountOverviewAppointmentParams.date:type_name -> google.type.Date
	25, // 60: moego.api.appointment.v1.CountOverviewAppointmentParams.date_type:type_name -> moego.models.appointment.v1.OverviewDateType
	27, // 61: moego.api.appointment.v1.CountOverviewAppointmentParams.overview_status:type_name -> moego.models.appointment.v1.OverviewStatus
	22, // 62: moego.api.appointment.v1.CountOverviewAppointmentParams.filter:type_name -> moego.api.appointment.v1.CountOverviewAppointmentParams.Filter
	27, // 63: moego.api.appointment.v1.CountOverviewAppointmentParams.overview_statuses:type_name -> moego.models.appointment.v1.OverviewStatus
	23, // 64: moego.api.appointment.v1.CountOverviewAppointmentResult.count_with_overview_status:type_name -> moego.api.appointment.v1.CountOverviewAppointmentResult.CountWithOverviewStatus
	36, // 65: moego.api.appointment.v1.GetOverviewListParams.Filter.report_status:type_name -> moego.models.appointment.v1.OverviewReportStatus
	51, // 66: moego.api.appointment.v1.GetOverviewListParams.Filter.appointment_statuses:type_name -> moego.models.appointment.v1.AppointmentStatus
	52, // 67: moego.api.appointment.v1.ServiceDetailOverview.Binding.binding_time:type_name -> google.protobuf.Timestamp
	24, // 68: moego.api.appointment.v1.ListOverviewAppointmentParams.Filter.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	51, // 69: moego.api.appointment.v1.ListOverviewAppointmentParams.Filter.appointment_statuses:type_name -> moego.models.appointment.v1.AppointmentStatus
	36, // 70: moego.api.appointment.v1.ListOverviewAppointmentParams.Filter.report_status:type_name -> moego.models.appointment.v1.OverviewReportStatus
	24, // 71: moego.api.appointment.v1.CountOverviewAppointmentParams.Filter.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	27, // 72: moego.api.appointment.v1.CountOverviewAppointmentResult.CountWithOverviewStatus.overview_status:type_name -> moego.models.appointment.v1.OverviewStatus
	1,  // 73: moego.api.appointment.v1.OverviewService.GetOverviewList:input_type -> moego.api.appointment.v1.GetOverviewListParams
	14, // 74: moego.api.appointment.v1.OverviewService.ListOverviewAppointment:input_type -> moego.api.appointment.v1.ListOverviewAppointmentParams
	16, // 75: moego.api.appointment.v1.OverviewService.CountOverviewAppointment:input_type -> moego.api.appointment.v1.CountOverviewAppointmentParams
	13, // 76: moego.api.appointment.v1.OverviewService.GetOverviewList:output_type -> moego.api.appointment.v1.GetOverviewListResult
	15, // 77: moego.api.appointment.v1.OverviewService.ListOverviewAppointment:output_type -> moego.api.appointment.v1.ListOverviewAppointmentResult
	17, // 78: moego.api.appointment.v1.OverviewService.CountOverviewAppointment:output_type -> moego.api.appointment.v1.CountOverviewAppointmentResult
	76, // [76:79] is the sub-list for method output_type
	73, // [73:76] is the sub-list for method input_type
	73, // [73:73] is the sub-list for extension type_name
	73, // [73:73] is the sub-list for extension extendee
	0,  // [0:73] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_overview_api_proto_init() }
func file_moego_api_appointment_v1_overview_api_proto_init() {
	if File_moego_api_appointment_v1_overview_api_proto != nil {
		return
	}
	file_moego_api_appointment_v1_appointment_view_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOverviewListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverviewItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceDetailOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VaccineComposite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCompositeOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddOnCompositeOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationServiceOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerCompositeOverview); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OverviewEntry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentCountByServiceItemType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetCountByServiceItemType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOverviewListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOverviewAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOverviewAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountOverviewAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountOverviewAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOverviewListParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceDetailOverview_Binding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceCompositeOverview_LodgingInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOverviewAppointmentParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountOverviewAppointmentParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_overview_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountOverviewAppointmentResult_CountWithOverviewStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_appointment_v1_overview_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_overview_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_overview_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_overview_api_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_overview_api_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_overview_api_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_overview_api_proto_msgTypes[20].OneofWrappers = []interface{}{}
	file_moego_api_appointment_v1_overview_api_proto_msgTypes[21].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_overview_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_overview_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_overview_api_proto_depIdxs,
		EnumInfos:         file_moego_api_appointment_v1_overview_api_proto_enumTypes,
		MessageInfos:      file_moego_api_appointment_v1_overview_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_overview_api_proto = out.File
	file_moego_api_appointment_v1_overview_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_overview_api_proto_goTypes = nil
	file_moego_api_appointment_v1_overview_api_proto_depIdxs = nil
}
