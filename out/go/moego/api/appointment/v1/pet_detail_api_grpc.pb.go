// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/appointment/v1/pet_detail_api.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PetDetailServiceClient is the client API for PetDetailService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PetDetailServiceClient interface {
	// Save or update pet's selected services
	// If there is no pet, the selected services will be saved directly.
	// If there is a pet, it will delete the original services selected for the pet, then save the newly selected services again.
	SaveOrUpdatePetDetails(ctx context.Context, in *SaveOrUpdatePetDetailsParams, opts ...grpc.CallOption) (*SaveOrUpdatePetDetailsResult, error)
	// Delete selected pet
	DeletePet(ctx context.Context, in *DeletePetParams, opts ...grpc.CallOption) (*DeletePetResult, error)
	// Delete selected pet evaluation service detail
	DeletePetEvaluation(ctx context.Context, in *DeletePetEvaluationParams, opts ...grpc.CallOption) (*DeletePetEvaluationResult, error)
	// pre check for create evaluation service
	PreCreateEvaluationServiceCheck(ctx context.Context, in *PreCreateEvaluationServiceCheckParams, opts ...grpc.CallOption) (*PreCreateEvaluationServiceCheckResult, error)
	// Count pet detail
	CountPetDetail(ctx context.Context, in *CountPetDetailParams, opts ...grpc.CallOption) (*CountPetDetailResult, error)
}

type petDetailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPetDetailServiceClient(cc grpc.ClientConnInterface) PetDetailServiceClient {
	return &petDetailServiceClient{cc}
}

func (c *petDetailServiceClient) SaveOrUpdatePetDetails(ctx context.Context, in *SaveOrUpdatePetDetailsParams, opts ...grpc.CallOption) (*SaveOrUpdatePetDetailsResult, error) {
	out := new(SaveOrUpdatePetDetailsResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.PetDetailService/SaveOrUpdatePetDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) DeletePet(ctx context.Context, in *DeletePetParams, opts ...grpc.CallOption) (*DeletePetResult, error) {
	out := new(DeletePetResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.PetDetailService/DeletePet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) DeletePetEvaluation(ctx context.Context, in *DeletePetEvaluationParams, opts ...grpc.CallOption) (*DeletePetEvaluationResult, error) {
	out := new(DeletePetEvaluationResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.PetDetailService/DeletePetEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) PreCreateEvaluationServiceCheck(ctx context.Context, in *PreCreateEvaluationServiceCheckParams, opts ...grpc.CallOption) (*PreCreateEvaluationServiceCheckResult, error) {
	out := new(PreCreateEvaluationServiceCheckResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.PetDetailService/PreCreateEvaluationServiceCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) CountPetDetail(ctx context.Context, in *CountPetDetailParams, opts ...grpc.CallOption) (*CountPetDetailResult, error) {
	out := new(CountPetDetailResult)
	err := c.cc.Invoke(ctx, "/moego.api.appointment.v1.PetDetailService/CountPetDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetDetailServiceServer is the server API for PetDetailService service.
// All implementations must embed UnimplementedPetDetailServiceServer
// for forward compatibility
type PetDetailServiceServer interface {
	// Save or update pet's selected services
	// If there is no pet, the selected services will be saved directly.
	// If there is a pet, it will delete the original services selected for the pet, then save the newly selected services again.
	SaveOrUpdatePetDetails(context.Context, *SaveOrUpdatePetDetailsParams) (*SaveOrUpdatePetDetailsResult, error)
	// Delete selected pet
	DeletePet(context.Context, *DeletePetParams) (*DeletePetResult, error)
	// Delete selected pet evaluation service detail
	DeletePetEvaluation(context.Context, *DeletePetEvaluationParams) (*DeletePetEvaluationResult, error)
	// pre check for create evaluation service
	PreCreateEvaluationServiceCheck(context.Context, *PreCreateEvaluationServiceCheckParams) (*PreCreateEvaluationServiceCheckResult, error)
	// Count pet detail
	CountPetDetail(context.Context, *CountPetDetailParams) (*CountPetDetailResult, error)
	mustEmbedUnimplementedPetDetailServiceServer()
}

// UnimplementedPetDetailServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPetDetailServiceServer struct {
}

func (UnimplementedPetDetailServiceServer) SaveOrUpdatePetDetails(context.Context, *SaveOrUpdatePetDetailsParams) (*SaveOrUpdatePetDetailsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveOrUpdatePetDetails not implemented")
}
func (UnimplementedPetDetailServiceServer) DeletePet(context.Context, *DeletePetParams) (*DeletePetResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePet not implemented")
}
func (UnimplementedPetDetailServiceServer) DeletePetEvaluation(context.Context, *DeletePetEvaluationParams) (*DeletePetEvaluationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetEvaluation not implemented")
}
func (UnimplementedPetDetailServiceServer) PreCreateEvaluationServiceCheck(context.Context, *PreCreateEvaluationServiceCheckParams) (*PreCreateEvaluationServiceCheckResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreCreateEvaluationServiceCheck not implemented")
}
func (UnimplementedPetDetailServiceServer) CountPetDetail(context.Context, *CountPetDetailParams) (*CountPetDetailResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountPetDetail not implemented")
}
func (UnimplementedPetDetailServiceServer) mustEmbedUnimplementedPetDetailServiceServer() {}

// UnsafePetDetailServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PetDetailServiceServer will
// result in compilation errors.
type UnsafePetDetailServiceServer interface {
	mustEmbedUnimplementedPetDetailServiceServer()
}

func RegisterPetDetailServiceServer(s grpc.ServiceRegistrar, srv PetDetailServiceServer) {
	s.RegisterService(&PetDetailService_ServiceDesc, srv)
}

func _PetDetailService_SaveOrUpdatePetDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveOrUpdatePetDetailsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).SaveOrUpdatePetDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.PetDetailService/SaveOrUpdatePetDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).SaveOrUpdatePetDetails(ctx, req.(*SaveOrUpdatePetDetailsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_DeletePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).DeletePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.PetDetailService/DeletePet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).DeletePet(ctx, req.(*DeletePetParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_DeletePetEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetEvaluationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).DeletePetEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.PetDetailService/DeletePetEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).DeletePetEvaluation(ctx, req.(*DeletePetEvaluationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_PreCreateEvaluationServiceCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreCreateEvaluationServiceCheckParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).PreCreateEvaluationServiceCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.PetDetailService/PreCreateEvaluationServiceCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).PreCreateEvaluationServiceCheck(ctx, req.(*PreCreateEvaluationServiceCheckParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_CountPetDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountPetDetailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).CountPetDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.appointment.v1.PetDetailService/CountPetDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).CountPetDetail(ctx, req.(*CountPetDetailParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PetDetailService_ServiceDesc is the grpc.ServiceDesc for PetDetailService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PetDetailService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.appointment.v1.PetDetailService",
	HandlerType: (*PetDetailServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SaveOrUpdatePetDetails",
			Handler:    _PetDetailService_SaveOrUpdatePetDetails_Handler,
		},
		{
			MethodName: "DeletePet",
			Handler:    _PetDetailService_DeletePet_Handler,
		},
		{
			MethodName: "DeletePetEvaluation",
			Handler:    _PetDetailService_DeletePetEvaluation_Handler,
		},
		{
			MethodName: "PreCreateEvaluationServiceCheck",
			Handler:    _PetDetailService_PreCreateEvaluationServiceCheck_Handler,
		},
		{
			MethodName: "CountPetDetail",
			Handler:    _PetDetailService_CountPetDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/appointment/v1/pet_detail_api.proto",
}
