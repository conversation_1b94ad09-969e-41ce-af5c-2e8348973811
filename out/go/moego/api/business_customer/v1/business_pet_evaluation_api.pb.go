// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_pet_evaluation_api.proto

package businesscustomerapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// List evaluation history params
type ListPetEvaluationHistoryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
}

func (x *ListPetEvaluationHistoryParams) Reset() {
	*x = ListPetEvaluationHistoryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetEvaluationHistoryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetEvaluationHistoryParams) ProtoMessage() {}

func (x *ListPetEvaluationHistoryParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetEvaluationHistoryParams.ProtoReflect.Descriptor instead.
func (*ListPetEvaluationHistoryParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListPetEvaluationHistoryParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// List evaluation history result
type ListPetEvaluationHistoryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation history view
	EvaluationHistories []*EvaluationHistoryView `protobuf:"bytes,1,rep,name=evaluation_histories,json=evaluationHistories,proto3" json:"evaluation_histories,omitempty"`
	// evaluation view
	Evaluations []*v1.EvaluationView `protobuf:"bytes,2,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *ListPetEvaluationHistoryResult) Reset() {
	*x = ListPetEvaluationHistoryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetEvaluationHistoryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetEvaluationHistoryResult) ProtoMessage() {}

func (x *ListPetEvaluationHistoryResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetEvaluationHistoryResult.ProtoReflect.Descriptor instead.
func (*ListPetEvaluationHistoryResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetEvaluationHistoryResult) GetEvaluationHistories() []*EvaluationHistoryView {
	if x != nil {
		return x.EvaluationHistories
	}
	return nil
}

func (x *ListPetEvaluationHistoryResult) GetEvaluations() []*v1.EvaluationView {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// evaluation history view
type EvaluationHistoryView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// action type
	ActionType v11.PetEvaluationHistoryModel_ActionType `protobuf:"varint,1,opt,name=action_type,json=actionType,proto3,enum=moego.models.business_customer.v1.PetEvaluationHistoryModel_ActionType" json:"action_type,omitempty"`
	// operator
	Operator *Operator `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	// operate time
	OperateDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=operate_date,json=operateDate,proto3" json:"operate_date,omitempty"`
	// evaluation extra
	EvaluationExtra *EvaluationExtra `protobuf:"bytes,4,opt,name=evaluation_extra,json=evaluationExtra,proto3" json:"evaluation_extra,omitempty"`
	// evaluation id
	EvaluationId int64 `protobuf:"varint,5,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
}

func (x *EvaluationHistoryView) Reset() {
	*x = EvaluationHistoryView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationHistoryView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationHistoryView) ProtoMessage() {}

func (x *EvaluationHistoryView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationHistoryView.ProtoReflect.Descriptor instead.
func (*EvaluationHistoryView) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{2}
}

func (x *EvaluationHistoryView) GetActionType() v11.PetEvaluationHistoryModel_ActionType {
	if x != nil {
		return x.ActionType
	}
	return v11.PetEvaluationHistoryModel_ActionType(0)
}

func (x *EvaluationHistoryView) GetOperator() *Operator {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *EvaluationHistoryView) GetOperateDate() *timestamppb.Timestamp {
	if x != nil {
		return x.OperateDate
	}
	return nil
}

func (x *EvaluationHistoryView) GetEvaluationExtra() *EvaluationExtra {
	if x != nil {
		return x.EvaluationExtra
	}
	return nil
}

func (x *EvaluationHistoryView) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

// evaluation extra
type EvaluationExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reset interval days
	ResetIntervalDays int32 `protobuf:"varint,1,opt,name=reset_interval_days,json=resetIntervalDays,proto3" json:"reset_interval_days,omitempty"`
	// original value
	OriginalStatus v12.EvaluationStatus `protobuf:"varint,2,opt,name=original_status,json=originalStatus,proto3,enum=moego.models.customer.v1.EvaluationStatus" json:"original_status,omitempty"`
	// new value
	NewStatus v12.EvaluationStatus `protobuf:"varint,3,opt,name=new_status,json=newStatus,proto3,enum=moego.models.customer.v1.EvaluationStatus" json:"new_status,omitempty"`
}

func (x *EvaluationExtra) Reset() {
	*x = EvaluationExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationExtra) ProtoMessage() {}

func (x *EvaluationExtra) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationExtra.ProtoReflect.Descriptor instead.
func (*EvaluationExtra) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{3}
}

func (x *EvaluationExtra) GetResetIntervalDays() int32 {
	if x != nil {
		return x.ResetIntervalDays
	}
	return 0
}

func (x *EvaluationExtra) GetOriginalStatus() v12.EvaluationStatus {
	if x != nil {
		return x.OriginalStatus
	}
	return v12.EvaluationStatus(0)
}

func (x *EvaluationExtra) GetNewStatus() v12.EvaluationStatus {
	if x != nil {
		return x.NewStatus
	}
	return v12.EvaluationStatus(0)
}

// Operator
type Operator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// operator id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// operator name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Operator) Reset() {
	*x = Operator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operator) ProtoMessage() {}

func (x *Operator) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operator.ProtoReflect.Descriptor instead.
func (*Operator) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{4}
}

func (x *Operator) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Operator) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// ListPetEvaluationParams
type ListPetEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
}

func (x *ListPetEvaluationParams) Reset() {
	*x = ListPetEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetEvaluationParams) ProtoMessage() {}

func (x *ListPetEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetEvaluationParams.ProtoReflect.Descriptor instead.
func (*ListPetEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{5}
}

func (x *ListPetEvaluationParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// ListPetEvaluationResult
type ListPetEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet evaluations
	PetEvaluations []*v11.PetEvaluationModel `protobuf:"bytes,1,rep,name=pet_evaluations,json=petEvaluations,proto3" json:"pet_evaluations,omitempty"`
	// evaluation view
	Evaluations []*v1.EvaluationView `protobuf:"bytes,2,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *ListPetEvaluationResult) Reset() {
	*x = ListPetEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetEvaluationResult) ProtoMessage() {}

func (x *ListPetEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetEvaluationResult.ProtoReflect.Descriptor instead.
func (*ListPetEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{6}
}

func (x *ListPetEvaluationResult) GetPetEvaluations() []*v11.PetEvaluationModel {
	if x != nil {
		return x.PetEvaluations
	}
	return nil
}

func (x *ListPetEvaluationResult) GetEvaluations() []*v1.EvaluationView {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// UpdatePetEvaluationParams
type UpdatePetEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// evaluation id
	EvaluationId int64 `protobuf:"varint,3,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
	// evaluation status
	EvaluationStatus v12.EvaluationStatus `protobuf:"varint,4,opt,name=evaluation_status,json=evaluationStatus,proto3,enum=moego.models.customer.v1.EvaluationStatus" json:"evaluation_status,omitempty"`
	// action type
	ActionType v11.PetEvaluationHistoryModel_ActionType `protobuf:"varint,5,opt,name=action_type,json=actionType,proto3,enum=moego.models.business_customer.v1.PetEvaluationHistoryModel_ActionType" json:"action_type,omitempty"`
}

func (x *UpdatePetEvaluationParams) Reset() {
	*x = UpdatePetEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetEvaluationParams) ProtoMessage() {}

func (x *UpdatePetEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetEvaluationParams.ProtoReflect.Descriptor instead.
func (*UpdatePetEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{7}
}

func (x *UpdatePetEvaluationParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *UpdatePetEvaluationParams) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

func (x *UpdatePetEvaluationParams) GetEvaluationStatus() v12.EvaluationStatus {
	if x != nil {
		return x.EvaluationStatus
	}
	return v12.EvaluationStatus(0)
}

func (x *UpdatePetEvaluationParams) GetActionType() v11.PetEvaluationHistoryModel_ActionType {
	if x != nil {
		return x.ActionType
	}
	return v11.PetEvaluationHistoryModel_ActionType(0)
}

// UpdatePetEvaluationResult
type UpdatePetEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePetEvaluationResult) Reset() {
	*x = UpdatePetEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetEvaluationResult) ProtoMessage() {}

func (x *UpdatePetEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetEvaluationResult.ProtoReflect.Descriptor instead.
func (*UpdatePetEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{8}
}

// ListEvaluationStatusByPetServiceParams
type ListEvaluationStatusByPetServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id and service id
	PetServiceIds []*ListEvaluationStatusByPetServiceParams_PetServiceIdParams `protobuf:"bytes,1,rep,name=pet_service_ids,json=petServiceIds,proto3" json:"pet_service_ids,omitempty"`
}

func (x *ListEvaluationStatusByPetServiceParams) Reset() {
	*x = ListEvaluationStatusByPetServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationStatusByPetServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationStatusByPetServiceParams) ProtoMessage() {}

func (x *ListEvaluationStatusByPetServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationStatusByPetServiceParams.ProtoReflect.Descriptor instead.
func (*ListEvaluationStatusByPetServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{9}
}

func (x *ListEvaluationStatusByPetServiceParams) GetPetServiceIds() []*ListEvaluationStatusByPetServiceParams_PetServiceIdParams {
	if x != nil {
		return x.PetServiceIds
	}
	return nil
}

// ListEvaluationStatusByPetServiceResult
type ListEvaluationStatusByPetServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation status result
	EvaluationStatusResults []*ListEvaluationStatusByPetServiceResult_EvaluationStatusResult `protobuf:"bytes,1,rep,name=evaluation_status_results,json=evaluationStatusResults,proto3" json:"evaluation_status_results,omitempty"`
}

func (x *ListEvaluationStatusByPetServiceResult) Reset() {
	*x = ListEvaluationStatusByPetServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationStatusByPetServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationStatusByPetServiceResult) ProtoMessage() {}

func (x *ListEvaluationStatusByPetServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationStatusByPetServiceResult.ProtoReflect.Descriptor instead.
func (*ListEvaluationStatusByPetServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{10}
}

func (x *ListEvaluationStatusByPetServiceResult) GetEvaluationStatusResults() []*ListEvaluationStatusByPetServiceResult_EvaluationStatusResult {
	if x != nil {
		return x.EvaluationStatusResults
	}
	return nil
}

// pet id and service id
type ListEvaluationStatusByPetServiceParams_PetServiceIdParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
}

func (x *ListEvaluationStatusByPetServiceParams_PetServiceIdParams) Reset() {
	*x = ListEvaluationStatusByPetServiceParams_PetServiceIdParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationStatusByPetServiceParams_PetServiceIdParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationStatusByPetServiceParams_PetServiceIdParams) ProtoMessage() {}

func (x *ListEvaluationStatusByPetServiceParams_PetServiceIdParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationStatusByPetServiceParams_PetServiceIdParams.ProtoReflect.Descriptor instead.
func (*ListEvaluationStatusByPetServiceParams_PetServiceIdParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListEvaluationStatusByPetServiceParams_PetServiceIdParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListEvaluationStatusByPetServiceParams_PetServiceIdParams) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

// evaluation status result
type ListEvaluationStatusByPetServiceResult_EvaluationStatusResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// service id
	ServiceId int64 `protobuf:"varint,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// pet evaluation status
	FinalEvaluationStatus v12.EvaluationStatus `protobuf:"varint,3,opt,name=final_evaluation_status,json=finalEvaluationStatus,proto3,enum=moego.models.customer.v1.EvaluationStatus" json:"final_evaluation_status,omitempty"`
	// whether evaluation is required
	IsEvaluationRequired bool `protobuf:"varint,6,opt,name=is_evaluation_required,json=isEvaluationRequired,proto3" json:"is_evaluation_required,omitempty"`
	// whether evaluation is required before online booking
	IsEvaluationRequiredForOb bool `protobuf:"varint,7,opt,name=is_evaluation_required_for_ob,json=isEvaluationRequiredForOb,proto3" json:"is_evaluation_required_for_ob,omitempty"`
	// evaluation id
	EvaluationId int64 `protobuf:"varint,8,opt,name=evaluation_id,json=evaluationId,proto3" json:"evaluation_id,omitempty"`
}

func (x *ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) Reset() {
	*x = ListEvaluationStatusByPetServiceResult_EvaluationStatusResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) ProtoMessage() {}

func (x *ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationStatusByPetServiceResult_EvaluationStatusResult.ProtoReflect.Descriptor instead.
func (*ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) GetFinalEvaluationStatus() v12.EvaluationStatus {
	if x != nil {
		return x.FinalEvaluationStatus
	}
	return v12.EvaluationStatus(0)
}

func (x *ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) GetIsEvaluationRequired() bool {
	if x != nil {
		return x.IsEvaluationRequired
	}
	return false
}

func (x *ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) GetIsEvaluationRequiredForOb() bool {
	if x != nil {
		return x.IsEvaluationRequiredForOb
	}
	return false
}

func (x *ListEvaluationStatusByPetServiceResult_EvaluationStatusResult) GetEvaluationId() int64 {
	if x != nil {
		return x.EvaluationId
	}
	return 0
}

var File_moego_api_business_customer_v1_business_pet_evaluation_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDesc = []byte{
	0x0a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x46, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x65, 0x74, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x40, 0x0a, 0x1e, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x06, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x22, 0xd6, 0x01, 0x0a, 0x1e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x68,
	0x0a, 0x14, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x13, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x4a, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x90, 0x03, 0x0a, 0x15, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12, 0x68,
	0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x3d,
	0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x5a, 0x0a,
	0x10, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x0f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x2c, 0x0a, 0x0d, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xe1, 0x01, 0x0a, 0x0f, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x2e, 0x0a, 0x13, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x64, 0x61,
	0x79, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x44, 0x61, 0x79, 0x73, 0x12, 0x53, 0x0a, 0x0f, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x49, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x09, 0x6e, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x2e, 0x0a, 0x08, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x39, 0x0a, 0x17, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x22, 0xc5, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x5e, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x4a, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xac,
	0x02, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x06,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x11, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x10, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x68, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x1b, 0x0a,
	0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x94, 0x02, 0x0a, 0x26, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x79, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x8b, 0x01, 0x0a, 0x0f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x59, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x79, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92,
	0x01, 0x02, 0x08, 0x01, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x73, 0x1a, 0x5c, 0x0a, 0x12, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x22, 0x96, 0x04, 0x0a, 0x26, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x79, 0x50, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x99, 0x01, 0x0a,
	0x19, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x5d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x79, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x17, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0xcf, 0x02, 0x0a, 0x16, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x17, 0x66, 0x69, 0x6e,
	0x61, 0x6c, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x15, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a,
	0x16, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69,
	0x73, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x1d, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6f,
	0x72, 0x5f, 0x6f, 0x62, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x69, 0x73, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x46, 0x6f, 0x72, 0x4f, 0x62, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x32, 0x86, 0x05, 0x0a, 0x1c, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x18,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x8b, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xb2,
	0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x79, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x79, 0x50, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x46, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x79, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x95, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescData = file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_goTypes = []interface{}{
	(*ListPetEvaluationHistoryParams)(nil),                                // 0: moego.api.business_customer.v1.ListPetEvaluationHistoryParams
	(*ListPetEvaluationHistoryResult)(nil),                                // 1: moego.api.business_customer.v1.ListPetEvaluationHistoryResult
	(*EvaluationHistoryView)(nil),                                         // 2: moego.api.business_customer.v1.EvaluationHistoryView
	(*EvaluationExtra)(nil),                                               // 3: moego.api.business_customer.v1.EvaluationExtra
	(*Operator)(nil),                                                      // 4: moego.api.business_customer.v1.Operator
	(*ListPetEvaluationParams)(nil),                                       // 5: moego.api.business_customer.v1.ListPetEvaluationParams
	(*ListPetEvaluationResult)(nil),                                       // 6: moego.api.business_customer.v1.ListPetEvaluationResult
	(*UpdatePetEvaluationParams)(nil),                                     // 7: moego.api.business_customer.v1.UpdatePetEvaluationParams
	(*UpdatePetEvaluationResult)(nil),                                     // 8: moego.api.business_customer.v1.UpdatePetEvaluationResult
	(*ListEvaluationStatusByPetServiceParams)(nil),                        // 9: moego.api.business_customer.v1.ListEvaluationStatusByPetServiceParams
	(*ListEvaluationStatusByPetServiceResult)(nil),                        // 10: moego.api.business_customer.v1.ListEvaluationStatusByPetServiceResult
	(*ListEvaluationStatusByPetServiceParams_PetServiceIdParams)(nil),     // 11: moego.api.business_customer.v1.ListEvaluationStatusByPetServiceParams.PetServiceIdParams
	(*ListEvaluationStatusByPetServiceResult_EvaluationStatusResult)(nil), // 12: moego.api.business_customer.v1.ListEvaluationStatusByPetServiceResult.EvaluationStatusResult
	(*v1.EvaluationView)(nil),                                             // 13: moego.models.offering.v1.EvaluationView
	(v11.PetEvaluationHistoryModel_ActionType)(0),                         // 14: moego.models.business_customer.v1.PetEvaluationHistoryModel.ActionType
	(*timestamppb.Timestamp)(nil),                                         // 15: google.protobuf.Timestamp
	(v12.EvaluationStatus)(0),                                             // 16: moego.models.customer.v1.EvaluationStatus
	(*v11.PetEvaluationModel)(nil),                                        // 17: moego.models.business_customer.v1.PetEvaluationModel
}
var file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_depIdxs = []int32{
	2,  // 0: moego.api.business_customer.v1.ListPetEvaluationHistoryResult.evaluation_histories:type_name -> moego.api.business_customer.v1.EvaluationHistoryView
	13, // 1: moego.api.business_customer.v1.ListPetEvaluationHistoryResult.evaluations:type_name -> moego.models.offering.v1.EvaluationView
	14, // 2: moego.api.business_customer.v1.EvaluationHistoryView.action_type:type_name -> moego.models.business_customer.v1.PetEvaluationHistoryModel.ActionType
	4,  // 3: moego.api.business_customer.v1.EvaluationHistoryView.operator:type_name -> moego.api.business_customer.v1.Operator
	15, // 4: moego.api.business_customer.v1.EvaluationHistoryView.operate_date:type_name -> google.protobuf.Timestamp
	3,  // 5: moego.api.business_customer.v1.EvaluationHistoryView.evaluation_extra:type_name -> moego.api.business_customer.v1.EvaluationExtra
	16, // 6: moego.api.business_customer.v1.EvaluationExtra.original_status:type_name -> moego.models.customer.v1.EvaluationStatus
	16, // 7: moego.api.business_customer.v1.EvaluationExtra.new_status:type_name -> moego.models.customer.v1.EvaluationStatus
	17, // 8: moego.api.business_customer.v1.ListPetEvaluationResult.pet_evaluations:type_name -> moego.models.business_customer.v1.PetEvaluationModel
	13, // 9: moego.api.business_customer.v1.ListPetEvaluationResult.evaluations:type_name -> moego.models.offering.v1.EvaluationView
	16, // 10: moego.api.business_customer.v1.UpdatePetEvaluationParams.evaluation_status:type_name -> moego.models.customer.v1.EvaluationStatus
	14, // 11: moego.api.business_customer.v1.UpdatePetEvaluationParams.action_type:type_name -> moego.models.business_customer.v1.PetEvaluationHistoryModel.ActionType
	11, // 12: moego.api.business_customer.v1.ListEvaluationStatusByPetServiceParams.pet_service_ids:type_name -> moego.api.business_customer.v1.ListEvaluationStatusByPetServiceParams.PetServiceIdParams
	12, // 13: moego.api.business_customer.v1.ListEvaluationStatusByPetServiceResult.evaluation_status_results:type_name -> moego.api.business_customer.v1.ListEvaluationStatusByPetServiceResult.EvaluationStatusResult
	16, // 14: moego.api.business_customer.v1.ListEvaluationStatusByPetServiceResult.EvaluationStatusResult.final_evaluation_status:type_name -> moego.models.customer.v1.EvaluationStatus
	0,  // 15: moego.api.business_customer.v1.BusinessPetEvaluationService.ListPetEvaluationHistory:input_type -> moego.api.business_customer.v1.ListPetEvaluationHistoryParams
	5,  // 16: moego.api.business_customer.v1.BusinessPetEvaluationService.ListPetEvaluation:input_type -> moego.api.business_customer.v1.ListPetEvaluationParams
	7,  // 17: moego.api.business_customer.v1.BusinessPetEvaluationService.UpdatePetEvaluation:input_type -> moego.api.business_customer.v1.UpdatePetEvaluationParams
	9,  // 18: moego.api.business_customer.v1.BusinessPetEvaluationService.ListEvaluationStatusByPetService:input_type -> moego.api.business_customer.v1.ListEvaluationStatusByPetServiceParams
	1,  // 19: moego.api.business_customer.v1.BusinessPetEvaluationService.ListPetEvaluationHistory:output_type -> moego.api.business_customer.v1.ListPetEvaluationHistoryResult
	6,  // 20: moego.api.business_customer.v1.BusinessPetEvaluationService.ListPetEvaluation:output_type -> moego.api.business_customer.v1.ListPetEvaluationResult
	8,  // 21: moego.api.business_customer.v1.BusinessPetEvaluationService.UpdatePetEvaluation:output_type -> moego.api.business_customer.v1.UpdatePetEvaluationResult
	10, // 22: moego.api.business_customer.v1.BusinessPetEvaluationService.ListEvaluationStatusByPetService:output_type -> moego.api.business_customer.v1.ListEvaluationStatusByPetServiceResult
	19, // [19:23] is the sub-list for method output_type
	15, // [15:19] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_init() }
func file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_init() {
	if File_moego_api_business_customer_v1_business_pet_evaluation_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetEvaluationHistoryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetEvaluationHistoryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationHistoryView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationStatusByPetServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationStatusByPetServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationStatusByPetServiceParams_PetServiceIdParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationStatusByPetServiceResult_EvaluationStatusResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_depIdxs,
		MessageInfos:      file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_pet_evaluation_api_proto = out.File
	file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_pet_evaluation_api_proto_depIdxs = nil
}
