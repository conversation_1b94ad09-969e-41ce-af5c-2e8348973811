// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/fulfillment/v1/fulfillment_api.proto

package fulfillmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// FulfillmentServiceClient is the client API for FulfillmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FulfillmentServiceClient interface {
	// Enroll a pet into a group class instance
	EnrollPet(ctx context.Context, in *EnrollPetParams, opts ...grpc.CallOption) (*EnrollPetResult, error)
	// Check in group class session
	// It will be retrieved within the current business day
	CheckInGroupClassSession(ctx context.Context, in *CheckInGroupClassSessionParams, opts ...grpc.CallOption) (*CheckInGroupClassSessionResult, error)
	// Preview order line items, include services and surcharges
	PreviewOrderLineItems(ctx context.Context, in *PreviewOrderLineItemsParams, opts ...grpc.CallOption) (*PreviewOrderLineItemsResult, error)
}

type fulfillmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFulfillmentServiceClient(cc grpc.ClientConnInterface) FulfillmentServiceClient {
	return &fulfillmentServiceClient{cc}
}

func (c *fulfillmentServiceClient) EnrollPet(ctx context.Context, in *EnrollPetParams, opts ...grpc.CallOption) (*EnrollPetResult, error) {
	out := new(EnrollPetResult)
	err := c.cc.Invoke(ctx, "/moego.api.fulfillment.v1.FulfillmentService/EnrollPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) CheckInGroupClassSession(ctx context.Context, in *CheckInGroupClassSessionParams, opts ...grpc.CallOption) (*CheckInGroupClassSessionResult, error) {
	out := new(CheckInGroupClassSessionResult)
	err := c.cc.Invoke(ctx, "/moego.api.fulfillment.v1.FulfillmentService/CheckInGroupClassSession", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fulfillmentServiceClient) PreviewOrderLineItems(ctx context.Context, in *PreviewOrderLineItemsParams, opts ...grpc.CallOption) (*PreviewOrderLineItemsResult, error) {
	out := new(PreviewOrderLineItemsResult)
	err := c.cc.Invoke(ctx, "/moego.api.fulfillment.v1.FulfillmentService/PreviewOrderLineItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FulfillmentServiceServer is the server API for FulfillmentService service.
// All implementations must embed UnimplementedFulfillmentServiceServer
// for forward compatibility
type FulfillmentServiceServer interface {
	// Enroll a pet into a group class instance
	EnrollPet(context.Context, *EnrollPetParams) (*EnrollPetResult, error)
	// Check in group class session
	// It will be retrieved within the current business day
	CheckInGroupClassSession(context.Context, *CheckInGroupClassSessionParams) (*CheckInGroupClassSessionResult, error)
	// Preview order line items, include services and surcharges
	PreviewOrderLineItems(context.Context, *PreviewOrderLineItemsParams) (*PreviewOrderLineItemsResult, error)
	mustEmbedUnimplementedFulfillmentServiceServer()
}

// UnimplementedFulfillmentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFulfillmentServiceServer struct {
}

func (UnimplementedFulfillmentServiceServer) EnrollPet(context.Context, *EnrollPetParams) (*EnrollPetResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnrollPet not implemented")
}
func (UnimplementedFulfillmentServiceServer) CheckInGroupClassSession(context.Context, *CheckInGroupClassSessionParams) (*CheckInGroupClassSessionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckInGroupClassSession not implemented")
}
func (UnimplementedFulfillmentServiceServer) PreviewOrderLineItems(context.Context, *PreviewOrderLineItemsParams) (*PreviewOrderLineItemsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewOrderLineItems not implemented")
}
func (UnimplementedFulfillmentServiceServer) mustEmbedUnimplementedFulfillmentServiceServer() {}

// UnsafeFulfillmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FulfillmentServiceServer will
// result in compilation errors.
type UnsafeFulfillmentServiceServer interface {
	mustEmbedUnimplementedFulfillmentServiceServer()
}

func RegisterFulfillmentServiceServer(s grpc.ServiceRegistrar, srv FulfillmentServiceServer) {
	s.RegisterService(&FulfillmentService_ServiceDesc, srv)
}

func _FulfillmentService_EnrollPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnrollPetParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).EnrollPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.fulfillment.v1.FulfillmentService/EnrollPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).EnrollPet(ctx, req.(*EnrollPetParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_CheckInGroupClassSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckInGroupClassSessionParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).CheckInGroupClassSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.fulfillment.v1.FulfillmentService/CheckInGroupClassSession",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).CheckInGroupClassSession(ctx, req.(*CheckInGroupClassSessionParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _FulfillmentService_PreviewOrderLineItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewOrderLineItemsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FulfillmentServiceServer).PreviewOrderLineItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.fulfillment.v1.FulfillmentService/PreviewOrderLineItems",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FulfillmentServiceServer).PreviewOrderLineItems(ctx, req.(*PreviewOrderLineItemsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// FulfillmentService_ServiceDesc is the grpc.ServiceDesc for FulfillmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FulfillmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.fulfillment.v1.FulfillmentService",
	HandlerType: (*FulfillmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EnrollPet",
			Handler:    _FulfillmentService_EnrollPet_Handler,
		},
		{
			MethodName: "CheckInGroupClassSession",
			Handler:    _FulfillmentService_CheckInGroupClassSession_Handler,
		},
		{
			MethodName: "PreviewOrderLineItems",
			Handler:    _FulfillmentService_PreviewOrderLineItems_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/fulfillment/v1/fulfillment_api.proto",
}
