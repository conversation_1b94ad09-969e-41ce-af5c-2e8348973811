// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/grooming/v1/grooming_report_api.proto

package groomingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GroomingReportServiceClient is the client API for GroomingReportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroomingReportServiceClient interface {
	// list grooming report card
	ListGroomingReportCard(ctx context.Context, in *ListGroomingReportCardParams, opts ...grpc.CallOption) (*ListGroomingReportCardResult, error)
	// batch delete grooming report
	BatchDeleteGroomingReportCard(ctx context.Context, in *BatchDeleteGroomingReportCardParams, opts ...grpc.CallOption) (*BatchDeleteGroomingReportCardResult, error)
	// batch send grooming report
	BatchSendGroomingReportCard(ctx context.Context, in *BatchSendGroomingReportCardParams, opts ...grpc.CallOption) (*BatchSendGroomingReportCardResult, error)
}

type groomingReportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroomingReportServiceClient(cc grpc.ClientConnInterface) GroomingReportServiceClient {
	return &groomingReportServiceClient{cc}
}

func (c *groomingReportServiceClient) ListGroomingReportCard(ctx context.Context, in *ListGroomingReportCardParams, opts ...grpc.CallOption) (*ListGroomingReportCardResult, error) {
	out := new(ListGroomingReportCardResult)
	err := c.cc.Invoke(ctx, "/moego.api.grooming.v1.GroomingReportService/ListGroomingReportCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groomingReportServiceClient) BatchDeleteGroomingReportCard(ctx context.Context, in *BatchDeleteGroomingReportCardParams, opts ...grpc.CallOption) (*BatchDeleteGroomingReportCardResult, error) {
	out := new(BatchDeleteGroomingReportCardResult)
	err := c.cc.Invoke(ctx, "/moego.api.grooming.v1.GroomingReportService/BatchDeleteGroomingReportCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groomingReportServiceClient) BatchSendGroomingReportCard(ctx context.Context, in *BatchSendGroomingReportCardParams, opts ...grpc.CallOption) (*BatchSendGroomingReportCardResult, error) {
	out := new(BatchSendGroomingReportCardResult)
	err := c.cc.Invoke(ctx, "/moego.api.grooming.v1.GroomingReportService/BatchSendGroomingReportCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroomingReportServiceServer is the server API for GroomingReportService service.
// All implementations must embed UnimplementedGroomingReportServiceServer
// for forward compatibility
type GroomingReportServiceServer interface {
	// list grooming report card
	ListGroomingReportCard(context.Context, *ListGroomingReportCardParams) (*ListGroomingReportCardResult, error)
	// batch delete grooming report
	BatchDeleteGroomingReportCard(context.Context, *BatchDeleteGroomingReportCardParams) (*BatchDeleteGroomingReportCardResult, error)
	// batch send grooming report
	BatchSendGroomingReportCard(context.Context, *BatchSendGroomingReportCardParams) (*BatchSendGroomingReportCardResult, error)
	mustEmbedUnimplementedGroomingReportServiceServer()
}

// UnimplementedGroomingReportServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGroomingReportServiceServer struct {
}

func (UnimplementedGroomingReportServiceServer) ListGroomingReportCard(context.Context, *ListGroomingReportCardParams) (*ListGroomingReportCardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGroomingReportCard not implemented")
}
func (UnimplementedGroomingReportServiceServer) BatchDeleteGroomingReportCard(context.Context, *BatchDeleteGroomingReportCardParams) (*BatchDeleteGroomingReportCardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteGroomingReportCard not implemented")
}
func (UnimplementedGroomingReportServiceServer) BatchSendGroomingReportCard(context.Context, *BatchSendGroomingReportCardParams) (*BatchSendGroomingReportCardResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchSendGroomingReportCard not implemented")
}
func (UnimplementedGroomingReportServiceServer) mustEmbedUnimplementedGroomingReportServiceServer() {}

// UnsafeGroomingReportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroomingReportServiceServer will
// result in compilation errors.
type UnsafeGroomingReportServiceServer interface {
	mustEmbedUnimplementedGroomingReportServiceServer()
}

func RegisterGroomingReportServiceServer(s grpc.ServiceRegistrar, srv GroomingReportServiceServer) {
	s.RegisterService(&GroomingReportService_ServiceDesc, srv)
}

func _GroomingReportService_ListGroomingReportCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGroomingReportCardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingReportServiceServer).ListGroomingReportCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.grooming.v1.GroomingReportService/ListGroomingReportCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingReportServiceServer).ListGroomingReportCard(ctx, req.(*ListGroomingReportCardParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroomingReportService_BatchDeleteGroomingReportCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteGroomingReportCardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingReportServiceServer).BatchDeleteGroomingReportCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.grooming.v1.GroomingReportService/BatchDeleteGroomingReportCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingReportServiceServer).BatchDeleteGroomingReportCard(ctx, req.(*BatchDeleteGroomingReportCardParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroomingReportService_BatchSendGroomingReportCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSendGroomingReportCardParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingReportServiceServer).BatchSendGroomingReportCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.grooming.v1.GroomingReportService/BatchSendGroomingReportCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingReportServiceServer).BatchSendGroomingReportCard(ctx, req.(*BatchSendGroomingReportCardParams))
	}
	return interceptor(ctx, in, info, handler)
}

// GroomingReportService_ServiceDesc is the grpc.ServiceDesc for GroomingReportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroomingReportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.grooming.v1.GroomingReportService",
	HandlerType: (*GroomingReportServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListGroomingReportCard",
			Handler:    _GroomingReportService_ListGroomingReportCard_Handler,
		},
		{
			MethodName: "BatchDeleteGroomingReportCard",
			Handler:    _GroomingReportService_BatchDeleteGroomingReportCard_Handler,
		},
		{
			MethodName: "BatchSendGroomingReportCard",
			Handler:    _GroomingReportService_BatchSendGroomingReportCard_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/grooming/v1/grooming_report_api.proto",
}
