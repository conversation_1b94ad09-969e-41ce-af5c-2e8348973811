// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/offering/v1/evaluation_api.proto

package offeringapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EvaluationServiceClient is the client API for EvaluationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EvaluationServiceClient interface {
	// create evaluation
	CreateEvaluation(ctx context.Context, in *CreateEvaluationParams, opts ...grpc.CallOption) (*CreateEvaluationResult, error)
	// update evaluation
	UpdateEvaluation(ctx context.Context, in *UpdateEvaluationParams, opts ...grpc.CallOption) (*UpdateEvaluationResult, error)
	// delete evaluation
	DeleteEvaluation(ctx context.Context, in *DeleteEvaluationParams, opts ...grpc.CallOption) (*DeleteEvaluationResult, error)
	// get evaluation
	GetEvaluation(ctx context.Context, in *GetEvaluationParams, opts ...grpc.CallOption) (*GetEvaluationResult, error)
	// get evaluation list
	GetEvaluationList(ctx context.Context, in *GetEvaluationListParams, opts ...grpc.CallOption) (*GetEvaluationListResult, error)
	// get applicable evaluation list
	GetApplicableEvaluationList(ctx context.Context, in *GetApplicableEvaluationListParams, opts ...grpc.CallOption) (*GetApplicableEvaluationListResult, error)
	// get business list with applicable evaluation
	GetBusinessListWithApplicableEvaluation(ctx context.Context, in *GetBusinessListWithApplicableEvaluationParams, opts ...grpc.CallOption) (*GetBusinessListWithApplicableEvaluationResult, error)
}

type evaluationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEvaluationServiceClient(cc grpc.ClientConnInterface) EvaluationServiceClient {
	return &evaluationServiceClient{cc}
}

func (c *evaluationServiceClient) CreateEvaluation(ctx context.Context, in *CreateEvaluationParams, opts ...grpc.CallOption) (*CreateEvaluationResult, error) {
	out := new(CreateEvaluationResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.EvaluationService/CreateEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) UpdateEvaluation(ctx context.Context, in *UpdateEvaluationParams, opts ...grpc.CallOption) (*UpdateEvaluationResult, error) {
	out := new(UpdateEvaluationResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.EvaluationService/UpdateEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) DeleteEvaluation(ctx context.Context, in *DeleteEvaluationParams, opts ...grpc.CallOption) (*DeleteEvaluationResult, error) {
	out := new(DeleteEvaluationResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.EvaluationService/DeleteEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) GetEvaluation(ctx context.Context, in *GetEvaluationParams, opts ...grpc.CallOption) (*GetEvaluationResult, error) {
	out := new(GetEvaluationResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.EvaluationService/GetEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) GetEvaluationList(ctx context.Context, in *GetEvaluationListParams, opts ...grpc.CallOption) (*GetEvaluationListResult, error) {
	out := new(GetEvaluationListResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.EvaluationService/GetEvaluationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) GetApplicableEvaluationList(ctx context.Context, in *GetApplicableEvaluationListParams, opts ...grpc.CallOption) (*GetApplicableEvaluationListResult, error) {
	out := new(GetApplicableEvaluationListResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.EvaluationService/GetApplicableEvaluationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) GetBusinessListWithApplicableEvaluation(ctx context.Context, in *GetBusinessListWithApplicableEvaluationParams, opts ...grpc.CallOption) (*GetBusinessListWithApplicableEvaluationResult, error) {
	out := new(GetBusinessListWithApplicableEvaluationResult)
	err := c.cc.Invoke(ctx, "/moego.api.offering.v1.EvaluationService/GetBusinessListWithApplicableEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EvaluationServiceServer is the server API for EvaluationService service.
// All implementations must embed UnimplementedEvaluationServiceServer
// for forward compatibility
type EvaluationServiceServer interface {
	// create evaluation
	CreateEvaluation(context.Context, *CreateEvaluationParams) (*CreateEvaluationResult, error)
	// update evaluation
	UpdateEvaluation(context.Context, *UpdateEvaluationParams) (*UpdateEvaluationResult, error)
	// delete evaluation
	DeleteEvaluation(context.Context, *DeleteEvaluationParams) (*DeleteEvaluationResult, error)
	// get evaluation
	GetEvaluation(context.Context, *GetEvaluationParams) (*GetEvaluationResult, error)
	// get evaluation list
	GetEvaluationList(context.Context, *GetEvaluationListParams) (*GetEvaluationListResult, error)
	// get applicable evaluation list
	GetApplicableEvaluationList(context.Context, *GetApplicableEvaluationListParams) (*GetApplicableEvaluationListResult, error)
	// get business list with applicable evaluation
	GetBusinessListWithApplicableEvaluation(context.Context, *GetBusinessListWithApplicableEvaluationParams) (*GetBusinessListWithApplicableEvaluationResult, error)
	mustEmbedUnimplementedEvaluationServiceServer()
}

// UnimplementedEvaluationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEvaluationServiceServer struct {
}

func (UnimplementedEvaluationServiceServer) CreateEvaluation(context.Context, *CreateEvaluationParams) (*CreateEvaluationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) UpdateEvaluation(context.Context, *UpdateEvaluationParams) (*UpdateEvaluationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) DeleteEvaluation(context.Context, *DeleteEvaluationParams) (*DeleteEvaluationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) GetEvaluation(context.Context, *GetEvaluationParams) (*GetEvaluationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) GetEvaluationList(context.Context, *GetEvaluationListParams) (*GetEvaluationListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvaluationList not implemented")
}
func (UnimplementedEvaluationServiceServer) GetApplicableEvaluationList(context.Context, *GetApplicableEvaluationListParams) (*GetApplicableEvaluationListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicableEvaluationList not implemented")
}
func (UnimplementedEvaluationServiceServer) GetBusinessListWithApplicableEvaluation(context.Context, *GetBusinessListWithApplicableEvaluationParams) (*GetBusinessListWithApplicableEvaluationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessListWithApplicableEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) mustEmbedUnimplementedEvaluationServiceServer() {}

// UnsafeEvaluationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EvaluationServiceServer will
// result in compilation errors.
type UnsafeEvaluationServiceServer interface {
	mustEmbedUnimplementedEvaluationServiceServer()
}

func RegisterEvaluationServiceServer(s grpc.ServiceRegistrar, srv EvaluationServiceServer) {
	s.RegisterService(&EvaluationService_ServiceDesc, srv)
}

func _EvaluationService_CreateEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEvaluationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).CreateEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.EvaluationService/CreateEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).CreateEvaluation(ctx, req.(*CreateEvaluationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_UpdateEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEvaluationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).UpdateEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.EvaluationService/UpdateEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).UpdateEvaluation(ctx, req.(*UpdateEvaluationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_DeleteEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEvaluationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).DeleteEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.EvaluationService/DeleteEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).DeleteEvaluation(ctx, req.(*DeleteEvaluationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_GetEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvaluationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).GetEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.EvaluationService/GetEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).GetEvaluation(ctx, req.(*GetEvaluationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_GetEvaluationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvaluationListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).GetEvaluationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.EvaluationService/GetEvaluationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).GetEvaluationList(ctx, req.(*GetEvaluationListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_GetApplicableEvaluationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicableEvaluationListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).GetApplicableEvaluationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.EvaluationService/GetApplicableEvaluationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).GetApplicableEvaluationList(ctx, req.(*GetApplicableEvaluationListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_GetBusinessListWithApplicableEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessListWithApplicableEvaluationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).GetBusinessListWithApplicableEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.offering.v1.EvaluationService/GetBusinessListWithApplicableEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).GetBusinessListWithApplicableEvaluation(ctx, req.(*GetBusinessListWithApplicableEvaluationParams))
	}
	return interceptor(ctx, in, info, handler)
}

// EvaluationService_ServiceDesc is the grpc.ServiceDesc for EvaluationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EvaluationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.offering.v1.EvaluationService",
	HandlerType: (*EvaluationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateEvaluation",
			Handler:    _EvaluationService_CreateEvaluation_Handler,
		},
		{
			MethodName: "UpdateEvaluation",
			Handler:    _EvaluationService_UpdateEvaluation_Handler,
		},
		{
			MethodName: "DeleteEvaluation",
			Handler:    _EvaluationService_DeleteEvaluation_Handler,
		},
		{
			MethodName: "GetEvaluation",
			Handler:    _EvaluationService_GetEvaluation_Handler,
		},
		{
			MethodName: "GetEvaluationList",
			Handler:    _EvaluationService_GetEvaluationList_Handler,
		},
		{
			MethodName: "GetApplicableEvaluationList",
			Handler:    _EvaluationService_GetApplicableEvaluationList_Handler,
		},
		{
			MethodName: "GetBusinessListWithApplicableEvaluation",
			Handler:    _EvaluationService_GetBusinessListWithApplicableEvaluation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/offering/v1/evaluation_api.proto",
}
