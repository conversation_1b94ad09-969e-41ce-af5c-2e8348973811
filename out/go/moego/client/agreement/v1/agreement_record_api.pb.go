// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/agreement/v1/agreement_record_api.proto

package agreementapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get agreement record list for customer params
type GetRecordListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetRecordListParams_Name
	//	*GetRecordListParams_Domain
	Anonymous isGetRecordListParams_Anonymous `protobuf_oneof:"anonymous"`
	// sign status
	SignedStatus *v1.SignedStatus `protobuf:"varint,3,opt,name=signed_status,json=signedStatus,proto3,enum=moego.models.agreement.v1.SignedStatus,oneof" json:"signed_status,omitempty"`
	// page info
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// if true, the agreement record will be listed in the company, otherwise only the records of the business
	// default is false
	ListCompanyRecord *bool `protobuf:"varint,5,opt,name=list_company_record,json=listCompanyRecord,proto3,oneof" json:"list_company_record,omitempty"`
}

func (x *GetRecordListParams) Reset() {
	*x = GetRecordListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_agreement_v1_agreement_record_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecordListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordListParams) ProtoMessage() {}

func (x *GetRecordListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_agreement_v1_agreement_record_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordListParams.ProtoReflect.Descriptor instead.
func (*GetRecordListParams) Descriptor() ([]byte, []int) {
	return file_moego_client_agreement_v1_agreement_record_api_proto_rawDescGZIP(), []int{0}
}

func (m *GetRecordListParams) GetAnonymous() isGetRecordListParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetRecordListParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetRecordListParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetRecordListParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetRecordListParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *GetRecordListParams) GetSignedStatus() v1.SignedStatus {
	if x != nil && x.SignedStatus != nil {
		return *x.SignedStatus
	}
	return v1.SignedStatus(0)
}

func (x *GetRecordListParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetRecordListParams) GetListCompanyRecord() bool {
	if x != nil && x.ListCompanyRecord != nil {
		return *x.ListCompanyRecord
	}
	return false
}

type isGetRecordListParams_Anonymous interface {
	isGetRecordListParams_Anonymous()
}

type GetRecordListParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetRecordListParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetRecordListParams_Name) isGetRecordListParams_Anonymous() {}

func (*GetRecordListParams_Domain) isGetRecordListParams_Anonymous() {}

// get agreement record list for customer result
type GetRecordListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// record list
	AgreementRecordSimpleView []*v1.AgreementRecordSimpleView `protobuf:"bytes,1,rep,name=agreement_record_simple_view,json=agreementRecordSimpleView,proto3" json:"agreement_record_simple_view,omitempty"`
	// page info
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// agreement list
	AgreementSimpleView []*v1.AgreementModelSimpleView `protobuf:"bytes,3,rep,name=agreement_simple_view,json=agreementSimpleView,proto3" json:"agreement_simple_view,omitempty"`
}

func (x *GetRecordListResult) Reset() {
	*x = GetRecordListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_agreement_v1_agreement_record_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecordListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecordListResult) ProtoMessage() {}

func (x *GetRecordListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_agreement_v1_agreement_record_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecordListResult.ProtoReflect.Descriptor instead.
func (*GetRecordListResult) Descriptor() ([]byte, []int) {
	return file_moego_client_agreement_v1_agreement_record_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetRecordListResult) GetAgreementRecordSimpleView() []*v1.AgreementRecordSimpleView {
	if x != nil {
		return x.AgreementRecordSimpleView
	}
	return nil
}

func (x *GetRecordListResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetRecordListResult) GetAgreementSimpleView() []*v1.AgreementModelSimpleView {
	if x != nil {
		return x.AgreementSimpleView
	}
	return nil
}

var File_moego_client_agreement_v1_agreement_record_api_proto protoreflect.FileDescriptor

var file_moego_client_agreement_v1_agreement_record_api_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe2, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x5d, 0x0a, 0x0d, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x13, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x02, 0x52, 0x11, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f,
	0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x16, 0x0a,
	0x14, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0xb9, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x75, 0x0a,
	0x1c, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x19, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x67, 0x0a, 0x15, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x76, 0x69, 0x65,
	0x77, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x13, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65,
	0x77, 0x32, 0x89, 0x01, 0x0a, 0x16, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6f, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x84, 0x01,
	0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_agreement_v1_agreement_record_api_proto_rawDescOnce sync.Once
	file_moego_client_agreement_v1_agreement_record_api_proto_rawDescData = file_moego_client_agreement_v1_agreement_record_api_proto_rawDesc
)

func file_moego_client_agreement_v1_agreement_record_api_proto_rawDescGZIP() []byte {
	file_moego_client_agreement_v1_agreement_record_api_proto_rawDescOnce.Do(func() {
		file_moego_client_agreement_v1_agreement_record_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_agreement_v1_agreement_record_api_proto_rawDescData)
	})
	return file_moego_client_agreement_v1_agreement_record_api_proto_rawDescData
}

var file_moego_client_agreement_v1_agreement_record_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_client_agreement_v1_agreement_record_api_proto_goTypes = []interface{}{
	(*GetRecordListParams)(nil),          // 0: moego.client.agreement.v1.GetRecordListParams
	(*GetRecordListResult)(nil),          // 1: moego.client.agreement.v1.GetRecordListResult
	(v1.SignedStatus)(0),                 // 2: moego.models.agreement.v1.SignedStatus
	(*v2.PaginationRequest)(nil),         // 3: moego.utils.v2.PaginationRequest
	(*v1.AgreementRecordSimpleView)(nil), // 4: moego.models.agreement.v1.AgreementRecordSimpleView
	(*v2.PaginationResponse)(nil),        // 5: moego.utils.v2.PaginationResponse
	(*v1.AgreementModelSimpleView)(nil),  // 6: moego.models.agreement.v1.AgreementModelSimpleView
}
var file_moego_client_agreement_v1_agreement_record_api_proto_depIdxs = []int32{
	2, // 0: moego.client.agreement.v1.GetRecordListParams.signed_status:type_name -> moego.models.agreement.v1.SignedStatus
	3, // 1: moego.client.agreement.v1.GetRecordListParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	4, // 2: moego.client.agreement.v1.GetRecordListResult.agreement_record_simple_view:type_name -> moego.models.agreement.v1.AgreementRecordSimpleView
	5, // 3: moego.client.agreement.v1.GetRecordListResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	6, // 4: moego.client.agreement.v1.GetRecordListResult.agreement_simple_view:type_name -> moego.models.agreement.v1.AgreementModelSimpleView
	0, // 5: moego.client.agreement.v1.AgreementRecordService.GetRecordList:input_type -> moego.client.agreement.v1.GetRecordListParams
	1, // 6: moego.client.agreement.v1.AgreementRecordService.GetRecordList:output_type -> moego.client.agreement.v1.GetRecordListResult
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_client_agreement_v1_agreement_record_api_proto_init() }
func file_moego_client_agreement_v1_agreement_record_api_proto_init() {
	if File_moego_client_agreement_v1_agreement_record_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_agreement_v1_agreement_record_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecordListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_agreement_v1_agreement_record_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecordListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_agreement_v1_agreement_record_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetRecordListParams_Name)(nil),
		(*GetRecordListParams_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_agreement_v1_agreement_record_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_agreement_v1_agreement_record_api_proto_goTypes,
		DependencyIndexes: file_moego_client_agreement_v1_agreement_record_api_proto_depIdxs,
		MessageInfos:      file_moego_client_agreement_v1_agreement_record_api_proto_msgTypes,
	}.Build()
	File_moego_client_agreement_v1_agreement_record_api_proto = out.File
	file_moego_client_agreement_v1_agreement_record_api_proto_rawDesc = nil
	file_moego_client_agreement_v1_agreement_record_api_proto_goTypes = nil
	file_moego_client_agreement_v1_agreement_record_api_proto_depIdxs = nil
}
