// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/online_booking/v1/appointment_api.proto

package onlinebookingapipb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The enum for appointment card type
type AppointmentCardType int32

const (
	// unspecified
	AppointmentCardType_APPOINTMENT_CARD_TYPE_UNSPECIFIED AppointmentCardType = 0
	// The last finished appointment
	// status = finished and end_date < today
	AppointmentCardType_LAST AppointmentCardType = 2
	// The pending appointment
	AppointmentCardType_PENDING AppointmentCardType = 4
	// The upcoming appointment
	// status in (unconfirmed, confirmed, checked_in, ready)
	// upcoming: today < start_date
	// today: start_date < today < end_date
	// delayed: today > end_date
	AppointmentCardType_UPCOMING AppointmentCardType = 6
	// The in progress appointment
	// status = checked_in
	AppointmentCardType_IN_PROGRESS AppointmentCardType = 8
)

// Enum value maps for AppointmentCardType.
var (
	AppointmentCardType_name = map[int32]string{
		0: "APPOINTMENT_CARD_TYPE_UNSPECIFIED",
		2: "LAST",
		4: "PENDING",
		6: "UPCOMING",
		8: "IN_PROGRESS",
	}
	AppointmentCardType_value = map[string]int32{
		"APPOINTMENT_CARD_TYPE_UNSPECIFIED": 0,
		"LAST":                              2,
		"PENDING":                           4,
		"UPCOMING":                          6,
		"IN_PROGRESS":                       8,
	}
)

func (x AppointmentCardType) Enum() *AppointmentCardType {
	p := new(AppointmentCardType)
	*p = x
	return p
}

func (x AppointmentCardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentCardType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_client_online_booking_v1_appointment_api_proto_enumTypes[0].Descriptor()
}

func (AppointmentCardType) Type() protoreflect.EnumType {
	return &file_moego_client_online_booking_v1_appointment_api_proto_enumTypes[0]
}

func (x AppointmentCardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentCardType.Descriptor instead.
func (AppointmentCardType) EnumDescriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{0}
}

// The appointment type enum
type ListAppointmentsParams_AppointmentType int32

const (
	// unspecified
	ListAppointmentsParams_APPOINTMENT_TYPE_UNSPECIFIED ListAppointmentsParams_AppointmentType = 0
	// The pending type
	ListAppointmentsParams_PENDING ListAppointmentsParams_AppointmentType = 1
	// The upcoming type
	ListAppointmentsParams_UPCOMING ListAppointmentsParams_AppointmentType = 2
	// The past type
	ListAppointmentsParams_PAST ListAppointmentsParams_AppointmentType = 3
	// The canceled type
	ListAppointmentsParams_CANCELED ListAppointmentsParams_AppointmentType = 4
)

// Enum value maps for ListAppointmentsParams_AppointmentType.
var (
	ListAppointmentsParams_AppointmentType_name = map[int32]string{
		0: "APPOINTMENT_TYPE_UNSPECIFIED",
		1: "PENDING",
		2: "UPCOMING",
		3: "PAST",
		4: "CANCELED",
	}
	ListAppointmentsParams_AppointmentType_value = map[string]int32{
		"APPOINTMENT_TYPE_UNSPECIFIED": 0,
		"PENDING":                      1,
		"UPCOMING":                     2,
		"PAST":                         3,
		"CANCELED":                     4,
	}
)

func (x ListAppointmentsParams_AppointmentType) Enum() *ListAppointmentsParams_AppointmentType {
	p := new(ListAppointmentsParams_AppointmentType)
	*p = x
	return p
}

func (x ListAppointmentsParams_AppointmentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAppointmentsParams_AppointmentType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_client_online_booking_v1_appointment_api_proto_enumTypes[1].Descriptor()
}

func (ListAppointmentsParams_AppointmentType) Type() protoreflect.EnumType {
	return &file_moego_client_online_booking_v1_appointment_api_proto_enumTypes[1]
}

func (x ListAppointmentsParams_AppointmentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAppointmentsParams_AppointmentType.Descriptor instead.
func (ListAppointmentsParams_AppointmentType) EnumDescriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{6, 0}
}

// appointment list sort field
type ListAppointmentsParams_AppointmentSortField int32

const (
	// unspecified
	ListAppointmentsParams_APPOINTMENT_SORT_FIELD_UNSPECIFIED ListAppointmentsParams_AppointmentSortField = 0
	// appointment date and time
	ListAppointmentsParams_DATE_TIME ListAppointmentsParams_AppointmentSortField = 1
)

// Enum value maps for ListAppointmentsParams_AppointmentSortField.
var (
	ListAppointmentsParams_AppointmentSortField_name = map[int32]string{
		0: "APPOINTMENT_SORT_FIELD_UNSPECIFIED",
		1: "DATE_TIME",
	}
	ListAppointmentsParams_AppointmentSortField_value = map[string]int32{
		"APPOINTMENT_SORT_FIELD_UNSPECIFIED": 0,
		"DATE_TIME":                          1,
	}
)

func (x ListAppointmentsParams_AppointmentSortField) Enum() *ListAppointmentsParams_AppointmentSortField {
	p := new(ListAppointmentsParams_AppointmentSortField)
	*p = x
	return p
}

func (x ListAppointmentsParams_AppointmentSortField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAppointmentsParams_AppointmentSortField) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_client_online_booking_v1_appointment_api_proto_enumTypes[2].Descriptor()
}

func (ListAppointmentsParams_AppointmentSortField) Type() protoreflect.EnumType {
	return &file_moego_client_online_booking_v1_appointment_api_proto_enumTypes[2]
}

func (x ListAppointmentsParams_AppointmentSortField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAppointmentsParams_AppointmentSortField.Descriptor instead.
func (ListAppointmentsParams_AppointmentSortField) EnumDescriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{6, 1}
}

// The params message for GetPriorityAppointmentCard
type GetPriorityAppointmentCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetPriorityAppointmentCardParams_Name
	//	*GetPriorityAppointmentCardParams_Domain
	Anonymous isGetPriorityAppointmentCardParams_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *GetPriorityAppointmentCardParams) Reset() {
	*x = GetPriorityAppointmentCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPriorityAppointmentCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPriorityAppointmentCardParams) ProtoMessage() {}

func (x *GetPriorityAppointmentCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPriorityAppointmentCardParams.ProtoReflect.Descriptor instead.
func (*GetPriorityAppointmentCardParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{0}
}

func (m *GetPriorityAppointmentCardParams) GetAnonymous() isGetPriorityAppointmentCardParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetPriorityAppointmentCardParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetPriorityAppointmentCardParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetPriorityAppointmentCardParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetPriorityAppointmentCardParams_Domain); ok {
		return x.Domain
	}
	return ""
}

type isGetPriorityAppointmentCardParams_Anonymous interface {
	isGetPriorityAppointmentCardParams_Anonymous()
}

type GetPriorityAppointmentCardParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetPriorityAppointmentCardParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetPriorityAppointmentCardParams_Name) isGetPriorityAppointmentCardParams_Anonymous() {}

func (*GetPriorityAppointmentCardParams_Domain) isGetPriorityAppointmentCardParams_Anonymous() {}

// The result message for GetPriorityAppointmentCard
type GetPriorityAppointmentCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment card item
	Card *AppointmentCardItem `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
	// priority appointment card item count
	Count int32 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetPriorityAppointmentCardResult) Reset() {
	*x = GetPriorityAppointmentCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPriorityAppointmentCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPriorityAppointmentCardResult) ProtoMessage() {}

func (x *GetPriorityAppointmentCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPriorityAppointmentCardResult.ProtoReflect.Descriptor instead.
func (*GetPriorityAppointmentCardResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetPriorityAppointmentCardResult) GetCard() *AppointmentCardItem {
	if x != nil {
		return x.Card
	}
	return nil
}

func (x *GetPriorityAppointmentCardResult) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// The appointment card item message
type AppointmentCardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment card type
	CardType AppointmentCardType `protobuf:"varint,1,opt,name=card_type,json=cardType,proto3,enum=moego.client.online_booking.v1.AppointmentCardType" json:"card_type,omitempty"`
	// appointment summary item
	Appointment *AppointmentSummaryItem `protobuf:"bytes,2,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// pet and services summary item
	PetAndServices []*PetAndServicesSummaryItem `protobuf:"bytes,3,rep,name=pet_and_services,json=petAndServices,proto3" json:"pet_and_services,omitempty"`
}

func (x *AppointmentCardItem) Reset() {
	*x = AppointmentCardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentCardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentCardItem) ProtoMessage() {}

func (x *AppointmentCardItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentCardItem.ProtoReflect.Descriptor instead.
func (*AppointmentCardItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{2}
}

func (x *AppointmentCardItem) GetCardType() AppointmentCardType {
	if x != nil {
		return x.CardType
	}
	return AppointmentCardType_APPOINTMENT_CARD_TYPE_UNSPECIFIED
}

func (x *AppointmentCardItem) GetAppointment() *AppointmentSummaryItem {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *AppointmentCardItem) GetPetAndServices() []*PetAndServicesSummaryItem {
	if x != nil {
		return x.PetAndServices
	}
	return nil
}

// The appointment summary item message
type AppointmentSummaryItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The booking request id or appointment id
	//
	// Types that are assignable to Id:
	//
	//	*AppointmentSummaryItem_BookingRequestId
	//	*AppointmentSummaryItem_AppointmentId
	Id isAppointmentSummaryItem_Id `protobuf_oneof:"id"`
	// The appointment start date
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// The appointment start time
	StartTime *int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// The appointment end date
	EndDate *string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// The appointment end time
	EndTime *int32 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// The main care type
	MainCareType v1.ServiceItemType `protobuf:"varint,7,opt,name=main_care_type,json=mainCareType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"main_care_type,omitempty"`
	// The flag of is booking request
	IsBookingRequest bool `protobuf:"varint,8,opt,name=is_booking_request,json=isBookingRequest,proto3" json:"is_booking_request,omitempty"`
}

func (x *AppointmentSummaryItem) Reset() {
	*x = AppointmentSummaryItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentSummaryItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentSummaryItem) ProtoMessage() {}

func (x *AppointmentSummaryItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentSummaryItem.ProtoReflect.Descriptor instead.
func (*AppointmentSummaryItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{3}
}

func (m *AppointmentSummaryItem) GetId() isAppointmentSummaryItem_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *AppointmentSummaryItem) GetBookingRequestId() int64 {
	if x, ok := x.GetId().(*AppointmentSummaryItem_BookingRequestId); ok {
		return x.BookingRequestId
	}
	return 0
}

func (x *AppointmentSummaryItem) GetAppointmentId() int64 {
	if x, ok := x.GetId().(*AppointmentSummaryItem_AppointmentId); ok {
		return x.AppointmentId
	}
	return 0
}

func (x *AppointmentSummaryItem) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *AppointmentSummaryItem) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *AppointmentSummaryItem) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *AppointmentSummaryItem) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *AppointmentSummaryItem) GetMainCareType() v1.ServiceItemType {
	if x != nil {
		return x.MainCareType
	}
	return v1.ServiceItemType(0)
}

func (x *AppointmentSummaryItem) GetIsBookingRequest() bool {
	if x != nil {
		return x.IsBookingRequest
	}
	return false
}

type isAppointmentSummaryItem_Id interface {
	isAppointmentSummaryItem_Id()
}

type AppointmentSummaryItem_BookingRequestId struct {
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,1,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof"`
}

type AppointmentSummaryItem_AppointmentId struct {
	// The appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3,oneof"`
}

func (*AppointmentSummaryItem_BookingRequestId) isAppointmentSummaryItem_Id() {}

func (*AppointmentSummaryItem_AppointmentId) isAppointmentSummaryItem_Id() {}

// The pet and service summary item
type PetAndServicesSummaryItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet
	Pet *PetAndServicesSummaryItem_PetItem `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// services
	Services []*PetAndServicesSummaryItem_ServiceItem `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *PetAndServicesSummaryItem) Reset() {
	*x = PetAndServicesSummaryItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetAndServicesSummaryItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetAndServicesSummaryItem) ProtoMessage() {}

func (x *PetAndServicesSummaryItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetAndServicesSummaryItem.ProtoReflect.Descriptor instead.
func (*PetAndServicesSummaryItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{4}
}

func (x *PetAndServicesSummaryItem) GetPet() *PetAndServicesSummaryItem_PetItem {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *PetAndServicesSummaryItem) GetServices() []*PetAndServicesSummaryItem_ServiceItem {
	if x != nil {
		return x.Services
	}
	return nil
}

// The appointment payment item message
type AppointmentPaymentItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The estimated total price
	EstimatedTotalPrice float64 `protobuf:"fixed64,1,opt,name=estimated_total_price,json=estimatedTotalPrice,proto3" json:"estimated_total_price,omitempty"`
	// The total amount
	TotalAmount float64 `protobuf:"fixed64,2,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// The payment status
	PaymentStatus v11.AppointmentPaymentStatus `protobuf:"varint,3,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.appointment.v1.AppointmentPaymentStatus" json:"payment_status,omitempty"`
	// The estimated total price for evaluation
	EvaluationEstimatedTotalPrice float64 `protobuf:"fixed64,4,opt,name=evaluation_estimated_total_price,json=evaluationEstimatedTotalPrice,proto3" json:"evaluation_estimated_total_price,omitempty"`
}

func (x *AppointmentPaymentItem) Reset() {
	*x = AppointmentPaymentItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentPaymentItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentPaymentItem) ProtoMessage() {}

func (x *AppointmentPaymentItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentPaymentItem.ProtoReflect.Descriptor instead.
func (*AppointmentPaymentItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{5}
}

func (x *AppointmentPaymentItem) GetEstimatedTotalPrice() float64 {
	if x != nil {
		return x.EstimatedTotalPrice
	}
	return 0
}

func (x *AppointmentPaymentItem) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *AppointmentPaymentItem) GetPaymentStatus() v11.AppointmentPaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return v11.AppointmentPaymentStatus(0)
}

func (x *AppointmentPaymentItem) GetEvaluationEstimatedTotalPrice() float64 {
	if x != nil {
		return x.EvaluationEstimatedTotalPrice
	}
	return 0
}

// The params message for ListAppointments
type ListAppointmentsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListAppointmentsParams_Name
	//	*ListAppointmentsParams_Domain
	Anonymous isListAppointmentsParams_Anonymous `protobuf_oneof:"anonymous"`
	// The filter
	Filter *ListAppointmentsParams_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// The pagination request
	Pagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// sort by
	Sorts []*ListAppointmentsParams_AppointmentSortDef `protobuf:"bytes,5,rep,name=sorts,proto3" json:"sorts,omitempty"`
}

func (x *ListAppointmentsParams) Reset() {
	*x = ListAppointmentsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsParams) ProtoMessage() {}

func (x *ListAppointmentsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsParams.ProtoReflect.Descriptor instead.
func (*ListAppointmentsParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{6}
}

func (m *ListAppointmentsParams) GetAnonymous() isListAppointmentsParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListAppointmentsParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListAppointmentsParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListAppointmentsParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListAppointmentsParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ListAppointmentsParams) GetFilter() *ListAppointmentsParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAppointmentsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAppointmentsParams) GetSorts() []*ListAppointmentsParams_AppointmentSortDef {
	if x != nil {
		return x.Sorts
	}
	return nil
}

type isListAppointmentsParams_Anonymous interface {
	isListAppointmentsParams_Anonymous()
}

type ListAppointmentsParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListAppointmentsParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListAppointmentsParams_Name) isListAppointmentsParams_Anonymous() {}

func (*ListAppointmentsParams_Domain) isListAppointmentsParams_Anonymous() {}

// The result message for ListAppointments
type ListAppointmentsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointments
	Appointments []*ListAppointmentsResult_AppointmentListItem `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// The pagination response
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListAppointmentsResult) Reset() {
	*x = ListAppointmentsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsResult) ProtoMessage() {}

func (x *ListAppointmentsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsResult.ProtoReflect.Descriptor instead.
func (*ListAppointmentsResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{7}
}

func (x *ListAppointmentsResult) GetAppointments() []*ListAppointmentsResult_AppointmentListItem {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListAppointmentsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// The params message for GetAppointmentDetail
type GetAppointmentDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetAppointmentDetailParams_Name
	//	*GetAppointmentDetailParams_Domain
	Anonymous isGetAppointmentDetailParams_Anonymous `protobuf_oneof:"anonymous"`
	// appointment id or booking request id
	//
	// Types that are assignable to Id:
	//
	//	*GetAppointmentDetailParams_AppointmentId
	//	*GetAppointmentDetailParams_BookingRequestId
	Id isGetAppointmentDetailParams_Id `protobuf_oneof:"id"`
}

func (x *GetAppointmentDetailParams) Reset() {
	*x = GetAppointmentDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentDetailParams) ProtoMessage() {}

func (x *GetAppointmentDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentDetailParams.ProtoReflect.Descriptor instead.
func (*GetAppointmentDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{8}
}

func (m *GetAppointmentDetailParams) GetAnonymous() isGetAppointmentDetailParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetAppointmentDetailParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetAppointmentDetailParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetAppointmentDetailParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetAppointmentDetailParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (m *GetAppointmentDetailParams) GetId() isGetAppointmentDetailParams_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *GetAppointmentDetailParams) GetAppointmentId() int64 {
	if x, ok := x.GetId().(*GetAppointmentDetailParams_AppointmentId); ok {
		return x.AppointmentId
	}
	return 0
}

func (x *GetAppointmentDetailParams) GetBookingRequestId() int64 {
	if x, ok := x.GetId().(*GetAppointmentDetailParams_BookingRequestId); ok {
		return x.BookingRequestId
	}
	return 0
}

type isGetAppointmentDetailParams_Anonymous interface {
	isGetAppointmentDetailParams_Anonymous()
}

type GetAppointmentDetailParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetAppointmentDetailParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetAppointmentDetailParams_Name) isGetAppointmentDetailParams_Anonymous() {}

func (*GetAppointmentDetailParams_Domain) isGetAppointmentDetailParams_Anonymous() {}

type isGetAppointmentDetailParams_Id interface {
	isGetAppointmentDetailParams_Id()
}

type GetAppointmentDetailParams_AppointmentId struct {
	// The appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3,oneof"`
}

type GetAppointmentDetailParams_BookingRequestId struct {
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,4,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof"`
}

func (*GetAppointmentDetailParams_AppointmentId) isGetAppointmentDetailParams_Id() {}

func (*GetAppointmentDetailParams_BookingRequestId) isGetAppointmentDetailParams_Id() {}

// The result message for GetAppointmentDetail
type GetAppointmentDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment detail item
	Appointment *GetAppointmentDetailResult_AppointmentItem `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// pet and services detail item
	PetAndServices []*GetAppointmentDetailResult_PetAndServicesDetailItem `protobuf:"bytes,2,rep,name=pet_and_services,json=petAndServices,proto3" json:"pet_and_services,omitempty"`
	// payment detail item
	Payment *GetAppointmentDetailResult_PaymentItem `protobuf:"bytes,3,opt,name=payment,proto3" json:"payment,omitempty"`
}

func (x *GetAppointmentDetailResult) Reset() {
	*x = GetAppointmentDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentDetailResult) ProtoMessage() {}

func (x *GetAppointmentDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentDetailResult.ProtoReflect.Descriptor instead.
func (*GetAppointmentDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetAppointmentDetailResult) GetAppointment() *GetAppointmentDetailResult_AppointmentItem {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *GetAppointmentDetailResult) GetPetAndServices() []*GetAppointmentDetailResult_PetAndServicesDetailItem {
	if x != nil {
		return x.PetAndServices
	}
	return nil
}

func (x *GetAppointmentDetailResult) GetPayment() *GetAppointmentDetailResult_PaymentItem {
	if x != nil {
		return x.Payment
	}
	return nil
}

// the params message for GetTrainingDetail
type GetGroupClassDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*GetGroupClassDetailParams_Name
	//	*GetGroupClassDetailParams_Domain
	Anonymous isGetGroupClassDetailParams_Anonymous `protobuf_oneof:"anonymous"`
	// appointment id or booking request id
	//
	// Types that are assignable to Id:
	//
	//	*GetGroupClassDetailParams_AppointmentId
	//	*GetGroupClassDetailParams_BookingRequestId
	Id isGetGroupClassDetailParams_Id `protobuf_oneof:"id"`
}

func (x *GetGroupClassDetailParams) Reset() {
	*x = GetGroupClassDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroupClassDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupClassDetailParams) ProtoMessage() {}

func (x *GetGroupClassDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupClassDetailParams.ProtoReflect.Descriptor instead.
func (*GetGroupClassDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{10}
}

func (m *GetGroupClassDetailParams) GetAnonymous() isGetGroupClassDetailParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *GetGroupClassDetailParams) GetName() string {
	if x, ok := x.GetAnonymous().(*GetGroupClassDetailParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *GetGroupClassDetailParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*GetGroupClassDetailParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (m *GetGroupClassDetailParams) GetId() isGetGroupClassDetailParams_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *GetGroupClassDetailParams) GetAppointmentId() int64 {
	if x, ok := x.GetId().(*GetGroupClassDetailParams_AppointmentId); ok {
		return x.AppointmentId
	}
	return 0
}

func (x *GetGroupClassDetailParams) GetBookingRequestId() int64 {
	if x, ok := x.GetId().(*GetGroupClassDetailParams_BookingRequestId); ok {
		return x.BookingRequestId
	}
	return 0
}

type isGetGroupClassDetailParams_Anonymous interface {
	isGetGroupClassDetailParams_Anonymous()
}

type GetGroupClassDetailParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type GetGroupClassDetailParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*GetGroupClassDetailParams_Name) isGetGroupClassDetailParams_Anonymous() {}

func (*GetGroupClassDetailParams_Domain) isGetGroupClassDetailParams_Anonymous() {}

type isGetGroupClassDetailParams_Id interface {
	isGetGroupClassDetailParams_Id()
}

type GetGroupClassDetailParams_AppointmentId struct {
	// The appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3,oneof"`
}

type GetGroupClassDetailParams_BookingRequestId struct {
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,4,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof"`
}

func (*GetGroupClassDetailParams_AppointmentId) isGetGroupClassDetailParams_Id() {}

func (*GetGroupClassDetailParams_BookingRequestId) isGetGroupClassDetailParams_Id() {}

// the result message for GetTrainingDetail
type GetGroupClassDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet
	Pet *GetAppointmentDetailResult_PetItem `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// instance
	GroupClassInstance *v12.GroupClassInstanceView `protobuf:"bytes,2,opt,name=group_class_instance,json=groupClassInstance,proto3" json:"group_class_instance,omitempty"`
	// class
	ServiceModel *v1.ServiceModel `protobuf:"bytes,3,opt,name=service_model,json=serviceModel,proto3" json:"service_model,omitempty"`
}

func (x *GetGroupClassDetailResult) Reset() {
	*x = GetGroupClassDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGroupClassDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupClassDetailResult) ProtoMessage() {}

func (x *GetGroupClassDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupClassDetailResult.ProtoReflect.Descriptor instead.
func (*GetGroupClassDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetGroupClassDetailResult) GetPet() *GetAppointmentDetailResult_PetItem {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *GetGroupClassDetailResult) GetGroupClassInstance() *v12.GroupClassInstanceView {
	if x != nil {
		return x.GroupClassInstance
	}
	return nil
}

func (x *GetGroupClassDetailResult) GetServiceModel() *v1.ServiceModel {
	if x != nil {
		return x.ServiceModel
	}
	return nil
}

// Update pet feeding medication params
type ReschedulePetFeedingMedicationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ReschedulePetFeedingMedicationParams_Name
	//	*ReschedulePetFeedingMedicationParams_Domain
	Anonymous isReschedulePetFeedingMedicationParams_Anonymous `protobuf_oneof:"anonymous"`
	// appointment id or booking request id
	//
	// Types that are assignable to Id:
	//
	//	*ReschedulePetFeedingMedicationParams_AppointmentId
	//	*ReschedulePetFeedingMedicationParams_BookingRequestId
	Id isReschedulePetFeedingMedicationParams_Id `protobuf_oneof:"id"`
	// Pet's schedules
	Schedules []*ReschedulePetFeedingMedicationParams_PetScheduleDef `protobuf:"bytes,5,rep,name=schedules,proto3" json:"schedules,omitempty"`
}

func (x *ReschedulePetFeedingMedicationParams) Reset() {
	*x = ReschedulePetFeedingMedicationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReschedulePetFeedingMedicationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReschedulePetFeedingMedicationParams) ProtoMessage() {}

func (x *ReschedulePetFeedingMedicationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReschedulePetFeedingMedicationParams.ProtoReflect.Descriptor instead.
func (*ReschedulePetFeedingMedicationParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{12}
}

func (m *ReschedulePetFeedingMedicationParams) GetAnonymous() isReschedulePetFeedingMedicationParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ReschedulePetFeedingMedicationParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ReschedulePetFeedingMedicationParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ReschedulePetFeedingMedicationParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ReschedulePetFeedingMedicationParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (m *ReschedulePetFeedingMedicationParams) GetId() isReschedulePetFeedingMedicationParams_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *ReschedulePetFeedingMedicationParams) GetAppointmentId() int64 {
	if x, ok := x.GetId().(*ReschedulePetFeedingMedicationParams_AppointmentId); ok {
		return x.AppointmentId
	}
	return 0
}

func (x *ReschedulePetFeedingMedicationParams) GetBookingRequestId() int64 {
	if x, ok := x.GetId().(*ReschedulePetFeedingMedicationParams_BookingRequestId); ok {
		return x.BookingRequestId
	}
	return 0
}

func (x *ReschedulePetFeedingMedicationParams) GetSchedules() []*ReschedulePetFeedingMedicationParams_PetScheduleDef {
	if x != nil {
		return x.Schedules
	}
	return nil
}

type isReschedulePetFeedingMedicationParams_Anonymous interface {
	isReschedulePetFeedingMedicationParams_Anonymous()
}

type ReschedulePetFeedingMedicationParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ReschedulePetFeedingMedicationParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ReschedulePetFeedingMedicationParams_Name) isReschedulePetFeedingMedicationParams_Anonymous() {
}

func (*ReschedulePetFeedingMedicationParams_Domain) isReschedulePetFeedingMedicationParams_Anonymous() {
}

type isReschedulePetFeedingMedicationParams_Id interface {
	isReschedulePetFeedingMedicationParams_Id()
}

type ReschedulePetFeedingMedicationParams_AppointmentId struct {
	// The appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3,oneof"`
}

type ReschedulePetFeedingMedicationParams_BookingRequestId struct {
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,4,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof"`
}

func (*ReschedulePetFeedingMedicationParams_AppointmentId) isReschedulePetFeedingMedicationParams_Id() {
}

func (*ReschedulePetFeedingMedicationParams_BookingRequestId) isReschedulePetFeedingMedicationParams_Id() {
}

// Update pet feeding medication result
type ReschedulePetFeedingMedicationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReschedulePetFeedingMedicationResult) Reset() {
	*x = ReschedulePetFeedingMedicationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReschedulePetFeedingMedicationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReschedulePetFeedingMedicationResult) ProtoMessage() {}

func (x *ReschedulePetFeedingMedicationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReschedulePetFeedingMedicationResult.ProtoReflect.Descriptor instead.
func (*ReschedulePetFeedingMedicationResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{13}
}

// The params message for CancelAppointment
type CancelAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*CancelAppointmentParams_Name
	//	*CancelAppointmentParams_Domain
	Anonymous isCancelAppointmentParams_Anonymous `protobuf_oneof:"anonymous"`
	// appointment id or booking request id
	//
	// Types that are assignable to Id:
	//
	//	*CancelAppointmentParams_AppointmentId
	//	*CancelAppointmentParams_BookingRequestId
	Id isCancelAppointmentParams_Id `protobuf_oneof:"id"`
}

func (x *CancelAppointmentParams) Reset() {
	*x = CancelAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelAppointmentParams) ProtoMessage() {}

func (x *CancelAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelAppointmentParams.ProtoReflect.Descriptor instead.
func (*CancelAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{14}
}

func (m *CancelAppointmentParams) GetAnonymous() isCancelAppointmentParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *CancelAppointmentParams) GetName() string {
	if x, ok := x.GetAnonymous().(*CancelAppointmentParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *CancelAppointmentParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*CancelAppointmentParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (m *CancelAppointmentParams) GetId() isCancelAppointmentParams_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *CancelAppointmentParams) GetAppointmentId() int64 {
	if x, ok := x.GetId().(*CancelAppointmentParams_AppointmentId); ok {
		return x.AppointmentId
	}
	return 0
}

func (x *CancelAppointmentParams) GetBookingRequestId() int64 {
	if x, ok := x.GetId().(*CancelAppointmentParams_BookingRequestId); ok {
		return x.BookingRequestId
	}
	return 0
}

type isCancelAppointmentParams_Anonymous interface {
	isCancelAppointmentParams_Anonymous()
}

type CancelAppointmentParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type CancelAppointmentParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*CancelAppointmentParams_Name) isCancelAppointmentParams_Anonymous() {}

func (*CancelAppointmentParams_Domain) isCancelAppointmentParams_Anonymous() {}

type isCancelAppointmentParams_Id interface {
	isCancelAppointmentParams_Id()
}

type CancelAppointmentParams_AppointmentId struct {
	// The appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3,oneof"`
}

type CancelAppointmentParams_BookingRequestId struct {
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,4,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof"`
}

func (*CancelAppointmentParams_AppointmentId) isCancelAppointmentParams_Id() {}

func (*CancelAppointmentParams_BookingRequestId) isCancelAppointmentParams_Id() {}

// The result message for CancelAppointment
type CancelAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CancelAppointmentResult) Reset() {
	*x = CancelAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelAppointmentResult) ProtoMessage() {}

func (x *CancelAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelAppointmentResult.ProtoReflect.Descriptor instead.
func (*CancelAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{15}
}

// The params message for UpdateAppointment
type UpdateAppointmentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*UpdateAppointmentParams_Name
	//	*UpdateAppointmentParams_Domain
	Anonymous isUpdateAppointmentParams_Anonymous `protobuf_oneof:"anonymous"`
	// appointment id or booking request id
	//
	// Types that are assignable to Id:
	//
	//	*UpdateAppointmentParams_AppointmentId
	//	*UpdateAppointmentParams_BookingRequestId
	Id isUpdateAppointmentParams_Id `protobuf_oneof:"id"`
	// The params message for UpdatePetServiceDetail
	PetAndServices []*UpdateAppointmentParams_UpdatePetServiceDetailParams `protobuf:"bytes,5,rep,name=pet_and_services,json=petAndServices,proto3" json:"pet_and_services,omitempty"`
}

func (x *UpdateAppointmentParams) Reset() {
	*x = UpdateAppointmentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentParams) ProtoMessage() {}

func (x *UpdateAppointmentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentParams.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{16}
}

func (m *UpdateAppointmentParams) GetAnonymous() isUpdateAppointmentParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *UpdateAppointmentParams) GetName() string {
	if x, ok := x.GetAnonymous().(*UpdateAppointmentParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *UpdateAppointmentParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*UpdateAppointmentParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (m *UpdateAppointmentParams) GetId() isUpdateAppointmentParams_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *UpdateAppointmentParams) GetAppointmentId() int64 {
	if x, ok := x.GetId().(*UpdateAppointmentParams_AppointmentId); ok {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateAppointmentParams) GetBookingRequestId() int64 {
	if x, ok := x.GetId().(*UpdateAppointmentParams_BookingRequestId); ok {
		return x.BookingRequestId
	}
	return 0
}

func (x *UpdateAppointmentParams) GetPetAndServices() []*UpdateAppointmentParams_UpdatePetServiceDetailParams {
	if x != nil {
		return x.PetAndServices
	}
	return nil
}

type isUpdateAppointmentParams_Anonymous interface {
	isUpdateAppointmentParams_Anonymous()
}

type UpdateAppointmentParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type UpdateAppointmentParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*UpdateAppointmentParams_Name) isUpdateAppointmentParams_Anonymous() {}

func (*UpdateAppointmentParams_Domain) isUpdateAppointmentParams_Anonymous() {}

type isUpdateAppointmentParams_Id interface {
	isUpdateAppointmentParams_Id()
}

type UpdateAppointmentParams_AppointmentId struct {
	// The appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3,oneof"`
}

type UpdateAppointmentParams_BookingRequestId struct {
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,4,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof"`
}

func (*UpdateAppointmentParams_AppointmentId) isUpdateAppointmentParams_Id() {}

func (*UpdateAppointmentParams_BookingRequestId) isUpdateAppointmentParams_Id() {}

// The result message for UpdateAppointment
type UpdateAppointmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAppointmentResult) Reset() {
	*x = UpdateAppointmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentResult) ProtoMessage() {}

func (x *UpdateAppointmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentResult.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{17}
}

// check if dates available for reschedule request
type IsAvailableForRescheduleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*IsAvailableForRescheduleParams_Name
	//	*IsAvailableForRescheduleParams_Domain
	Anonymous isIsAvailableForRescheduleParams_Anonymous `protobuf_oneof:"anonymous"`
	// appointment id or booking request id
	//
	// Types that are assignable to Id:
	//
	//	*IsAvailableForRescheduleParams_AppointmentId
	//	*IsAvailableForRescheduleParams_BookingRequestId
	Id isIsAvailableForRescheduleParams_Id `protobuf_oneof:"id"`
	// selected service item type
	ServiceItemType v1.ServiceItemType `protobuf:"varint,5,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// date from
	StartDate *date.Date `protobuf:"bytes,6,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// date to
	EndDate *date.Date `protobuf:"bytes,7,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// do reschedule for this service id
	ServiceId *int64 `protobuf:"varint,8,opt,name=service_id,json=serviceId,proto3,oneof" json:"service_id,omitempty"`
}

func (x *IsAvailableForRescheduleParams) Reset() {
	*x = IsAvailableForRescheduleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsAvailableForRescheduleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsAvailableForRescheduleParams) ProtoMessage() {}

func (x *IsAvailableForRescheduleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsAvailableForRescheduleParams.ProtoReflect.Descriptor instead.
func (*IsAvailableForRescheduleParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{18}
}

func (m *IsAvailableForRescheduleParams) GetAnonymous() isIsAvailableForRescheduleParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *IsAvailableForRescheduleParams) GetName() string {
	if x, ok := x.GetAnonymous().(*IsAvailableForRescheduleParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *IsAvailableForRescheduleParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*IsAvailableForRescheduleParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (m *IsAvailableForRescheduleParams) GetId() isIsAvailableForRescheduleParams_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *IsAvailableForRescheduleParams) GetAppointmentId() int64 {
	if x, ok := x.GetId().(*IsAvailableForRescheduleParams_AppointmentId); ok {
		return x.AppointmentId
	}
	return 0
}

func (x *IsAvailableForRescheduleParams) GetBookingRequestId() int64 {
	if x, ok := x.GetId().(*IsAvailableForRescheduleParams_BookingRequestId); ok {
		return x.BookingRequestId
	}
	return 0
}

func (x *IsAvailableForRescheduleParams) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *IsAvailableForRescheduleParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *IsAvailableForRescheduleParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *IsAvailableForRescheduleParams) GetServiceId() int64 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

type isIsAvailableForRescheduleParams_Anonymous interface {
	isIsAvailableForRescheduleParams_Anonymous()
}

type IsAvailableForRescheduleParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type IsAvailableForRescheduleParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*IsAvailableForRescheduleParams_Name) isIsAvailableForRescheduleParams_Anonymous() {}

func (*IsAvailableForRescheduleParams_Domain) isIsAvailableForRescheduleParams_Anonymous() {}

type isIsAvailableForRescheduleParams_Id interface {
	isIsAvailableForRescheduleParams_Id()
}

type IsAvailableForRescheduleParams_AppointmentId struct {
	// The appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3,oneof"`
}

type IsAvailableForRescheduleParams_BookingRequestId struct {
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,4,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof"`
}

func (*IsAvailableForRescheduleParams_AppointmentId) isIsAvailableForRescheduleParams_Id() {}

func (*IsAvailableForRescheduleParams_BookingRequestId) isIsAvailableForRescheduleParams_Id() {}

// check if dates available for reschedule result
type IsAvailableForRescheduleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is available
	IsAvailable bool `protobuf:"varint,1,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
}

func (x *IsAvailableForRescheduleResult) Reset() {
	*x = IsAvailableForRescheduleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsAvailableForRescheduleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsAvailableForRescheduleResult) ProtoMessage() {}

func (x *IsAvailableForRescheduleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsAvailableForRescheduleResult.ProtoReflect.Descriptor instead.
func (*IsAvailableForRescheduleResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{19}
}

func (x *IsAvailableForRescheduleResult) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

// list evaluations params
type ListEvaluationsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListEvaluationsParams_Name
	//	*ListEvaluationsParams_Domain
	Anonymous isListEvaluationsParams_Anonymous `protobuf_oneof:"anonymous"`
	// The pagination request
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListEvaluationsParams) Reset() {
	*x = ListEvaluationsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationsParams) ProtoMessage() {}

func (x *ListEvaluationsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationsParams.ProtoReflect.Descriptor instead.
func (*ListEvaluationsParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{20}
}

func (m *ListEvaluationsParams) GetAnonymous() isListEvaluationsParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListEvaluationsParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListEvaluationsParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListEvaluationsParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListEvaluationsParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *ListEvaluationsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type isListEvaluationsParams_Anonymous interface {
	isListEvaluationsParams_Anonymous()
}

type ListEvaluationsParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListEvaluationsParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListEvaluationsParams_Name) isListEvaluationsParams_Anonymous() {}

func (*ListEvaluationsParams_Domain) isListEvaluationsParams_Anonymous() {}

// list evaluations results
type ListEvaluationsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointments
	Appointments []*AppointmentSummaryItem `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// The pagination response
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListEvaluationsResult) Reset() {
	*x = ListEvaluationsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationsResult) ProtoMessage() {}

func (x *ListEvaluationsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationsResult.ProtoReflect.Descriptor instead.
func (*ListEvaluationsResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{21}
}

func (x *ListEvaluationsResult) GetAppointments() []*AppointmentSummaryItem {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListEvaluationsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// The pet item
type PetAndServicesSummaryItem_PetItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// The pet type
	PetType v13.PetType `protobuf:"varint,3,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// The avatar path
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
}

func (x *PetAndServicesSummaryItem_PetItem) Reset() {
	*x = PetAndServicesSummaryItem_PetItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetAndServicesSummaryItem_PetItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetAndServicesSummaryItem_PetItem) ProtoMessage() {}

func (x *PetAndServicesSummaryItem_PetItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetAndServicesSummaryItem_PetItem.ProtoReflect.Descriptor instead.
func (*PetAndServicesSummaryItem_PetItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{4, 0}
}

func (x *PetAndServicesSummaryItem_PetItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetAndServicesSummaryItem_PetItem) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *PetAndServicesSummaryItem_PetItem) GetPetType() v13.PetType {
	if x != nil {
		return x.PetType
	}
	return v13.PetType(0)
}

func (x *PetAndServicesSummaryItem_PetItem) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

// The service item
type PetAndServicesSummaryItem_ServiceItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The service name
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// The care type
	CareType v1.ServiceItemType `protobuf:"varint,3,opt,name=care_type,json=careType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"care_type,omitempty"`
	// The service start date
	StartDate *date.Date `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// The service start time
	StartTime *int32 `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// The service end date
	EndDate *date.Date `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// The service end time
	EndTime *int32 `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
}

func (x *PetAndServicesSummaryItem_ServiceItem) Reset() {
	*x = PetAndServicesSummaryItem_ServiceItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetAndServicesSummaryItem_ServiceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetAndServicesSummaryItem_ServiceItem) ProtoMessage() {}

func (x *PetAndServicesSummaryItem_ServiceItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetAndServicesSummaryItem_ServiceItem.ProtoReflect.Descriptor instead.
func (*PetAndServicesSummaryItem_ServiceItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{4, 1}
}

func (x *PetAndServicesSummaryItem_ServiceItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetAndServicesSummaryItem_ServiceItem) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *PetAndServicesSummaryItem_ServiceItem) GetCareType() v1.ServiceItemType {
	if x != nil {
		return x.CareType
	}
	return v1.ServiceItemType(0)
}

func (x *PetAndServicesSummaryItem_ServiceItem) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *PetAndServicesSummaryItem_ServiceItem) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *PetAndServicesSummaryItem_ServiceItem) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *PetAndServicesSummaryItem_ServiceItem) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

// The filter message
type ListAppointmentsParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The appointment type
	AppointmentType ListAppointmentsParams_AppointmentType `protobuf:"varint,1,opt,name=appointment_type,json=appointmentType,proto3,enum=moego.client.online_booking.v1.ListAppointmentsParams_AppointmentType" json:"appointment_type,omitempty"`
}

func (x *ListAppointmentsParams_Filter) Reset() {
	*x = ListAppointmentsParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsParams_Filter) ProtoMessage() {}

func (x *ListAppointmentsParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsParams_Filter.ProtoReflect.Descriptor instead.
func (*ListAppointmentsParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ListAppointmentsParams_Filter) GetAppointmentType() ListAppointmentsParams_AppointmentType {
	if x != nil {
		return x.AppointmentType
	}
	return ListAppointmentsParams_APPOINTMENT_TYPE_UNSPECIFIED
}

// appointment list sort definition
type ListAppointmentsParams_AppointmentSortDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sort field
	Field ListAppointmentsParams_AppointmentSortField `protobuf:"varint,1,opt,name=field,proto3,enum=moego.client.online_booking.v1.ListAppointmentsParams_AppointmentSortField" json:"field,omitempty"`
	// sort asc or desc
	Asc bool `protobuf:"varint,2,opt,name=asc,proto3" json:"asc,omitempty"`
}

func (x *ListAppointmentsParams_AppointmentSortDef) Reset() {
	*x = ListAppointmentsParams_AppointmentSortDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsParams_AppointmentSortDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsParams_AppointmentSortDef) ProtoMessage() {}

func (x *ListAppointmentsParams_AppointmentSortDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsParams_AppointmentSortDef.ProtoReflect.Descriptor instead.
func (*ListAppointmentsParams_AppointmentSortDef) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{6, 1}
}

func (x *ListAppointmentsParams_AppointmentSortDef) GetField() ListAppointmentsParams_AppointmentSortField {
	if x != nil {
		return x.Field
	}
	return ListAppointmentsParams_APPOINTMENT_SORT_FIELD_UNSPECIFIED
}

func (x *ListAppointmentsParams_AppointmentSortDef) GetAsc() bool {
	if x != nil {
		return x.Asc
	}
	return false
}

// The appointment list item message
type ListAppointmentsResult_AppointmentListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment summary item
	Appointment *AppointmentSummaryItem `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// pet and services summary item
	PetAndServices []*PetAndServicesSummaryItem `protobuf:"bytes,2,rep,name=pet_and_services,json=petAndServices,proto3" json:"pet_and_services,omitempty"`
	// payment info
	Payment *AppointmentPaymentItem `protobuf:"bytes,3,opt,name=payment,proto3" json:"payment,omitempty"`
}

func (x *ListAppointmentsResult_AppointmentListItem) Reset() {
	*x = ListAppointmentsResult_AppointmentListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsResult_AppointmentListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsResult_AppointmentListItem) ProtoMessage() {}

func (x *ListAppointmentsResult_AppointmentListItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsResult_AppointmentListItem.ProtoReflect.Descriptor instead.
func (*ListAppointmentsResult_AppointmentListItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ListAppointmentsResult_AppointmentListItem) GetAppointment() *AppointmentSummaryItem {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *ListAppointmentsResult_AppointmentListItem) GetPetAndServices() []*PetAndServicesSummaryItem {
	if x != nil {
		return x.PetAndServices
	}
	return nil
}

func (x *ListAppointmentsResult_AppointmentListItem) GetPayment() *AppointmentPaymentItem {
	if x != nil {
		return x.Payment
	}
	return nil
}

// The appointment item message
type GetAppointmentDetailResult_AppointmentItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The appointment id or booking request id
	//
	// Types that are assignable to Id:
	//
	//	*GetAppointmentDetailResult_AppointmentItem_AppointmentId
	//	*GetAppointmentDetailResult_AppointmentItem_BookingRequestId
	Id isGetAppointmentDetailResult_AppointmentItem_Id `protobuf_oneof:"id"`
	// The appointment start date, boarding/daycare arrive date
	StartDate string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// The appointment start time, boarding/daycare arrive time
	StartTime int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The appointment end date, boarding/daycare pickup date
	EndDate string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// The appointment end time, boarding/daycare pickup time
	EndTime int32 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// The main care type
	MainCareType v1.ServiceItemType `protobuf:"varint,7,opt,name=main_care_type,json=mainCareType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"main_care_type,omitempty"`
	// The flag of is booking request
	IsBookingRequest bool `protobuf:"varint,8,opt,name=is_booking_request,json=isBookingRequest,proto3" json:"is_booking_request,omitempty"`
	// The check-in and check-out date time
	// There is only one check-in and check-out time for boarding, grooming and evaluation
	// There are multiple check-in and check-out time for daycare
	CheckInOutDateTimes []*GetAppointmentDetailResult_CheckInOutDateTime `protobuf:"bytes,9,rep,name=check_in_out_date_times,json=checkInOutDateTimes,proto3" json:"check_in_out_date_times,omitempty"`
	// appointment status. For appointment only
	AppointmentStatus *v11.AppointmentStatus `protobuf:"varint,10,opt,name=appointment_status,json=appointmentStatus,proto3,enum=moego.models.appointment.v1.AppointmentStatus,oneof" json:"appointment_status,omitempty"`
}

func (x *GetAppointmentDetailResult_AppointmentItem) Reset() {
	*x = GetAppointmentDetailResult_AppointmentItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentDetailResult_AppointmentItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentDetailResult_AppointmentItem) ProtoMessage() {}

func (x *GetAppointmentDetailResult_AppointmentItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentDetailResult_AppointmentItem.ProtoReflect.Descriptor instead.
func (*GetAppointmentDetailResult_AppointmentItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{9, 0}
}

func (m *GetAppointmentDetailResult_AppointmentItem) GetId() isGetAppointmentDetailResult_AppointmentItem_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetAppointmentId() int64 {
	if x, ok := x.GetId().(*GetAppointmentDetailResult_AppointmentItem_AppointmentId); ok {
		return x.AppointmentId
	}
	return 0
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetBookingRequestId() int64 {
	if x, ok := x.GetId().(*GetAppointmentDetailResult_AppointmentItem_BookingRequestId); ok {
		return x.BookingRequestId
	}
	return 0
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetMainCareType() v1.ServiceItemType {
	if x != nil {
		return x.MainCareType
	}
	return v1.ServiceItemType(0)
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetIsBookingRequest() bool {
	if x != nil {
		return x.IsBookingRequest
	}
	return false
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetCheckInOutDateTimes() []*GetAppointmentDetailResult_CheckInOutDateTime {
	if x != nil {
		return x.CheckInOutDateTimes
	}
	return nil
}

func (x *GetAppointmentDetailResult_AppointmentItem) GetAppointmentStatus() v11.AppointmentStatus {
	if x != nil && x.AppointmentStatus != nil {
		return *x.AppointmentStatus
	}
	return v11.AppointmentStatus(0)
}

type isGetAppointmentDetailResult_AppointmentItem_Id interface {
	isGetAppointmentDetailResult_AppointmentItem_Id()
}

type GetAppointmentDetailResult_AppointmentItem_AppointmentId struct {
	// The appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3,oneof"`
}

type GetAppointmentDetailResult_AppointmentItem_BookingRequestId struct {
	// The booking request id
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3,oneof"`
}

func (*GetAppointmentDetailResult_AppointmentItem_AppointmentId) isGetAppointmentDetailResult_AppointmentItem_Id() {
}

func (*GetAppointmentDetailResult_AppointmentItem_BookingRequestId) isGetAppointmentDetailResult_AppointmentItem_Id() {
}

// The check-in and check-out date time message
type GetAppointmentDetailResult_CheckInOutDateTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The check-in date
	CheckInDate string `protobuf:"bytes,1,opt,name=check_in_date,json=checkInDate,proto3" json:"check_in_date,omitempty"`
	// The check-in time
	CheckInTime int32 `protobuf:"varint,2,opt,name=check_in_time,json=checkInTime,proto3" json:"check_in_time,omitempty"`
	// The check-out date
	CheckOutDate string `protobuf:"bytes,3,opt,name=check_out_date,json=checkOutDate,proto3" json:"check_out_date,omitempty"`
	// The check-out time
	CheckOutTime int32 `protobuf:"varint,4,opt,name=check_out_time,json=checkOutTime,proto3" json:"check_out_time,omitempty"`
}

func (x *GetAppointmentDetailResult_CheckInOutDateTime) Reset() {
	*x = GetAppointmentDetailResult_CheckInOutDateTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentDetailResult_CheckInOutDateTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentDetailResult_CheckInOutDateTime) ProtoMessage() {}

func (x *GetAppointmentDetailResult_CheckInOutDateTime) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentDetailResult_CheckInOutDateTime.ProtoReflect.Descriptor instead.
func (*GetAppointmentDetailResult_CheckInOutDateTime) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{9, 1}
}

func (x *GetAppointmentDetailResult_CheckInOutDateTime) GetCheckInDate() string {
	if x != nil {
		return x.CheckInDate
	}
	return ""
}

func (x *GetAppointmentDetailResult_CheckInOutDateTime) GetCheckInTime() int32 {
	if x != nil {
		return x.CheckInTime
	}
	return 0
}

func (x *GetAppointmentDetailResult_CheckInOutDateTime) GetCheckOutDate() string {
	if x != nil {
		return x.CheckOutDate
	}
	return ""
}

func (x *GetAppointmentDetailResult_CheckInOutDateTime) GetCheckOutTime() int32 {
	if x != nil {
		return x.CheckOutTime
	}
	return 0
}

// The pet and service detail item
type GetAppointmentDetailResult_PetAndServicesDetailItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet
	Pet *GetAppointmentDetailResult_PetItem `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// services detail items
	Services []*GetAppointmentDetailResult_ServiceDetailItem `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	// add-ons detail items
	AddOns []*GetAppointmentDetailResult_AddOnDetailItem `protobuf:"bytes,3,rep,name=add_ons,json=addOns,proto3" json:"add_ons,omitempty"`
}

func (x *GetAppointmentDetailResult_PetAndServicesDetailItem) Reset() {
	*x = GetAppointmentDetailResult_PetAndServicesDetailItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentDetailResult_PetAndServicesDetailItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentDetailResult_PetAndServicesDetailItem) ProtoMessage() {}

func (x *GetAppointmentDetailResult_PetAndServicesDetailItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentDetailResult_PetAndServicesDetailItem.ProtoReflect.Descriptor instead.
func (*GetAppointmentDetailResult_PetAndServicesDetailItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{9, 2}
}

func (x *GetAppointmentDetailResult_PetAndServicesDetailItem) GetPet() *GetAppointmentDetailResult_PetItem {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *GetAppointmentDetailResult_PetAndServicesDetailItem) GetServices() []*GetAppointmentDetailResult_ServiceDetailItem {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *GetAppointmentDetailResult_PetAndServicesDetailItem) GetAddOns() []*GetAppointmentDetailResult_AddOnDetailItem {
	if x != nil {
		return x.AddOns
	}
	return nil
}

// The pet item
type GetAppointmentDetailResult_PetItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The pet id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// The pet type
	PetType v13.PetType `protobuf:"varint,3,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// The pet breed
	Breed string `protobuf:"bytes,4,opt,name=breed,proto3" json:"breed,omitempty"`
	// The avatar path
	AvatarPath string `protobuf:"bytes,5,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// vet name
	VetName string `protobuf:"bytes,6,opt,name=vet_name,json=vetName,proto3" json:"vet_name,omitempty"`
	// vet phone number
	VetPhoneNumber string `protobuf:"bytes,7,opt,name=vet_phone_number,json=vetPhoneNumber,proto3" json:"vet_phone_number,omitempty"`
	// vet address
	VetAddress string `protobuf:"bytes,8,opt,name=vet_address,json=vetAddress,proto3" json:"vet_address,omitempty"`
	// emergency contact name
	EmergencyContactName string `protobuf:"bytes,9,opt,name=emergency_contact_name,json=emergencyContactName,proto3" json:"emergency_contact_name,omitempty"`
	// emergency contact phone number
	EmergencyContactPhoneNumber string `protobuf:"bytes,10,opt,name=emergency_contact_phone_number,json=emergencyContactPhoneNumber,proto3" json:"emergency_contact_phone_number,omitempty"`
}

func (x *GetAppointmentDetailResult_PetItem) Reset() {
	*x = GetAppointmentDetailResult_PetItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentDetailResult_PetItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentDetailResult_PetItem) ProtoMessage() {}

func (x *GetAppointmentDetailResult_PetItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentDetailResult_PetItem.ProtoReflect.Descriptor instead.
func (*GetAppointmentDetailResult_PetItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{9, 3}
}

func (x *GetAppointmentDetailResult_PetItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAppointmentDetailResult_PetItem) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *GetAppointmentDetailResult_PetItem) GetPetType() v13.PetType {
	if x != nil {
		return x.PetType
	}
	return v13.PetType(0)
}

func (x *GetAppointmentDetailResult_PetItem) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *GetAppointmentDetailResult_PetItem) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *GetAppointmentDetailResult_PetItem) GetVetName() string {
	if x != nil {
		return x.VetName
	}
	return ""
}

func (x *GetAppointmentDetailResult_PetItem) GetVetPhoneNumber() string {
	if x != nil {
		return x.VetPhoneNumber
	}
	return ""
}

func (x *GetAppointmentDetailResult_PetItem) GetVetAddress() string {
	if x != nil {
		return x.VetAddress
	}
	return ""
}

func (x *GetAppointmentDetailResult_PetItem) GetEmergencyContactName() string {
	if x != nil {
		return x.EmergencyContactName
	}
	return ""
}

func (x *GetAppointmentDetailResult_PetItem) GetEmergencyContactPhoneNumber() string {
	if x != nil {
		return x.EmergencyContactPhoneNumber
	}
	return ""
}

// The service detail item message
type GetAppointmentDetailResult_ServiceDetailItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The service name
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// The care type
	CareType v1.ServiceItemType `protobuf:"varint,3,opt,name=care_type,json=careType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"care_type,omitempty"`
	// pet detail id
	PetDetailId int64 `protobuf:"varint,4,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// The add-on date type
	DateType v11.PetDetailDateType `protobuf:"varint,5,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType" json:"date_type,omitempty"`
	// The specific dates
	SpecificDates []string `protobuf:"bytes,6,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// The service start date
	StartDate *string `protobuf:"bytes,7,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// The service start time
	StartTime *int32 `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// The service end date
	EndDate *string `protobuf:"bytes,9,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// The service end time
	EndTime *int32 `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// feeding instruction
	Feedings []*v11.AppointmentPetFeedingScheduleDef `protobuf:"bytes,11,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// medication instruction
	Medications []*v11.AppointmentPetMedicationScheduleDef `protobuf:"bytes,12,rep,name=medications,proto3" json:"medications,omitempty"`
	// max duration (only for daycare service)
	MaxDuration *int32 `protobuf:"varint,13,opt,name=max_duration,json=maxDuration,proto3,oneof" json:"max_duration,omitempty"`
	// service time, in minutes
	ServiceTime *int32 `protobuf:"varint,14,opt,name=service_time,json=serviceTime,proto3,oneof" json:"service_time,omitempty"`
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) Reset() {
	*x = GetAppointmentDetailResult_ServiceDetailItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentDetailResult_ServiceDetailItem) ProtoMessage() {}

func (x *GetAppointmentDetailResult_ServiceDetailItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentDetailResult_ServiceDetailItem.ProtoReflect.Descriptor instead.
func (*GetAppointmentDetailResult_ServiceDetailItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{9, 4}
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetCareType() v1.ServiceItemType {
	if x != nil {
		return x.CareType
	}
	return v1.ServiceItemType(0)
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetDateType() v11.PetDetailDateType {
	if x != nil {
		return x.DateType
	}
	return v11.PetDetailDateType(0)
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetFeedings() []*v11.AppointmentPetFeedingScheduleDef {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetMedications() []*v11.AppointmentPetMedicationScheduleDef {
	if x != nil {
		return x.Medications
	}
	return nil
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetMaxDuration() int32 {
	if x != nil && x.MaxDuration != nil {
		return *x.MaxDuration
	}
	return 0
}

func (x *GetAppointmentDetailResult_ServiceDetailItem) GetServiceTime() int32 {
	if x != nil && x.ServiceTime != nil {
		return *x.ServiceTime
	}
	return 0
}

// The add-on detail item message
type GetAppointmentDetailResult_AddOnDetailItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The add-on id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The add-on name
	AddOnName string `protobuf:"bytes,2,opt,name=add_on_name,json=addOnName,proto3" json:"add_on_name,omitempty"`
	// The add-on date type
	DateType v11.PetDetailDateType `protobuf:"varint,3,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType" json:"date_type,omitempty"`
	// The specific dates
	SpecificDates []string `protobuf:"bytes,4,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// The care type
	CareType v1.ServiceItemType `protobuf:"varint,5,opt,name=care_type,json=careType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"care_type,omitempty"`
	// pet detail id
	PetDetailId int64 `protobuf:"varint,6,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// The service start time
	StartTime *int32 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// The service end time
	EndTime *int32 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// quantity per day
	QuantityPerDay *int32 `protobuf:"varint,9,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
	// service time, in minutes
	ServiceTime *int32 `protobuf:"varint,10,opt,name=service_time,json=serviceTime,proto3,oneof" json:"service_time,omitempty"`
	// if require dedicated staff
	RequireDedicatedStaff bool `protobuf:"varint,11,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// start date
	// dateType 为 PET_DETAIL_DATE_DATE_POINT 时，start_date 有值
	StartDate *string `protobuf:"bytes,12,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) Reset() {
	*x = GetAppointmentDetailResult_AddOnDetailItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentDetailResult_AddOnDetailItem) ProtoMessage() {}

func (x *GetAppointmentDetailResult_AddOnDetailItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentDetailResult_AddOnDetailItem.ProtoReflect.Descriptor instead.
func (*GetAppointmentDetailResult_AddOnDetailItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{9, 5}
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetAddOnName() string {
	if x != nil {
		return x.AddOnName
	}
	return ""
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetDateType() v11.PetDetailDateType {
	if x != nil {
		return x.DateType
	}
	return v11.PetDetailDateType(0)
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetCareType() v1.ServiceItemType {
	if x != nil {
		return x.CareType
	}
	return v1.ServiceItemType(0)
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetServiceTime() int32 {
	if x != nil && x.ServiceTime != nil {
		return *x.ServiceTime
	}
	return 0
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *GetAppointmentDetailResult_AddOnDetailItem) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

// The payment item
type GetAppointmentDetailResult_PaymentItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The estimated total price
	EstimatedTotalPrice float64 `protobuf:"fixed64,1,opt,name=estimated_total_price,json=estimatedTotalPrice,proto3" json:"estimated_total_price,omitempty"`
	// The total amount
	TotalAmount float64 `protobuf:"fixed64,2,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// The payment status
	PaymentStatus v11.AppointmentPaymentStatus `protobuf:"varint,3,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.appointment.v1.AppointmentPaymentStatus" json:"payment_status,omitempty"`
	// paid amount
	PaidAmount *float64 `protobuf:"fixed64,4,opt,name=paid_amount,json=paidAmount,proto3,oneof" json:"paid_amount,omitempty"`
	// pre pay amount
	PrePayAmount *float64 `protobuf:"fixed64,5,opt,name=pre_pay_amount,json=prePayAmount,proto3,oneof" json:"pre_pay_amount,omitempty"`
	// pre auth enable
	PreAuthEnable *bool `protobuf:"varint,6,opt,name=pre_auth_enable,json=preAuthEnable,proto3,oneof" json:"pre_auth_enable,omitempty"`
	// The estimated total price for evaluation
	EvaluationEstimatedTotalPrice float64 `protobuf:"fixed64,7,opt,name=evaluation_estimated_total_price,json=evaluationEstimatedTotalPrice,proto3" json:"evaluation_estimated_total_price,omitempty"`
}

func (x *GetAppointmentDetailResult_PaymentItem) Reset() {
	*x = GetAppointmentDetailResult_PaymentItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentDetailResult_PaymentItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentDetailResult_PaymentItem) ProtoMessage() {}

func (x *GetAppointmentDetailResult_PaymentItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentDetailResult_PaymentItem.ProtoReflect.Descriptor instead.
func (*GetAppointmentDetailResult_PaymentItem) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{9, 6}
}

func (x *GetAppointmentDetailResult_PaymentItem) GetEstimatedTotalPrice() float64 {
	if x != nil {
		return x.EstimatedTotalPrice
	}
	return 0
}

func (x *GetAppointmentDetailResult_PaymentItem) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *GetAppointmentDetailResult_PaymentItem) GetPaymentStatus() v11.AppointmentPaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return v11.AppointmentPaymentStatus(0)
}

func (x *GetAppointmentDetailResult_PaymentItem) GetPaidAmount() float64 {
	if x != nil && x.PaidAmount != nil {
		return *x.PaidAmount
	}
	return 0
}

func (x *GetAppointmentDetailResult_PaymentItem) GetPrePayAmount() float64 {
	if x != nil && x.PrePayAmount != nil {
		return *x.PrePayAmount
	}
	return 0
}

func (x *GetAppointmentDetailResult_PaymentItem) GetPreAuthEnable() bool {
	if x != nil && x.PreAuthEnable != nil {
		return *x.PreAuthEnable
	}
	return false
}

func (x *GetAppointmentDetailResult_PaymentItem) GetEvaluationEstimatedTotalPrice() float64 {
	if x != nil {
		return x.EvaluationEstimatedTotalPrice
	}
	return 0
}

// Pet's feeding and medication schedules
type ReschedulePetFeedingMedicationParams_PetScheduleDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet detail id
	PetDetailId int64 `protobuf:"varint,1,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// The care type
	CareType v1.ServiceItemType `protobuf:"varint,2,opt,name=care_type,json=careType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"care_type,omitempty"`
	// feeding instruction
	Feedings []*v11.AppointmentPetFeedingScheduleDef `protobuf:"bytes,3,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// medication instruction
	Medications []*v11.AppointmentPetMedicationScheduleDef `protobuf:"bytes,4,rep,name=medications,proto3" json:"medications,omitempty"`
}

func (x *ReschedulePetFeedingMedicationParams_PetScheduleDef) Reset() {
	*x = ReschedulePetFeedingMedicationParams_PetScheduleDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReschedulePetFeedingMedicationParams_PetScheduleDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReschedulePetFeedingMedicationParams_PetScheduleDef) ProtoMessage() {}

func (x *ReschedulePetFeedingMedicationParams_PetScheduleDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReschedulePetFeedingMedicationParams_PetScheduleDef.ProtoReflect.Descriptor instead.
func (*ReschedulePetFeedingMedicationParams_PetScheduleDef) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{12, 0}
}

func (x *ReschedulePetFeedingMedicationParams_PetScheduleDef) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *ReschedulePetFeedingMedicationParams_PetScheduleDef) GetCareType() v1.ServiceItemType {
	if x != nil {
		return x.CareType
	}
	return v1.ServiceItemType(0)
}

func (x *ReschedulePetFeedingMedicationParams_PetScheduleDef) GetFeedings() []*v11.AppointmentPetFeedingScheduleDef {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *ReschedulePetFeedingMedicationParams_PetScheduleDef) GetMedications() []*v11.AppointmentPetMedicationScheduleDef {
	if x != nil {
		return x.Medications
	}
	return nil
}

// The params message for UpdatePetServiceDetail
type UpdateAppointmentParams_UpdatePetServiceDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// the params message for UpdateServiceDetail
	Services []*UpdateAppointmentParams_UpdateServiceDetailParams `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
	// the params message for UpdateAddOnDetail
	AddOns []*UpdateAppointmentParams_UpdateAddOnDetailParams `protobuf:"bytes,3,rep,name=add_ons,json=addOns,proto3" json:"add_ons,omitempty"`
}

func (x *UpdateAppointmentParams_UpdatePetServiceDetailParams) Reset() {
	*x = UpdateAppointmentParams_UpdatePetServiceDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentParams_UpdatePetServiceDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentParams_UpdatePetServiceDetailParams) ProtoMessage() {}

func (x *UpdateAppointmentParams_UpdatePetServiceDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentParams_UpdatePetServiceDetailParams.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentParams_UpdatePetServiceDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{16, 0}
}

func (x *UpdateAppointmentParams_UpdatePetServiceDetailParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *UpdateAppointmentParams_UpdatePetServiceDetailParams) GetServices() []*UpdateAppointmentParams_UpdateServiceDetailParams {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *UpdateAppointmentParams_UpdatePetServiceDetailParams) GetAddOns() []*UpdateAppointmentParams_UpdateAddOnDetailParams {
	if x != nil {
		return x.AddOns
	}
	return nil
}

// The service detail item message
type UpdateAppointmentParams_UpdateServiceDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The care type. Non-updatable fields
	CareType v1.ServiceItemType `protobuf:"varint,1,opt,name=care_type,json=careType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"care_type,omitempty"`
	// pet detail id. Non-updatable fields
	PetDetailId int64 `protobuf:"varint,2,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// The add-on date type. It is required for update date/time
	DateType *v11.PetDetailDateType `protobuf:"varint,3,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
	// The specific dates. Valid only when date_type is PET_DETAIL_DATE_SPECIFIC_DATE
	SpecificDates []string `protobuf:"bytes,4,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// The service start date
	StartDate *string `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// The service start time
	StartTime *int32 `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// The service end date
	EndDate *string `protobuf:"bytes,7,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// The service end time
	EndTime *int32 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// quantity per day
	QuantityPerDay *int32 `protobuf:"varint,9,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) Reset() {
	*x = UpdateAppointmentParams_UpdateServiceDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentParams_UpdateServiceDetailParams) ProtoMessage() {}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentParams_UpdateServiceDetailParams.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentParams_UpdateServiceDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{16, 1}
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) GetCareType() v1.ServiceItemType {
	if x != nil {
		return x.CareType
	}
	return v1.ServiceItemType(0)
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) GetDateType() v11.PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v11.PetDetailDateType(0)
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *UpdateAppointmentParams_UpdateServiceDetailParams) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

// The add-on detail item message
type UpdateAppointmentParams_UpdateAddOnDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The care type. Non-updatable fields
	CareType v1.ServiceItemType `protobuf:"varint,1,opt,name=care_type,json=careType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"care_type,omitempty"`
	// pet detail id. Non-updatable fields
	PetDetailId int64 `protobuf:"varint,2,opt,name=pet_detail_id,json=petDetailId,proto3" json:"pet_detail_id,omitempty"`
	// The add-on date type. It is required for update date/time
	DateType *v11.PetDetailDateType `protobuf:"varint,3,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
	// The specific dates. Valid only when date_type is valid
	SpecificDates []string `protobuf:"bytes,4,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// The service start time
	StartTime *int32 `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3,oneof" json:"start_time,omitempty"`
	// The service end time
	EndTime *int32 `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3,oneof" json:"end_time,omitempty"`
	// quantity per day
	QuantityPerDay *int32 `protobuf:"varint,7,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
	// start date
	// dateType 为 PET_DETAIL_DATE_DATE_POINT 时，start_date 有值
	StartDate *string `protobuf:"bytes,8,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) Reset() {
	*x = UpdateAppointmentParams_UpdateAddOnDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentParams_UpdateAddOnDetailParams) ProtoMessage() {}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentParams_UpdateAddOnDetailParams.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentParams_UpdateAddOnDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP(), []int{16, 2}
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) GetCareType() v1.ServiceItemType {
	if x != nil {
		return x.CareType
	}
	return v1.ServiceItemType(0)
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) GetPetDetailId() int64 {
	if x != nil {
		return x.PetDetailId
	}
	return 0
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) GetDateType() v11.PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v11.PetDetailDateType(0)
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) GetSpecificDates() []string {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) GetStartTime() int32 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) GetEndTime() int32 {
	if x != nil && x.EndTime != nil {
		return *x.EndTime
	}
	return 0
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

func (x *UpdateAppointmentParams_UpdateAddOnDetailParams) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

var File_moego_client_online_booking_v1_appointment_api_proto protoreflect.FileDescriptor

var file_moego_client_online_booking_v1_appointment_api_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x47, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x66, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x64, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42,
	0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42,
	0x01, 0x22, 0x81, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x47, 0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xa6, 0x02, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x50, 0x0a,
	0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x58, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x63, 0x0a, 0x10, 0x70, 0x65, 0x74,
	0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e,
	0x70, 0x65, 0x74, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0xb6,
	0x03, 0x0a, 0x16, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a, 0x0e, 0x6d, 0x61,
	0x69, 0x6e, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6d,
	0x61, 0x69, 0x6e, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x69,
	0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x04, 0x0a, 0x02, 0x69, 0x64, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xda, 0x05, 0x0a, 0x19, 0x50, 0x65, 0x74, 0x41,
	0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x53, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x50, 0x65,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x61, 0x0a, 0x08, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65,
	0x74, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x1a, 0x93, 0x01,
	0x0a, 0x07, 0x50, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50,
	0x61, 0x74, 0x68, 0x1a, 0xee, 0x02, 0x0a, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x02,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x48, 0x03,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x22, 0x96, 0x02, 0x0a, 0x16, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x32, 0x0a, 0x15, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13,
	0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5c, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x20, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1d,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0xa2, 0x06,
	0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x55, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x5f, 0x0a, 0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66, 0x52, 0x05, 0x73, 0x6f,
	0x72, 0x74, 0x73, 0x1a, 0x87, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x7d,
	0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x89, 0x01,
	0x0a, 0x12, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x72,
	0x74, 0x44, 0x65, 0x66, 0x12, 0x61, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x73, 0x63, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x61, 0x73, 0x63, 0x22, 0x66, 0x0a, 0x0f, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c,
	0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x55,
	0x50, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x53,
	0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10,
	0x04, 0x22, 0x4d, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x50, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01,
	0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8,
	0x42, 0x01, 0x22, 0xf5, 0x03, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6e, 0x0a,
	0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x42, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x1a, 0xa6, 0x02, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x58, 0x0a, 0x0b, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x63, 0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x65, 0x74, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x41, 0x6e, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xbd, 0x01, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x27, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x01, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01,
	0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x42, 0x04, 0x0a, 0x02, 0x69, 0x64, 0x22, 0x95, 0x1d, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6c, 0x0a, 0x0b, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x7d, 0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x61,
	0x6e, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65,
	0x74, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x41, 0x6e, 0x64, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x60, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0xe4, 0x04, 0x0a, 0x0f, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x27, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x00, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4f, 0x0a, 0x0e, 0x6d, 0x61, 0x69,
	0x6e, 0x5f, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x6d, 0x61,
	0x69, 0x6e, 0x43, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x83, 0x01, 0x0a, 0x17, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x13, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x49, 0x6e, 0x4f, 0x75, 0x74, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x62,
	0x0a, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x01, 0x52, 0x11, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88,
	0x01, 0x01, 0x42, 0x04, 0x0a, 0x02, 0x69, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a,
	0xa8, 0x01, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x69, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6f, 0x75,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0xbf, 0x02, 0x0a, 0x18, 0x50,
	0x65, 0x74, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x54, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x50, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x68, 0x0a,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x63, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x5f, 0x6f,
	0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x61, 0x64, 0x64, 0x4f, 0x6e, 0x73, 0x1a, 0x8a, 0x03, 0x0a,
	0x07, 0x50, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x65, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x76, 0x65, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76,
	0x65, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a,
	0x0b, 0x76, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x34,
	0x0a, 0x16, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x1e, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x65, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x1a, 0x97, 0x06, 0x0a, 0x11, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x46, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x63, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x4b,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x48, 0x03, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x08, 0x66, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52, 0x08, 0x66, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x62, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0b, 0x6d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x26, 0x0a, 0x0c, 0x6d, 0x61, 0x78,
	0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x04, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x48, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x1a, 0xe9, 0x04, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x61, 0x64, 0x64, 0x5f, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64,
	0x64, 0x4f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x09, 0x63,
	0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x61, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x03, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x1a,
	0xc0, 0x03, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x32, 0x0a, 0x15, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13,
	0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5c, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x69,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e, 0x70, 0x72,
	0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x50, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74,
	0x68, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02,
	0x52, 0x0d, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x47, 0x0a, 0x20, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1d, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x27, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f,
	0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x04, 0x0a, 0x02, 0x69,
	0x64, 0x22, 0x9f, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x54, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x5f, 0x0a, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x12, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x4b, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x22, 0xc7, 0x05, 0x0a, 0x24, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x27, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x01, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x84, 0x01, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e,
	0x50, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11,
	0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x09, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x1a, 0xf6, 0x02, 0x0a,
	0x0e, 0x50, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x12,
	0x2b, 0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0b, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x09,
	0x63, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x6c, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x46,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44, 0x65,
	0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00, 0x10, 0x64, 0x22, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x75,
	0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00, 0x10,
	0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f,
	0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x04, 0x0a, 0x02, 0x69, 0x64, 0x22, 0x26, 0x0a,
	0x24, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x46, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xba, 0x01, 0x0a, 0x17, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x12, 0x27, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e,
	0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x04, 0x0a, 0x02,
	0x69, 0x64, 0x22, 0x19, 0x0a, 0x17, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xed, 0x0e,
	0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x27, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x01, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01,
	0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x7e, 0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x41, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x1a, 0x97, 0x02, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x6d, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x12, 0x68, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x52, 0x06, 0x61, 0x64, 0x64, 0x4f, 0x6e, 0x73, 0x1a, 0xaf, 0x05, 0x0a,
	0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x52, 0x0a, 0x09, 0x63, 0x61,
	0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b,
	0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b,
	0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x5c, 0x0a, 0x09, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0e, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x21, 0xfa, 0x42, 0x1e, 0x92, 0x01, 0x1b, 0x10, 0x64, 0x22, 0x17, 0x72, 0x15, 0x32,
	0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x24, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13,
	0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x24, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0,
	0x0b, 0x28, 0x00, 0x48, 0x02, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x24, 0x48, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x04, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x10, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x05,
	0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79,
	0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x71, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x1a, 0xe4,
	0x04, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x4f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x52, 0x0a, 0x09, 0x63, 0x61,
	0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04,
	0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b,
	0x0a, 0x0d, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b,
	0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x5c, 0x0a, 0x09, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0e, 0x73, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x21, 0xfa, 0x42, 0x1e, 0x92, 0x01, 0x1b, 0x10, 0x64, 0x22, 0x17, 0x72, 0x15, 0x32,
	0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64,
	0x7b, 0x32, 0x7d, 0x24, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0,
	0x0b, 0x28, 0x00, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28,
	0x00, 0x48, 0x02, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x36, 0x0a, 0x10, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f,
	0x64, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02,
	0x28, 0x00, 0x48, 0x03, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x65,
	0x72, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17,
	0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x04, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70,
	0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f,
	0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x04, 0x0a, 0x02, 0x69, 0x64, 0x22, 0x19, 0x0a,
	0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xc0, 0x03, 0x0a, 0x1e, 0x49, 0x73, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x27, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x01, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75,
	0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x04, 0x0a, 0x02, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x43, 0x0a, 0x1e, 0x49,
	0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x22, 0x9c, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x10, 0x0a,
	0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22,
	0xb7, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5a, 0x0a, 0x0c, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0x72, 0x0a, 0x13, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x25, 0x0a, 0x21, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x41, 0x53, 0x54, 0x10,
	0x02, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0c,
	0x0a, 0x08, 0x55, 0x50, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b,
	0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x08, 0x32, 0xb8, 0x0a,
	0x0a, 0x12, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0xa0, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8e, 0x01, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xac, 0x01, 0x0a, 0x1e,
	0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x46, 0x65, 0x65,
	0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x50, 0x65, 0x74, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x18, 0x49,
	0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x92, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x66, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_online_booking_v1_appointment_api_proto_rawDescOnce sync.Once
	file_moego_client_online_booking_v1_appointment_api_proto_rawDescData = file_moego_client_online_booking_v1_appointment_api_proto_rawDesc
)

func file_moego_client_online_booking_v1_appointment_api_proto_rawDescGZIP() []byte {
	file_moego_client_online_booking_v1_appointment_api_proto_rawDescOnce.Do(func() {
		file_moego_client_online_booking_v1_appointment_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_online_booking_v1_appointment_api_proto_rawDescData)
	})
	return file_moego_client_online_booking_v1_appointment_api_proto_rawDescData
}

var file_moego_client_online_booking_v1_appointment_api_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_client_online_booking_v1_appointment_api_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_moego_client_online_booking_v1_appointment_api_proto_goTypes = []interface{}{
	(AppointmentCardType)(0),                                     // 0: moego.client.online_booking.v1.AppointmentCardType
	(ListAppointmentsParams_AppointmentType)(0),                  // 1: moego.client.online_booking.v1.ListAppointmentsParams.AppointmentType
	(ListAppointmentsParams_AppointmentSortField)(0),             // 2: moego.client.online_booking.v1.ListAppointmentsParams.AppointmentSortField
	(*GetPriorityAppointmentCardParams)(nil),                     // 3: moego.client.online_booking.v1.GetPriorityAppointmentCardParams
	(*GetPriorityAppointmentCardResult)(nil),                     // 4: moego.client.online_booking.v1.GetPriorityAppointmentCardResult
	(*AppointmentCardItem)(nil),                                  // 5: moego.client.online_booking.v1.AppointmentCardItem
	(*AppointmentSummaryItem)(nil),                               // 6: moego.client.online_booking.v1.AppointmentSummaryItem
	(*PetAndServicesSummaryItem)(nil),                            // 7: moego.client.online_booking.v1.PetAndServicesSummaryItem
	(*AppointmentPaymentItem)(nil),                               // 8: moego.client.online_booking.v1.AppointmentPaymentItem
	(*ListAppointmentsParams)(nil),                               // 9: moego.client.online_booking.v1.ListAppointmentsParams
	(*ListAppointmentsResult)(nil),                               // 10: moego.client.online_booking.v1.ListAppointmentsResult
	(*GetAppointmentDetailParams)(nil),                           // 11: moego.client.online_booking.v1.GetAppointmentDetailParams
	(*GetAppointmentDetailResult)(nil),                           // 12: moego.client.online_booking.v1.GetAppointmentDetailResult
	(*GetGroupClassDetailParams)(nil),                            // 13: moego.client.online_booking.v1.GetGroupClassDetailParams
	(*GetGroupClassDetailResult)(nil),                            // 14: moego.client.online_booking.v1.GetGroupClassDetailResult
	(*ReschedulePetFeedingMedicationParams)(nil),                 // 15: moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams
	(*ReschedulePetFeedingMedicationResult)(nil),                 // 16: moego.client.online_booking.v1.ReschedulePetFeedingMedicationResult
	(*CancelAppointmentParams)(nil),                              // 17: moego.client.online_booking.v1.CancelAppointmentParams
	(*CancelAppointmentResult)(nil),                              // 18: moego.client.online_booking.v1.CancelAppointmentResult
	(*UpdateAppointmentParams)(nil),                              // 19: moego.client.online_booking.v1.UpdateAppointmentParams
	(*UpdateAppointmentResult)(nil),                              // 20: moego.client.online_booking.v1.UpdateAppointmentResult
	(*IsAvailableForRescheduleParams)(nil),                       // 21: moego.client.online_booking.v1.IsAvailableForRescheduleParams
	(*IsAvailableForRescheduleResult)(nil),                       // 22: moego.client.online_booking.v1.IsAvailableForRescheduleResult
	(*ListEvaluationsParams)(nil),                                // 23: moego.client.online_booking.v1.ListEvaluationsParams
	(*ListEvaluationsResult)(nil),                                // 24: moego.client.online_booking.v1.ListEvaluationsResult
	(*PetAndServicesSummaryItem_PetItem)(nil),                    // 25: moego.client.online_booking.v1.PetAndServicesSummaryItem.PetItem
	(*PetAndServicesSummaryItem_ServiceItem)(nil),                // 26: moego.client.online_booking.v1.PetAndServicesSummaryItem.ServiceItem
	(*ListAppointmentsParams_Filter)(nil),                        // 27: moego.client.online_booking.v1.ListAppointmentsParams.Filter
	(*ListAppointmentsParams_AppointmentSortDef)(nil),            // 28: moego.client.online_booking.v1.ListAppointmentsParams.AppointmentSortDef
	(*ListAppointmentsResult_AppointmentListItem)(nil),           // 29: moego.client.online_booking.v1.ListAppointmentsResult.AppointmentListItem
	(*GetAppointmentDetailResult_AppointmentItem)(nil),           // 30: moego.client.online_booking.v1.GetAppointmentDetailResult.AppointmentItem
	(*GetAppointmentDetailResult_CheckInOutDateTime)(nil),        // 31: moego.client.online_booking.v1.GetAppointmentDetailResult.CheckInOutDateTime
	(*GetAppointmentDetailResult_PetAndServicesDetailItem)(nil),  // 32: moego.client.online_booking.v1.GetAppointmentDetailResult.PetAndServicesDetailItem
	(*GetAppointmentDetailResult_PetItem)(nil),                   // 33: moego.client.online_booking.v1.GetAppointmentDetailResult.PetItem
	(*GetAppointmentDetailResult_ServiceDetailItem)(nil),         // 34: moego.client.online_booking.v1.GetAppointmentDetailResult.ServiceDetailItem
	(*GetAppointmentDetailResult_AddOnDetailItem)(nil),           // 35: moego.client.online_booking.v1.GetAppointmentDetailResult.AddOnDetailItem
	(*GetAppointmentDetailResult_PaymentItem)(nil),               // 36: moego.client.online_booking.v1.GetAppointmentDetailResult.PaymentItem
	(*ReschedulePetFeedingMedicationParams_PetScheduleDef)(nil),  // 37: moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams.PetScheduleDef
	(*UpdateAppointmentParams_UpdatePetServiceDetailParams)(nil), // 38: moego.client.online_booking.v1.UpdateAppointmentParams.UpdatePetServiceDetailParams
	(*UpdateAppointmentParams_UpdateServiceDetailParams)(nil),    // 39: moego.client.online_booking.v1.UpdateAppointmentParams.UpdateServiceDetailParams
	(*UpdateAppointmentParams_UpdateAddOnDetailParams)(nil),      // 40: moego.client.online_booking.v1.UpdateAppointmentParams.UpdateAddOnDetailParams
	(v1.ServiceItemType)(0),                                      // 41: moego.models.offering.v1.ServiceItemType
	(v11.AppointmentPaymentStatus)(0),                            // 42: moego.models.appointment.v1.AppointmentPaymentStatus
	(*v2.PaginationRequest)(nil),                                 // 43: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                                // 44: moego.utils.v2.PaginationResponse
	(*v12.GroupClassInstanceView)(nil),                           // 45: moego.api.offering.v1.GroupClassInstanceView
	(*v1.ServiceModel)(nil),                                      // 46: moego.models.offering.v1.ServiceModel
	(*date.Date)(nil),                                            // 47: google.type.Date
	(v13.PetType)(0),                                             // 48: moego.models.customer.v1.PetType
	(v11.AppointmentStatus)(0),                                   // 49: moego.models.appointment.v1.AppointmentStatus
	(v11.PetDetailDateType)(0),                                   // 50: moego.models.appointment.v1.PetDetailDateType
	(*v11.AppointmentPetFeedingScheduleDef)(nil),                 // 51: moego.models.appointment.v1.AppointmentPetFeedingScheduleDef
	(*v11.AppointmentPetMedicationScheduleDef)(nil),              // 52: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef
}
var file_moego_client_online_booking_v1_appointment_api_proto_depIdxs = []int32{
	5,  // 0: moego.client.online_booking.v1.GetPriorityAppointmentCardResult.card:type_name -> moego.client.online_booking.v1.AppointmentCardItem
	0,  // 1: moego.client.online_booking.v1.AppointmentCardItem.card_type:type_name -> moego.client.online_booking.v1.AppointmentCardType
	6,  // 2: moego.client.online_booking.v1.AppointmentCardItem.appointment:type_name -> moego.client.online_booking.v1.AppointmentSummaryItem
	7,  // 3: moego.client.online_booking.v1.AppointmentCardItem.pet_and_services:type_name -> moego.client.online_booking.v1.PetAndServicesSummaryItem
	41, // 4: moego.client.online_booking.v1.AppointmentSummaryItem.main_care_type:type_name -> moego.models.offering.v1.ServiceItemType
	25, // 5: moego.client.online_booking.v1.PetAndServicesSummaryItem.pet:type_name -> moego.client.online_booking.v1.PetAndServicesSummaryItem.PetItem
	26, // 6: moego.client.online_booking.v1.PetAndServicesSummaryItem.services:type_name -> moego.client.online_booking.v1.PetAndServicesSummaryItem.ServiceItem
	42, // 7: moego.client.online_booking.v1.AppointmentPaymentItem.payment_status:type_name -> moego.models.appointment.v1.AppointmentPaymentStatus
	27, // 8: moego.client.online_booking.v1.ListAppointmentsParams.filter:type_name -> moego.client.online_booking.v1.ListAppointmentsParams.Filter
	43, // 9: moego.client.online_booking.v1.ListAppointmentsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	28, // 10: moego.client.online_booking.v1.ListAppointmentsParams.sorts:type_name -> moego.client.online_booking.v1.ListAppointmentsParams.AppointmentSortDef
	29, // 11: moego.client.online_booking.v1.ListAppointmentsResult.appointments:type_name -> moego.client.online_booking.v1.ListAppointmentsResult.AppointmentListItem
	44, // 12: moego.client.online_booking.v1.ListAppointmentsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	30, // 13: moego.client.online_booking.v1.GetAppointmentDetailResult.appointment:type_name -> moego.client.online_booking.v1.GetAppointmentDetailResult.AppointmentItem
	32, // 14: moego.client.online_booking.v1.GetAppointmentDetailResult.pet_and_services:type_name -> moego.client.online_booking.v1.GetAppointmentDetailResult.PetAndServicesDetailItem
	36, // 15: moego.client.online_booking.v1.GetAppointmentDetailResult.payment:type_name -> moego.client.online_booking.v1.GetAppointmentDetailResult.PaymentItem
	33, // 16: moego.client.online_booking.v1.GetGroupClassDetailResult.pet:type_name -> moego.client.online_booking.v1.GetAppointmentDetailResult.PetItem
	45, // 17: moego.client.online_booking.v1.GetGroupClassDetailResult.group_class_instance:type_name -> moego.api.offering.v1.GroupClassInstanceView
	46, // 18: moego.client.online_booking.v1.GetGroupClassDetailResult.service_model:type_name -> moego.models.offering.v1.ServiceModel
	37, // 19: moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams.schedules:type_name -> moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams.PetScheduleDef
	38, // 20: moego.client.online_booking.v1.UpdateAppointmentParams.pet_and_services:type_name -> moego.client.online_booking.v1.UpdateAppointmentParams.UpdatePetServiceDetailParams
	41, // 21: moego.client.online_booking.v1.IsAvailableForRescheduleParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	47, // 22: moego.client.online_booking.v1.IsAvailableForRescheduleParams.start_date:type_name -> google.type.Date
	47, // 23: moego.client.online_booking.v1.IsAvailableForRescheduleParams.end_date:type_name -> google.type.Date
	43, // 24: moego.client.online_booking.v1.ListEvaluationsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	6,  // 25: moego.client.online_booking.v1.ListEvaluationsResult.appointments:type_name -> moego.client.online_booking.v1.AppointmentSummaryItem
	44, // 26: moego.client.online_booking.v1.ListEvaluationsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	48, // 27: moego.client.online_booking.v1.PetAndServicesSummaryItem.PetItem.pet_type:type_name -> moego.models.customer.v1.PetType
	41, // 28: moego.client.online_booking.v1.PetAndServicesSummaryItem.ServiceItem.care_type:type_name -> moego.models.offering.v1.ServiceItemType
	47, // 29: moego.client.online_booking.v1.PetAndServicesSummaryItem.ServiceItem.start_date:type_name -> google.type.Date
	47, // 30: moego.client.online_booking.v1.PetAndServicesSummaryItem.ServiceItem.end_date:type_name -> google.type.Date
	1,  // 31: moego.client.online_booking.v1.ListAppointmentsParams.Filter.appointment_type:type_name -> moego.client.online_booking.v1.ListAppointmentsParams.AppointmentType
	2,  // 32: moego.client.online_booking.v1.ListAppointmentsParams.AppointmentSortDef.field:type_name -> moego.client.online_booking.v1.ListAppointmentsParams.AppointmentSortField
	6,  // 33: moego.client.online_booking.v1.ListAppointmentsResult.AppointmentListItem.appointment:type_name -> moego.client.online_booking.v1.AppointmentSummaryItem
	7,  // 34: moego.client.online_booking.v1.ListAppointmentsResult.AppointmentListItem.pet_and_services:type_name -> moego.client.online_booking.v1.PetAndServicesSummaryItem
	8,  // 35: moego.client.online_booking.v1.ListAppointmentsResult.AppointmentListItem.payment:type_name -> moego.client.online_booking.v1.AppointmentPaymentItem
	41, // 36: moego.client.online_booking.v1.GetAppointmentDetailResult.AppointmentItem.main_care_type:type_name -> moego.models.offering.v1.ServiceItemType
	31, // 37: moego.client.online_booking.v1.GetAppointmentDetailResult.AppointmentItem.check_in_out_date_times:type_name -> moego.client.online_booking.v1.GetAppointmentDetailResult.CheckInOutDateTime
	49, // 38: moego.client.online_booking.v1.GetAppointmentDetailResult.AppointmentItem.appointment_status:type_name -> moego.models.appointment.v1.AppointmentStatus
	33, // 39: moego.client.online_booking.v1.GetAppointmentDetailResult.PetAndServicesDetailItem.pet:type_name -> moego.client.online_booking.v1.GetAppointmentDetailResult.PetItem
	34, // 40: moego.client.online_booking.v1.GetAppointmentDetailResult.PetAndServicesDetailItem.services:type_name -> moego.client.online_booking.v1.GetAppointmentDetailResult.ServiceDetailItem
	35, // 41: moego.client.online_booking.v1.GetAppointmentDetailResult.PetAndServicesDetailItem.add_ons:type_name -> moego.client.online_booking.v1.GetAppointmentDetailResult.AddOnDetailItem
	48, // 42: moego.client.online_booking.v1.GetAppointmentDetailResult.PetItem.pet_type:type_name -> moego.models.customer.v1.PetType
	41, // 43: moego.client.online_booking.v1.GetAppointmentDetailResult.ServiceDetailItem.care_type:type_name -> moego.models.offering.v1.ServiceItemType
	50, // 44: moego.client.online_booking.v1.GetAppointmentDetailResult.ServiceDetailItem.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	51, // 45: moego.client.online_booking.v1.GetAppointmentDetailResult.ServiceDetailItem.feedings:type_name -> moego.models.appointment.v1.AppointmentPetFeedingScheduleDef
	52, // 46: moego.client.online_booking.v1.GetAppointmentDetailResult.ServiceDetailItem.medications:type_name -> moego.models.appointment.v1.AppointmentPetMedicationScheduleDef
	50, // 47: moego.client.online_booking.v1.GetAppointmentDetailResult.AddOnDetailItem.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	41, // 48: moego.client.online_booking.v1.GetAppointmentDetailResult.AddOnDetailItem.care_type:type_name -> moego.models.offering.v1.ServiceItemType
	42, // 49: moego.client.online_booking.v1.GetAppointmentDetailResult.PaymentItem.payment_status:type_name -> moego.models.appointment.v1.AppointmentPaymentStatus
	41, // 50: moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams.PetScheduleDef.care_type:type_name -> moego.models.offering.v1.ServiceItemType
	51, // 51: moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams.PetScheduleDef.feedings:type_name -> moego.models.appointment.v1.AppointmentPetFeedingScheduleDef
	52, // 52: moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams.PetScheduleDef.medications:type_name -> moego.models.appointment.v1.AppointmentPetMedicationScheduleDef
	39, // 53: moego.client.online_booking.v1.UpdateAppointmentParams.UpdatePetServiceDetailParams.services:type_name -> moego.client.online_booking.v1.UpdateAppointmentParams.UpdateServiceDetailParams
	40, // 54: moego.client.online_booking.v1.UpdateAppointmentParams.UpdatePetServiceDetailParams.add_ons:type_name -> moego.client.online_booking.v1.UpdateAppointmentParams.UpdateAddOnDetailParams
	41, // 55: moego.client.online_booking.v1.UpdateAppointmentParams.UpdateServiceDetailParams.care_type:type_name -> moego.models.offering.v1.ServiceItemType
	50, // 56: moego.client.online_booking.v1.UpdateAppointmentParams.UpdateServiceDetailParams.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	41, // 57: moego.client.online_booking.v1.UpdateAppointmentParams.UpdateAddOnDetailParams.care_type:type_name -> moego.models.offering.v1.ServiceItemType
	50, // 58: moego.client.online_booking.v1.UpdateAppointmentParams.UpdateAddOnDetailParams.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	3,  // 59: moego.client.online_booking.v1.AppointmentService.GetPriorityAppointmentCard:input_type -> moego.client.online_booking.v1.GetPriorityAppointmentCardParams
	9,  // 60: moego.client.online_booking.v1.AppointmentService.ListAppointments:input_type -> moego.client.online_booking.v1.ListAppointmentsParams
	11, // 61: moego.client.online_booking.v1.AppointmentService.GetAppointmentDetail:input_type -> moego.client.online_booking.v1.GetAppointmentDetailParams
	13, // 62: moego.client.online_booking.v1.AppointmentService.GetGroupClassDetail:input_type -> moego.client.online_booking.v1.GetGroupClassDetailParams
	15, // 63: moego.client.online_booking.v1.AppointmentService.ReschedulePetFeedingMedication:input_type -> moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams
	17, // 64: moego.client.online_booking.v1.AppointmentService.CancelAppointment:input_type -> moego.client.online_booking.v1.CancelAppointmentParams
	19, // 65: moego.client.online_booking.v1.AppointmentService.UpdateAppointment:input_type -> moego.client.online_booking.v1.UpdateAppointmentParams
	21, // 66: moego.client.online_booking.v1.AppointmentService.IsAvailableForReschedule:input_type -> moego.client.online_booking.v1.IsAvailableForRescheduleParams
	23, // 67: moego.client.online_booking.v1.AppointmentService.ListEvaluations:input_type -> moego.client.online_booking.v1.ListEvaluationsParams
	4,  // 68: moego.client.online_booking.v1.AppointmentService.GetPriorityAppointmentCard:output_type -> moego.client.online_booking.v1.GetPriorityAppointmentCardResult
	10, // 69: moego.client.online_booking.v1.AppointmentService.ListAppointments:output_type -> moego.client.online_booking.v1.ListAppointmentsResult
	12, // 70: moego.client.online_booking.v1.AppointmentService.GetAppointmentDetail:output_type -> moego.client.online_booking.v1.GetAppointmentDetailResult
	14, // 71: moego.client.online_booking.v1.AppointmentService.GetGroupClassDetail:output_type -> moego.client.online_booking.v1.GetGroupClassDetailResult
	16, // 72: moego.client.online_booking.v1.AppointmentService.ReschedulePetFeedingMedication:output_type -> moego.client.online_booking.v1.ReschedulePetFeedingMedicationResult
	18, // 73: moego.client.online_booking.v1.AppointmentService.CancelAppointment:output_type -> moego.client.online_booking.v1.CancelAppointmentResult
	20, // 74: moego.client.online_booking.v1.AppointmentService.UpdateAppointment:output_type -> moego.client.online_booking.v1.UpdateAppointmentResult
	22, // 75: moego.client.online_booking.v1.AppointmentService.IsAvailableForReschedule:output_type -> moego.client.online_booking.v1.IsAvailableForRescheduleResult
	24, // 76: moego.client.online_booking.v1.AppointmentService.ListEvaluations:output_type -> moego.client.online_booking.v1.ListEvaluationsResult
	68, // [68:77] is the sub-list for method output_type
	59, // [59:68] is the sub-list for method input_type
	59, // [59:59] is the sub-list for extension type_name
	59, // [59:59] is the sub-list for extension extendee
	0,  // [0:59] is the sub-list for field type_name
}

func init() { file_moego_client_online_booking_v1_appointment_api_proto_init() }
func file_moego_client_online_booking_v1_appointment_api_proto_init() {
	if File_moego_client_online_booking_v1_appointment_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPriorityAppointmentCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPriorityAppointmentCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentCardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentSummaryItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetAndServicesSummaryItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentPaymentItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroupClassDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGroupClassDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReschedulePetFeedingMedicationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReschedulePetFeedingMedicationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsAvailableForRescheduleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsAvailableForRescheduleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetAndServicesSummaryItem_PetItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetAndServicesSummaryItem_ServiceItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsParams_AppointmentSortDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsResult_AppointmentListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentDetailResult_AppointmentItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentDetailResult_CheckInOutDateTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentDetailResult_PetAndServicesDetailItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentDetailResult_PetItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentDetailResult_ServiceDetailItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentDetailResult_AddOnDetailItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentDetailResult_PaymentItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReschedulePetFeedingMedicationParams_PetScheduleDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentParams_UpdatePetServiceDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentParams_UpdateServiceDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentParams_UpdateAddOnDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetPriorityAppointmentCardParams_Name)(nil),
		(*GetPriorityAppointmentCardParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*AppointmentSummaryItem_BookingRequestId)(nil),
		(*AppointmentSummaryItem_AppointmentId)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*ListAppointmentsParams_Name)(nil),
		(*ListAppointmentsParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*GetAppointmentDetailParams_Name)(nil),
		(*GetAppointmentDetailParams_Domain)(nil),
		(*GetAppointmentDetailParams_AppointmentId)(nil),
		(*GetAppointmentDetailParams_BookingRequestId)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*GetGroupClassDetailParams_Name)(nil),
		(*GetGroupClassDetailParams_Domain)(nil),
		(*GetGroupClassDetailParams_AppointmentId)(nil),
		(*GetGroupClassDetailParams_BookingRequestId)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*ReschedulePetFeedingMedicationParams_Name)(nil),
		(*ReschedulePetFeedingMedicationParams_Domain)(nil),
		(*ReschedulePetFeedingMedicationParams_AppointmentId)(nil),
		(*ReschedulePetFeedingMedicationParams_BookingRequestId)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*CancelAppointmentParams_Name)(nil),
		(*CancelAppointmentParams_Domain)(nil),
		(*CancelAppointmentParams_AppointmentId)(nil),
		(*CancelAppointmentParams_BookingRequestId)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*UpdateAppointmentParams_Name)(nil),
		(*UpdateAppointmentParams_Domain)(nil),
		(*UpdateAppointmentParams_AppointmentId)(nil),
		(*UpdateAppointmentParams_BookingRequestId)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[18].OneofWrappers = []interface{}{
		(*IsAvailableForRescheduleParams_Name)(nil),
		(*IsAvailableForRescheduleParams_Domain)(nil),
		(*IsAvailableForRescheduleParams_AppointmentId)(nil),
		(*IsAvailableForRescheduleParams_BookingRequestId)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*ListEvaluationsParams_Name)(nil),
		(*ListEvaluationsParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[23].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[27].OneofWrappers = []interface{}{
		(*GetAppointmentDetailResult_AppointmentItem_AppointmentId)(nil),
		(*GetAppointmentDetailResult_AppointmentItem_BookingRequestId)(nil),
	}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[31].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[32].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[33].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[36].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_appointment_api_proto_msgTypes[37].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_online_booking_v1_appointment_api_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_online_booking_v1_appointment_api_proto_goTypes,
		DependencyIndexes: file_moego_client_online_booking_v1_appointment_api_proto_depIdxs,
		EnumInfos:         file_moego_client_online_booking_v1_appointment_api_proto_enumTypes,
		MessageInfos:      file_moego_client_online_booking_v1_appointment_api_proto_msgTypes,
	}.Build()
	File_moego_client_online_booking_v1_appointment_api_proto = out.File
	file_moego_client_online_booking_v1_appointment_api_proto_rawDesc = nil
	file_moego_client_online_booking_v1_appointment_api_proto_goTypes = nil
	file_moego_client_online_booking_v1_appointment_api_proto_depIdxs = nil
}
