// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/online_booking/v1/booking_request_api.proto

package onlinebookingapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Source type
type SourceType int32

const (
	// Unspecified
	SourceType_SOURCE_TYPE_UNSPECIFIED SourceType = 0
	// Marketing campaign email
	SourceType_MARKETING_CAMPAIGN_EMAIL SourceType = 1
)

// Enum value maps for SourceType.
var (
	SourceType_name = map[int32]string{
		0: "SOURCE_TYPE_UNSPECIFIED",
		1: "MARKETING_CAMPAIGN_EMAIL",
	}
	SourceType_value = map[string]int32{
		"SOURCE_TYPE_UNSPECIFIED":  0,
		"MARKETING_CAMPAIGN_EMAIL": 1,
	}
)

func (x SourceType) Enum() *SourceType {
	p := new(SourceType)
	*p = x
	return p
}

func (x SourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_client_online_booking_v1_booking_request_api_proto_enumTypes[0].Descriptor()
}

func (SourceType) Type() protoreflect.EnumType {
	return &file_moego_client_online_booking_v1_booking_request_api_proto_enumTypes[0]
}

func (x SourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SourceType.Descriptor instead.
func (SourceType) EnumDescriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{0}
}

// OB request address params
type Address struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Existing address id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// address 1
	Address1 *string `protobuf:"bytes,2,opt,name=address1,proto3,oneof" json:"address1,omitempty"`
	// address 2
	Address2 *string `protobuf:"bytes,3,opt,name=address2,proto3,oneof" json:"address2,omitempty"`
	// city
	City *string `protobuf:"bytes,4,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// state
	State *string `protobuf:"bytes,5,opt,name=state,proto3,oneof" json:"state,omitempty"`
	// zip code
	Zipcode *string `protobuf:"bytes,6,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// country
	Country *string `protobuf:"bytes,7,opt,name=country,proto3,oneof" json:"country,omitempty"`
	// lat
	Lat *string `protobuf:"bytes,8,opt,name=lat,proto3,oneof" json:"lat,omitempty"`
	// lng
	Lng *string `protobuf:"bytes,9,opt,name=lng,proto3,oneof" json:"lng,omitempty"`
	// Whether the address is profile request address,
	// if true, id is profile_request_address id,
	// if false, id is customer_address id
	IsProfileRequestAddress *bool `protobuf:"varint,10,opt,name=is_profile_request_address,json=isProfileRequestAddress,proto3,oneof" json:"is_profile_request_address,omitempty"`
}

func (x *Address) Reset() {
	*x = Address{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{0}
}

func (x *Address) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Address) GetAddress1() string {
	if x != nil && x.Address1 != nil {
		return *x.Address1
	}
	return ""
}

func (x *Address) GetAddress2() string {
	if x != nil && x.Address2 != nil {
		return *x.Address2
	}
	return ""
}

func (x *Address) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *Address) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *Address) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *Address) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *Address) GetLat() string {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return ""
}

func (x *Address) GetLng() string {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return ""
}

func (x *Address) GetIsProfileRequestAddress() bool {
	if x != nil && x.IsProfileRequestAddress != nil {
		return *x.IsProfileRequestAddress
	}
	return false
}

// OB request customer params
type Customer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer first name
	FirstName *string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// Customer last name
	LastName *string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// Phone number
	PhoneNumber *string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// Email
	Email *string `protobuf:"bytes,4,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// Answers map
	AnswersMap map[string]*structpb.Value `protobuf:"bytes,5,rep,name=answers_map,json=answersMap,proto3" json:"answers_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Charge token
	ChargeToken *string `protobuf:"bytes,6,opt,name=charge_token,json=chargeToken,proto3,oneof" json:"charge_token,omitempty"`
	// Has stripe card
	HasStripeCard *bool `protobuf:"varint,7,opt,name=has_stripe_card,json=hasStripeCard,proto3,oneof" json:"has_stripe_card,omitempty"`
	// Stripe customer id
	StripeCustomerId *string `protobuf:"bytes,8,opt,name=stripe_customer_id,json=stripeCustomerId,proto3,oneof" json:"stripe_customer_id,omitempty"`
	// Additional info
	AdditionalInfo *Customer_AdditionalInfo `protobuf:"bytes,9,opt,name=additional_info,json=additionalInfo,proto3,oneof" json:"additional_info,omitempty"`
	// Address
	Address *Address `protobuf:"bytes,10,opt,name=address,proto3,oneof" json:"address,omitempty"`
	// birthday
	Birthday *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// Emergency contact
	EmergencyContact *Customer_Contact `protobuf:"bytes,12,opt,name=emergency_contact,json=emergencyContact,proto3" json:"emergency_contact,omitempty"`
	// pickup contact
	PickupContact *Customer_Contact `protobuf:"bytes,13,opt,name=pickup_contact,json=pickupContact,proto3" json:"pickup_contact,omitempty"`
}

func (x *Customer) Reset() {
	*x = Customer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer) ProtoMessage() {}

func (x *Customer) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer.ProtoReflect.Descriptor instead.
func (*Customer) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{1}
}

func (x *Customer) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *Customer) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *Customer) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *Customer) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *Customer) GetAnswersMap() map[string]*structpb.Value {
	if x != nil {
		return x.AnswersMap
	}
	return nil
}

func (x *Customer) GetChargeToken() string {
	if x != nil && x.ChargeToken != nil {
		return *x.ChargeToken
	}
	return ""
}

func (x *Customer) GetHasStripeCard() bool {
	if x != nil && x.HasStripeCard != nil {
		return *x.HasStripeCard
	}
	return false
}

func (x *Customer) GetStripeCustomerId() string {
	if x != nil && x.StripeCustomerId != nil {
		return *x.StripeCustomerId
	}
	return ""
}

func (x *Customer) GetAdditionalInfo() *Customer_AdditionalInfo {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

func (x *Customer) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *Customer) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *Customer) GetEmergencyContact() *Customer_Contact {
	if x != nil {
		return x.EmergencyContact
	}
	return nil
}

func (x *Customer) GetPickupContact() *Customer_Contact {
	if x != nil {
		return x.PickupContact
	}
	return nil
}

// OB request pet params
type Pet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id
	PetId *int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
	// Name
	PetName *string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3,oneof" json:"pet_name,omitempty"`
	// Avatar path
	AvatarPath *string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// Breed
	Breed *string `protobuf:"bytes,4,opt,name=breed,proto3,oneof" json:"breed,omitempty"`
	// Breed mix
	BreedMix *int32 `protobuf:"varint,5,opt,name=breed_mix,json=breedMix,proto3,oneof" json:"breed_mix,omitempty"`
	// Pet type id
	PetTypeId *int32 `protobuf:"varint,6,opt,name=pet_type_id,json=petTypeId,proto3,oneof" json:"pet_type_id,omitempty"`
	// Gender
	Gender *int32 `protobuf:"varint,7,opt,name=gender,proto3,oneof" json:"gender,omitempty"`
	// Birthday
	Birthday *string `protobuf:"bytes,8,opt,name=birthday,proto3,oneof" json:"birthday,omitempty"`
	// Weight
	Weight *string `protobuf:"bytes,9,opt,name=weight,proto3,oneof" json:"weight,omitempty"`
	// Fixed
	Fixed *string `protobuf:"bytes,10,opt,name=fixed,proto3,oneof" json:"fixed,omitempty"`
	// Behavior
	Behavior *string `protobuf:"bytes,11,opt,name=behavior,proto3,oneof" json:"behavior,omitempty"`
	// Hair length
	HairLength *string `protobuf:"bytes,12,opt,name=hair_length,json=hairLength,proto3,oneof" json:"hair_length,omitempty"`
	// Expiry notification
	ExpiryNotification *int32 `protobuf:"varint,13,opt,name=expiry_notification,json=expiryNotification,proto3,oneof" json:"expiry_notification,omitempty"`
	// Vet name
	VetName *string `protobuf:"bytes,14,opt,name=vet_name,json=vetName,proto3,oneof" json:"vet_name,omitempty"`
	// Vet phone
	VetPhone *string `protobuf:"bytes,15,opt,name=vet_phone,json=vetPhone,proto3,oneof" json:"vet_phone,omitempty"`
	// Vet address
	VetAddress *string `protobuf:"bytes,16,opt,name=vet_address,json=vetAddress,proto3,oneof" json:"vet_address,omitempty"`
	// Emergency contact name
	EmergencyContactName *string `protobuf:"bytes,17,opt,name=emergency_contact_name,json=emergencyContactName,proto3,oneof" json:"emergency_contact_name,omitempty"`
	// Emergency contact phone
	EmergencyContactPhone *string `protobuf:"bytes,18,opt,name=emergency_contact_phone,json=emergencyContactPhone,proto3,oneof" json:"emergency_contact_phone,omitempty"`
	// Health issues
	HealthIssues *string `protobuf:"bytes,19,opt,name=health_issues,json=healthIssues,proto3,oneof" json:"health_issues,omitempty"`
	// Vaccines
	VaccineList []*Pet_Vaccine `protobuf:"bytes,20,rep,name=vaccine_list,json=vaccineList,proto3" json:"vaccine_list,omitempty"`
	// Pet image
	PetImage *string `protobuf:"bytes,21,opt,name=pet_image,json=petImage,proto3,oneof" json:"pet_image,omitempty"`
	// Is selected
	//
	//	bool is_selected = 22;
	//
	// Pet question answers
	PetQuestionAnswers map[string]*structpb.Value `protobuf:"bytes,25,rep,name=pet_question_answers,json=petQuestionAnswers,proto3" json:"pet_question_answers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Pet) Reset() {
	*x = Pet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pet) ProtoMessage() {}

func (x *Pet) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pet.ProtoReflect.Descriptor instead.
func (*Pet) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{2}
}

func (x *Pet) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

func (x *Pet) GetPetName() string {
	if x != nil && x.PetName != nil {
		return *x.PetName
	}
	return ""
}

func (x *Pet) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *Pet) GetBreed() string {
	if x != nil && x.Breed != nil {
		return *x.Breed
	}
	return ""
}

func (x *Pet) GetBreedMix() int32 {
	if x != nil && x.BreedMix != nil {
		return *x.BreedMix
	}
	return 0
}

func (x *Pet) GetPetTypeId() int32 {
	if x != nil && x.PetTypeId != nil {
		return *x.PetTypeId
	}
	return 0
}

func (x *Pet) GetGender() int32 {
	if x != nil && x.Gender != nil {
		return *x.Gender
	}
	return 0
}

func (x *Pet) GetBirthday() string {
	if x != nil && x.Birthday != nil {
		return *x.Birthday
	}
	return ""
}

func (x *Pet) GetWeight() string {
	if x != nil && x.Weight != nil {
		return *x.Weight
	}
	return ""
}

func (x *Pet) GetFixed() string {
	if x != nil && x.Fixed != nil {
		return *x.Fixed
	}
	return ""
}

func (x *Pet) GetBehavior() string {
	if x != nil && x.Behavior != nil {
		return *x.Behavior
	}
	return ""
}

func (x *Pet) GetHairLength() string {
	if x != nil && x.HairLength != nil {
		return *x.HairLength
	}
	return ""
}

func (x *Pet) GetExpiryNotification() int32 {
	if x != nil && x.ExpiryNotification != nil {
		return *x.ExpiryNotification
	}
	return 0
}

func (x *Pet) GetVetName() string {
	if x != nil && x.VetName != nil {
		return *x.VetName
	}
	return ""
}

func (x *Pet) GetVetPhone() string {
	if x != nil && x.VetPhone != nil {
		return *x.VetPhone
	}
	return ""
}

func (x *Pet) GetVetAddress() string {
	if x != nil && x.VetAddress != nil {
		return *x.VetAddress
	}
	return ""
}

func (x *Pet) GetEmergencyContactName() string {
	if x != nil && x.EmergencyContactName != nil {
		return *x.EmergencyContactName
	}
	return ""
}

func (x *Pet) GetEmergencyContactPhone() string {
	if x != nil && x.EmergencyContactPhone != nil {
		return *x.EmergencyContactPhone
	}
	return ""
}

func (x *Pet) GetHealthIssues() string {
	if x != nil && x.HealthIssues != nil {
		return *x.HealthIssues
	}
	return ""
}

func (x *Pet) GetVaccineList() []*Pet_Vaccine {
	if x != nil {
		return x.VaccineList
	}
	return nil
}

func (x *Pet) GetPetImage() string {
	if x != nil && x.PetImage != nil {
		return *x.PetImage
	}
	return ""
}

func (x *Pet) GetPetQuestionAnswers() map[string]*structpb.Value {
	if x != nil {
		return x.PetQuestionAnswers
	}
	return nil
}

// OB agreement params
type Agreement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Agreement id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Signature
	Signature string `protobuf:"bytes,3,opt,name=signature,proto3" json:"signature,omitempty"`
}

func (x *Agreement) Reset() {
	*x = Agreement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Agreement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Agreement) ProtoMessage() {}

func (x *Agreement) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Agreement.ProtoReflect.Descriptor instead.
func (*Agreement) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{3}
}

func (x *Agreement) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Agreement) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

// Feeding
type Feeding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Feeding time
	Time []*v1.FeedingModel_FeedingSchedule `protobuf:"bytes,1,rep,name=time,proto3" json:"time,omitempty"`
	// Feeding amount
	//
	// Deprecated: Do not use.
	Amount *float64 `protobuf:"fixed64,2,opt,name=amount,proto3,oneof" json:"amount,omitempty"`
	// Feeding unit
	Unit *string `protobuf:"bytes,3,opt,name=unit,proto3,oneof" json:"unit,omitempty"`
	// Food type
	FoodType *string `protobuf:"bytes,4,opt,name=food_type,json=foodType,proto3,oneof" json:"food_type,omitempty"`
	// Food source
	FoodSource *string `protobuf:"bytes,5,opt,name=food_source,json=foodSource,proto3,oneof" json:"food_source,omitempty"`
	// Feeding instructions
	Instruction *string `protobuf:"bytes,6,opt,name=instruction,proto3,oneof" json:"instruction,omitempty"`
	// Feeding note
	Note *string `protobuf:"bytes,7,opt,name=note,proto3,oneof" json:"note,omitempty"`
	// Feeding amount, such as 1.2, 1/2, 1 etc.
	AmountStr *string `protobuf:"bytes,8,opt,name=amount_str,json=amountStr,proto3,oneof" json:"amount_str,omitempty"`
}

func (x *Feeding) Reset() {
	*x = Feeding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Feeding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feeding) ProtoMessage() {}

func (x *Feeding) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feeding.ProtoReflect.Descriptor instead.
func (*Feeding) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{4}
}

func (x *Feeding) GetTime() []*v1.FeedingModel_FeedingSchedule {
	if x != nil {
		return x.Time
	}
	return nil
}

// Deprecated: Do not use.
func (x *Feeding) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *Feeding) GetUnit() string {
	if x != nil && x.Unit != nil {
		return *x.Unit
	}
	return ""
}

func (x *Feeding) GetFoodType() string {
	if x != nil && x.FoodType != nil {
		return *x.FoodType
	}
	return ""
}

func (x *Feeding) GetFoodSource() string {
	if x != nil && x.FoodSource != nil {
		return *x.FoodSource
	}
	return ""
}

func (x *Feeding) GetInstruction() string {
	if x != nil && x.Instruction != nil {
		return *x.Instruction
	}
	return ""
}

func (x *Feeding) GetNote() string {
	if x != nil && x.Note != nil {
		return *x.Note
	}
	return ""
}

func (x *Feeding) GetAmountStr() string {
	if x != nil && x.AmountStr != nil {
		return *x.AmountStr
	}
	return ""
}

// Medication
type Medication struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Medication time
	Time []*v1.MedicationModel_MedicationSchedule `protobuf:"bytes,1,rep,name=time,proto3" json:"time,omitempty"`
	// Medication amount
	//
	// Deprecated: Do not use.
	Amount *float64 `protobuf:"fixed64,2,opt,name=amount,proto3,oneof" json:"amount,omitempty"`
	// Medication unit
	Unit *string `protobuf:"bytes,3,opt,name=unit,proto3,oneof" json:"unit,omitempty"`
	// Medication name
	MedicationName *string `protobuf:"bytes,4,opt,name=medication_name,json=medicationName,proto3,oneof" json:"medication_name,omitempty"`
	// Medication notes
	Notes *string `protobuf:"bytes,5,opt,name=notes,proto3,oneof" json:"notes,omitempty"`
	// Medication amount, such as 1.2, 1/2, 1 etc.
	AmountStr *string `protobuf:"bytes,8,opt,name=amount_str,json=amountStr,proto3,oneof" json:"amount_str,omitempty"`
	// Medication select date
	SelectedDate *v11.AppointmentPetMedicationScheduleDef_SelectedDateDef `protobuf:"bytes,9,opt,name=selected_date,json=selectedDate,proto3,oneof" json:"selected_date,omitempty"`
}

func (x *Medication) Reset() {
	*x = Medication{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Medication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Medication) ProtoMessage() {}

func (x *Medication) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Medication.ProtoReflect.Descriptor instead.
func (*Medication) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{5}
}

func (x *Medication) GetTime() []*v1.MedicationModel_MedicationSchedule {
	if x != nil {
		return x.Time
	}
	return nil
}

// Deprecated: Do not use.
func (x *Medication) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *Medication) GetUnit() string {
	if x != nil && x.Unit != nil {
		return *x.Unit
	}
	return ""
}

func (x *Medication) GetMedicationName() string {
	if x != nil && x.MedicationName != nil {
		return *x.MedicationName
	}
	return ""
}

func (x *Medication) GetNotes() string {
	if x != nil && x.Notes != nil {
		return *x.Notes
	}
	return ""
}

func (x *Medication) GetAmountStr() string {
	if x != nil && x.AmountStr != nil {
		return *x.AmountStr
	}
	return ""
}

func (x *Medication) GetSelectedDate() *v11.AppointmentPetMedicationScheduleDef_SelectedDateDef {
	if x != nil {
		return x.SelectedDate
	}
	return nil
}

// Boarding addon
type BoardingAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Boarding addon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Is every day, not include checkout day
	//
	// Deprecated: Do not use.
	IsEveryDay *bool `protobuf:"varint,3,opt,name=is_every_day,json=isEveryDay,proto3,oneof" json:"is_every_day,omitempty"`
	// Specific dates
	Dates []string `protobuf:"bytes,4,rep,name=dates,proto3" json:"dates,omitempty"`
	// Addon price
	ServicePrice float64 `protobuf:"fixed64,5,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// Tax
	TaxId int64 `protobuf:"varint,6,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// Duration
	Duration int32 `protobuf:"varint,7,opt,name=duration,proto3" json:"duration,omitempty"`
	// Quantity per day
	QuantityPerDay *int32 `protobuf:"varint,8,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
	// date type
	DateType *v11.PetDetailDateType `protobuf:"varint,9,opt,name=date_type,json=dateType,proto3,enum=moego.models.appointment.v1.PetDetailDateType,oneof" json:"date_type,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,10,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
}

func (x *BoardingAddon) Reset() {
	*x = BoardingAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingAddon) ProtoMessage() {}

func (x *BoardingAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingAddon.ProtoReflect.Descriptor instead.
func (*BoardingAddon) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{6}
}

func (x *BoardingAddon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Deprecated: Do not use.
func (x *BoardingAddon) GetIsEveryDay() bool {
	if x != nil && x.IsEveryDay != nil {
		return *x.IsEveryDay
	}
	return false
}

func (x *BoardingAddon) GetDates() []string {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *BoardingAddon) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *BoardingAddon) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *BoardingAddon) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *BoardingAddon) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

func (x *BoardingAddon) GetDateType() v11.PetDetailDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return v11.PetDetailDateType(0)
}

func (x *BoardingAddon) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

// Daycare addon
type DaycareAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Daycare addon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Is every day
	IsEveryDay *bool `protobuf:"varint,3,opt,name=is_every_day,json=isEveryDay,proto3,oneof" json:"is_every_day,omitempty"`
	// Specific dates
	Dates []string `protobuf:"bytes,4,rep,name=dates,proto3" json:"dates,omitempty"`
	// Addon price
	ServicePrice float64 `protobuf:"fixed64,5,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// Tax
	TaxId int64 `protobuf:"varint,6,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// Duration
	Duration int32 `protobuf:"varint,7,opt,name=duration,proto3" json:"duration,omitempty"`
	// Quantity per day
	QuantityPerDay *int32 `protobuf:"varint,8,opt,name=quantity_per_day,json=quantityPerDay,proto3,oneof" json:"quantity_per_day,omitempty"`
}

func (x *DaycareAddon) Reset() {
	*x = DaycareAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareAddon) ProtoMessage() {}

func (x *DaycareAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareAddon.ProtoReflect.Descriptor instead.
func (*DaycareAddon) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{7}
}

func (x *DaycareAddon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DaycareAddon) GetIsEveryDay() bool {
	if x != nil && x.IsEveryDay != nil {
		return *x.IsEveryDay
	}
	return false
}

func (x *DaycareAddon) GetDates() []string {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *DaycareAddon) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *DaycareAddon) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *DaycareAddon) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *DaycareAddon) GetQuantityPerDay() int32 {
	if x != nil && x.QuantityPerDay != nil {
		return *x.QuantityPerDay
	}
	return 0
}

// Daycare addon
type GroomingAddon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// grooming addon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GroomingAddon) Reset() {
	*x = GroomingAddon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingAddon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingAddon) ProtoMessage() {}

func (x *GroomingAddon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingAddon.ProtoReflect.Descriptor instead.
func (*GroomingAddon) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{8}
}

func (x *GroomingAddon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// OB request service
type Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Service type
	//
	// Types that are assignable to Service:
	//
	//	*Service_Grooming_
	//	*Service_Boarding_
	//	*Service_Daycare_
	//	*Service_Evaluation_
	//	*Service_DogWalking_
	//	*Service_GroupClass_
	Service isService_Service `protobuf_oneof:"service"`
}

func (x *Service) Reset() {
	*x = Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{9}
}

func (m *Service) GetService() isService_Service {
	if m != nil {
		return m.Service
	}
	return nil
}

func (x *Service) GetGrooming() *Service_Grooming {
	if x, ok := x.GetService().(*Service_Grooming_); ok {
		return x.Grooming
	}
	return nil
}

func (x *Service) GetBoarding() *Service_Boarding {
	if x, ok := x.GetService().(*Service_Boarding_); ok {
		return x.Boarding
	}
	return nil
}

func (x *Service) GetDaycare() *Service_Daycare {
	if x, ok := x.GetService().(*Service_Daycare_); ok {
		return x.Daycare
	}
	return nil
}

func (x *Service) GetEvaluation() *Service_Evaluation {
	if x, ok := x.GetService().(*Service_Evaluation_); ok {
		return x.Evaluation
	}
	return nil
}

func (x *Service) GetDogWalking() *Service_DogWalking {
	if x, ok := x.GetService().(*Service_DogWalking_); ok {
		return x.DogWalking
	}
	return nil
}

func (x *Service) GetGroupClass() *Service_GroupClass {
	if x, ok := x.GetService().(*Service_GroupClass_); ok {
		return x.GroupClass
	}
	return nil
}

type isService_Service interface {
	isService_Service()
}

type Service_Grooming_ struct {
	// Grooming service
	Grooming *Service_Grooming `protobuf:"bytes,1,opt,name=grooming,proto3,oneof"`
}

type Service_Boarding_ struct {
	// Boarding service
	Boarding *Service_Boarding `protobuf:"bytes,2,opt,name=boarding,proto3,oneof"`
}

type Service_Daycare_ struct {
	// Daycare service
	Daycare *Service_Daycare `protobuf:"bytes,3,opt,name=daycare,proto3,oneof"`
}

type Service_Evaluation_ struct {
	// Evaluation service
	Evaluation *Service_Evaluation `protobuf:"bytes,4,opt,name=evaluation,proto3,oneof"`
}

type Service_DogWalking_ struct {
	// Dog walking service
	DogWalking *Service_DogWalking `protobuf:"bytes,5,opt,name=dog_walking,json=dogWalking,proto3,oneof"`
}

type Service_GroupClass_ struct {
	// Group class service
	GroupClass *Service_GroupClass `protobuf:"bytes,6,opt,name=group_class,json=groupClass,proto3,oneof"`
}

func (*Service_Grooming_) isService_Service() {}

func (*Service_Boarding_) isService_Service() {}

func (*Service_Daycare_) isService_Service() {}

func (*Service_Evaluation_) isService_Service() {}

func (*Service_DogWalking_) isService_Service() {}

func (*Service_GroupClass_) isService_Service() {}

// PrePay
type PrePay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service total
	ServiceTotal float64 `protobuf:"fixed64,1,opt,name=service_total,json=serviceTotal,proto3" json:"service_total,omitempty"`
	// tax amount
	TaxAmount float64 `protobuf:"fixed64,2,opt,name=tax_amount,json=taxAmount,proto3" json:"tax_amount,omitempty"`
	// service charge amount
	ServiceChargeAmount float64 `protobuf:"fixed64,3,opt,name=service_charge_amount,json=serviceChargeAmount,proto3" json:"service_charge_amount,omitempty"`
}

func (x *PrePay) Reset() {
	*x = PrePay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrePay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrePay) ProtoMessage() {}

func (x *PrePay) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrePay.ProtoReflect.Descriptor instead.
func (*PrePay) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{10}
}

func (x *PrePay) GetServiceTotal() float64 {
	if x != nil {
		return x.ServiceTotal
	}
	return 0
}

func (x *PrePay) GetTaxAmount() float64 {
	if x != nil {
		return x.TaxAmount
	}
	return 0
}

func (x *PrePay) GetServiceChargeAmount() float64 {
	if x != nil {
		return x.ServiceChargeAmount
	}
	return 0
}

// PreAuth
type PreAuth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Tips amount
	TipsAmount float64 `protobuf:"fixed64,1,opt,name=tips_amount,json=tipsAmount,proto3" json:"tips_amount,omitempty"`
	// Service total
	ServiceTotal float64 `protobuf:"fixed64,2,opt,name=service_total,json=serviceTotal,proto3" json:"service_total,omitempty"`
	// Tax amount
	TaxAmount float64 `protobuf:"fixed64,3,opt,name=tax_amount,json=taxAmount,proto3" json:"tax_amount,omitempty"`
	// Service charge amount
	ServiceChargeAmount float64 `protobuf:"fixed64,4,opt,name=service_charge_amount,json=serviceChargeAmount,proto3" json:"service_charge_amount,omitempty"`
	// Payment method id
	PaymentMethodId string `protobuf:"bytes,5,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
	// Card number
	CardNumber string `protobuf:"bytes,6,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`
	// Charge token
	ChargeToken string `protobuf:"bytes,7,opt,name=charge_token,json=chargeToken,proto3" json:"charge_token,omitempty"`
}

func (x *PreAuth) Reset() {
	*x = PreAuth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreAuth) ProtoMessage() {}

func (x *PreAuth) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreAuth.ProtoReflect.Descriptor instead.
func (*PreAuth) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{11}
}

func (x *PreAuth) GetTipsAmount() float64 {
	if x != nil {
		return x.TipsAmount
	}
	return 0
}

func (x *PreAuth) GetServiceTotal() float64 {
	if x != nil {
		return x.ServiceTotal
	}
	return 0
}

func (x *PreAuth) GetTaxAmount() float64 {
	if x != nil {
		return x.TaxAmount
	}
	return 0
}

func (x *PreAuth) GetServiceChargeAmount() float64 {
	if x != nil {
		return x.ServiceChargeAmount
	}
	return 0
}

func (x *PreAuth) GetPaymentMethodId() string {
	if x != nil {
		return x.PaymentMethodId
	}
	return ""
}

func (x *PreAuth) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *PreAuth) GetChargeToken() string {
	if x != nil {
		return x.ChargeToken
	}
	return ""
}

// Discount code
type DiscountCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Discount code id
	DiscountCodeId int64 `protobuf:"varint,1,opt,name=discount_code_id,json=discountCodeId,proto3" json:"discount_code_id,omitempty"`
	// Discount amount
	DiscountAmount float64 `protobuf:"fixed64,2,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	// Discount code
	DiscountCode string `protobuf:"bytes,3,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
}

func (x *DiscountCode) Reset() {
	*x = DiscountCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DiscountCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscountCode) ProtoMessage() {}

func (x *DiscountCode) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscountCode.ProtoReflect.Descriptor instead.
func (*DiscountCode) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{12}
}

func (x *DiscountCode) GetDiscountCodeId() int64 {
	if x != nil {
		return x.DiscountCodeId
	}
	return 0
}

func (x *DiscountCode) GetDiscountAmount() float64 {
	if x != nil {
		return x.DiscountAmount
	}
	return 0
}

func (x *DiscountCode) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

// Membership apply Info
type Membership struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// membership id
	MembershipIds []int64 `protobuf:"varint,1,rep,packed,name=membership_ids,json=membershipIds,proto3" json:"membership_ids,omitempty"`
}

func (x *Membership) Reset() {
	*x = Membership{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Membership) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Membership) ProtoMessage() {}

func (x *Membership) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Membership.ProtoReflect.Descriptor instead.
func (*Membership) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{13}
}

func (x *Membership) GetMembershipIds() []int64 {
	if x != nil {
		return x.MembershipIds
	}
	return nil
}

// Create booking request request
type SubmitBookingRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*SubmitBookingRequestParams_Name
	//	*SubmitBookingRequestParams_Domain
	Anonymous isSubmitBookingRequestParams_Anonymous `protobuf_oneof:"anonymous"`
	// Customer
	Customer *Customer `protobuf:"bytes,3,opt,name=customer,proto3,oneof" json:"customer,omitempty"`
	// Pet to services mapping
	PetServices []*PetServices `protobuf:"bytes,4,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// Agreements
	Agreements []*Agreement `protobuf:"bytes,5,rep,name=agreements,proto3" json:"agreements,omitempty"`
	// Prepay guid
	PrepayGuid *string `protobuf:"bytes,6,opt,name=prepay_guid,json=prepayGuid,proto3,oneof" json:"prepay_guid,omitempty"`
	// PrePay
	// Order decoupling 之后，payment 流程会在 submit 之后进行，所以 submit 接口已经不再需要 pre_pay 参数
	//
	// Deprecated: Do not use.
	PrePay *PrePay `protobuf:"bytes,7,opt,name=pre_pay,json=prePay,proto3,oneof" json:"pre_pay,omitempty"`
	// Source type
	SourceType *SourceType `protobuf:"varint,8,opt,name=source_type,json=sourceType,proto3,enum=moego.client.online_booking.v1.SourceType,oneof" json:"source_type,omitempty"`
	// PreAuth
	PreAuth *PreAuth `protobuf:"bytes,9,opt,name=pre_auth,json=preAuth,proto3,oneof" json:"pre_auth,omitempty"`
	// Discount code
	DiscountCode *DiscountCode `protobuf:"bytes,10,opt,name=discount_code,json=discountCode,proto3,oneof" json:"discount_code,omitempty"`
	// Additional notes
	AdditionalNotes *string `protobuf:"bytes,11,opt,name=additional_notes,json=additionalNotes,proto3,oneof" json:"additional_notes,omitempty"`
	// membership apply info
	Membership *Membership `protobuf:"bytes,12,opt,name=membership,proto3,oneof" json:"membership,omitempty"`
}

func (x *SubmitBookingRequestParams) Reset() {
	*x = SubmitBookingRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitBookingRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitBookingRequestParams) ProtoMessage() {}

func (x *SubmitBookingRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitBookingRequestParams.ProtoReflect.Descriptor instead.
func (*SubmitBookingRequestParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{14}
}

func (m *SubmitBookingRequestParams) GetAnonymous() isSubmitBookingRequestParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *SubmitBookingRequestParams) GetName() string {
	if x, ok := x.GetAnonymous().(*SubmitBookingRequestParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *SubmitBookingRequestParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*SubmitBookingRequestParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *SubmitBookingRequestParams) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *SubmitBookingRequestParams) GetPetServices() []*PetServices {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *SubmitBookingRequestParams) GetAgreements() []*Agreement {
	if x != nil {
		return x.Agreements
	}
	return nil
}

func (x *SubmitBookingRequestParams) GetPrepayGuid() string {
	if x != nil && x.PrepayGuid != nil {
		return *x.PrepayGuid
	}
	return ""
}

// Deprecated: Do not use.
func (x *SubmitBookingRequestParams) GetPrePay() *PrePay {
	if x != nil {
		return x.PrePay
	}
	return nil
}

func (x *SubmitBookingRequestParams) GetSourceType() SourceType {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return SourceType_SOURCE_TYPE_UNSPECIFIED
}

func (x *SubmitBookingRequestParams) GetPreAuth() *PreAuth {
	if x != nil {
		return x.PreAuth
	}
	return nil
}

func (x *SubmitBookingRequestParams) GetDiscountCode() *DiscountCode {
	if x != nil {
		return x.DiscountCode
	}
	return nil
}

func (x *SubmitBookingRequestParams) GetAdditionalNotes() string {
	if x != nil && x.AdditionalNotes != nil {
		return *x.AdditionalNotes
	}
	return ""
}

func (x *SubmitBookingRequestParams) GetMembership() *Membership {
	if x != nil {
		return x.Membership
	}
	return nil
}

type isSubmitBookingRequestParams_Anonymous interface {
	isSubmitBookingRequestParams_Anonymous()
}

type SubmitBookingRequestParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type SubmitBookingRequestParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*SubmitBookingRequestParams_Name) isSubmitBookingRequestParams_Anonymous() {}

func (*SubmitBookingRequestParams_Domain) isSubmitBookingRequestParams_Anonymous() {}

// Pet services
type PetServices struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pet id
	Pet *Pet `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	// Service id
	Services []*Service `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *PetServices) Reset() {
	*x = PetServices{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetServices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetServices) ProtoMessage() {}

func (x *PetServices) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetServices.ProtoReflect.Descriptor instead.
func (*PetServices) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{15}
}

func (x *PetServices) GetPet() *Pet {
	if x != nil {
		return x.Pet
	}
	return nil
}

func (x *PetServices) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// Create booking request response
type SubmitBookingRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The booking request id
	// 当提交的 service 里同时包含 waitlist 和正常 service 时，返回的 id 是正常 service 创建的 booking request id
	// 当提交的 service 里只有 waitlist 时，返回的 id 是 waitlist 创建的 booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// order id
	OrderId int64 `protobuf:"varint,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// If the customer is new_visitor, return created customer id
	// If the customer is existing, return existing customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 这个 BookingRequest 是否被自动 accept
	// 特别注意：
	// 当 prepay 开启时，只会返回 false，因为支付流程时异步的，没法在这个接口里判断支付状态
	// 当 prepay 关闭时，可以正常返回 true/false
	AutoAcceptRequest bool `protobuf:"varint,4,opt,name=auto_accept_request,json=autoAcceptRequest,proto3" json:"auto_accept_request,omitempty"`
}

func (x *SubmitBookingRequestResult) Reset() {
	*x = SubmitBookingRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitBookingRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitBookingRequestResult) ProtoMessage() {}

func (x *SubmitBookingRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitBookingRequestResult.ProtoReflect.Descriptor instead.
func (*SubmitBookingRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{16}
}

func (x *SubmitBookingRequestResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubmitBookingRequestResult) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *SubmitBookingRequestResult) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SubmitBookingRequestResult) GetAutoAcceptRequest() bool {
	if x != nil {
		return x.AutoAcceptRequest
	}
	return false
}

// The params message for reschedule booking request
type RescheduleBookingRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// reschedule grooming params
	GroomingReschedule *v11.GroomingRescheduleDef `protobuf:"bytes,2,opt,name=grooming_reschedule,json=groomingReschedule,proto3" json:"grooming_reschedule,omitempty"`
}

func (x *RescheduleBookingRequestParams) Reset() {
	*x = RescheduleBookingRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleBookingRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleBookingRequestParams) ProtoMessage() {}

func (x *RescheduleBookingRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleBookingRequestParams.ProtoReflect.Descriptor instead.
func (*RescheduleBookingRequestParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{17}
}

func (x *RescheduleBookingRequestParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RescheduleBookingRequestParams) GetGroomingReschedule() *v11.GroomingRescheduleDef {
	if x != nil {
		return x.GroomingReschedule
	}
	return nil
}

// The result message for reschedule booking request
type RescheduleBookingRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The flag of auto accepted
	IsAutoAccept bool `protobuf:"varint,1,opt,name=is_auto_accept,json=isAutoAccept,proto3" json:"is_auto_accept,omitempty"`
}

func (x *RescheduleBookingRequestResult) Reset() {
	*x = RescheduleBookingRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleBookingRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleBookingRequestResult) ProtoMessage() {}

func (x *RescheduleBookingRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleBookingRequestResult.ProtoReflect.Descriptor instead.
func (*RescheduleBookingRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{18}
}

func (x *RescheduleBookingRequestResult) GetIsAutoAccept() bool {
	if x != nil {
		return x.IsAutoAccept
	}
	return false
}

// The params message for cancel booking request
type CancelBookingRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The booking request id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CancelBookingRequestParams) Reset() {
	*x = CancelBookingRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelBookingRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelBookingRequestParams) ProtoMessage() {}

func (x *CancelBookingRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelBookingRequestParams.ProtoReflect.Descriptor instead.
func (*CancelBookingRequestParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{19}
}

func (x *CancelBookingRequestParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// The result message for cancel booking request
type CancelBookingRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CancelBookingRequestResult) Reset() {
	*x = CancelBookingRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelBookingRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelBookingRequestResult) ProtoMessage() {}

func (x *CancelBookingRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelBookingRequestResult.ProtoReflect.Descriptor instead.
func (*CancelBookingRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{20}
}

// Create booking request request
type CalculateBookingRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*CalculateBookingRequestParams_Name
	//	*CalculateBookingRequestParams_Domain
	Anonymous isCalculateBookingRequestParams_Anonymous `protobuf_oneof:"anonymous"`
	// Pet to services mapping
	PetServices []*PetServices `protobuf:"bytes,4,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
}

func (x *CalculateBookingRequestParams) Reset() {
	*x = CalculateBookingRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateBookingRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateBookingRequestParams) ProtoMessage() {}

func (x *CalculateBookingRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateBookingRequestParams.ProtoReflect.Descriptor instead.
func (*CalculateBookingRequestParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{21}
}

func (m *CalculateBookingRequestParams) GetAnonymous() isCalculateBookingRequestParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *CalculateBookingRequestParams) GetName() string {
	if x, ok := x.GetAnonymous().(*CalculateBookingRequestParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *CalculateBookingRequestParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*CalculateBookingRequestParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *CalculateBookingRequestParams) GetPetServices() []*PetServices {
	if x != nil {
		return x.PetServices
	}
	return nil
}

type isCalculateBookingRequestParams_Anonymous interface {
	isCalculateBookingRequestParams_Anonymous()
}

type CalculateBookingRequestParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type CalculateBookingRequestParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*CalculateBookingRequestParams_Name) isCalculateBookingRequestParams_Anonymous() {}

func (*CalculateBookingRequestParams_Domain) isCalculateBookingRequestParams_Anonymous() {}

// Calculate booking request response
type CalculateBookingRequestResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// estimated total price
	EstimatedTotalPrice float64 `protobuf:"fixed64,1,opt,name=estimated_total_price,json=estimatedTotalPrice,proto3" json:"estimated_total_price,omitempty"`
}

func (x *CalculateBookingRequestResult) Reset() {
	*x = CalculateBookingRequestResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateBookingRequestResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateBookingRequestResult) ProtoMessage() {}

func (x *CalculateBookingRequestResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateBookingRequestResult.ProtoReflect.Descriptor instead.
func (*CalculateBookingRequestResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{22}
}

func (x *CalculateBookingRequestResult) GetEstimatedTotalPrice() float64 {
	if x != nil {
		return x.EstimatedTotalPrice
	}
	return 0
}

// OB additional info
type Customer_AdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Referral source
	ReferralSourceId *int32 `protobuf:"varint,1,opt,name=referral_source_id,json=referralSourceId,proto3,oneof" json:"referral_source_id,omitempty"`
	// Referral source desc
	ReferralSourceDesc *string `protobuf:"bytes,2,opt,name=referral_source_desc,json=referralSourceDesc,proto3,oneof" json:"referral_source_desc,omitempty"`
	// Preferred groomer
	PreferredGroomerId *int32 `protobuf:"varint,3,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3,oneof" json:"preferred_groomer_id,omitempty"`
	// Preferred frequency
	PreferredFrequencyDay *int32 `protobuf:"varint,4,opt,name=preferred_frequency_day,json=preferredFrequencyDay,proto3,oneof" json:"preferred_frequency_day,omitempty"`
	// Preferred frequency type (0-by days, 1-by weeks)
	PreferredFrequencyType *int32 `protobuf:"varint,5,opt,name=preferred_frequency_type,json=preferredFrequencyType,proto3,oneof" json:"preferred_frequency_type,omitempty"`
	// Preferred days of the week
	PreferredDay []int32 `protobuf:"varint,6,rep,packed,name=preferred_day,json=preferredDay,proto3" json:"preferred_day,omitempty"`
	// Preferred times of the day
	PreferredTime []int32 `protobuf:"varint,7,rep,packed,name=preferred_time,json=preferredTime,proto3" json:"preferred_time,omitempty"`
}

func (x *Customer_AdditionalInfo) Reset() {
	*x = Customer_AdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Customer_AdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer_AdditionalInfo) ProtoMessage() {}

func (x *Customer_AdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer_AdditionalInfo.ProtoReflect.Descriptor instead.
func (*Customer_AdditionalInfo) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Customer_AdditionalInfo) GetReferralSourceId() int32 {
	if x != nil && x.ReferralSourceId != nil {
		return *x.ReferralSourceId
	}
	return 0
}

func (x *Customer_AdditionalInfo) GetReferralSourceDesc() string {
	if x != nil && x.ReferralSourceDesc != nil {
		return *x.ReferralSourceDesc
	}
	return ""
}

func (x *Customer_AdditionalInfo) GetPreferredGroomerId() int32 {
	if x != nil && x.PreferredGroomerId != nil {
		return *x.PreferredGroomerId
	}
	return 0
}

func (x *Customer_AdditionalInfo) GetPreferredFrequencyDay() int32 {
	if x != nil && x.PreferredFrequencyDay != nil {
		return *x.PreferredFrequencyDay
	}
	return 0
}

func (x *Customer_AdditionalInfo) GetPreferredFrequencyType() int32 {
	if x != nil && x.PreferredFrequencyType != nil {
		return *x.PreferredFrequencyType
	}
	return 0
}

func (x *Customer_AdditionalInfo) GetPreferredDay() []int32 {
	if x != nil {
		return x.PreferredDay
	}
	return nil
}

func (x *Customer_AdditionalInfo) GetPreferredTime() []int32 {
	if x != nil {
		return x.PreferredTime
	}
	return nil
}

// contact
type Customer_Contact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// First name
	FirstName *string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// Last name
	LastName *string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// Phone number
	PhoneNumber *string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
}

func (x *Customer_Contact) Reset() {
	*x = Customer_Contact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Customer_Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer_Contact) ProtoMessage() {}

func (x *Customer_Contact) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer_Contact.ProtoReflect.Descriptor instead.
func (*Customer_Contact) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{1, 2}
}

func (x *Customer_Contact) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *Customer_Contact) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *Customer_Contact) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

// OB request vaccine params
type Pet_Vaccine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Vaccine binding id
	VaccineBindingId *int64 `protobuf:"varint,1,opt,name=vaccine_binding_id,json=vaccineBindingId,proto3,oneof" json:"vaccine_binding_id,omitempty"`
	// Vaccine name
	Type *int32 `protobuf:"varint,2,opt,name=type,proto3,oneof" json:"type,omitempty"`
	// Vaccine id
	VaccineId *int32 `protobuf:"varint,3,opt,name=vaccine_id,json=vaccineId,proto3,oneof" json:"vaccine_id,omitempty"`
	// Vaccine name
	ExpirationDate *string `protobuf:"bytes,4,opt,name=expiration_date,json=expirationDate,proto3,oneof" json:"expiration_date,omitempty"`
	// Vaccine document
	VaccineDocument *string `protobuf:"bytes,5,opt,name=vaccine_document,json=vaccineDocument,proto3,oneof" json:"vaccine_document,omitempty"`
	// Document urls
	DocumentUrls []string `protobuf:"bytes,6,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
}

func (x *Pet_Vaccine) Reset() {
	*x = Pet_Vaccine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pet_Vaccine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pet_Vaccine) ProtoMessage() {}

func (x *Pet_Vaccine) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pet_Vaccine.ProtoReflect.Descriptor instead.
func (*Pet_Vaccine) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Pet_Vaccine) GetVaccineBindingId() int64 {
	if x != nil && x.VaccineBindingId != nil {
		return *x.VaccineBindingId
	}
	return 0
}

func (x *Pet_Vaccine) GetType() int32 {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return 0
}

func (x *Pet_Vaccine) GetVaccineId() int32 {
	if x != nil && x.VaccineId != nil {
		return *x.VaccineId
	}
	return 0
}

func (x *Pet_Vaccine) GetExpirationDate() string {
	if x != nil && x.ExpirationDate != nil {
		return *x.ExpirationDate
	}
	return ""
}

func (x *Pet_Vaccine) GetVaccineDocument() string {
	if x != nil && x.VaccineDocument != nil {
		return *x.VaccineDocument
	}
	return ""
}

func (x *Pet_Vaccine) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

// Grooming service
type Service_Grooming struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// start date
	StartDate string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// addons
	Addons []*GroomingAddon `protobuf:"bytes,4,rep,name=addons,proto3" json:"addons,omitempty"`
}

func (x *Service_Grooming) Reset() {
	*x = Service_Grooming{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_Grooming) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_Grooming) ProtoMessage() {}

func (x *Service_Grooming) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_Grooming.ProtoReflect.Descriptor instead.
func (*Service_Grooming) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{9, 0}
}

func (x *Service_Grooming) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *Service_Grooming) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *Service_Grooming) GetAddons() []*GroomingAddon {
	if x != nil {
		return x.Addons
	}
	return nil
}

// Boarding service
type Service_Boarding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Boarding service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Arrival date
	// 当 boarding service detail 加入 waitlist 时，这个参数可以为空
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// Arrival time, in minutes since midnight
	ArrivalTime int32 `protobuf:"varint,4,opt,name=arrival_time,json=arrivalTime,proto3" json:"arrival_time,omitempty"`
	// Pick up date
	// 当 boarding service detail 加入 waitlist 时，这个参数可以为空
	EndDate *string `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// Pick up time, in minutes since midnight
	PickupTime int32 `protobuf:"varint,6,opt,name=pickup_time,json=pickupTime,proto3" json:"pickup_time,omitempty"`
	// Service price
	ServicePrice float64 `protobuf:"fixed64,7,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// Tax
	TaxId int64 `protobuf:"varint,8,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// Feeding. Deprecated, use feedings instead
	//
	// Deprecated: Do not use.
	Feeding *Feeding `protobuf:"bytes,9,opt,name=feeding,proto3,oneof" json:"feeding,omitempty"`
	// Medication. Deprecated, use medications instead
	//
	// Deprecated: Do not use.
	Medication *Medication `protobuf:"bytes,10,opt,name=medication,proto3,oneof" json:"medication,omitempty"`
	// Addons
	Addons []*BoardingAddon `protobuf:"bytes,11,rep,name=addons,proto3" json:"addons,omitempty"`
	// Feedings
	Feedings []*Feeding `protobuf:"bytes,12,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// Medications
	Medications []*Medication `protobuf:"bytes,13,rep,name=medications,proto3" json:"medications,omitempty"`
	// boarding service waitlist
	Waitlist *v12.CreateBoardingServiceWaitlistRequest `protobuf:"bytes,15,opt,name=waitlist,proto3,oneof" json:"waitlist,omitempty"`
}

func (x *Service_Boarding) Reset() {
	*x = Service_Boarding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_Boarding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_Boarding) ProtoMessage() {}

func (x *Service_Boarding) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_Boarding.ProtoReflect.Descriptor instead.
func (*Service_Boarding) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{9, 1}
}

func (x *Service_Boarding) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *Service_Boarding) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *Service_Boarding) GetArrivalTime() int32 {
	if x != nil {
		return x.ArrivalTime
	}
	return 0
}

func (x *Service_Boarding) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

func (x *Service_Boarding) GetPickupTime() int32 {
	if x != nil {
		return x.PickupTime
	}
	return 0
}

func (x *Service_Boarding) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *Service_Boarding) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

// Deprecated: Do not use.
func (x *Service_Boarding) GetFeeding() *Feeding {
	if x != nil {
		return x.Feeding
	}
	return nil
}

// Deprecated: Do not use.
func (x *Service_Boarding) GetMedication() *Medication {
	if x != nil {
		return x.Medication
	}
	return nil
}

func (x *Service_Boarding) GetAddons() []*BoardingAddon {
	if x != nil {
		return x.Addons
	}
	return nil
}

func (x *Service_Boarding) GetFeedings() []*Feeding {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *Service_Boarding) GetMedications() []*Medication {
	if x != nil {
		return x.Medications
	}
	return nil
}

func (x *Service_Boarding) GetWaitlist() *v12.CreateBoardingServiceWaitlistRequest {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

// Daycare service
type Service_Daycare struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Daycare service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// Dates of the daycare, in the format of "yyyy-MM-dd"
	// 当 daycare service detail 加入 waitlist 时，这个参数可以为空
	Dates []string `protobuf:"bytes,2,rep,name=dates,proto3" json:"dates,omitempty"`
	// Arrival time, in minutes since midnight
	ArrivalTime int32 `protobuf:"varint,4,opt,name=arrival_time,json=arrivalTime,proto3" json:"arrival_time,omitempty"`
	// Pick up time, in minutes since midnight
	PickupTime *int32 `protobuf:"varint,6,opt,name=pickup_time,json=pickupTime,proto3,oneof" json:"pickup_time,omitempty"`
	// Service price
	ServicePrice float64 `protobuf:"fixed64,7,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// Tax
	TaxId int64 `protobuf:"varint,8,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// Max stay duration
	MaxDuration int32 `protobuf:"varint,9,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// Feeding. Deprecated, use feedings instead
	//
	// Deprecated: Do not use.
	Feeding *Feeding `protobuf:"bytes,10,opt,name=feeding,proto3,oneof" json:"feeding,omitempty"`
	// Medication. Deprecated, use medications instead
	//
	// Deprecated: Do not use.
	Medication *Medication `protobuf:"bytes,11,opt,name=medication,proto3,oneof" json:"medication,omitempty"`
	// Addons
	Addons []*DaycareAddon `protobuf:"bytes,12,rep,name=addons,proto3" json:"addons,omitempty"`
	// Feedings
	Feedings []*Feeding `protobuf:"bytes,13,rep,name=feedings,proto3" json:"feedings,omitempty"`
	// Medications
	Medications []*Medication `protobuf:"bytes,14,rep,name=medications,proto3" json:"medications,omitempty"`
	// daycare service waitlist
	Waitlist *v12.CreateDaycareServiceWaitlistRequest `protobuf:"bytes,15,opt,name=waitlist,proto3,oneof" json:"waitlist,omitempty"`
}

func (x *Service_Daycare) Reset() {
	*x = Service_Daycare{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_Daycare) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_Daycare) ProtoMessage() {}

func (x *Service_Daycare) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_Daycare.ProtoReflect.Descriptor instead.
func (*Service_Daycare) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{9, 2}
}

func (x *Service_Daycare) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *Service_Daycare) GetDates() []string {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *Service_Daycare) GetArrivalTime() int32 {
	if x != nil {
		return x.ArrivalTime
	}
	return 0
}

func (x *Service_Daycare) GetPickupTime() int32 {
	if x != nil && x.PickupTime != nil {
		return *x.PickupTime
	}
	return 0
}

func (x *Service_Daycare) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *Service_Daycare) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *Service_Daycare) GetMaxDuration() int32 {
	if x != nil {
		return x.MaxDuration
	}
	return 0
}

// Deprecated: Do not use.
func (x *Service_Daycare) GetFeeding() *Feeding {
	if x != nil {
		return x.Feeding
	}
	return nil
}

// Deprecated: Do not use.
func (x *Service_Daycare) GetMedication() *Medication {
	if x != nil {
		return x.Medication
	}
	return nil
}

func (x *Service_Daycare) GetAddons() []*DaycareAddon {
	if x != nil {
		return x.Addons
	}
	return nil
}

func (x *Service_Daycare) GetFeedings() []*Feeding {
	if x != nil {
		return x.Feedings
	}
	return nil
}

func (x *Service_Daycare) GetMedications() []*Medication {
	if x != nil {
		return x.Medications
	}
	return nil
}

func (x *Service_Daycare) GetWaitlist() *v12.CreateDaycareServiceWaitlistRequest {
	if x != nil {
		return x.Waitlist
	}
	return nil
}

// Evaluation service
type Service_Evaluation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Evaluation service id
	// 这个是 evaluation id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The date of the evaluation, in the format of "yyyy-MM-dd"
	Date string `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// Minutes since midnight
	Time int32 `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	// Service price
	ServicePrice float64 `protobuf:"fixed64,4,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// Duration
	Duration int32 `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`
	// 这个 evaluation 绑定的 service id
	// 暂时只有 boarding & daycare service
	TargetServiceId *int64 `protobuf:"varint,6,opt,name=target_service_id,json=targetServiceId,proto3,oneof" json:"target_service_id,omitempty"`
}

func (x *Service_Evaluation) Reset() {
	*x = Service_Evaluation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_Evaluation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_Evaluation) ProtoMessage() {}

func (x *Service_Evaluation) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_Evaluation.ProtoReflect.Descriptor instead.
func (*Service_Evaluation) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{9, 3}
}

func (x *Service_Evaluation) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *Service_Evaluation) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *Service_Evaluation) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *Service_Evaluation) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *Service_Evaluation) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Service_Evaluation) GetTargetServiceId() int64 {
	if x != nil && x.TargetServiceId != nil {
		return *x.TargetServiceId
	}
	return 0
}

// Dog walking service
type Service_DogWalking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Dog walking service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// The staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The date of the evaluation, in the format of "yyyy-MM-dd"
	Date string `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	// Minutes since midnight
	Time int32 `protobuf:"varint,5,opt,name=time,proto3" json:"time,omitempty"`
	// Service price
	ServicePrice float64 `protobuf:"fixed64,6,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
	// Duration
	Duration int32 `protobuf:"varint,7,opt,name=duration,proto3" json:"duration,omitempty"`
	// Tax
	TaxId int64 `protobuf:"varint,8,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
}

func (x *Service_DogWalking) Reset() {
	*x = Service_DogWalking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_DogWalking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_DogWalking) ProtoMessage() {}

func (x *Service_DogWalking) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_DogWalking.ProtoReflect.Descriptor instead.
func (*Service_DogWalking) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{9, 4}
}

func (x *Service_DogWalking) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *Service_DogWalking) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *Service_DogWalking) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *Service_DogWalking) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *Service_DogWalking) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

func (x *Service_DogWalking) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Service_DogWalking) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

// Group class service
type Service_GroupClass struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Group class instance id
	GroupClassInstanceId int64 `protobuf:"varint,1,opt,name=group_class_instance_id,json=groupClassInstanceId,proto3" json:"group_class_instance_id,omitempty"`
	// The trainer id, same to the staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// The dates of each group session
	Dates []string `protobuf:"bytes,3,rep,name=dates,proto3" json:"dates,omitempty"`
	// The start time of per group class session
	StartTime int32 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The end time of per group class session
	EndTime int32 `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// The group class price
	ServicePrice float64 `protobuf:"fixed64,6,opt,name=service_price,json=servicePrice,proto3" json:"service_price,omitempty"`
}

func (x *Service_GroupClass) Reset() {
	*x = Service_GroupClass{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service_GroupClass) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service_GroupClass) ProtoMessage() {}

func (x *Service_GroupClass) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service_GroupClass.ProtoReflect.Descriptor instead.
func (*Service_GroupClass) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP(), []int{9, 5}
}

func (x *Service_GroupClass) GetGroupClassInstanceId() int64 {
	if x != nil {
		return x.GroupClassInstanceId
	}
	return 0
}

func (x *Service_GroupClass) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *Service_GroupClass) GetDates() []string {
	if x != nil {
		return x.Dates
	}
	return nil
}

func (x *Service_GroupClass) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Service_GroupClass) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Service_GroupClass) GetServicePrice() float64 {
	if x != nil {
		return x.ServicePrice
	}
	return 0
}

var File_moego_client_online_booking_v1_booking_request_api_proto protoreflect.FileDescriptor

var file_moego_client_online_booking_v1_booking_request_api_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70,
	0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9f, 0x04, 0x0a, 0x07, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18,
	0xff, 0x01, 0x48, 0x01, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x88, 0x01,
	0x01, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48,
	0x02, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x88, 0x01, 0x01, 0x12, 0x23,
	0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x03, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79,
	0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x04,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x07, 0x7a, 0x69,
	0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x05, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff,
	0x01, 0x48, 0x06, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x1e, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x07, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x1e, 0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x08, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12,
	0x40, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x09, 0x52, 0x17, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01,
	0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x31, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x32, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x08, 0x0a, 0x06, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64,
	0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x06, 0x0a,
	0x04, 0x5f, 0x6c, 0x61, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x6e, 0x67, 0x42, 0x1d, 0x0a,
	0x1b, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xad, 0x0e, 0x0a,
	0x08, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x0a, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x09, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x09, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0xd0, 0x01, 0x01,
	0x60, 0x01, 0x48, 0x03, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x59,
	0x0a, 0x0b, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x41, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x4d, 0x61, 0x70, 0x12, 0x26, 0x0a, 0x0c, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x04, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x68, 0x61, 0x73, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x0d, 0x68, 0x61,
	0x73, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x61, 0x72, 0x64, 0x88, 0x01, 0x01, 0x12, 0x31,
	0x0a, 0x12, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x10, 0x73, 0x74,
	0x72, 0x69, 0x70, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x65, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x07, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x48, 0x08, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x3b, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x09,
	0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x5d, 0x0a,
	0x11, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x10, 0x65, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x57, 0x0a, 0x0e,
	0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x0d, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x1a, 0x55, 0x0a, 0x0f, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xa9, 0x04, 0x0a,
	0x0e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3a, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x1a, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x14, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x14,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x48, 0x02, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x17,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x03, 0x52, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x79, 0x88,
	0x01, 0x01, 0x12, 0x46, 0x0a, 0x18, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x04, 0x52,
	0x16, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x61, 0x79, 0x12,
	0x25, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x17, 0x0a,
	0x15, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42,
	0x1a, 0x0a, 0x18, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x42, 0x1b, 0x0a, 0x19, 0x5f,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x1a, 0xc0, 0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18,
	0x32, 0x48, 0x00, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x29, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x48, 0x01, 0x52,
	0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x1e, 0x48, 0x02, 0x52, 0x0b, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x68, 0x61, 0x73, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42,
	0x12, 0x0a, 0x10, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x22, 0x9b, 0x0f, 0x0a,
	0x03, 0x50, 0x65, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x07, 0x70, 0x65, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61,
	0x74, 0x68, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x03,
	0x52, 0x05, 0x62, 0x72, 0x65, 0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x62, 0x72,
	0x65, 0x65, 0x64, 0x5f, 0x6d, 0x69, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x04, 0x52, 0x08, 0x62, 0x72, 0x65, 0x65, 0x64, 0x4d,
	0x69, 0x78, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x20, 0x00, 0x48, 0x05, 0x52, 0x09, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x06, 0x52, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x08, 0x62, 0x69, 0x72,
	0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0xfa, 0x42, 0x1a,
	0x72, 0x18, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0xd0, 0x01, 0x01, 0x48, 0x07, 0x52, 0x08, 0x62, 0x69,
	0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x06, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x48, 0x08, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x23, 0x0a, 0x05, 0x66, 0x69, 0x78, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x09, 0x52, 0x05, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x08, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f,
	0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff,
	0x01, 0x48, 0x0a, 0x52, 0x08, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x2e, 0x0a, 0x0b, 0x68, 0x61, 0x69, 0x72, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48,
	0x0b, 0x52, 0x0a, 0x68, 0x61, 0x69, 0x72, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x88, 0x01, 0x01,
	0x12, 0x3d, 0x0a, 0x13, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x0c, 0x52, 0x12, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x28, 0x0a, 0x08, 0x76, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x0d, 0x52, 0x07, 0x76,
	0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x09, 0x76, 0x65, 0x74,
	0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x0e, 0x52, 0x08, 0x76, 0x65, 0x74, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x76, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0f, 0x52, 0x0a, 0x76, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x43, 0x0a, 0x16, 0x65,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x10, 0x52, 0x14, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e,
	0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x45, 0x0a, 0x17, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x11, 0x52, 0x15, 0x65,
	0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10, 0x48, 0x12, 0x52, 0x0c, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x49, 0x73, 0x73, 0x75, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x4e, 0x0a, 0x0c, 0x76,
	0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x2e, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x52, 0x0b,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x09, 0x70,
	0x65, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10, 0x48, 0x13, 0x52, 0x08, 0x70, 0x65, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x6d, 0x0a, 0x14, 0x70, 0x65, 0x74, 0x5f, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x18,
	0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x12, 0x70, 0x65, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x1a, 0x5d, 0x0a, 0x17, 0x50, 0x65, 0x74, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x8e, 0x03, 0x0a, 0x07, 0x56, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x12, 0x3a, 0x0a, 0x12, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x10, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x1a, 0x02, 0x28, 0x00, 0x48, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2b, 0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x02, 0x52, 0x09,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4b, 0x0a, 0x0f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0xfa, 0x42, 0x1a, 0x72, 0x18, 0x32, 0x13, 0x5e, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x24, 0xd0, 0x01, 0x01, 0x48, 0x03, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x10, 0x76, 0x61, 0x63,
	0x63, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x0f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x73, 0x42, 0x15,
	0x0a, 0x13, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x62, 0x72, 0x65,
	0x65, 0x64, 0x5f, 0x6d, 0x69, 0x78, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72,
	0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x68, 0x61, 0x69, 0x72, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x42, 0x16, 0x0a, 0x14, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x76, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x76, 0x65, 0x74, 0x5f, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x76, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x1a,
	0x0a, 0x18, 0x5f, 0x65, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x73, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x42, 0x0a, 0x09, 0x41, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0xe5,
	0x03, 0x0a, 0x07, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x50, 0x0a, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e,
	0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x42, 0x10, 0x18, 0x01,
	0xfa, 0x42, 0x0b, 0x12, 0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x00,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x04, 0x75,
	0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2a,
	0x0a, 0x09, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x08, 0x66,
	0x6f, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0b, 0x66, 0x6f,
	0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x03, 0x52, 0x0a, 0x66, 0x6f, 0x6f,
	0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10, 0x48, 0x04, 0x52, 0x0b, 0x69, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x04, 0x6e,
	0x6f, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x48, 0x05, 0x52, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2c,
	0x0a, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x06, 0x52, 0x09,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x75, 0x6e, 0x69, 0x74,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x22, 0x90, 0x04, 0x0a, 0x0a, 0x4d, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2d, 0x0a,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x42, 0x10, 0x18,
	0x01, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48,
	0x00, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x04,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x36, 0x0a, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0xff, 0x01, 0x48, 0x02, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10,
	0x48, 0x03, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0a,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x04, 0x52, 0x09, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x0d, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x44, 0x65, 0x66, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x48, 0x05, 0x52, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6d,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x08,
	0x0a, 0x06, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0xe6, 0x03, 0x0a, 0x0d, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79,
	0x5f, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00,
	0x52, 0x0a, 0x69, 0x73, 0x45, 0x76, 0x65, 0x72, 0x79, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x3e, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x28,
	0xfa, 0x42, 0x25, 0x92, 0x01, 0x22, 0x22, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x10, 0x71, 0x75, 0x61, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x01, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72,
	0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x02, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42,
	0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x79,
	0x42, 0x13, 0x0a, 0x11, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65,
	0x72, 0x5f, 0x64, 0x61, 0x79, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x22, 0xbb, 0x02, 0x0a, 0x0c, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64,
	0x64, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x73, 0x45, 0x76, 0x65, 0x72, 0x79, 0x44, 0x61, 0x79,
	0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x28, 0xfa, 0x42, 0x25, 0x92, 0x01, 0x22, 0x22, 0x20, 0x72, 0x1e, 0x32, 0x1c,
	0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b,
	0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x05, 0x64, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x10, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x0e, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x50, 0x65, 0x72, 0x44, 0x61, 0x79, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69,
	0x73, 0x5f, 0x65, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x42, 0x13, 0x0a, 0x11, 0x5f,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x79,
	0x22, 0x28, 0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f,
	0x6e, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0xeb, 0x19, 0x0a, 0x07, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4e, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x08, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x12, 0x4e, 0x0a, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x08, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x4b, 0x0a, 0x07, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x48, 0x00, 0x52, 0x07, 0x64, 0x61, 0x79, 0x63,
	0x61, 0x72, 0x65, 0x12, 0x54, 0x0a, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0a, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x0b, 0x64, 0x6f, 0x67,
	0x5f, 0x77, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69,
	0x6e, 0x67, 0x48, 0x00, 0x52, 0x0a, 0x64, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67,
	0x12, 0x55, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x48, 0x00, 0x52, 0x0a, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x1a, 0xbd, 0x01, 0x0a, 0x08, 0x47, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x23, 0xfa, 0x42, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b,
	0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x45, 0x0a, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52,
	0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x1a, 0xff, 0x06, 0x0a, 0x08, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x23, 0xfa, 0x42, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b,
	0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39,
	0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0c, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x0b, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0xfa, 0x42, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e,
	0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32,
	0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x01, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0b, 0x70, 0x69, 0x63,
	0x6b, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x0a, 0x70, 0x69, 0x63, 0x6b,
	0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74,
	0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78,
	0x49, 0x64, 0x12, 0x4a, 0x0a, 0x07, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x02, 0x18, 0x01,
	0x48, 0x02, 0x52, 0x07, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x53,
	0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x02,
	0x18, 0x01, 0x48, 0x03, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x6f, 0x6e, 0x52, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x0a, 0x08, 0x66, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65,
	0x65, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x4c, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x66, 0x0a,
	0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x04, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69,
	0x73, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0xca, 0x06, 0x0a, 0x07, 0x44, 0x61,
	0x79, 0x63, 0x61, 0x72, 0x65, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3e, 0x0a,
	0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x28, 0xfa, 0x42,
	0x25, 0x92, 0x01, 0x22, 0x22, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d,
	0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d,
	0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x2d, 0x0a,
	0x0c, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52,
	0x0b, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x0b,
	0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x00, 0x52,
	0x0a, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61,
	0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a,
	0x07, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x02, 0x18, 0x01, 0x48, 0x01, 0x52, 0x07, 0x66,
	0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x53, 0x0a, 0x0a, 0x6d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x02, 0x18, 0x01, 0x48, 0x02, 0x52,
	0x0a, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x44,
	0x0a, 0x06, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x73, 0x12, 0x43, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x08, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0b, 0x6d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6d, 0x65, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x65, 0x0a, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x03, 0x52, 0x08, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0e,
	0x0a, 0x0c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6d,
	0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x77, 0x61,
	0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0x95, 0x02, 0x0a, 0x0a, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x37, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0xfa, 0x42, 0x20,
	0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00,
	0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x1a, 0x89,
	0x02, 0x0a, 0x0a, 0x44, 0x6f, 0x67, 0x57, 0x61, 0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x26, 0x0a,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0xfa, 0x42, 0x20, 0x72, 0x1e, 0x32, 0x1c,
	0x5e, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b,
	0x32, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x1e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x1a, 0xab, 0x02, 0x0a, 0x0a, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x3e, 0x0a, 0x17, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x14, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x42, 0x0a,
	0x05, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x2c, 0xfa, 0x42,
	0x29, 0x92, 0x01, 0x26, 0x08, 0x01, 0x10, 0x64, 0x22, 0x20, 0x72, 0x1e, 0x32, 0x1c, 0x5e, 0x5b,
	0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x34, 0x7d, 0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d,
	0x2d, 0x5b, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x32, 0x7d, 0x24, 0x52, 0x05, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x29, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28,
	0x00, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x0e, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xb0, 0x01, 0x0a, 0x06, 0x50, 0x72, 0x65,
	0x50, 0x61, 0x79, 0x12, 0x33, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12,
	0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2d, 0x0a, 0x0a, 0x74, 0x61, 0x78, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x09, 0x74, 0x61,
	0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xf0, 0x02, 0x0a, 0x07,
	0x50, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x12, 0x2f, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x73, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0a, 0x74, 0x69,
	0x70, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52,
	0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2d, 0x0a,
	0x0a, 0x74, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x52, 0x09, 0x74, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x15,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x13, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x34, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x2b, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff,
	0x01, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa9,
	0x01, 0x0a, 0x0c, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x31, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x12, 0x37, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x12, 0x09, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0e, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x0d, 0x64,
	0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0c, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x46, 0x0a, 0x0a, 0x4d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x38, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x49,
	0x64, 0x73, 0x22, 0xa4, 0x07, 0x0a, 0x1a, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x12, 0x49, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x48, 0x01, 0x52,
	0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x4e, 0x0a, 0x0c,
	0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52,
	0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x0a,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x70, 0x61,
	0x79, 0x5f, 0x67, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0a,
	0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x47, 0x75, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a,
	0x07, 0x70, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x65, 0x50, 0x61, 0x79, 0x42, 0x02, 0x18, 0x01, 0x48, 0x03, 0x52, 0x06, 0x70, 0x72,
	0x65, 0x50, 0x61, 0x79, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x04, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x47, 0x0a, 0x08, 0x70, 0x72, 0x65,
	0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65,
	0x41, 0x75, 0x74, 0x68, 0x48, 0x05, 0x52, 0x07, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x88,
	0x01, 0x01, 0x12, 0x56, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x48, 0x06, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x10, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x4f, 0x0a, 0x0a, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x48, 0x08, 0x52, 0x0a, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x09, 0x61,
	0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70,
	0x72, 0x65, 0x70, 0x61, 0x79, 0x5f, 0x67, 0x75, 0x69, 0x64, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x70,
	0x72, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x72, 0x65, 0x5f, 0x61,
	0x75, 0x74, 0x68, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x22, 0x93, 0x01, 0x0a, 0x0b, 0x50, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x03, 0x70, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x70, 0x65, 0x74, 0x12, 0x43, 0x0a, 0x08, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22,
	0x98, 0x01, 0x0a, 0x1a, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xa8, 0x01, 0x0a, 0x1e, 0x52,
	0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x6d, 0x0a, 0x13, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x12, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x22, 0x46, 0x0a, 0x1e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x69, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x22, 0x35, 0x0a,
	0x1a, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x1c, 0x0a, 0x1a, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0xb1, 0x01, 0x0a, 0x1d, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x4e, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75,
	0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x53, 0x0a, 0x1d, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x2a, 0x47, 0x0a, 0x0a, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x10, 0x01, 0x32, 0xf0, 0x04, 0x0a, 0x15, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8e,
	0x01, 0x0a, 0x14, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x9a, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8e, 0x01, 0x0a,
	0x14, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x97, 0x01,
	0x0a, 0x17, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x92, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x66, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_online_booking_v1_booking_request_api_proto_rawDescOnce sync.Once
	file_moego_client_online_booking_v1_booking_request_api_proto_rawDescData = file_moego_client_online_booking_v1_booking_request_api_proto_rawDesc
)

func file_moego_client_online_booking_v1_booking_request_api_proto_rawDescGZIP() []byte {
	file_moego_client_online_booking_v1_booking_request_api_proto_rawDescOnce.Do(func() {
		file_moego_client_online_booking_v1_booking_request_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_online_booking_v1_booking_request_api_proto_rawDescData)
	})
	return file_moego_client_online_booking_v1_booking_request_api_proto_rawDescData
}

var file_moego_client_online_booking_v1_booking_request_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_moego_client_online_booking_v1_booking_request_api_proto_goTypes = []interface{}{
	(SourceType)(0),                               // 0: moego.client.online_booking.v1.SourceType
	(*Address)(nil),                               // 1: moego.client.online_booking.v1.Address
	(*Customer)(nil),                              // 2: moego.client.online_booking.v1.Customer
	(*Pet)(nil),                                   // 3: moego.client.online_booking.v1.Pet
	(*Agreement)(nil),                             // 4: moego.client.online_booking.v1.Agreement
	(*Feeding)(nil),                               // 5: moego.client.online_booking.v1.Feeding
	(*Medication)(nil),                            // 6: moego.client.online_booking.v1.Medication
	(*BoardingAddon)(nil),                         // 7: moego.client.online_booking.v1.BoardingAddon
	(*DaycareAddon)(nil),                          // 8: moego.client.online_booking.v1.DaycareAddon
	(*GroomingAddon)(nil),                         // 9: moego.client.online_booking.v1.GroomingAddon
	(*Service)(nil),                               // 10: moego.client.online_booking.v1.Service
	(*PrePay)(nil),                                // 11: moego.client.online_booking.v1.PrePay
	(*PreAuth)(nil),                               // 12: moego.client.online_booking.v1.PreAuth
	(*DiscountCode)(nil),                          // 13: moego.client.online_booking.v1.DiscountCode
	(*Membership)(nil),                            // 14: moego.client.online_booking.v1.Membership
	(*SubmitBookingRequestParams)(nil),            // 15: moego.client.online_booking.v1.SubmitBookingRequestParams
	(*PetServices)(nil),                           // 16: moego.client.online_booking.v1.PetServices
	(*SubmitBookingRequestResult)(nil),            // 17: moego.client.online_booking.v1.SubmitBookingRequestResult
	(*RescheduleBookingRequestParams)(nil),        // 18: moego.client.online_booking.v1.RescheduleBookingRequestParams
	(*RescheduleBookingRequestResult)(nil),        // 19: moego.client.online_booking.v1.RescheduleBookingRequestResult
	(*CancelBookingRequestParams)(nil),            // 20: moego.client.online_booking.v1.CancelBookingRequestParams
	(*CancelBookingRequestResult)(nil),            // 21: moego.client.online_booking.v1.CancelBookingRequestResult
	(*CalculateBookingRequestParams)(nil),         // 22: moego.client.online_booking.v1.CalculateBookingRequestParams
	(*CalculateBookingRequestResult)(nil),         // 23: moego.client.online_booking.v1.CalculateBookingRequestResult
	nil,                                           // 24: moego.client.online_booking.v1.Customer.AnswersMapEntry
	(*Customer_AdditionalInfo)(nil),               // 25: moego.client.online_booking.v1.Customer.AdditionalInfo
	(*Customer_Contact)(nil),                      // 26: moego.client.online_booking.v1.Customer.Contact
	nil,                                           // 27: moego.client.online_booking.v1.Pet.PetQuestionAnswersEntry
	(*Pet_Vaccine)(nil),                           // 28: moego.client.online_booking.v1.Pet.Vaccine
	(*Service_Grooming)(nil),                      // 29: moego.client.online_booking.v1.Service.Grooming
	(*Service_Boarding)(nil),                      // 30: moego.client.online_booking.v1.Service.Boarding
	(*Service_Daycare)(nil),                       // 31: moego.client.online_booking.v1.Service.Daycare
	(*Service_Evaluation)(nil),                    // 32: moego.client.online_booking.v1.Service.Evaluation
	(*Service_DogWalking)(nil),                    // 33: moego.client.online_booking.v1.Service.DogWalking
	(*Service_GroupClass)(nil),                    // 34: moego.client.online_booking.v1.Service.GroupClass
	(*timestamppb.Timestamp)(nil),                 // 35: google.protobuf.Timestamp
	(*v1.FeedingModel_FeedingSchedule)(nil),       // 36: moego.models.online_booking.v1.FeedingModel.FeedingSchedule
	(*v1.MedicationModel_MedicationSchedule)(nil), // 37: moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	(*v11.AppointmentPetMedicationScheduleDef_SelectedDateDef)(nil), // 38: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	(v11.PetDetailDateType)(0),                                      // 39: moego.models.appointment.v1.PetDetailDateType
	(*date.Date)(nil),                                               // 40: google.type.Date
	(*v11.GroomingRescheduleDef)(nil),                               // 41: moego.models.appointment.v1.GroomingRescheduleDef
	(*structpb.Value)(nil),                                          // 42: google.protobuf.Value
	(*v12.CreateBoardingServiceWaitlistRequest)(nil),                // 43: moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequest
	(*v12.CreateDaycareServiceWaitlistRequest)(nil),                 // 44: moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequest
}
var file_moego_client_online_booking_v1_booking_request_api_proto_depIdxs = []int32{
	24, // 0: moego.client.online_booking.v1.Customer.answers_map:type_name -> moego.client.online_booking.v1.Customer.AnswersMapEntry
	25, // 1: moego.client.online_booking.v1.Customer.additional_info:type_name -> moego.client.online_booking.v1.Customer.AdditionalInfo
	1,  // 2: moego.client.online_booking.v1.Customer.address:type_name -> moego.client.online_booking.v1.Address
	35, // 3: moego.client.online_booking.v1.Customer.birthday:type_name -> google.protobuf.Timestamp
	26, // 4: moego.client.online_booking.v1.Customer.emergency_contact:type_name -> moego.client.online_booking.v1.Customer.Contact
	26, // 5: moego.client.online_booking.v1.Customer.pickup_contact:type_name -> moego.client.online_booking.v1.Customer.Contact
	28, // 6: moego.client.online_booking.v1.Pet.vaccine_list:type_name -> moego.client.online_booking.v1.Pet.Vaccine
	27, // 7: moego.client.online_booking.v1.Pet.pet_question_answers:type_name -> moego.client.online_booking.v1.Pet.PetQuestionAnswersEntry
	36, // 8: moego.client.online_booking.v1.Feeding.time:type_name -> moego.models.online_booking.v1.FeedingModel.FeedingSchedule
	37, // 9: moego.client.online_booking.v1.Medication.time:type_name -> moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	38, // 10: moego.client.online_booking.v1.Medication.selected_date:type_name -> moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	39, // 11: moego.client.online_booking.v1.BoardingAddon.date_type:type_name -> moego.models.appointment.v1.PetDetailDateType
	40, // 12: moego.client.online_booking.v1.BoardingAddon.start_date:type_name -> google.type.Date
	29, // 13: moego.client.online_booking.v1.Service.grooming:type_name -> moego.client.online_booking.v1.Service.Grooming
	30, // 14: moego.client.online_booking.v1.Service.boarding:type_name -> moego.client.online_booking.v1.Service.Boarding
	31, // 15: moego.client.online_booking.v1.Service.daycare:type_name -> moego.client.online_booking.v1.Service.Daycare
	32, // 16: moego.client.online_booking.v1.Service.evaluation:type_name -> moego.client.online_booking.v1.Service.Evaluation
	33, // 17: moego.client.online_booking.v1.Service.dog_walking:type_name -> moego.client.online_booking.v1.Service.DogWalking
	34, // 18: moego.client.online_booking.v1.Service.group_class:type_name -> moego.client.online_booking.v1.Service.GroupClass
	2,  // 19: moego.client.online_booking.v1.SubmitBookingRequestParams.customer:type_name -> moego.client.online_booking.v1.Customer
	16, // 20: moego.client.online_booking.v1.SubmitBookingRequestParams.pet_services:type_name -> moego.client.online_booking.v1.PetServices
	4,  // 21: moego.client.online_booking.v1.SubmitBookingRequestParams.agreements:type_name -> moego.client.online_booking.v1.Agreement
	11, // 22: moego.client.online_booking.v1.SubmitBookingRequestParams.pre_pay:type_name -> moego.client.online_booking.v1.PrePay
	0,  // 23: moego.client.online_booking.v1.SubmitBookingRequestParams.source_type:type_name -> moego.client.online_booking.v1.SourceType
	12, // 24: moego.client.online_booking.v1.SubmitBookingRequestParams.pre_auth:type_name -> moego.client.online_booking.v1.PreAuth
	13, // 25: moego.client.online_booking.v1.SubmitBookingRequestParams.discount_code:type_name -> moego.client.online_booking.v1.DiscountCode
	14, // 26: moego.client.online_booking.v1.SubmitBookingRequestParams.membership:type_name -> moego.client.online_booking.v1.Membership
	3,  // 27: moego.client.online_booking.v1.PetServices.pet:type_name -> moego.client.online_booking.v1.Pet
	10, // 28: moego.client.online_booking.v1.PetServices.services:type_name -> moego.client.online_booking.v1.Service
	41, // 29: moego.client.online_booking.v1.RescheduleBookingRequestParams.grooming_reschedule:type_name -> moego.models.appointment.v1.GroomingRescheduleDef
	16, // 30: moego.client.online_booking.v1.CalculateBookingRequestParams.pet_services:type_name -> moego.client.online_booking.v1.PetServices
	42, // 31: moego.client.online_booking.v1.Customer.AnswersMapEntry.value:type_name -> google.protobuf.Value
	42, // 32: moego.client.online_booking.v1.Pet.PetQuestionAnswersEntry.value:type_name -> google.protobuf.Value
	9,  // 33: moego.client.online_booking.v1.Service.Grooming.addons:type_name -> moego.client.online_booking.v1.GroomingAddon
	5,  // 34: moego.client.online_booking.v1.Service.Boarding.feeding:type_name -> moego.client.online_booking.v1.Feeding
	6,  // 35: moego.client.online_booking.v1.Service.Boarding.medication:type_name -> moego.client.online_booking.v1.Medication
	7,  // 36: moego.client.online_booking.v1.Service.Boarding.addons:type_name -> moego.client.online_booking.v1.BoardingAddon
	5,  // 37: moego.client.online_booking.v1.Service.Boarding.feedings:type_name -> moego.client.online_booking.v1.Feeding
	6,  // 38: moego.client.online_booking.v1.Service.Boarding.medications:type_name -> moego.client.online_booking.v1.Medication
	43, // 39: moego.client.online_booking.v1.Service.Boarding.waitlist:type_name -> moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequest
	5,  // 40: moego.client.online_booking.v1.Service.Daycare.feeding:type_name -> moego.client.online_booking.v1.Feeding
	6,  // 41: moego.client.online_booking.v1.Service.Daycare.medication:type_name -> moego.client.online_booking.v1.Medication
	8,  // 42: moego.client.online_booking.v1.Service.Daycare.addons:type_name -> moego.client.online_booking.v1.DaycareAddon
	5,  // 43: moego.client.online_booking.v1.Service.Daycare.feedings:type_name -> moego.client.online_booking.v1.Feeding
	6,  // 44: moego.client.online_booking.v1.Service.Daycare.medications:type_name -> moego.client.online_booking.v1.Medication
	44, // 45: moego.client.online_booking.v1.Service.Daycare.waitlist:type_name -> moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequest
	15, // 46: moego.client.online_booking.v1.BookingRequestService.SubmitBookingRequest:input_type -> moego.client.online_booking.v1.SubmitBookingRequestParams
	18, // 47: moego.client.online_booking.v1.BookingRequestService.RescheduleBookingRequest:input_type -> moego.client.online_booking.v1.RescheduleBookingRequestParams
	20, // 48: moego.client.online_booking.v1.BookingRequestService.CancelBookingRequest:input_type -> moego.client.online_booking.v1.CancelBookingRequestParams
	22, // 49: moego.client.online_booking.v1.BookingRequestService.CalculateBookingRequest:input_type -> moego.client.online_booking.v1.CalculateBookingRequestParams
	17, // 50: moego.client.online_booking.v1.BookingRequestService.SubmitBookingRequest:output_type -> moego.client.online_booking.v1.SubmitBookingRequestResult
	19, // 51: moego.client.online_booking.v1.BookingRequestService.RescheduleBookingRequest:output_type -> moego.client.online_booking.v1.RescheduleBookingRequestResult
	21, // 52: moego.client.online_booking.v1.BookingRequestService.CancelBookingRequest:output_type -> moego.client.online_booking.v1.CancelBookingRequestResult
	23, // 53: moego.client.online_booking.v1.BookingRequestService.CalculateBookingRequest:output_type -> moego.client.online_booking.v1.CalculateBookingRequestResult
	50, // [50:54] is the sub-list for method output_type
	46, // [46:50] is the sub-list for method input_type
	46, // [46:46] is the sub-list for extension type_name
	46, // [46:46] is the sub-list for extension extendee
	0,  // [0:46] is the sub-list for field type_name
}

func init() { file_moego_client_online_booking_v1_booking_request_api_proto_init() }
func file_moego_client_online_booking_v1_booking_request_api_proto_init() {
	if File_moego_client_online_booking_v1_booking_request_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Address); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Customer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Agreement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Feeding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Medication); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingAddon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrePay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreAuth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DiscountCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Membership); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitBookingRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetServices); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitBookingRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleBookingRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleBookingRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelBookingRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelBookingRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateBookingRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateBookingRequestResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Customer_AdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Customer_Contact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pet_Vaccine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_Grooming); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_Boarding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_Daycare); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_Evaluation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_DogWalking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service_GroupClass); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*Service_Grooming_)(nil),
		(*Service_Boarding_)(nil),
		(*Service_Daycare_)(nil),
		(*Service_Evaluation_)(nil),
		(*Service_DogWalking_)(nil),
		(*Service_GroupClass_)(nil),
	}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*SubmitBookingRequestParams_Name)(nil),
		(*SubmitBookingRequestParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*CalculateBookingRequestParams_Name)(nil),
		(*CalculateBookingRequestParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[24].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[25].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[27].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[29].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[30].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes[31].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_online_booking_v1_booking_request_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_online_booking_v1_booking_request_api_proto_goTypes,
		DependencyIndexes: file_moego_client_online_booking_v1_booking_request_api_proto_depIdxs,
		EnumInfos:         file_moego_client_online_booking_v1_booking_request_api_proto_enumTypes,
		MessageInfos:      file_moego_client_online_booking_v1_booking_request_api_proto_msgTypes,
	}.Build()
	File_moego_client_online_booking_v1_booking_request_api_proto = out.File
	file_moego_client_online_booking_v1_booking_request_api_proto_rawDesc = nil
	file_moego_client_online_booking_v1_booking_request_api_proto_goTypes = nil
	file_moego_client_online_booking_v1_booking_request_api_proto_depIdxs = nil
}
