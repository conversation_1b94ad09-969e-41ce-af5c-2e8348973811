// @since 2024-06-24 18:09:23
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/appointment/v1/daily_report_defs.proto

package appointmentpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// report def
type ReportDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// daily report content
	Content *ContentDef `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *ReportDef) Reset() {
	*x = ReportDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportDef) ProtoMessage() {}

func (x *ReportDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportDef.ProtoReflect.Descriptor instead.
func (*ReportDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_defs_proto_rawDescGZIP(), []int{0}
}

func (x *ReportDef) GetContent() *ContentDef {
	if x != nil {
		return x.Content
	}
	return nil
}

// content
type ContentDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// photo
	Photos []string `protobuf:"bytes,1,rep,name=photos,proto3" json:"photos,omitempty"`
	// video
	Videos []string `protobuf:"bytes,2,rep,name=videos,proto3" json:"videos,omitempty"`
	// overall feedbacks
	Feedbacks []*QuestionDef `protobuf:"bytes,3,rep,name=feedbacks,proto3" json:"feedbacks,omitempty"`
	// theme color
	ThemeColor string `protobuf:"bytes,4,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`
}

func (x *ContentDef) Reset() {
	*x = ContentDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentDef) ProtoMessage() {}

func (x *ContentDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentDef.ProtoReflect.Descriptor instead.
func (*ContentDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_defs_proto_rawDescGZIP(), []int{1}
}

func (x *ContentDef) GetPhotos() []string {
	if x != nil {
		return x.Photos
	}
	return nil
}

func (x *ContentDef) GetVideos() []string {
	if x != nil {
		return x.Videos
	}
	return nil
}

func (x *ContentDef) GetFeedbacks() []*QuestionDef {
	if x != nil {
		return x.Feedbacks
	}
	return nil
}

func (x *ContentDef) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

// question
type QuestionDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// category
	Category QuestionCategoryType `protobuf:"varint,2,opt,name=category,proto3,enum=moego.models.appointment.v1.QuestionCategoryType" json:"category,omitempty"`
	// type, 1: single choice, 2: multiple choice, 3: input text, 4: short input text, 5: tag choice
	Type QuestionType `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.appointment.v1.QuestionType" json:"type,omitempty"`
	// key, unique for each question
	Key string `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	// title, question title
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// required
	Required bool `protobuf:"varint,6,opt,name=required,proto3" json:"required,omitempty"`
	// show
	Show bool `protobuf:"varint,7,opt,name=show,proto3" json:"show,omitempty"`
	// options, only for single choice and multiple choice
	Options []string `protobuf:"bytes,8,rep,name=options,proto3" json:"options,omitempty"`
	// choices, only for single choice and multiple choice
	Choices []string `protobuf:"bytes,9,rep,name=choices,proto3" json:"choices,omitempty"`
	// custom options
	CustomOptions []string `protobuf:"bytes,10,rep,name=custom_options,json=customOptions,proto3" json:"custom_options,omitempty"`
	// input text, only for input text
	InputText string `protobuf:"bytes,11,opt,name=input_text,json=inputText,proto3" json:"input_text,omitempty"`
	// placeholder, input text placeholder
	Placeholder string `protobuf:"bytes,12,opt,name=placeholder,proto3" json:"placeholder,omitempty"`
}

func (x *QuestionDef) Reset() {
	*x = QuestionDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuestionDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestionDef) ProtoMessage() {}

func (x *QuestionDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestionDef.ProtoReflect.Descriptor instead.
func (*QuestionDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_defs_proto_rawDescGZIP(), []int{2}
}

func (x *QuestionDef) GetCategory() QuestionCategoryType {
	if x != nil {
		return x.Category
	}
	return QuestionCategoryType_QUESTION_CATEGORY_TYPE_UNSPECIFIED
}

func (x *QuestionDef) GetType() QuestionType {
	if x != nil {
		return x.Type
	}
	return QuestionType_QUESTION_TYPE_UNSPECIFIED
}

func (x *QuestionDef) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *QuestionDef) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *QuestionDef) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *QuestionDef) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *QuestionDef) GetOptions() []string {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *QuestionDef) GetChoices() []string {
	if x != nil {
		return x.Choices
	}
	return nil
}

func (x *QuestionDef) GetCustomOptions() []string {
	if x != nil {
		return x.CustomOptions
	}
	return nil
}

func (x *QuestionDef) GetInputText() string {
	if x != nil {
		return x.InputText
	}
	return ""
}

func (x *QuestionDef) GetPlaceholder() string {
	if x != nil {
		return x.Placeholder
	}
	return ""
}

// daily report sent history def
type SentHistoryRecordDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// report
	Report *ReportDef `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
	// description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// service date
	ServiceDate *date.Date `protobuf:"bytes,3,opt,name=service_date,json=serviceDate,proto3" json:"service_date,omitempty"`
	// send time
	SendTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	// sent success
	SentSuccess bool `protobuf:"varint,5,opt,name=sent_success,json=sentSuccess,proto3" json:"sent_success,omitempty"`
}

func (x *SentHistoryRecordDef) Reset() {
	*x = SentHistoryRecordDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SentHistoryRecordDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SentHistoryRecordDef) ProtoMessage() {}

func (x *SentHistoryRecordDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SentHistoryRecordDef.ProtoReflect.Descriptor instead.
func (*SentHistoryRecordDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_defs_proto_rawDescGZIP(), []int{3}
}

func (x *SentHistoryRecordDef) GetReport() *ReportDef {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *SentHistoryRecordDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SentHistoryRecordDef) GetServiceDate() *date.Date {
	if x != nil {
		return x.ServiceDate
	}
	return nil
}

func (x *SentHistoryRecordDef) GetSendTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SendTime
	}
	return nil
}

func (x *SentHistoryRecordDef) GetSentSuccess() bool {
	if x != nil {
		return x.SentSuccess
	}
	return false
}

// sent result def
type SentResultDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// sent success
	SentSuccess bool `protobuf:"varint,3,opt,name=sent_success,json=sentSuccess,proto3" json:"sent_success,omitempty"`
	// error message
	ErrorMessage string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *SentResultDef) Reset() {
	*x = SentResultDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SentResultDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SentResultDef) ProtoMessage() {}

func (x *SentResultDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SentResultDef.ProtoReflect.Descriptor instead.
func (*SentResultDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_defs_proto_rawDescGZIP(), []int{4}
}

func (x *SentResultDef) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *SentResultDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *SentResultDef) GetSentSuccess() bool {
	if x != nil {
		return x.SentSuccess
	}
	return false
}

func (x *SentResultDef) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// daily report config def
type DailyReportConfigDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,4,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// uuid
	Uuid string `protobuf:"bytes,5,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// report
	Report *ReportDef `protobuf:"bytes,6,opt,name=report,proto3" json:"report,omitempty"`
	// report card status
	Status ReportCardStatus `protobuf:"varint,7,opt,name=status,proto3,enum=moego.models.appointment.v1.ReportCardStatus" json:"status,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// send time
	SendTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	// send method
	SendMethod SendMethod `protobuf:"varint,10,opt,name=send_method,json=sendMethod,proto3,enum=moego.models.appointment.v1.SendMethod" json:"send_method,omitempty"`
}

func (x *DailyReportConfigDef) Reset() {
	*x = DailyReportConfigDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyReportConfigDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyReportConfigDef) ProtoMessage() {}

func (x *DailyReportConfigDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyReportConfigDef.ProtoReflect.Descriptor instead.
func (*DailyReportConfigDef) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_defs_proto_rawDescGZIP(), []int{5}
}

func (x *DailyReportConfigDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DailyReportConfigDef) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *DailyReportConfigDef) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *DailyReportConfigDef) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *DailyReportConfigDef) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *DailyReportConfigDef) GetReport() *ReportDef {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *DailyReportConfigDef) GetStatus() ReportCardStatus {
	if x != nil {
		return x.Status
	}
	return ReportCardStatus_REPORT_CARD_STATUS_UNSPECIFIED
}

func (x *DailyReportConfigDef) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *DailyReportConfigDef) GetSendTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SendTime
	}
	return nil
}

func (x *DailyReportConfigDef) GetSendMethod() SendMethod {
	if x != nil {
		return x.SendMethod
	}
	return SendMethod_SEND_METHOD_UNSPECIFIED
}

// list daily report config filter
type ListDailyReportConfigFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// report card status
	Status *ReportCardStatus `protobuf:"varint,1,opt,name=status,proto3,enum=moego.models.appointment.v1.ReportCardStatus,oneof" json:"status,omitempty"`
	// service item types
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,2,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
}

func (x *ListDailyReportConfigFilter) Reset() {
	*x = ListDailyReportConfigFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDailyReportConfigFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyReportConfigFilter) ProtoMessage() {}

func (x *ListDailyReportConfigFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyReportConfigFilter.ProtoReflect.Descriptor instead.
func (*ListDailyReportConfigFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_appointment_v1_daily_report_defs_proto_rawDescGZIP(), []int{6}
}

func (x *ListDailyReportConfigFilter) GetStatus() ReportCardStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ReportCardStatus_REPORT_CARD_STATUS_UNSPECIFIED
}

func (x *ListDailyReportConfigFilter) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ListDailyReportConfigFilter) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListDailyReportConfigFilter) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListDailyReportConfigFilter) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

var File_moego_models_appointment_v1_daily_report_defs_proto protoreflect.FileDescriptor

var file_moego_models_appointment_v1_daily_report_defs_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61,
	0x69, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x58, 0x0a, 0x09, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x65, 0x66, 0x12, 0x4b, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x22, 0x82, 0x02, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x66,
	0x12, 0x2a, 0x0a, 0x06, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x10, 0x05, 0x22, 0x08, 0x72, 0x06, 0x18, 0xe8,
	0x07, 0x88, 0x01, 0x01, 0x52, 0x06, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x12, 0x2a, 0x0a, 0x06,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x12, 0xfa, 0x42,
	0x0f, 0x92, 0x01, 0x0c, 0x10, 0x01, 0x22, 0x08, 0x72, 0x06, 0x18, 0xe8, 0x07, 0x88, 0x01, 0x01,
	0x52, 0x06, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x12, 0x50, 0x0a, 0x09, 0x66, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x09, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x73, 0x12, 0x4a, 0x0a, 0x0b, 0x74, 0x68,
	0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x29, 0xfa, 0x42, 0x26, 0x72, 0x24, 0x32, 0x22, 0x5e, 0x23, 0x28, 0x5b, 0x41, 0x2d, 0x46, 0x61,
	0x2d, 0x66, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x7d, 0x7c, 0x5b, 0x41, 0x2d, 0x46, 0x61, 0x2d,
	0x66, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x33, 0x7d, 0x29, 0x24, 0x52, 0x0a, 0x74, 0x68, 0x65, 0x6d,
	0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x8a, 0x04, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x59, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x49, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15,
	0x10, 0x01, 0x18, 0x64, 0x32, 0x0f, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x41, 0x2d, 0x5a, 0x30, 0x2d,
	0x39, 0x5f, 0x5d, 0x2b, 0x24, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x64, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x68, 0x6f, 0x77, 0x12, 0x26, 0x0a, 0x07, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92,
	0x01, 0x06, 0x22, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x26, 0x0a, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x72, 0x02, 0x18, 0x64,
	0x52, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x0e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x72, 0x02, 0x18, 0x64, 0x52,
	0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x27,
	0x0a, 0x0a, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xf4, 0x03, 0x52, 0x09, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x54, 0x65, 0x78, 0x74, 0x12, 0x2a, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65,
	0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0xac, 0x02, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x22, 0x8a, 0x02, 0x0a, 0x14, 0x53, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x66, 0x12, 0x3e, 0x0a, 0x06,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x65, 0x66, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34,
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x22, 0x95, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44,
	0x65, 0x66, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xe0, 0x03, 0x0a, 0x14, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65,
	0x66, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x12, 0x3e, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66, 0x52, 0x06, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x48, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52,
	0x0a, 0x73, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0xa2, 0x03, 0x0a, 0x1b,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x56, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x6a, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92,
	0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x35, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x02, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x42, 0x87, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_appointment_v1_daily_report_defs_proto_rawDescOnce sync.Once
	file_moego_models_appointment_v1_daily_report_defs_proto_rawDescData = file_moego_models_appointment_v1_daily_report_defs_proto_rawDesc
)

func file_moego_models_appointment_v1_daily_report_defs_proto_rawDescGZIP() []byte {
	file_moego_models_appointment_v1_daily_report_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_appointment_v1_daily_report_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_appointment_v1_daily_report_defs_proto_rawDescData)
	})
	return file_moego_models_appointment_v1_daily_report_defs_proto_rawDescData
}

var file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_models_appointment_v1_daily_report_defs_proto_goTypes = []interface{}{
	(*ReportDef)(nil),                   // 0: moego.models.appointment.v1.ReportDef
	(*ContentDef)(nil),                  // 1: moego.models.appointment.v1.ContentDef
	(*QuestionDef)(nil),                 // 2: moego.models.appointment.v1.QuestionDef
	(*SentHistoryRecordDef)(nil),        // 3: moego.models.appointment.v1.SentHistoryRecordDef
	(*SentResultDef)(nil),               // 4: moego.models.appointment.v1.SentResultDef
	(*DailyReportConfigDef)(nil),        // 5: moego.models.appointment.v1.DailyReportConfigDef
	(*ListDailyReportConfigFilter)(nil), // 6: moego.models.appointment.v1.ListDailyReportConfigFilter
	(QuestionCategoryType)(0),           // 7: moego.models.appointment.v1.QuestionCategoryType
	(QuestionType)(0),                   // 8: moego.models.appointment.v1.QuestionType
	(*date.Date)(nil),                   // 9: google.type.Date
	(*timestamppb.Timestamp)(nil),       // 10: google.protobuf.Timestamp
	(ReportCardStatus)(0),               // 11: moego.models.appointment.v1.ReportCardStatus
	(SendMethod)(0),                     // 12: moego.models.appointment.v1.SendMethod
	(v1.ServiceItemType)(0),             // 13: moego.models.offering.v1.ServiceItemType
}
var file_moego_models_appointment_v1_daily_report_defs_proto_depIdxs = []int32{
	1,  // 0: moego.models.appointment.v1.ReportDef.content:type_name -> moego.models.appointment.v1.ContentDef
	2,  // 1: moego.models.appointment.v1.ContentDef.feedbacks:type_name -> moego.models.appointment.v1.QuestionDef
	7,  // 2: moego.models.appointment.v1.QuestionDef.category:type_name -> moego.models.appointment.v1.QuestionCategoryType
	8,  // 3: moego.models.appointment.v1.QuestionDef.type:type_name -> moego.models.appointment.v1.QuestionType
	0,  // 4: moego.models.appointment.v1.SentHistoryRecordDef.report:type_name -> moego.models.appointment.v1.ReportDef
	9,  // 5: moego.models.appointment.v1.SentHistoryRecordDef.service_date:type_name -> google.type.Date
	10, // 6: moego.models.appointment.v1.SentHistoryRecordDef.send_time:type_name -> google.protobuf.Timestamp
	0,  // 7: moego.models.appointment.v1.DailyReportConfigDef.report:type_name -> moego.models.appointment.v1.ReportDef
	11, // 8: moego.models.appointment.v1.DailyReportConfigDef.status:type_name -> moego.models.appointment.v1.ReportCardStatus
	10, // 9: moego.models.appointment.v1.DailyReportConfigDef.update_time:type_name -> google.protobuf.Timestamp
	10, // 10: moego.models.appointment.v1.DailyReportConfigDef.send_time:type_name -> google.protobuf.Timestamp
	12, // 11: moego.models.appointment.v1.DailyReportConfigDef.send_method:type_name -> moego.models.appointment.v1.SendMethod
	11, // 12: moego.models.appointment.v1.ListDailyReportConfigFilter.status:type_name -> moego.models.appointment.v1.ReportCardStatus
	13, // 13: moego.models.appointment.v1.ListDailyReportConfigFilter.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	9,  // 14: moego.models.appointment.v1.ListDailyReportConfigFilter.start_date:type_name -> google.type.Date
	9,  // 15: moego.models.appointment.v1.ListDailyReportConfigFilter.end_date:type_name -> google.type.Date
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_models_appointment_v1_daily_report_defs_proto_init() }
func file_moego_models_appointment_v1_daily_report_defs_proto_init() {
	if File_moego_models_appointment_v1_daily_report_defs_proto != nil {
		return
	}
	file_moego_models_appointment_v1_daily_report_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuestionDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SentHistoryRecordDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SentResultDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyReportConfigDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDailyReportConfigFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_appointment_v1_daily_report_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_appointment_v1_daily_report_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_appointment_v1_daily_report_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_appointment_v1_daily_report_defs_proto_msgTypes,
	}.Build()
	File_moego_models_appointment_v1_daily_report_defs_proto = out.File
	file_moego_models_appointment_v1_daily_report_defs_proto_rawDesc = nil
	file_moego_models_appointment_v1_daily_report_defs_proto_goTypes = nil
	file_moego_models_appointment_v1_daily_report_defs_proto_depIdxs = nil
}
