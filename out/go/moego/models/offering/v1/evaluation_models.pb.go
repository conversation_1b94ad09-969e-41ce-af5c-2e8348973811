// @since 2024-03-12 11:36:30
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/evaluation_models.proto

package offeringpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The Evaluation model
type EvaluationModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// whether the evaluation is available for all business
	AvailableForAllBusiness bool `protobuf:"varint,2,opt,name=available_for_all_business,json=availableForAllBusiness,proto3" json:"available_for_all_business,omitempty"`
	// available business ids (if available_for_all_business is false)
	AvailableBusinessIds []int64 `protobuf:"varint,3,rep,packed,name=available_business_ids,json=availableBusinessIds,proto3" json:"available_business_ids,omitempty"`
	// service item types that require evaluation
	ServiceItemTypes []ServiceItemType `protobuf:"varint,4,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// name
	Name string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,9,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// whether the service is available for all lodging
	LodgingFilter bool `protobuf:"varint,10,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if available_for_all_lodgings is false)
	CustomizedLodgingIds []int64 `protobuf:"varint,11,rep,packed,name=customized_lodging_ids,json=customizedLodgingIds,proto3" json:"customized_lodging_ids,omitempty"`
	// description
	Description string `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`
	// name shown in ob flow
	AliasForOnlineBooking string `protobuf:"bytes,13,opt,name=alias_for_online_booking,json=aliasForOnlineBooking,proto3" json:"alias_for_online_booking,omitempty"`
	// is available for all staff. default is true
	IsAllStaff bool `protobuf:"varint,14,opt,name=is_all_staff,json=isAllStaff,proto3" json:"is_all_staff,omitempty"`
	// available staff ids(only if is_all_staff is false)
	AllowedStaffList []int64 `protobuf:"varint,15,rep,packed,name=allowed_staff_list,json=allowedStaffList,proto3" json:"allowed_staff_list,omitempty"`
	// is allow staff auto assign. default is false
	AllowStaffAutoAssign bool `protobuf:"varint,16,opt,name=allow_staff_auto_assign,json=allowStaffAutoAssign,proto3" json:"allow_staff_auto_assign,omitempty"`
	// is resettable
	IsResettable bool `protobuf:"varint,17,opt,name=is_resettable,json=isResettable,proto3" json:"is_resettable,omitempty"`
	// reset interval days
	ResetIntervalDays int32 `protobuf:"varint,18,opt,name=reset_interval_days,json=resetIntervalDays,proto3" json:"reset_interval_days,omitempty"`
	// company_id
	CompanyId int64 `protobuf:"varint,19,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// breed filter
	BreedFilter bool `protobuf:"varint,20,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// pet type breed filter
	BreedFilters []*PetBreedFilter `protobuf:"bytes,21,rep,name=breed_filters,json=breedFilters,proto3" json:"breed_filters,omitempty"`
	// tax_id
	TaxId int64 `protobuf:"varint,22,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,23,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,24,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// update time
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *EvaluationModel) Reset() {
	*x = EvaluationModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationModel) ProtoMessage() {}

func (x *EvaluationModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationModel.ProtoReflect.Descriptor instead.
func (*EvaluationModel) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_evaluation_models_proto_rawDescGZIP(), []int{0}
}

func (x *EvaluationModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EvaluationModel) GetAvailableForAllBusiness() bool {
	if x != nil {
		return x.AvailableForAllBusiness
	}
	return false
}

func (x *EvaluationModel) GetAvailableBusinessIds() []int64 {
	if x != nil {
		return x.AvailableBusinessIds
	}
	return nil
}

func (x *EvaluationModel) GetServiceItemTypes() []ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *EvaluationModel) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *EvaluationModel) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *EvaluationModel) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *EvaluationModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvaluationModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *EvaluationModel) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *EvaluationModel) GetCustomizedLodgingIds() []int64 {
	if x != nil {
		return x.CustomizedLodgingIds
	}
	return nil
}

func (x *EvaluationModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EvaluationModel) GetAliasForOnlineBooking() string {
	if x != nil {
		return x.AliasForOnlineBooking
	}
	return ""
}

func (x *EvaluationModel) GetIsAllStaff() bool {
	if x != nil {
		return x.IsAllStaff
	}
	return false
}

func (x *EvaluationModel) GetAllowedStaffList() []int64 {
	if x != nil {
		return x.AllowedStaffList
	}
	return nil
}

func (x *EvaluationModel) GetAllowStaffAutoAssign() bool {
	if x != nil {
		return x.AllowStaffAutoAssign
	}
	return false
}

func (x *EvaluationModel) GetIsResettable() bool {
	if x != nil {
		return x.IsResettable
	}
	return false
}

func (x *EvaluationModel) GetResetIntervalDays() int32 {
	if x != nil {
		return x.ResetIntervalDays
	}
	return 0
}

func (x *EvaluationModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *EvaluationModel) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *EvaluationModel) GetBreedFilters() []*PetBreedFilter {
	if x != nil {
		return x.BreedFilters
	}
	return nil
}

func (x *EvaluationModel) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *EvaluationModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *EvaluationModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *EvaluationModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

// evaluation view
type EvaluationView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service item types that require evaluation
	ServiceItemTypes []ServiceItemType `protobuf:"varint,4,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// name
	Name string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,9,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// description
	Description string `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`
	// reset interval days
	ResetIntervalDays int32 `protobuf:"varint,18,opt,name=reset_interval_days,json=resetIntervalDays,proto3" json:"reset_interval_days,omitempty"`
	// tax_id
	TaxId *int64 `protobuf:"varint,22,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
}

func (x *EvaluationView) Reset() {
	*x = EvaluationView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationView) ProtoMessage() {}

func (x *EvaluationView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationView.ProtoReflect.Descriptor instead.
func (*EvaluationView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_evaluation_models_proto_rawDescGZIP(), []int{1}
}

func (x *EvaluationView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EvaluationView) GetServiceItemTypes() []ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *EvaluationView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *EvaluationView) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *EvaluationView) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *EvaluationView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvaluationView) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *EvaluationView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EvaluationView) GetResetIntervalDays() int32 {
	if x != nil {
		return x.ResetIntervalDays
	}
	return 0
}

func (x *EvaluationView) GetTaxId() int64 {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return 0
}

// The message for pet type & breed name filter
type PetBreedFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type id
	PetType v1.PetType `protobuf:"varint,1,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// allow all breeds
	IsAllBreed bool `protobuf:"varint,2,opt,name=is_all_breed,json=isAllBreed,proto3" json:"is_all_breed,omitempty"`
	// pet breed names
	BreedNames []string `protobuf:"bytes,3,rep,name=breed_names,json=breedNames,proto3" json:"breed_names,omitempty"`
}

func (x *PetBreedFilter) Reset() {
	*x = PetBreedFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetBreedFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetBreedFilter) ProtoMessage() {}

func (x *PetBreedFilter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetBreedFilter.ProtoReflect.Descriptor instead.
func (*PetBreedFilter) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_evaluation_models_proto_rawDescGZIP(), []int{2}
}

func (x *PetBreedFilter) GetPetType() v1.PetType {
	if x != nil {
		return x.PetType
	}
	return v1.PetType(0)
}

func (x *PetBreedFilter) GetIsAllBreed() bool {
	if x != nil {
		return x.IsAllBreed
	}
	return false
}

func (x *PetBreedFilter) GetBreedNames() []string {
	if x != nil {
		return x.BreedNames
	}
	return nil
}

// the evaluation brief view
type EvaluationBriefView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,2,opt,name=price,proto3" json:"price,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,4,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// name
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// name shown in ob flow
	AliasForOnlineBooking string `protobuf:"bytes,7,opt,name=alias_for_online_booking,json=aliasForOnlineBooking,proto3" json:"alias_for_online_booking,omitempty"`
	// is online booking available
	IsOnlineBookingAvailable bool `protobuf:"varint,8,opt,name=is_online_booking_available,json=isOnlineBookingAvailable,proto3" json:"is_online_booking_available,omitempty"`
	// is available for all staff. default is true
	IsAllStaff bool `protobuf:"varint,9,opt,name=is_all_staff,json=isAllStaff,proto3" json:"is_all_staff,omitempty"`
	// available staff ids(only if is_all_staff is false)
	AllowedStaffList []int64 `protobuf:"varint,10,rep,packed,name=allowed_staff_list,json=allowedStaffList,proto3" json:"allowed_staff_list,omitempty"`
	// is allow staff auto assign. default is false
	AllowStaffAutoAssign bool `protobuf:"varint,11,opt,name=allow_staff_auto_assign,json=allowStaffAutoAssign,proto3" json:"allow_staff_auto_assign,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,12,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// breed filter
	BreedFilter bool `protobuf:"varint,20,opt,name=breed_filter,json=breedFilter,proto3" json:"breed_filter,omitempty"`
	// customized breed filter
	BreedFilters []*PetBreedFilter `protobuf:"bytes,21,rep,name=breed_filters,json=breedFilters,proto3" json:"breed_filters,omitempty"`
	// tax_id
	TaxId int64 `protobuf:"varint,22,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,35,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,36,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *EvaluationBriefView) Reset() {
	*x = EvaluationBriefView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationBriefView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationBriefView) ProtoMessage() {}

func (x *EvaluationBriefView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationBriefView.ProtoReflect.Descriptor instead.
func (*EvaluationBriefView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_evaluation_models_proto_rawDescGZIP(), []int{3}
}

func (x *EvaluationBriefView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EvaluationBriefView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *EvaluationBriefView) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *EvaluationBriefView) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *EvaluationBriefView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvaluationBriefView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EvaluationBriefView) GetAliasForOnlineBooking() string {
	if x != nil {
		return x.AliasForOnlineBooking
	}
	return ""
}

func (x *EvaluationBriefView) GetIsOnlineBookingAvailable() bool {
	if x != nil {
		return x.IsOnlineBookingAvailable
	}
	return false
}

func (x *EvaluationBriefView) GetIsAllStaff() bool {
	if x != nil {
		return x.IsAllStaff
	}
	return false
}

func (x *EvaluationBriefView) GetAllowedStaffList() []int64 {
	if x != nil {
		return x.AllowedStaffList
	}
	return nil
}

func (x *EvaluationBriefView) GetAllowStaffAutoAssign() bool {
	if x != nil {
		return x.AllowStaffAutoAssign
	}
	return false
}

func (x *EvaluationBriefView) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *EvaluationBriefView) GetBreedFilter() bool {
	if x != nil {
		return x.BreedFilter
	}
	return false
}

func (x *EvaluationBriefView) GetBreedFilters() []*PetBreedFilter {
	if x != nil {
		return x.BreedFilters
	}
	return nil
}

func (x *EvaluationBriefView) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *EvaluationBriefView) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *EvaluationBriefView) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// evaluation service client view
type EvaluationServiceClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the unique id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// whether the evaluation is available for all business
	AvailableForAllBusiness bool `protobuf:"varint,2,opt,name=available_for_all_business,json=availableForAllBusiness,proto3" json:"available_for_all_business,omitempty"`
	// available business ids (if available_for_all_business is false)
	AvailableBusinessIds []int64 `protobuf:"varint,3,rep,packed,name=available_business_ids,json=availableBusinessIds,proto3" json:"available_business_ids,omitempty"`
	// service item types that require evaluation
	ServiceItemTypes []ServiceItemType `protobuf:"varint,4,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// price
	Price float64 `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	// duration
	Duration int32 `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// name
	Name string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	// is active
	IsActive bool `protobuf:"varint,9,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// whether the service is available for all lodging
	LodgingFilter bool `protobuf:"varint,10,opt,name=lodging_filter,json=lodgingFilter,proto3" json:"lodging_filter,omitempty"`
	// available lodging ids(only if available_for_all_lodgings is false)
	CustomizedLodgingIds []int64 `protobuf:"varint,11,rep,packed,name=customized_lodging_ids,json=customizedLodgingIds,proto3" json:"customized_lodging_ids,omitempty"`
	// description
	Description string `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`
	// name shown in ob flow
	AliasForOnlineBooking string `protobuf:"bytes,13,opt,name=alias_for_online_booking,json=aliasForOnlineBooking,proto3" json:"alias_for_online_booking,omitempty"`
	// is available for all staff. default is true
	IsAllStaff bool `protobuf:"varint,14,opt,name=is_all_staff,json=isAllStaff,proto3" json:"is_all_staff,omitempty"`
	// available staff ids(only if is_all_staff is false)
	AllowedStaffList []int64 `protobuf:"varint,15,rep,packed,name=allowed_staff_list,json=allowedStaffList,proto3" json:"allowed_staff_list,omitempty"`
	// is allow staff auto assign. default is false
	AllowStaffAutoAssign bool `protobuf:"varint,16,opt,name=allow_staff_auto_assign,json=allowStaffAutoAssign,proto3" json:"allow_staff_auto_assign,omitempty"`
	// is resettable
	IsResettable bool `protobuf:"varint,17,opt,name=is_resettable,json=isResettable,proto3" json:"is_resettable,omitempty"`
	// reset interval days
	ResetIntervalDays int32 `protobuf:"varint,18,opt,name=reset_interval_days,json=resetIntervalDays,proto3" json:"reset_interval_days,omitempty"`
	// company_id
	CompanyId int64 `protobuf:"varint,19,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *EvaluationServiceClientView) Reset() {
	*x = EvaluationServiceClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationServiceClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationServiceClientView) ProtoMessage() {}

func (x *EvaluationServiceClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_evaluation_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationServiceClientView.ProtoReflect.Descriptor instead.
func (*EvaluationServiceClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_evaluation_models_proto_rawDescGZIP(), []int{4}
}

func (x *EvaluationServiceClientView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EvaluationServiceClientView) GetAvailableForAllBusiness() bool {
	if x != nil {
		return x.AvailableForAllBusiness
	}
	return false
}

func (x *EvaluationServiceClientView) GetAvailableBusinessIds() []int64 {
	if x != nil {
		return x.AvailableBusinessIds
	}
	return nil
}

func (x *EvaluationServiceClientView) GetServiceItemTypes() []ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *EvaluationServiceClientView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *EvaluationServiceClientView) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *EvaluationServiceClientView) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *EvaluationServiceClientView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EvaluationServiceClientView) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *EvaluationServiceClientView) GetLodgingFilter() bool {
	if x != nil {
		return x.LodgingFilter
	}
	return false
}

func (x *EvaluationServiceClientView) GetCustomizedLodgingIds() []int64 {
	if x != nil {
		return x.CustomizedLodgingIds
	}
	return nil
}

func (x *EvaluationServiceClientView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EvaluationServiceClientView) GetAliasForOnlineBooking() string {
	if x != nil {
		return x.AliasForOnlineBooking
	}
	return ""
}

func (x *EvaluationServiceClientView) GetIsAllStaff() bool {
	if x != nil {
		return x.IsAllStaff
	}
	return false
}

func (x *EvaluationServiceClientView) GetAllowedStaffList() []int64 {
	if x != nil {
		return x.AllowedStaffList
	}
	return nil
}

func (x *EvaluationServiceClientView) GetAllowStaffAutoAssign() bool {
	if x != nil {
		return x.AllowStaffAutoAssign
	}
	return false
}

func (x *EvaluationServiceClientView) GetIsResettable() bool {
	if x != nil {
		return x.IsResettable
	}
	return false
}

func (x *EvaluationServiceClientView) GetResetIntervalDays() int32 {
	if x != nil {
		return x.ResetIntervalDays
	}
	return 0
}

func (x *EvaluationServiceClientView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

var File_moego_models_offering_v1_evaluation_models_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_evaluation_models_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdc, 0x08,
	0x0a, 0x0f, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66,
	0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x34,
	0x0a, 0x16, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x73, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a,
	0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37,
	0x0a, 0x18, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x6c,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x0f, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x52, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x44,
	0x61, 0x79, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x0d, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0c, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xf4, 0x02, 0x0a,
	0x0e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13,
	0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x64,
	0x61, 0x79, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x72, 0x65, 0x73, 0x65, 0x74,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x44, 0x61, 0x79, 0x73, 0x12, 0x1a, 0x0a, 0x06,
	0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05,
	0x74, 0x61, 0x78, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x61, 0x78,
	0x5f, 0x69, 0x64, 0x22, 0x91, 0x01, 0x0a, 0x0e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62,
	0x72, 0x65, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x41, 0x6c,
	0x6c, 0x42, 0x72, 0x65, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x72, 0x65,
	0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0xc7, 0x05, 0x0a, 0x13, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x18, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f,
	0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x46,
	0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x3d, 0x0a, 0x1b, 0x69, 0x73, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x69, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x20,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x12, 0x2c, 0x0a, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35,
	0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x75, 0x74, 0x6f, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x0d, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0c, 0x62, 0x72, 0x65, 0x65, 0x64, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0xae, 0x06, 0x0a, 0x1b, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x3b, 0x0a, 0x1a, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66,
	0x6f, 0x72, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x46, 0x6f, 0x72, 0x41, 0x6c, 0x6c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x34,
	0x0a, 0x16, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x73, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x03, 0x52, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a,
	0x65, 0x64, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37,
	0x0a, 0x18, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x6c,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x73, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x0f, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x6f, 0x77,
	0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x53,
	0x74, 0x61, 0x66, 0x66, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x52, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x44,
	0x61, 0x79, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_evaluation_models_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_evaluation_models_proto_rawDescData = file_moego_models_offering_v1_evaluation_models_proto_rawDesc
)

func file_moego_models_offering_v1_evaluation_models_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_evaluation_models_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_evaluation_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_evaluation_models_proto_rawDescData)
	})
	return file_moego_models_offering_v1_evaluation_models_proto_rawDescData
}

var file_moego_models_offering_v1_evaluation_models_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_offering_v1_evaluation_models_proto_goTypes = []interface{}{
	(*EvaluationModel)(nil),             // 0: moego.models.offering.v1.EvaluationModel
	(*EvaluationView)(nil),              // 1: moego.models.offering.v1.EvaluationView
	(*PetBreedFilter)(nil),              // 2: moego.models.offering.v1.PetBreedFilter
	(*EvaluationBriefView)(nil),         // 3: moego.models.offering.v1.EvaluationBriefView
	(*EvaluationServiceClientView)(nil), // 4: moego.models.offering.v1.EvaluationServiceClientView
	(ServiceItemType)(0),                // 5: moego.models.offering.v1.ServiceItemType
	(*timestamppb.Timestamp)(nil),       // 6: google.protobuf.Timestamp
	(v1.PetType)(0),                     // 7: moego.models.customer.v1.PetType
}
var file_moego_models_offering_v1_evaluation_models_proto_depIdxs = []int32{
	5,  // 0: moego.models.offering.v1.EvaluationModel.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	2,  // 1: moego.models.offering.v1.EvaluationModel.breed_filters:type_name -> moego.models.offering.v1.PetBreedFilter
	6,  // 2: moego.models.offering.v1.EvaluationModel.created_at:type_name -> google.protobuf.Timestamp
	6,  // 3: moego.models.offering.v1.EvaluationModel.updated_at:type_name -> google.protobuf.Timestamp
	6,  // 4: moego.models.offering.v1.EvaluationModel.deleted_at:type_name -> google.protobuf.Timestamp
	5,  // 5: moego.models.offering.v1.EvaluationView.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	7,  // 6: moego.models.offering.v1.PetBreedFilter.pet_type:type_name -> moego.models.customer.v1.PetType
	2,  // 7: moego.models.offering.v1.EvaluationBriefView.breed_filters:type_name -> moego.models.offering.v1.PetBreedFilter
	6,  // 8: moego.models.offering.v1.EvaluationBriefView.created_at:type_name -> google.protobuf.Timestamp
	6,  // 9: moego.models.offering.v1.EvaluationBriefView.updated_at:type_name -> google.protobuf.Timestamp
	5,  // 10: moego.models.offering.v1.EvaluationServiceClientView.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_evaluation_models_proto_init() }
func file_moego_models_offering_v1_evaluation_models_proto_init() {
	if File_moego_models_offering_v1_evaluation_models_proto != nil {
		return
	}
	file_moego_models_offering_v1_service_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_evaluation_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_evaluation_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_evaluation_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetBreedFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_evaluation_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationBriefView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_evaluation_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationServiceClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_offering_v1_evaluation_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_evaluation_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_evaluation_models_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_evaluation_models_proto_depIdxs,
		MessageInfos:      file_moego_models_offering_v1_evaluation_models_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_evaluation_models_proto = out.File
	file_moego_models_offering_v1_evaluation_models_proto_rawDesc = nil
	file_moego_models_offering_v1_evaluation_models_proto_goTypes = nil
	file_moego_models_offering_v1_evaluation_models_proto_depIdxs = nil
}
