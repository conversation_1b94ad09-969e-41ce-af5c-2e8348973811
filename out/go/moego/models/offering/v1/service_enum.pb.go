// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/service_enum.proto

package offeringpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The care type enum
type ServiceItemType int32

const (
	// Unspecified care type
	ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED ServiceItemType = 0
	// Grooming care type
	ServiceItemType_GROOMING ServiceItemType = 1
	// Boarding care type
	ServiceItemType_BOARDING ServiceItemType = 2
	// Daycare care type
	ServiceItemType_DAYCARE ServiceItemType = 3
	// Evaluation care type
	ServiceItemType_EVALUATION ServiceItemType = 4
	// Dog walking care type
	ServiceItemType_DOG_WALKING ServiceItemType = 5
	// Training group class
	ServiceItemType_GROUP_CLASS ServiceItemType = 6
)

// Enum value maps for ServiceItemType.
var (
	ServiceItemType_name = map[int32]string{
		0: "SERVICE_ITEM_TYPE_UNSPECIFIED",
		1: "GROOMING",
		2: "BOARDING",
		3: "DAYCARE",
		4: "EVALUATION",
		5: "DOG_WALKING",
		6: "GROUP_CLASS",
	}
	ServiceItemType_value = map[string]int32{
		"SERVICE_ITEM_TYPE_UNSPECIFIED": 0,
		"GROOMING":                      1,
		"BOARDING":                      2,
		"DAYCARE":                       3,
		"EVALUATION":                    4,
		"DOG_WALKING":                   5,
		"GROUP_CLASS":                   6,
	}
)

func (x ServiceItemType) Enum() *ServiceItemType {
	p := new(ServiceItemType)
	*p = x
	return p
}

func (x ServiceItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_service_enum_proto_enumTypes[0].Descriptor()
}

func (ServiceItemType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_service_enum_proto_enumTypes[0]
}

func (x ServiceItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceItemType.Descriptor instead.
func (ServiceItemType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_enum_proto_rawDescGZIP(), []int{0}
}

// service price unit
type ServicePriceUnit int32

const (
	// Unspecified service price unit
	ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED ServicePriceUnit = 0
	// Per session service price unit
	ServicePriceUnit_PER_SESSION ServicePriceUnit = 1
	// Per night service price unit
	ServicePriceUnit_PER_NIGHT ServicePriceUnit = 2
	// Per hour service price unit
	ServicePriceUnit_PER_HOUR ServicePriceUnit = 3
	// Per day service price unit
	ServicePriceUnit_PER_DAY ServicePriceUnit = 4
)

// Enum value maps for ServicePriceUnit.
var (
	ServicePriceUnit_name = map[int32]string{
		0: "SERVICE_PRICE_UNIT_UNSPECIFIED",
		1: "PER_SESSION",
		2: "PER_NIGHT",
		3: "PER_HOUR",
		4: "PER_DAY",
	}
	ServicePriceUnit_value = map[string]int32{
		"SERVICE_PRICE_UNIT_UNSPECIFIED": 0,
		"PER_SESSION":                    1,
		"PER_NIGHT":                      2,
		"PER_HOUR":                       3,
		"PER_DAY":                        4,
	}
)

func (x ServicePriceUnit) Enum() *ServicePriceUnit {
	p := new(ServicePriceUnit)
	*p = x
	return p
}

func (x ServicePriceUnit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServicePriceUnit) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_service_enum_proto_enumTypes[1].Descriptor()
}

func (ServicePriceUnit) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_service_enum_proto_enumTypes[1]
}

func (x ServicePriceUnit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServicePriceUnit.Descriptor instead.
func (ServicePriceUnit) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_enum_proto_rawDescGZIP(), []int{1}
}

// service type
type ServiceType int32

const (
	// Unspecified service type
	ServiceType_SERVICE_TYPE_UNSPECIFIED ServiceType = 0
	// normal service
	ServiceType_SERVICE ServiceType = 1
	// service add on
	ServiceType_ADDON ServiceType = 2
)

// Enum value maps for ServiceType.
var (
	ServiceType_name = map[int32]string{
		0: "SERVICE_TYPE_UNSPECIFIED",
		1: "SERVICE",
		2: "ADDON",
	}
	ServiceType_value = map[string]int32{
		"SERVICE_TYPE_UNSPECIFIED": 0,
		"SERVICE":                  1,
		"ADDON":                    2,
	}
)

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_service_enum_proto_enumTypes[2].Descriptor()
}

func (ServiceType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_service_enum_proto_enumTypes[2]
}

func (x ServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceType.Descriptor instead.
func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_enum_proto_rawDescGZIP(), []int{2}
}

// Service effective range type
// Customize the modification range used by price and duration.
type ServiceScopeType int32

const (
	// unspecified
	ServiceScopeType_SERVICE_SCOPE_TYPE_UNSPECIFIED ServiceScopeType = 0
	// only for this appointment
	ServiceScopeType_ONLY_THIS ServiceScopeType = 1
	// do not save
	ServiceScopeType_DO_NOT_SAVE ServiceScopeType = 2
	// for this and following appointments
	ServiceScopeType_THIS_AND_FUTURE ServiceScopeType = 3
	// for all upcoming appointments
	ServiceScopeType_ALL_UPCOMING ServiceScopeType = 4
)

// Enum value maps for ServiceScopeType.
var (
	ServiceScopeType_name = map[int32]string{
		0: "SERVICE_SCOPE_TYPE_UNSPECIFIED",
		1: "ONLY_THIS",
		2: "DO_NOT_SAVE",
		3: "THIS_AND_FUTURE",
		4: "ALL_UPCOMING",
	}
	ServiceScopeType_value = map[string]int32{
		"SERVICE_SCOPE_TYPE_UNSPECIFIED": 0,
		"ONLY_THIS":                      1,
		"DO_NOT_SAVE":                    2,
		"THIS_AND_FUTURE":                3,
		"ALL_UPCOMING":                   4,
	}
)

func (x ServiceScopeType) Enum() *ServiceScopeType {
	p := new(ServiceScopeType)
	*p = x
	return p
}

func (x ServiceScopeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceScopeType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_service_enum_proto_enumTypes[3].Descriptor()
}

func (ServiceScopeType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_service_enum_proto_enumTypes[3]
}

func (x ServiceScopeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceScopeType.Descriptor instead.
func (ServiceScopeType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_enum_proto_rawDescGZIP(), []int{3}
}

// Service override type
type ServiceOverrideType int32

const (
	// unspecified
	ServiceOverrideType_SERVICE_OVERRIDE_TYPE_UNSPECIFIED ServiceOverrideType = 0
	// override by location
	ServiceOverrideType_LOCATION ServiceOverrideType = 1
	// override by client
	ServiceOverrideType_CLIENT ServiceOverrideType = 2
	// override by staff
	ServiceOverrideType_STAFF ServiceOverrideType = 3
)

// Enum value maps for ServiceOverrideType.
var (
	ServiceOverrideType_name = map[int32]string{
		0: "SERVICE_OVERRIDE_TYPE_UNSPECIFIED",
		1: "LOCATION",
		2: "CLIENT",
		3: "STAFF",
	}
	ServiceOverrideType_value = map[string]int32{
		"SERVICE_OVERRIDE_TYPE_UNSPECIFIED": 0,
		"LOCATION":                          1,
		"CLIENT":                            2,
		"STAFF":                             3,
	}
)

func (x ServiceOverrideType) Enum() *ServiceOverrideType {
	p := new(ServiceOverrideType)
	*p = x
	return p
}

func (x ServiceOverrideType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceOverrideType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_service_enum_proto_enumTypes[4].Descriptor()
}

func (ServiceOverrideType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_service_enum_proto_enumTypes[4]
}

func (x ServiceOverrideType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceOverrideType.Descriptor instead.
func (ServiceOverrideType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_enum_proto_rawDescGZIP(), []int{4}
}

// Service OrderBy type
type ServiceOrderByType int32

const (
	// unspecified
	ServiceOrderByType_SERVICE_ORDER_BY_TYPE_UNSPECIFIED ServiceOrderByType = 0
	// default, order by category sort and service sort asc
	ServiceOrderByType_DEFAULT ServiceOrderByType = 1
	// order by max duration asc
	ServiceOrderByType_MAX_DURATION_ASC ServiceOrderByType = 2
)

// Enum value maps for ServiceOrderByType.
var (
	ServiceOrderByType_name = map[int32]string{
		0: "SERVICE_ORDER_BY_TYPE_UNSPECIFIED",
		1: "DEFAULT",
		2: "MAX_DURATION_ASC",
	}
	ServiceOrderByType_value = map[string]int32{
		"SERVICE_ORDER_BY_TYPE_UNSPECIFIED": 0,
		"DEFAULT":                           1,
		"MAX_DURATION_ASC":                  2,
	}
)

func (x ServiceOrderByType) Enum() *ServiceOrderByType {
	p := new(ServiceOrderByType)
	*p = x
	return p
}

func (x ServiceOrderByType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceOrderByType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_service_enum_proto_enumTypes[5].Descriptor()
}

func (ServiceOrderByType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_service_enum_proto_enumTypes[5]
}

func (x ServiceOrderByType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceOrderByType.Descriptor instead.
func (ServiceOrderByType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_enum_proto_rawDescGZIP(), []int{5}
}

// date type
type DateType int32

const (
	// unspecified
	DateType_DATE_TYPE_UNSPECIFIED DateType = 0
	// every day, except checkout day(the old enum name in pet detail is called every day, will cause confusion)
	DateType_EVERY_DAY_EXCEPT_CHECKOUT_DAY DateType = 1
	// specific date
	DateType_SPECIFIC_DATE DateType = 2
	// date point
	DateType_DATE_POINT DateType = 3
	// every day include checkout day
	DateType_EVERY_DAY_INCLUDE_CHECKOUT_DAY DateType = 4
	// every day except check-in day
	DateType_EVERY_DAY_EXCEPT_CHECKIN_DAY DateType = 5
	// last day
	DateType_LAST_DAY DateType = 6
	// first day
	DateType_FIRST_DAY DateType = 7
)

// Enum value maps for DateType.
var (
	DateType_name = map[int32]string{
		0: "DATE_TYPE_UNSPECIFIED",
		1: "EVERY_DAY_EXCEPT_CHECKOUT_DAY",
		2: "SPECIFIC_DATE",
		3: "DATE_POINT",
		4: "EVERY_DAY_INCLUDE_CHECKOUT_DAY",
		5: "EVERY_DAY_EXCEPT_CHECKIN_DAY",
		6: "LAST_DAY",
		7: "FIRST_DAY",
	}
	DateType_value = map[string]int32{
		"DATE_TYPE_UNSPECIFIED":          0,
		"EVERY_DAY_EXCEPT_CHECKOUT_DAY":  1,
		"SPECIFIC_DATE":                  2,
		"DATE_POINT":                     3,
		"EVERY_DAY_INCLUDE_CHECKOUT_DAY": 4,
		"EVERY_DAY_EXCEPT_CHECKIN_DAY":   5,
		"LAST_DAY":                       6,
		"FIRST_DAY":                      7,
	}
)

func (x DateType) Enum() *DateType {
	p := new(DateType)
	*p = x
	return p
}

func (x DateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DateType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_offering_v1_service_enum_proto_enumTypes[6].Descriptor()
}

func (DateType) Type() protoreflect.EnumType {
	return &file_moego_models_offering_v1_service_enum_proto_enumTypes[6]
}

func (x DateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DateType.Descriptor instead.
func (DateType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_service_enum_proto_rawDescGZIP(), []int{6}
}

var File_moego_models_offering_v1_service_enum_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_service_enum_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2a, 0x8f, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x41,
	0x59, 0x43, 0x41, 0x52, 0x45, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x56, 0x41, 0x4c, 0x55,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x4f, 0x47, 0x5f, 0x57,
	0x41, 0x4c, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x47, 0x52, 0x4f, 0x55,
	0x50, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x10, 0x06, 0x2a, 0x71, 0x0a, 0x10, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x22, 0x0a,
	0x1e, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x5f, 0x55,
	0x4e, 0x49, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x45, 0x52, 0x5f, 0x4e, 0x49, 0x47, 0x48, 0x54, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x45, 0x52, 0x5f, 0x48, 0x4f, 0x55, 0x52, 0x10, 0x03, 0x12,
	0x0b, 0x0a, 0x07, 0x50, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x04, 0x2a, 0x43, 0x0a, 0x0b,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x45, 0x52,
	0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x44, 0x44, 0x4f, 0x4e, 0x10,
	0x02, 0x2a, 0x7d, 0x0a, 0x10, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x70,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45,
	0x5f, 0x53, 0x43, 0x4f, 0x50, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x4e, 0x4c,
	0x59, 0x5f, 0x54, 0x48, 0x49, 0x53, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x4f, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x53, 0x41, 0x56, 0x45, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x48, 0x49,
	0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x46, 0x55, 0x54, 0x55, 0x52, 0x45, 0x10, 0x03, 0x12, 0x10,
	0x0a, 0x0c, 0x41, 0x4c, 0x4c, 0x5f, 0x55, 0x50, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x04,
	0x2a, 0x61, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x49, 0x44, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x46,
	0x46, 0x10, 0x03, 0x2a, 0x5e, 0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x45, 0x52,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x42, 0x59, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x4d, 0x41, 0x58, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x53,
	0x43, 0x10, 0x02, 0x2a, 0xce, 0x01, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x19, 0x0a, 0x15, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x45,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x44, 0x41, 0x59, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x5f,
	0x43, 0x48, 0x45, 0x43, 0x4b, 0x4f, 0x55, 0x54, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x01, 0x12, 0x11,
	0x0a, 0x0d, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x43, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10,
	0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x10,
	0x03, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x44, 0x41, 0x59, 0x5f, 0x49,
	0x4e, 0x43, 0x4c, 0x55, 0x44, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x4f, 0x55, 0x54, 0x5f,
	0x44, 0x41, 0x59, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x44,
	0x41, 0x59, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x49,
	0x4e, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x41, 0x53, 0x54, 0x5f,
	0x44, 0x41, 0x59, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x44,
	0x41, 0x59, 0x10, 0x07, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_service_enum_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_service_enum_proto_rawDescData = file_moego_models_offering_v1_service_enum_proto_rawDesc
)

func file_moego_models_offering_v1_service_enum_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_service_enum_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_service_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_service_enum_proto_rawDescData)
	})
	return file_moego_models_offering_v1_service_enum_proto_rawDescData
}

var file_moego_models_offering_v1_service_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_moego_models_offering_v1_service_enum_proto_goTypes = []interface{}{
	(ServiceItemType)(0),     // 0: moego.models.offering.v1.ServiceItemType
	(ServicePriceUnit)(0),    // 1: moego.models.offering.v1.ServicePriceUnit
	(ServiceType)(0),         // 2: moego.models.offering.v1.ServiceType
	(ServiceScopeType)(0),    // 3: moego.models.offering.v1.ServiceScopeType
	(ServiceOverrideType)(0), // 4: moego.models.offering.v1.ServiceOverrideType
	(ServiceOrderByType)(0),  // 5: moego.models.offering.v1.ServiceOrderByType
	(DateType)(0),            // 6: moego.models.offering.v1.DateType
}
var file_moego_models_offering_v1_service_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_service_enum_proto_init() }
func file_moego_models_offering_v1_service_enum_proto_init() {
	if File_moego_models_offering_v1_service_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_service_enum_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_service_enum_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_service_enum_proto_depIdxs,
		EnumInfos:         file_moego_models_offering_v1_service_enum_proto_enumTypes,
	}.Build()
	File_moego_models_offering_v1_service_enum_proto = out.File
	file_moego_models_offering_v1_service_enum_proto_rawDesc = nil
	file_moego_models_offering_v1_service_enum_proto_goTypes = nil
	file_moego_models_offering_v1_service_enum_proto_depIdxs = nil
}
