// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/promotion/v1/coupon.proto

package promotionpb

import (
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户类型
type User_Type int32

const (
	// 未定义
	User_TYPE_UNSPECIFIED User_Type = 0
	// MoeGo
	User_MOEGO User_Type = 1
	// 账号
	User_ACCOUNT User_Type = 2
	// 企业
	User_ENTERPRISE User_Type = 3
	// 公司
	User_COMPANY User_Type = 4
	// 商家
	User_BUSINESS User_Type = 5
	// 顾客
	User_CUSTOMER User_Type = 6
	// 员工
	User_STAFF User_Type = 7
)

// Enum value maps for User_Type.
var (
	User_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "MOEGO",
		2: "ACCOUNT",
		3: "ENTERPRISE",
		4: "COMPANY",
		5: "BUSINESS",
		6: "CUSTOMER",
		7: "STAFF",
	}
	User_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"MOEGO":            1,
		"ACCOUNT":          2,
		"ENTERPRISE":       3,
		"COMPANY":          4,
		"BUSINESS":         5,
		"CUSTOMER":         6,
		"STAFF":            7,
	}
)

func (x User_Type) Enum() *User_Type {
	p := new(User_Type)
	*p = x
	return p
}

func (x User_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (User_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_promotion_v1_coupon_proto_enumTypes[0].Descriptor()
}

func (User_Type) Type() protoreflect.EnumType {
	return &file_moego_models_promotion_v1_coupon_proto_enumTypes[0]
}

func (x User_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use User_Type.Descriptor instead.
func (User_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{2, 0}
}

// type
type CouponRevision_Type int32

const (
	// unspecified
	CouponRevision_TYPE_UNSPECIFIED CouponRevision_Type = 0
	// redeem 正向消费
	CouponRevision_REDEEM CouponRevision_Type = 1
	// refund 逆向退款
	CouponRevision_REFUND CouponRevision_Type = 2
)

// Enum value maps for CouponRevision_Type.
var (
	CouponRevision_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "REDEEM",
		2: "REFUND",
	}
	CouponRevision_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"REDEEM":           1,
		"REFUND":           2,
	}
)

func (x CouponRevision_Type) Enum() *CouponRevision_Type {
	p := new(CouponRevision_Type)
	*p = x
	return p
}

func (x CouponRevision_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CouponRevision_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_promotion_v1_coupon_proto_enumTypes[1].Descriptor()
}

func (CouponRevision_Type) Type() protoreflect.EnumType {
	return &file_moego_models_promotion_v1_coupon_proto_enumTypes[1]
}

func (x CouponRevision_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CouponRevision_Type.Descriptor instead.
func (CouponRevision_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{3, 0}
}

// Source type enum
type CouponSearchCondition_SourceType int32

const (
	// unspecified
	CouponSearchCondition_SOURCE_TYPE_UNSPECIFIED CouponSearchCondition_SourceType = 0
	// discount
	CouponSearchCondition_DISCOUNT CouponSearchCondition_SourceType = 1
	// package
	CouponSearchCondition_PACKAGE CouponSearchCondition_SourceType = 2
	// membership
	CouponSearchCondition_MEMBERSHIP CouponSearchCondition_SourceType = 3
)

// Enum value maps for CouponSearchCondition_SourceType.
var (
	CouponSearchCondition_SourceType_name = map[int32]string{
		0: "SOURCE_TYPE_UNSPECIFIED",
		1: "DISCOUNT",
		2: "PACKAGE",
		3: "MEMBERSHIP",
	}
	CouponSearchCondition_SourceType_value = map[string]int32{
		"SOURCE_TYPE_UNSPECIFIED": 0,
		"DISCOUNT":                1,
		"PACKAGE":                 2,
		"MEMBERSHIP":              3,
	}
)

func (x CouponSearchCondition_SourceType) Enum() *CouponSearchCondition_SourceType {
	p := new(CouponSearchCondition_SourceType)
	*p = x
	return p
}

func (x CouponSearchCondition_SourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CouponSearchCondition_SourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_promotion_v1_coupon_proto_enumTypes[2].Descriptor()
}

func (CouponSearchCondition_SourceType) Type() protoreflect.EnumType {
	return &file_moego_models_promotion_v1_coupon_proto_enumTypes[2]
}

func (x CouponSearchCondition_SourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CouponSearchCondition_SourceType.Descriptor instead.
func (CouponSearchCondition_SourceType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{7, 0}
}

// Coupon 是 Promotion 赋予用户之后的快照，作为优惠的唯一 fact
type Coupon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// promotion id
	PromotionId int64 `protobuf:"varint,2,opt,name=promotion_id,json=promotionId,proto3" json:"promotion_id,omitempty"`
	// source
	Source *Source `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	// name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// owner 优惠券的持有者
	Owner *User `protobuf:"bytes,6,opt,name=owner,proto3" json:"owner,omitempty"`
	// restrictions
	Restrictions *Restrictions `protobuf:"bytes,7,opt,name=restrictions,proto3" json:"restrictions,omitempty"`
	// discount
	Discount *Discount `protobuf:"bytes,8,opt,name=discount,proto3" json:"discount,omitempty"`
	// validity period 有效期，根据 promotion 的 available_period 计算得到
	ValidityPeriod *interval.Interval `protobuf:"bytes,9,opt,name=validity_period,json=validityPeriod,proto3" json:"validity_period,omitempty"`
	// redemptions
	Redemptions *Redemptions `protobuf:"bytes,10,opt,name=redemptions,proto3" json:"redemptions,omitempty"`
	// revision
	Revision int64 `protobuf:"varint,11,opt,name=revision,proto3" json:"revision,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *Coupon) Reset() {
	*x = Coupon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Coupon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Coupon) ProtoMessage() {}

func (x *Coupon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Coupon.ProtoReflect.Descriptor instead.
func (*Coupon) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{0}
}

func (x *Coupon) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Coupon) GetPromotionId() int64 {
	if x != nil {
		return x.PromotionId
	}
	return 0
}

func (x *Coupon) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *Coupon) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Coupon) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Coupon) GetOwner() *User {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *Coupon) GetRestrictions() *Restrictions {
	if x != nil {
		return x.Restrictions
	}
	return nil
}

func (x *Coupon) GetDiscount() *Discount {
	if x != nil {
		return x.Discount
	}
	return nil
}

func (x *Coupon) GetValidityPeriod() *interval.Interval {
	if x != nil {
		return x.ValidityPeriod
	}
	return nil
}

func (x *Coupon) GetRedemptions() *Redemptions {
	if x != nil {
		return x.Redemptions
	}
	return nil
}

func (x *Coupon) GetRevision() int64 {
	if x != nil {
		return x.Revision
	}
	return 0
}

func (x *Coupon) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Coupon) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// CouponUsage 优惠券使用记录
type CouponUsage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// coupon
	Coupon *Coupon `protobuf:"bytes,1,opt,name=coupon,proto3" json:"coupon,omitempty"`
	// targets
	Targets []*CouponApplicationTarget `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *CouponUsage) Reset() {
	*x = CouponUsage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponUsage) ProtoMessage() {}

func (x *CouponUsage) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponUsage.ProtoReflect.Descriptor instead.
func (*CouponUsage) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{1}
}

func (x *CouponUsage) GetCoupon() *Coupon {
	if x != nil {
		return x.Coupon
	}
	return nil
}

func (x *CouponUsage) GetTargets() []*CouponApplicationTarget {
	if x != nil {
		return x.Targets
	}
	return nil
}

// 用户
type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 类型
	Type User_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.promotion.v1.User_Type" json:"type,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{2}
}

func (x *User) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetType() User_Type {
	if x != nil {
		return x.Type
	}
	return User_TYPE_UNSPECIFIED
}

// coupon revision 消费记录，类似于 payment，不可修改，用来对账
type CouponRevision struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// type
	Type CouponRevision_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.promotion.v1.CouponRevision_Type" json:"type,omitempty"`
	// coupon id
	CouponId int64 `protobuf:"varint,3,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id,omitempty"`
	// order id
	OrderId int64 `protobuf:"varint,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// order lint item id
	OrderLineItemId int64 `protobuf:"varint,5,opt,name=order_line_item_id,json=orderLineItemId,proto3" json:"order_line_item_id,omitempty"`
	// user 使用者，一般是 customer
	User *User `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`
	// operator 操作者，一般是 staff
	Operator *User `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	// targets 当前扣减时使用的对象
	Targets *Targets `protobuf:"bytes,8,opt,name=targets,proto3" json:"targets,omitempty"`
	// redeemed times 单次使用的扣减次数
	RedeemedTimes int64 `protobuf:"varint,9,opt,name=redeemed_times,json=redeemedTimes,proto3" json:"redeemed_times,omitempty"`
	// cost 成本，根据 targets 当前在 order 中的价格进行记录
	Cost *money.Money `protobuf:"bytes,10,opt,name=cost,proto3" json:"cost,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *CouponRevision) Reset() {
	*x = CouponRevision{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponRevision) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponRevision) ProtoMessage() {}

func (x *CouponRevision) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponRevision.ProtoReflect.Descriptor instead.
func (*CouponRevision) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{3}
}

func (x *CouponRevision) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CouponRevision) GetType() CouponRevision_Type {
	if x != nil {
		return x.Type
	}
	return CouponRevision_TYPE_UNSPECIFIED
}

func (x *CouponRevision) GetCouponId() int64 {
	if x != nil {
		return x.CouponId
	}
	return 0
}

func (x *CouponRevision) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *CouponRevision) GetOrderLineItemId() int64 {
	if x != nil {
		return x.OrderLineItemId
	}
	return 0
}

func (x *CouponRevision) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *CouponRevision) GetOperator() *User {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *CouponRevision) GetTargets() *Targets {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *CouponRevision) GetRedeemedTimes() int64 {
	if x != nil {
		return x.RedeemedTimes
	}
	return 0
}

func (x *CouponRevision) GetCost() *money.Money {
	if x != nil {
		return x.Cost
	}
	return nil
}

func (x *CouponRevision) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// TargetDeduction 目标抵扣结果
type TargetDeduction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// target_type 目标类型
	TargetType TargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=moego.models.promotion.v1.TargetType" json:"target_type,omitempty"`
	// target_id 目标ID
	TargetId int64 `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// original_amount 原价
	OriginalAmount *money.Money `protobuf:"bytes,3,opt,name=original_amount,json=originalAmount,proto3" json:"original_amount,omitempty"`
	// final_amount 最终价格
	FinalAmount *money.Money `protobuf:"bytes,4,opt,name=final_amount,json=finalAmount,proto3" json:"final_amount,omitempty"`
	// coupon_deductions 优惠券抵扣列表
	CouponDeductions []*CouponDeduction `protobuf:"bytes,5,rep,name=coupon_deductions,json=couponDeductions,proto3" json:"coupon_deductions,omitempty"`
	// order item id
	OrderItemId *int64 `protobuf:"varint,6,opt,name=order_item_id,json=orderItemId,proto3,oneof" json:"order_item_id,omitempty"`
}

func (x *TargetDeduction) Reset() {
	*x = TargetDeduction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetDeduction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetDeduction) ProtoMessage() {}

func (x *TargetDeduction) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetDeduction.ProtoReflect.Descriptor instead.
func (*TargetDeduction) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{4}
}

func (x *TargetDeduction) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *TargetDeduction) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *TargetDeduction) GetOriginalAmount() *money.Money {
	if x != nil {
		return x.OriginalAmount
	}
	return nil
}

func (x *TargetDeduction) GetFinalAmount() *money.Money {
	if x != nil {
		return x.FinalAmount
	}
	return nil
}

func (x *TargetDeduction) GetCouponDeductions() []*CouponDeduction {
	if x != nil {
		return x.CouponDeductions
	}
	return nil
}

func (x *TargetDeduction) GetOrderItemId() int64 {
	if x != nil && x.OrderItemId != nil {
		return *x.OrderItemId
	}
	return 0
}

// CouponDeduction 优惠券抵扣
type CouponDeduction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// coupon 优惠券信息
	Coupon *Coupon `protobuf:"bytes,1,opt,name=coupon,proto3" json:"coupon,omitempty"`
	// deduction_value 抵扣值
	//
	// Types that are assignable to DeductionValue:
	//
	//	*CouponDeduction_FixedAmount
	//	*CouponDeduction_Percentage
	//	*CouponDeduction_Quantity
	DeductionValue isCouponDeduction_DeductionValue `protobuf_oneof:"deduction_value"`
}

func (x *CouponDeduction) Reset() {
	*x = CouponDeduction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponDeduction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponDeduction) ProtoMessage() {}

func (x *CouponDeduction) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponDeduction.ProtoReflect.Descriptor instead.
func (*CouponDeduction) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{5}
}

func (x *CouponDeduction) GetCoupon() *Coupon {
	if x != nil {
		return x.Coupon
	}
	return nil
}

func (m *CouponDeduction) GetDeductionValue() isCouponDeduction_DeductionValue {
	if m != nil {
		return m.DeductionValue
	}
	return nil
}

func (x *CouponDeduction) GetFixedAmount() *money.Money {
	if x, ok := x.GetDeductionValue().(*CouponDeduction_FixedAmount); ok {
		return x.FixedAmount
	}
	return nil
}

func (x *CouponDeduction) GetPercentage() *decimal.Decimal {
	if x, ok := x.GetDeductionValue().(*CouponDeduction_Percentage); ok {
		return x.Percentage
	}
	return nil
}

func (x *CouponDeduction) GetQuantity() int32 {
	if x, ok := x.GetDeductionValue().(*CouponDeduction_Quantity); ok {
		return x.Quantity
	}
	return 0
}

type isCouponDeduction_DeductionValue interface {
	isCouponDeduction_DeductionValue()
}

type CouponDeduction_FixedAmount struct {
	// 固定金额
	FixedAmount *money.Money `protobuf:"bytes,2,opt,name=fixed_amount,json=fixedAmount,proto3,oneof"`
}

type CouponDeduction_Percentage struct {
	// 百分比
	Percentage *decimal.Decimal `protobuf:"bytes,3,opt,name=percentage,proto3,oneof"` // 0.00 ～ 100.00%
}

type CouponDeduction_Quantity struct {
	// 数量
	Quantity int32 `protobuf:"varint,4,opt,name=quantity,proto3,oneof"`
}

func (*CouponDeduction_FixedAmount) isCouponDeduction_DeductionValue() {}

func (*CouponDeduction_Percentage) isCouponDeduction_DeductionValue() {}

func (*CouponDeduction_Quantity) isCouponDeduction_DeductionValue() {}

// Coupon Redeem
type CouponRedeem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// coupon
	CouponSources *Source `protobuf:"bytes,1,opt,name=coupon_sources,json=couponSources,proto3" json:"coupon_sources,omitempty"`
	// targets
	Targets []*CouponRedeem_RedeemTarget `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *CouponRedeem) Reset() {
	*x = CouponRedeem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponRedeem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponRedeem) ProtoMessage() {}

func (x *CouponRedeem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponRedeem.ProtoReflect.Descriptor instead.
func (*CouponRedeem) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{6}
}

func (x *CouponRedeem) GetCouponSources() *Source {
	if x != nil {
		return x.CouponSources
	}
	return nil
}

func (x *CouponRedeem) GetTargets() []*CouponRedeem_RedeemTarget {
	if x != nil {
		return x.Targets
	}
	return nil
}

// CouponSearchCondition 优惠券搜索条件
type CouponSearchCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// name keyword
	NameKeyword *string `protobuf:"bytes,2,opt,name=name_keyword,json=nameKeyword,proto3,oneof" json:"name_keyword,omitempty"`
	// source type
	SourceTypes []CouponSearchCondition_SourceType `protobuf:"varint,3,rep,packed,name=source_types,json=sourceTypes,proto3,enum=moego.models.promotion.v1.CouponSearchCondition_SourceType" json:"source_types,omitempty"`
	// targets
	Targets []*CouponSearchCondition_Target `protobuf:"bytes,4,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *CouponSearchCondition) Reset() {
	*x = CouponSearchCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponSearchCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponSearchCondition) ProtoMessage() {}

func (x *CouponSearchCondition) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponSearchCondition.ProtoReflect.Descriptor instead.
func (*CouponSearchCondition) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{7}
}

func (x *CouponSearchCondition) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CouponSearchCondition) GetNameKeyword() string {
	if x != nil && x.NameKeyword != nil {
		return *x.NameKeyword
	}
	return ""
}

func (x *CouponSearchCondition) GetSourceTypes() []CouponSearchCondition_SourceType {
	if x != nil {
		return x.SourceTypes
	}
	return nil
}

func (x *CouponSearchCondition) GetTargets() []*CouponSearchCondition_Target {
	if x != nil {
		return x.Targets
	}
	return nil
}

// Redeem Target
type CouponRedeem_RedeemTarget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// target_type 目标类型
	TargetType TargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=moego.models.promotion.v1.TargetType" json:"target_type,omitempty"`
	// target_id 目标ID
	TargetId int64 `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	// invoice item id
	OrderItemId int64 `protobuf:"varint,3,opt,name=order_item_id,json=orderItemId,proto3" json:"order_item_id,omitempty"`
	// idempotence_key
	IdempotenceKey string `protobuf:"bytes,4,opt,name=idempotence_key,json=idempotenceKey,proto3" json:"idempotence_key,omitempty"`
	// amount
	Amount int64 `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// 目标收入 -- discount 核销使用
	TargetSales *money.Money `protobuf:"bytes,6,opt,name=target_sales,json=targetSales,proto3" json:"target_sales,omitempty"`
}

func (x *CouponRedeem_RedeemTarget) Reset() {
	*x = CouponRedeem_RedeemTarget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponRedeem_RedeemTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponRedeem_RedeemTarget) ProtoMessage() {}

func (x *CouponRedeem_RedeemTarget) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponRedeem_RedeemTarget.ProtoReflect.Descriptor instead.
func (*CouponRedeem_RedeemTarget) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{6, 0}
}

func (x *CouponRedeem_RedeemTarget) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *CouponRedeem_RedeemTarget) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *CouponRedeem_RedeemTarget) GetOrderItemId() int64 {
	if x != nil {
		return x.OrderItemId
	}
	return 0
}

func (x *CouponRedeem_RedeemTarget) GetIdempotenceKey() string {
	if x != nil {
		return x.IdempotenceKey
	}
	return ""
}

func (x *CouponRedeem_RedeemTarget) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CouponRedeem_RedeemTarget) GetTargetSales() *money.Money {
	if x != nil {
		return x.TargetSales
	}
	return nil
}

// simple target
type CouponSearchCondition_Target struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// target type
	TargetType TargetType `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3,enum=moego.models.promotion.v1.TargetType" json:"target_type,omitempty"`
	// target id
	TargetId int64 `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
}

func (x *CouponSearchCondition_Target) Reset() {
	*x = CouponSearchCondition_Target{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CouponSearchCondition_Target) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponSearchCondition_Target) ProtoMessage() {}

func (x *CouponSearchCondition_Target) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_promotion_v1_coupon_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponSearchCondition_Target.ProtoReflect.Descriptor instead.
func (*CouponSearchCondition_Target) Descriptor() ([]byte, []int) {
	return file_moego_models_promotion_v1_coupon_proto_rawDescGZIP(), []int{7, 0}
}

func (x *CouponSearchCondition_Target) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_TARGET_TYPE_UNSPECIFIED
}

func (x *CouponSearchCondition_Target) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

var File_moego_models_promotion_v1_coupon_proto protoreflect.FileDescriptor

var file_moego_models_promotion_v1_coupon_proto_rawDesc = []byte{
	0x0a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x8d, 0x05, 0x0a, 0x06, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x39, 0x0a,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35,
	0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x3f, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x12, 0x48, 0x0a, 0x0b, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x0b, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x72, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0x96, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x39, 0x0a, 0x06, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x07, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52,
	0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0xca, 0x01, 0x0a, 0x04, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x38, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x78, 0x0a, 0x04, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x4f, 0x45,
	0x47, 0x4f, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x10,
	0x03, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x10, 0x04, 0x12, 0x0c,
	0x0a, 0x08, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54,
	0x41, 0x46, 0x46, 0x10, 0x07, 0x22, 0xb9, 0x04, 0x0a, 0x0e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x42, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x65, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x12, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x49,
	0x64, 0x12, 0x33, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x3c, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x04, 0x63, 0x6f, 0x73, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x04, 0x63, 0x6f, 0x73, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x34, 0x0a, 0x04, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x44,
	0x45, 0x45, 0x4d, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10,
	0x02, 0x22, 0xfe, 0x02, 0x0a, 0x0f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x65, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0f, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61,
	0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0b, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x57,
	0x0a, 0x11, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x44, 0x65, 0x64, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x44, 0x65, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x27, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x69, 0x64, 0x22, 0xee, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x44, 0x65, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x06, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x06, 0x63, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x12, 0x37, 0x0a, 0x0c, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x0b, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63,
	0x69, 0x6d, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x1c, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x42, 0x11, 0x0a, 0x0f, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0xba, 0x03, 0x0a, 0x0c, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x12, 0x48, 0x0a, 0x0e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x0d, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x4e,
	0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x1a, 0x8f,
	0x02, 0x0a, 0x0c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12,
	0x46, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x64, 0x65, 0x6d,
	0x70, 0x6f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6d, 0x70, 0x6f, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x4b, 0x65,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x73,
	0x22, 0xe9, 0x03, 0x0a, 0x15, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0c, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x5e, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x07, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x1a, 0x6d, 0x0a, 0x06, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x12, 0x46, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x49, 0x64, 0x22, 0x54, 0x0a, 0x0a, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x4d,
	0x45, 0x4d, 0x42, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x10, 0x03, 0x42, 0x0f, 0x0a, 0x0d, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x81, 0x01, 0x0a,
	0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_promotion_v1_coupon_proto_rawDescOnce sync.Once
	file_moego_models_promotion_v1_coupon_proto_rawDescData = file_moego_models_promotion_v1_coupon_proto_rawDesc
)

func file_moego_models_promotion_v1_coupon_proto_rawDescGZIP() []byte {
	file_moego_models_promotion_v1_coupon_proto_rawDescOnce.Do(func() {
		file_moego_models_promotion_v1_coupon_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_promotion_v1_coupon_proto_rawDescData)
	})
	return file_moego_models_promotion_v1_coupon_proto_rawDescData
}

var file_moego_models_promotion_v1_coupon_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_promotion_v1_coupon_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_models_promotion_v1_coupon_proto_goTypes = []interface{}{
	(User_Type)(0),                        // 0: moego.models.promotion.v1.User.Type
	(CouponRevision_Type)(0),              // 1: moego.models.promotion.v1.CouponRevision.Type
	(CouponSearchCondition_SourceType)(0), // 2: moego.models.promotion.v1.CouponSearchCondition.SourceType
	(*Coupon)(nil),                        // 3: moego.models.promotion.v1.Coupon
	(*CouponUsage)(nil),                   // 4: moego.models.promotion.v1.CouponUsage
	(*User)(nil),                          // 5: moego.models.promotion.v1.User
	(*CouponRevision)(nil),                // 6: moego.models.promotion.v1.CouponRevision
	(*TargetDeduction)(nil),               // 7: moego.models.promotion.v1.TargetDeduction
	(*CouponDeduction)(nil),               // 8: moego.models.promotion.v1.CouponDeduction
	(*CouponRedeem)(nil),                  // 9: moego.models.promotion.v1.CouponRedeem
	(*CouponSearchCondition)(nil),         // 10: moego.models.promotion.v1.CouponSearchCondition
	(*CouponRedeem_RedeemTarget)(nil),     // 11: moego.models.promotion.v1.CouponRedeem.RedeemTarget
	(*CouponSearchCondition_Target)(nil),  // 12: moego.models.promotion.v1.CouponSearchCondition.Target
	(*Source)(nil),                        // 13: moego.models.promotion.v1.Source
	(*Restrictions)(nil),                  // 14: moego.models.promotion.v1.Restrictions
	(*Discount)(nil),                      // 15: moego.models.promotion.v1.Discount
	(*interval.Interval)(nil),             // 16: google.type.Interval
	(*Redemptions)(nil),                   // 17: moego.models.promotion.v1.Redemptions
	(*timestamppb.Timestamp)(nil),         // 18: google.protobuf.Timestamp
	(*CouponApplicationTarget)(nil),       // 19: moego.models.promotion.v1.CouponApplicationTarget
	(*Targets)(nil),                       // 20: moego.models.promotion.v1.Targets
	(*money.Money)(nil),                   // 21: google.type.Money
	(TargetType)(0),                       // 22: moego.models.promotion.v1.TargetType
	(*decimal.Decimal)(nil),               // 23: google.type.Decimal
}
var file_moego_models_promotion_v1_coupon_proto_depIdxs = []int32{
	13, // 0: moego.models.promotion.v1.Coupon.source:type_name -> moego.models.promotion.v1.Source
	5,  // 1: moego.models.promotion.v1.Coupon.owner:type_name -> moego.models.promotion.v1.User
	14, // 2: moego.models.promotion.v1.Coupon.restrictions:type_name -> moego.models.promotion.v1.Restrictions
	15, // 3: moego.models.promotion.v1.Coupon.discount:type_name -> moego.models.promotion.v1.Discount
	16, // 4: moego.models.promotion.v1.Coupon.validity_period:type_name -> google.type.Interval
	17, // 5: moego.models.promotion.v1.Coupon.redemptions:type_name -> moego.models.promotion.v1.Redemptions
	18, // 6: moego.models.promotion.v1.Coupon.created_at:type_name -> google.protobuf.Timestamp
	18, // 7: moego.models.promotion.v1.Coupon.updated_at:type_name -> google.protobuf.Timestamp
	3,  // 8: moego.models.promotion.v1.CouponUsage.coupon:type_name -> moego.models.promotion.v1.Coupon
	19, // 9: moego.models.promotion.v1.CouponUsage.targets:type_name -> moego.models.promotion.v1.CouponApplicationTarget
	0,  // 10: moego.models.promotion.v1.User.type:type_name -> moego.models.promotion.v1.User.Type
	1,  // 11: moego.models.promotion.v1.CouponRevision.type:type_name -> moego.models.promotion.v1.CouponRevision.Type
	5,  // 12: moego.models.promotion.v1.CouponRevision.user:type_name -> moego.models.promotion.v1.User
	5,  // 13: moego.models.promotion.v1.CouponRevision.operator:type_name -> moego.models.promotion.v1.User
	20, // 14: moego.models.promotion.v1.CouponRevision.targets:type_name -> moego.models.promotion.v1.Targets
	21, // 15: moego.models.promotion.v1.CouponRevision.cost:type_name -> google.type.Money
	18, // 16: moego.models.promotion.v1.CouponRevision.created_at:type_name -> google.protobuf.Timestamp
	22, // 17: moego.models.promotion.v1.TargetDeduction.target_type:type_name -> moego.models.promotion.v1.TargetType
	21, // 18: moego.models.promotion.v1.TargetDeduction.original_amount:type_name -> google.type.Money
	21, // 19: moego.models.promotion.v1.TargetDeduction.final_amount:type_name -> google.type.Money
	8,  // 20: moego.models.promotion.v1.TargetDeduction.coupon_deductions:type_name -> moego.models.promotion.v1.CouponDeduction
	3,  // 21: moego.models.promotion.v1.CouponDeduction.coupon:type_name -> moego.models.promotion.v1.Coupon
	21, // 22: moego.models.promotion.v1.CouponDeduction.fixed_amount:type_name -> google.type.Money
	23, // 23: moego.models.promotion.v1.CouponDeduction.percentage:type_name -> google.type.Decimal
	13, // 24: moego.models.promotion.v1.CouponRedeem.coupon_sources:type_name -> moego.models.promotion.v1.Source
	11, // 25: moego.models.promotion.v1.CouponRedeem.targets:type_name -> moego.models.promotion.v1.CouponRedeem.RedeemTarget
	2,  // 26: moego.models.promotion.v1.CouponSearchCondition.source_types:type_name -> moego.models.promotion.v1.CouponSearchCondition.SourceType
	12, // 27: moego.models.promotion.v1.CouponSearchCondition.targets:type_name -> moego.models.promotion.v1.CouponSearchCondition.Target
	22, // 28: moego.models.promotion.v1.CouponRedeem.RedeemTarget.target_type:type_name -> moego.models.promotion.v1.TargetType
	21, // 29: moego.models.promotion.v1.CouponRedeem.RedeemTarget.target_sales:type_name -> google.type.Money
	22, // 30: moego.models.promotion.v1.CouponSearchCondition.Target.target_type:type_name -> moego.models.promotion.v1.TargetType
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_moego_models_promotion_v1_coupon_proto_init() }
func file_moego_models_promotion_v1_coupon_proto_init() {
	if File_moego_models_promotion_v1_coupon_proto != nil {
		return
	}
	file_moego_models_promotion_v1_promotion_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_promotion_v1_coupon_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Coupon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_coupon_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponUsage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_coupon_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_coupon_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponRevision); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_coupon_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetDeduction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_coupon_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponDeduction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_coupon_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponRedeem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_coupon_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponSearchCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_coupon_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponRedeem_RedeemTarget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_promotion_v1_coupon_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CouponSearchCondition_Target); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_promotion_v1_coupon_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_models_promotion_v1_coupon_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*CouponDeduction_FixedAmount)(nil),
		(*CouponDeduction_Percentage)(nil),
		(*CouponDeduction_Quantity)(nil),
	}
	file_moego_models_promotion_v1_coupon_proto_msgTypes[7].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_promotion_v1_coupon_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_promotion_v1_coupon_proto_goTypes,
		DependencyIndexes: file_moego_models_promotion_v1_coupon_proto_depIdxs,
		EnumInfos:         file_moego_models_promotion_v1_coupon_proto_enumTypes,
		MessageInfos:      file_moego_models_promotion_v1_coupon_proto_msgTypes,
	}.Build()
	File_moego_models_promotion_v1_coupon_proto = out.File
	file_moego_models_promotion_v1_coupon_proto_rawDesc = nil
	file_moego_models_promotion_v1_coupon_proto_goTypes = nil
	file_moego_models_promotion_v1_coupon_proto_depIdxs = nil
}
