// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/appointment/v1/check_in_out_alert_service.proto

package appointmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetAlertSettingRequest
type GetAlertSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetAlertSettingsRequest) Reset() {
	*x = GetAlertSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertSettingsRequest) ProtoMessage() {}

func (x *GetAlertSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetAlertSettingsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetAlertSettingsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// GetAlertSettingResponse
type GetAlertSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check in/out alerts settings
	Settings *v1.CheckInOutAlertSettings `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *GetAlertSettingsResponse) Reset() {
	*x = GetAlertSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertSettingsResponse) ProtoMessage() {}

func (x *GetAlertSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertSettingsResponse.ProtoReflect.Descriptor instead.
func (*GetAlertSettingsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAlertSettingsResponse) GetSettings() *v1.CheckInOutAlertSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

// SaveAlertSettingRequest
type SaveAlertSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// check in alert settings
	CheckInSettings *v1.CheckInAlertSettings `protobuf:"bytes,2,opt,name=check_in_settings,json=checkInSettings,proto3" json:"check_in_settings,omitempty"`
	// check out alert settings
	CheckOutSettings *v1.CheckOutAlertSettings `protobuf:"bytes,3,opt,name=check_out_settings,json=checkOutSettings,proto3" json:"check_out_settings,omitempty"`
}

func (x *SaveAlertSettingsRequest) Reset() {
	*x = SaveAlertSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAlertSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAlertSettingsRequest) ProtoMessage() {}

func (x *SaveAlertSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAlertSettingsRequest.ProtoReflect.Descriptor instead.
func (*SaveAlertSettingsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{2}
}

func (x *SaveAlertSettingsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SaveAlertSettingsRequest) GetCheckInSettings() *v1.CheckInAlertSettings {
	if x != nil {
		return x.CheckInSettings
	}
	return nil
}

func (x *SaveAlertSettingsRequest) GetCheckOutSettings() *v1.CheckOutAlertSettings {
	if x != nil {
		return x.CheckOutSettings
	}
	return nil
}

// SaveAlertSettingResponse
type SaveAlertSettingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check in/out alerts settings
	Settings *v1.CheckInOutAlertSettings `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
}

func (x *SaveAlertSettingsResponse) Reset() {
	*x = SaveAlertSettingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAlertSettingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAlertSettingsResponse) ProtoMessage() {}

func (x *SaveAlertSettingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAlertSettingsResponse.ProtoReflect.Descriptor instead.
func (*SaveAlertSettingsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{3}
}

func (x *SaveAlertSettingsResponse) GetSettings() *v1.CheckInOutAlertSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

// GetAlertsForCheckInRequest
type GetAlertsForCheckInRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetAlertsForCheckInRequest) Reset() {
	*x = GetAlertsForCheckInRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsForCheckInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsForCheckInRequest) ProtoMessage() {}

func (x *GetAlertsForCheckInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsForCheckInRequest.ProtoReflect.Descriptor instead.
func (*GetAlertsForCheckInRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetAlertsForCheckInRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetAlertsForCheckInRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAlertsForCheckInRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// GetAlertsForCheckInResponse
type GetAlertsForCheckInResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check in alert
	Alert *v1.AlertDetail `protobuf:"bytes,1,opt,name=alert,proto3,oneof" json:"alert,omitempty"`
}

func (x *GetAlertsForCheckInResponse) Reset() {
	*x = GetAlertsForCheckInResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsForCheckInResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsForCheckInResponse) ProtoMessage() {}

func (x *GetAlertsForCheckInResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsForCheckInResponse.ProtoReflect.Descriptor instead.
func (*GetAlertsForCheckInResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAlertsForCheckInResponse) GetAlert() *v1.AlertDetail {
	if x != nil {
		return x.Alert
	}
	return nil
}

// GetAlertsForCheckOutRequest
type GetAlertsForCheckOutRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetAlertsForCheckOutRequest) Reset() {
	*x = GetAlertsForCheckOutRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsForCheckOutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsForCheckOutRequest) ProtoMessage() {}

func (x *GetAlertsForCheckOutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsForCheckOutRequest.ProtoReflect.Descriptor instead.
func (*GetAlertsForCheckOutRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetAlertsForCheckOutRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetAlertsForCheckOutRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAlertsForCheckOutRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// GetAlertsForCheckOutResponse
type GetAlertsForCheckOutResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// check out alert
	Alert *v1.AlertDetail `protobuf:"bytes,1,opt,name=alert,proto3,oneof" json:"alert,omitempty"`
}

func (x *GetAlertsForCheckOutResponse) Reset() {
	*x = GetAlertsForCheckOutResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAlertsForCheckOutResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAlertsForCheckOutResponse) ProtoMessage() {}

func (x *GetAlertsForCheckOutResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAlertsForCheckOutResponse.ProtoReflect.Descriptor instead.
func (*GetAlertsForCheckOutResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetAlertsForCheckOutResponse) GetAlert() *v1.AlertDetail {
	if x != nil {
		return x.Alert
	}
	return nil
}

// BatchGetAlertsForCheckInRequest
type BatchGetAlertsForCheckInRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// client and pet ids
	ClientPets []*v1.ClientPetsMapping `protobuf:"bytes,3,rep,name=client_pets,json=clientPets,proto3" json:"client_pets,omitempty"`
}

func (x *BatchGetAlertsForCheckInRequest) Reset() {
	*x = BatchGetAlertsForCheckInRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAlertsForCheckInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAlertsForCheckInRequest) ProtoMessage() {}

func (x *BatchGetAlertsForCheckInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAlertsForCheckInRequest.ProtoReflect.Descriptor instead.
func (*BatchGetAlertsForCheckInRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{8}
}

func (x *BatchGetAlertsForCheckInRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchGetAlertsForCheckInRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchGetAlertsForCheckInRequest) GetClientPets() []*v1.ClientPetsMapping {
	if x != nil {
		return x.ClientPets
	}
	return nil
}

// BatchGetAlertsForCheckInResponse
type BatchGetAlertsForCheckInResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// client and pet alerts
	Alerts []*v1.AlertDetail `protobuf:"bytes,1,rep,name=alerts,proto3" json:"alerts,omitempty"`
}

func (x *BatchGetAlertsForCheckInResponse) Reset() {
	*x = BatchGetAlertsForCheckInResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAlertsForCheckInResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAlertsForCheckInResponse) ProtoMessage() {}

func (x *BatchGetAlertsForCheckInResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAlertsForCheckInResponse.ProtoReflect.Descriptor instead.
func (*BatchGetAlertsForCheckInResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP(), []int{9}
}

func (x *BatchGetAlertsForCheckInResponse) GetAlerts() []*v1.AlertDetail {
	if x != nil {
		return x.Alerts
	}
	return nil
}

var File_moego_service_appointment_v1_check_in_out_alert_service_proto protoreflect.FileDescriptor

var file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDesc = []byte{
	0x0a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x39, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x5f, 0x69, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x6f,
	0x75, 0x74, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x38, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22,
	0x6c, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x08, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0xfa, 0x01,
	0x0a, 0x18, 0x53, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x11, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x60, 0x0a, 0x12, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x10, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f,
	0x75, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x6d, 0x0a, 0x19, 0x53, 0x61,
	0x76, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x4f,
	0x75, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52,
	0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x6c, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43,
	0x0a, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x22, 0x84, 0x01,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0x6d, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52,
	0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x61, 0x6c,
	0x65, 0x72, 0x74, 0x22, 0xb2, 0x01, 0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x50, 0x65, 0x74, 0x73, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x0a, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74, 0x73, 0x22, 0x64, 0x0a, 0x20, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x06,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x32, 0xdc,
	0x05, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x4f, 0x75, 0x74, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01,
	0x0a, 0x11, 0x53, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6e, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46,
	0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f,
	0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x8a, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f,
	0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8d, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46,
	0x6f, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01,
	0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescOnce sync.Once
	file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescData = file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDesc
)

func file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescGZIP() []byte {
	file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescOnce.Do(func() {
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescData)
	})
	return file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDescData
}

var file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_service_appointment_v1_check_in_out_alert_service_proto_goTypes = []interface{}{
	(*GetAlertSettingsRequest)(nil),          // 0: moego.service.appointment.v1.GetAlertSettingsRequest
	(*GetAlertSettingsResponse)(nil),         // 1: moego.service.appointment.v1.GetAlertSettingsResponse
	(*SaveAlertSettingsRequest)(nil),         // 2: moego.service.appointment.v1.SaveAlertSettingsRequest
	(*SaveAlertSettingsResponse)(nil),        // 3: moego.service.appointment.v1.SaveAlertSettingsResponse
	(*GetAlertsForCheckInRequest)(nil),       // 4: moego.service.appointment.v1.GetAlertsForCheckInRequest
	(*GetAlertsForCheckInResponse)(nil),      // 5: moego.service.appointment.v1.GetAlertsForCheckInResponse
	(*GetAlertsForCheckOutRequest)(nil),      // 6: moego.service.appointment.v1.GetAlertsForCheckOutRequest
	(*GetAlertsForCheckOutResponse)(nil),     // 7: moego.service.appointment.v1.GetAlertsForCheckOutResponse
	(*BatchGetAlertsForCheckInRequest)(nil),  // 8: moego.service.appointment.v1.BatchGetAlertsForCheckInRequest
	(*BatchGetAlertsForCheckInResponse)(nil), // 9: moego.service.appointment.v1.BatchGetAlertsForCheckInResponse
	(*v1.CheckInOutAlertSettings)(nil),       // 10: moego.models.appointment.v1.CheckInOutAlertSettings
	(*v1.CheckInAlertSettings)(nil),          // 11: moego.models.appointment.v1.CheckInAlertSettings
	(*v1.CheckOutAlertSettings)(nil),         // 12: moego.models.appointment.v1.CheckOutAlertSettings
	(*v1.AlertDetail)(nil),                   // 13: moego.models.appointment.v1.AlertDetail
	(*v1.ClientPetsMapping)(nil),             // 14: moego.models.appointment.v1.ClientPetsMapping
}
var file_moego_service_appointment_v1_check_in_out_alert_service_proto_depIdxs = []int32{
	10, // 0: moego.service.appointment.v1.GetAlertSettingsResponse.settings:type_name -> moego.models.appointment.v1.CheckInOutAlertSettings
	11, // 1: moego.service.appointment.v1.SaveAlertSettingsRequest.check_in_settings:type_name -> moego.models.appointment.v1.CheckInAlertSettings
	12, // 2: moego.service.appointment.v1.SaveAlertSettingsRequest.check_out_settings:type_name -> moego.models.appointment.v1.CheckOutAlertSettings
	10, // 3: moego.service.appointment.v1.SaveAlertSettingsResponse.settings:type_name -> moego.models.appointment.v1.CheckInOutAlertSettings
	13, // 4: moego.service.appointment.v1.GetAlertsForCheckInResponse.alert:type_name -> moego.models.appointment.v1.AlertDetail
	13, // 5: moego.service.appointment.v1.GetAlertsForCheckOutResponse.alert:type_name -> moego.models.appointment.v1.AlertDetail
	14, // 6: moego.service.appointment.v1.BatchGetAlertsForCheckInRequest.client_pets:type_name -> moego.models.appointment.v1.ClientPetsMapping
	13, // 7: moego.service.appointment.v1.BatchGetAlertsForCheckInResponse.alerts:type_name -> moego.models.appointment.v1.AlertDetail
	0,  // 8: moego.service.appointment.v1.CheckInOutAlertService.GetAlertSettings:input_type -> moego.service.appointment.v1.GetAlertSettingsRequest
	2,  // 9: moego.service.appointment.v1.CheckInOutAlertService.SaveAlertSettings:input_type -> moego.service.appointment.v1.SaveAlertSettingsRequest
	8,  // 10: moego.service.appointment.v1.CheckInOutAlertService.BatchGetAlertsForCheckIn:input_type -> moego.service.appointment.v1.BatchGetAlertsForCheckInRequest
	4,  // 11: moego.service.appointment.v1.CheckInOutAlertService.GetAlertsForCheckIn:input_type -> moego.service.appointment.v1.GetAlertsForCheckInRequest
	6,  // 12: moego.service.appointment.v1.CheckInOutAlertService.GetAlertsForCheckOut:input_type -> moego.service.appointment.v1.GetAlertsForCheckOutRequest
	1,  // 13: moego.service.appointment.v1.CheckInOutAlertService.GetAlertSettings:output_type -> moego.service.appointment.v1.GetAlertSettingsResponse
	3,  // 14: moego.service.appointment.v1.CheckInOutAlertService.SaveAlertSettings:output_type -> moego.service.appointment.v1.SaveAlertSettingsResponse
	9,  // 15: moego.service.appointment.v1.CheckInOutAlertService.BatchGetAlertsForCheckIn:output_type -> moego.service.appointment.v1.BatchGetAlertsForCheckInResponse
	5,  // 16: moego.service.appointment.v1.CheckInOutAlertService.GetAlertsForCheckIn:output_type -> moego.service.appointment.v1.GetAlertsForCheckInResponse
	7,  // 17: moego.service.appointment.v1.CheckInOutAlertService.GetAlertsForCheckOut:output_type -> moego.service.appointment.v1.GetAlertsForCheckOutResponse
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_service_appointment_v1_check_in_out_alert_service_proto_init() }
func file_moego_service_appointment_v1_check_in_out_alert_service_proto_init() {
	if File_moego_service_appointment_v1_check_in_out_alert_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAlertSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAlertSettingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsForCheckInRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsForCheckInResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsForCheckOutRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAlertsForCheckOutResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAlertsForCheckInRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAlertsForCheckInResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes[7].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_appointment_v1_check_in_out_alert_service_proto_goTypes,
		DependencyIndexes: file_moego_service_appointment_v1_check_in_out_alert_service_proto_depIdxs,
		MessageInfos:      file_moego_service_appointment_v1_check_in_out_alert_service_proto_msgTypes,
	}.Build()
	File_moego_service_appointment_v1_check_in_out_alert_service_proto = out.File
	file_moego_service_appointment_v1_check_in_out_alert_service_proto_rawDesc = nil
	file_moego_service_appointment_v1_check_in_out_alert_service_proto_goTypes = nil
	file_moego_service_appointment_v1_check_in_out_alert_service_proto_depIdxs = nil
}
