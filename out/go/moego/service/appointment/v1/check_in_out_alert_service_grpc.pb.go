// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/check_in_out_alert_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CheckInOutAlertServiceClient is the client API for CheckInOutAlertService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CheckInOutAlertServiceClient interface {
	// get check-in-out alter settings
	GetAlertSettings(ctx context.Context, in *GetAlertSettingsRequest, opts ...grpc.CallOption) (*GetAlertSettingsResponse, error)
	// save check-in-out alter settings
	SaveAlertSettings(ctx context.Context, in *SaveAlertSettingsRequest, opts ...grpc.CallOption) (*SaveAlertSettingsResponse, error)
	// batch get alter detail for check in
	BatchGetAlertsForCheckIn(ctx context.Context, in *BatchGetAlertsForCheckInRequest, opts ...grpc.CallOption) (*BatchGetAlertsForCheckInResponse, error)
	// get alter detail for check in
	GetAlertsForCheckIn(ctx context.Context, in *GetAlertsForCheckInRequest, opts ...grpc.CallOption) (*GetAlertsForCheckInResponse, error)
	// get alter detail for check out
	GetAlertsForCheckOut(ctx context.Context, in *GetAlertsForCheckOutRequest, opts ...grpc.CallOption) (*GetAlertsForCheckOutResponse, error)
}

type checkInOutAlertServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCheckInOutAlertServiceClient(cc grpc.ClientConnInterface) CheckInOutAlertServiceClient {
	return &checkInOutAlertServiceClient{cc}
}

func (c *checkInOutAlertServiceClient) GetAlertSettings(ctx context.Context, in *GetAlertSettingsRequest, opts ...grpc.CallOption) (*GetAlertSettingsResponse, error) {
	out := new(GetAlertSettingsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.CheckInOutAlertService/GetAlertSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkInOutAlertServiceClient) SaveAlertSettings(ctx context.Context, in *SaveAlertSettingsRequest, opts ...grpc.CallOption) (*SaveAlertSettingsResponse, error) {
	out := new(SaveAlertSettingsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.CheckInOutAlertService/SaveAlertSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkInOutAlertServiceClient) BatchGetAlertsForCheckIn(ctx context.Context, in *BatchGetAlertsForCheckInRequest, opts ...grpc.CallOption) (*BatchGetAlertsForCheckInResponse, error) {
	out := new(BatchGetAlertsForCheckInResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.CheckInOutAlertService/BatchGetAlertsForCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkInOutAlertServiceClient) GetAlertsForCheckIn(ctx context.Context, in *GetAlertsForCheckInRequest, opts ...grpc.CallOption) (*GetAlertsForCheckInResponse, error) {
	out := new(GetAlertsForCheckInResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.CheckInOutAlertService/GetAlertsForCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkInOutAlertServiceClient) GetAlertsForCheckOut(ctx context.Context, in *GetAlertsForCheckOutRequest, opts ...grpc.CallOption) (*GetAlertsForCheckOutResponse, error) {
	out := new(GetAlertsForCheckOutResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.CheckInOutAlertService/GetAlertsForCheckOut", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CheckInOutAlertServiceServer is the server API for CheckInOutAlertService service.
// All implementations must embed UnimplementedCheckInOutAlertServiceServer
// for forward compatibility
type CheckInOutAlertServiceServer interface {
	// get check-in-out alter settings
	GetAlertSettings(context.Context, *GetAlertSettingsRequest) (*GetAlertSettingsResponse, error)
	// save check-in-out alter settings
	SaveAlertSettings(context.Context, *SaveAlertSettingsRequest) (*SaveAlertSettingsResponse, error)
	// batch get alter detail for check in
	BatchGetAlertsForCheckIn(context.Context, *BatchGetAlertsForCheckInRequest) (*BatchGetAlertsForCheckInResponse, error)
	// get alter detail for check in
	GetAlertsForCheckIn(context.Context, *GetAlertsForCheckInRequest) (*GetAlertsForCheckInResponse, error)
	// get alter detail for check out
	GetAlertsForCheckOut(context.Context, *GetAlertsForCheckOutRequest) (*GetAlertsForCheckOutResponse, error)
	mustEmbedUnimplementedCheckInOutAlertServiceServer()
}

// UnimplementedCheckInOutAlertServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCheckInOutAlertServiceServer struct {
}

func (UnimplementedCheckInOutAlertServiceServer) GetAlertSettings(context.Context, *GetAlertSettingsRequest) (*GetAlertSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAlertSettings not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) SaveAlertSettings(context.Context, *SaveAlertSettingsRequest) (*SaveAlertSettingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveAlertSettings not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) BatchGetAlertsForCheckIn(context.Context, *BatchGetAlertsForCheckInRequest) (*BatchGetAlertsForCheckInResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetAlertsForCheckIn not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) GetAlertsForCheckIn(context.Context, *GetAlertsForCheckInRequest) (*GetAlertsForCheckInResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAlertsForCheckIn not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) GetAlertsForCheckOut(context.Context, *GetAlertsForCheckOutRequest) (*GetAlertsForCheckOutResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAlertsForCheckOut not implemented")
}
func (UnimplementedCheckInOutAlertServiceServer) mustEmbedUnimplementedCheckInOutAlertServiceServer() {
}

// UnsafeCheckInOutAlertServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CheckInOutAlertServiceServer will
// result in compilation errors.
type UnsafeCheckInOutAlertServiceServer interface {
	mustEmbedUnimplementedCheckInOutAlertServiceServer()
}

func RegisterCheckInOutAlertServiceServer(s grpc.ServiceRegistrar, srv CheckInOutAlertServiceServer) {
	s.RegisterService(&CheckInOutAlertService_ServiceDesc, srv)
}

func _CheckInOutAlertService_GetAlertSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAlertSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).GetAlertSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.CheckInOutAlertService/GetAlertSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).GetAlertSettings(ctx, req.(*GetAlertSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CheckInOutAlertService_SaveAlertSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAlertSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).SaveAlertSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.CheckInOutAlertService/SaveAlertSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).SaveAlertSettings(ctx, req.(*SaveAlertSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CheckInOutAlertService_BatchGetAlertsForCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAlertsForCheckInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).BatchGetAlertsForCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.CheckInOutAlertService/BatchGetAlertsForCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).BatchGetAlertsForCheckIn(ctx, req.(*BatchGetAlertsForCheckInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CheckInOutAlertService_GetAlertsForCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAlertsForCheckInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).GetAlertsForCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.CheckInOutAlertService/GetAlertsForCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).GetAlertsForCheckIn(ctx, req.(*GetAlertsForCheckInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CheckInOutAlertService_GetAlertsForCheckOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAlertsForCheckOutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckInOutAlertServiceServer).GetAlertsForCheckOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.CheckInOutAlertService/GetAlertsForCheckOut",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckInOutAlertServiceServer).GetAlertsForCheckOut(ctx, req.(*GetAlertsForCheckOutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CheckInOutAlertService_ServiceDesc is the grpc.ServiceDesc for CheckInOutAlertService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CheckInOutAlertService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.CheckInOutAlertService",
	HandlerType: (*CheckInOutAlertServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAlertSettings",
			Handler:    _CheckInOutAlertService_GetAlertSettings_Handler,
		},
		{
			MethodName: "SaveAlertSettings",
			Handler:    _CheckInOutAlertService_SaveAlertSettings_Handler,
		},
		{
			MethodName: "BatchGetAlertsForCheckIn",
			Handler:    _CheckInOutAlertService_BatchGetAlertsForCheckIn_Handler,
		},
		{
			MethodName: "GetAlertsForCheckIn",
			Handler:    _CheckInOutAlertService_GetAlertsForCheckIn_Handler,
		},
		{
			MethodName: "GetAlertsForCheckOut",
			Handler:    _CheckInOutAlertService_GetAlertsForCheckOut_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/check_in_out_alert_service.proto",
}
