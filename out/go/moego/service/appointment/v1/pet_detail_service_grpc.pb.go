// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/pet_detail_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PetDetailServiceClient is the client API for PetDetailService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PetDetailServiceClient interface {
	// Get pet detail by id, not including deleted records.
	GetPetDetail(ctx context.Context, in *GetPetDetailRequest, opts ...grpc.CallOption) (*GetPetDetailResponse, error)
	// Save or update pet's selected services
	// If there is no pet, the selected services will be saved directly.
	// If there is a pet, it will delete the original services selected for the pet, then save the newly selected services again.
	SaveOrUpdatePetDetails(ctx context.Context, in *SaveOrUpdatePetDetailsRequest, opts ...grpc.CallOption) (*SaveOrUpdatePetDetailsResponse, error)
	// add new pet detail for extra order
	CreatePetDetailsForExtraOrder(ctx context.Context, in *CreatePetDetailsForExtraOrderRequest, opts ...grpc.CallOption) (*CreatePetDetailsForExtraOrderResponse, error)
	// delete pet detail for extra order
	DeletePetDetailForExtraOrder(ctx context.Context, in *DeletePetDetailForExtraOrderRequest, opts ...grpc.CallOption) (*DeletePetDetailForExtraOrderResponse, error)
	// Delete selected pet
	DeletePet(ctx context.Context, in *DeletePetRequest, opts ...grpc.CallOption) (*DeletePetResponse, error)
	// Delete selected pet evaluation service detail
	DeletePetEvaluation(ctx context.Context, in *DeletePetEvaluationRequest, opts ...grpc.CallOption) (*DeletePetEvaluationResponse, error)
	// Update upcoming appt pet details
	UpdateUpcomingPetDetails(ctx context.Context, in *UpdateUpcomingPetDetailsRequest, opts ...grpc.CallOption) (*UpdateUpcomingPetDetailsResponse, error)
	// Update upcoming appointments
	// NOTE: 这个接口调用很慢，业务代码不要尝试同步调用。
	// 这个接口的使用场景很独立，只有在修改 service 触发 apply to upcoming 时才会调用。
	// 不要尝试复用这个接口，除非你知道这个接口的真正目的。
	UpdateUpcomingAppointments(ctx context.Context, in *UpdateUpcomingAppointmentsRequest, opts ...grpc.CallOption) (*UpdateUpcomingAppointmentsResponse, error)
	// get pet detail list
	GetPetDetailList(ctx context.Context, in *GetPetDetailListRequest, opts ...grpc.CallOption) (*GetPetDetailListResponse, error)
	// Selectively update a PetDetail, only the fields that are set in the request will be updated
	UpdatePetDetail(ctx context.Context, in *UpdatePetDetailRequest, opts ...grpc.CallOption) (*UpdatePetDetailResponse, error)
	// get last pet detail
	GetLastPetDetail(ctx context.Context, in *GetLastPetDetailRequest, opts ...grpc.CallOption) (*GetLastPetDetailResponse, error)
	// get staff pet detail
	GetStaffPetDetails(ctx context.Context, in *GetStaffPetDetailsRequest, opts ...grpc.CallOption) (*GetStaffPetDetailsResponse, error)
}

type petDetailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPetDetailServiceClient(cc grpc.ClientConnInterface) PetDetailServiceClient {
	return &petDetailServiceClient{cc}
}

func (c *petDetailServiceClient) GetPetDetail(ctx context.Context, in *GetPetDetailRequest, opts ...grpc.CallOption) (*GetPetDetailResponse, error) {
	out := new(GetPetDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/GetPetDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) SaveOrUpdatePetDetails(ctx context.Context, in *SaveOrUpdatePetDetailsRequest, opts ...grpc.CallOption) (*SaveOrUpdatePetDetailsResponse, error) {
	out := new(SaveOrUpdatePetDetailsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/SaveOrUpdatePetDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) CreatePetDetailsForExtraOrder(ctx context.Context, in *CreatePetDetailsForExtraOrderRequest, opts ...grpc.CallOption) (*CreatePetDetailsForExtraOrderResponse, error) {
	out := new(CreatePetDetailsForExtraOrderResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/CreatePetDetailsForExtraOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) DeletePetDetailForExtraOrder(ctx context.Context, in *DeletePetDetailForExtraOrderRequest, opts ...grpc.CallOption) (*DeletePetDetailForExtraOrderResponse, error) {
	out := new(DeletePetDetailForExtraOrderResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/DeletePetDetailForExtraOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) DeletePet(ctx context.Context, in *DeletePetRequest, opts ...grpc.CallOption) (*DeletePetResponse, error) {
	out := new(DeletePetResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/DeletePet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) DeletePetEvaluation(ctx context.Context, in *DeletePetEvaluationRequest, opts ...grpc.CallOption) (*DeletePetEvaluationResponse, error) {
	out := new(DeletePetEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/DeletePetEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) UpdateUpcomingPetDetails(ctx context.Context, in *UpdateUpcomingPetDetailsRequest, opts ...grpc.CallOption) (*UpdateUpcomingPetDetailsResponse, error) {
	out := new(UpdateUpcomingPetDetailsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/UpdateUpcomingPetDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) UpdateUpcomingAppointments(ctx context.Context, in *UpdateUpcomingAppointmentsRequest, opts ...grpc.CallOption) (*UpdateUpcomingAppointmentsResponse, error) {
	out := new(UpdateUpcomingAppointmentsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/UpdateUpcomingAppointments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) GetPetDetailList(ctx context.Context, in *GetPetDetailListRequest, opts ...grpc.CallOption) (*GetPetDetailListResponse, error) {
	out := new(GetPetDetailListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/GetPetDetailList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) UpdatePetDetail(ctx context.Context, in *UpdatePetDetailRequest, opts ...grpc.CallOption) (*UpdatePetDetailResponse, error) {
	out := new(UpdatePetDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/UpdatePetDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) GetLastPetDetail(ctx context.Context, in *GetLastPetDetailRequest, opts ...grpc.CallOption) (*GetLastPetDetailResponse, error) {
	out := new(GetLastPetDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/GetLastPetDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petDetailServiceClient) GetStaffPetDetails(ctx context.Context, in *GetStaffPetDetailsRequest, opts ...grpc.CallOption) (*GetStaffPetDetailsResponse, error) {
	out := new(GetStaffPetDetailsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.PetDetailService/GetStaffPetDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetDetailServiceServer is the server API for PetDetailService service.
// All implementations must embed UnimplementedPetDetailServiceServer
// for forward compatibility
type PetDetailServiceServer interface {
	// Get pet detail by id, not including deleted records.
	GetPetDetail(context.Context, *GetPetDetailRequest) (*GetPetDetailResponse, error)
	// Save or update pet's selected services
	// If there is no pet, the selected services will be saved directly.
	// If there is a pet, it will delete the original services selected for the pet, then save the newly selected services again.
	SaveOrUpdatePetDetails(context.Context, *SaveOrUpdatePetDetailsRequest) (*SaveOrUpdatePetDetailsResponse, error)
	// add new pet detail for extra order
	CreatePetDetailsForExtraOrder(context.Context, *CreatePetDetailsForExtraOrderRequest) (*CreatePetDetailsForExtraOrderResponse, error)
	// delete pet detail for extra order
	DeletePetDetailForExtraOrder(context.Context, *DeletePetDetailForExtraOrderRequest) (*DeletePetDetailForExtraOrderResponse, error)
	// Delete selected pet
	DeletePet(context.Context, *DeletePetRequest) (*DeletePetResponse, error)
	// Delete selected pet evaluation service detail
	DeletePetEvaluation(context.Context, *DeletePetEvaluationRequest) (*DeletePetEvaluationResponse, error)
	// Update upcoming appt pet details
	UpdateUpcomingPetDetails(context.Context, *UpdateUpcomingPetDetailsRequest) (*UpdateUpcomingPetDetailsResponse, error)
	// Update upcoming appointments
	// NOTE: 这个接口调用很慢，业务代码不要尝试同步调用。
	// 这个接口的使用场景很独立，只有在修改 service 触发 apply to upcoming 时才会调用。
	// 不要尝试复用这个接口，除非你知道这个接口的真正目的。
	UpdateUpcomingAppointments(context.Context, *UpdateUpcomingAppointmentsRequest) (*UpdateUpcomingAppointmentsResponse, error)
	// get pet detail list
	GetPetDetailList(context.Context, *GetPetDetailListRequest) (*GetPetDetailListResponse, error)
	// Selectively update a PetDetail, only the fields that are set in the request will be updated
	UpdatePetDetail(context.Context, *UpdatePetDetailRequest) (*UpdatePetDetailResponse, error)
	// get last pet detail
	GetLastPetDetail(context.Context, *GetLastPetDetailRequest) (*GetLastPetDetailResponse, error)
	// get staff pet detail
	GetStaffPetDetails(context.Context, *GetStaffPetDetailsRequest) (*GetStaffPetDetailsResponse, error)
	mustEmbedUnimplementedPetDetailServiceServer()
}

// UnimplementedPetDetailServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPetDetailServiceServer struct {
}

func (UnimplementedPetDetailServiceServer) GetPetDetail(context.Context, *GetPetDetailRequest) (*GetPetDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetDetail not implemented")
}
func (UnimplementedPetDetailServiceServer) SaveOrUpdatePetDetails(context.Context, *SaveOrUpdatePetDetailsRequest) (*SaveOrUpdatePetDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveOrUpdatePetDetails not implemented")
}
func (UnimplementedPetDetailServiceServer) CreatePetDetailsForExtraOrder(context.Context, *CreatePetDetailsForExtraOrderRequest) (*CreatePetDetailsForExtraOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetDetailsForExtraOrder not implemented")
}
func (UnimplementedPetDetailServiceServer) DeletePetDetailForExtraOrder(context.Context, *DeletePetDetailForExtraOrderRequest) (*DeletePetDetailForExtraOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetDetailForExtraOrder not implemented")
}
func (UnimplementedPetDetailServiceServer) DeletePet(context.Context, *DeletePetRequest) (*DeletePetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePet not implemented")
}
func (UnimplementedPetDetailServiceServer) DeletePetEvaluation(context.Context, *DeletePetEvaluationRequest) (*DeletePetEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetEvaluation not implemented")
}
func (UnimplementedPetDetailServiceServer) UpdateUpcomingPetDetails(context.Context, *UpdateUpcomingPetDetailsRequest) (*UpdateUpcomingPetDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUpcomingPetDetails not implemented")
}
func (UnimplementedPetDetailServiceServer) UpdateUpcomingAppointments(context.Context, *UpdateUpcomingAppointmentsRequest) (*UpdateUpcomingAppointmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUpcomingAppointments not implemented")
}
func (UnimplementedPetDetailServiceServer) GetPetDetailList(context.Context, *GetPetDetailListRequest) (*GetPetDetailListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetDetailList not implemented")
}
func (UnimplementedPetDetailServiceServer) UpdatePetDetail(context.Context, *UpdatePetDetailRequest) (*UpdatePetDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetDetail not implemented")
}
func (UnimplementedPetDetailServiceServer) GetLastPetDetail(context.Context, *GetLastPetDetailRequest) (*GetLastPetDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastPetDetail not implemented")
}
func (UnimplementedPetDetailServiceServer) GetStaffPetDetails(context.Context, *GetStaffPetDetailsRequest) (*GetStaffPetDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaffPetDetails not implemented")
}
func (UnimplementedPetDetailServiceServer) mustEmbedUnimplementedPetDetailServiceServer() {}

// UnsafePetDetailServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PetDetailServiceServer will
// result in compilation errors.
type UnsafePetDetailServiceServer interface {
	mustEmbedUnimplementedPetDetailServiceServer()
}

func RegisterPetDetailServiceServer(s grpc.ServiceRegistrar, srv PetDetailServiceServer) {
	s.RegisterService(&PetDetailService_ServiceDesc, srv)
}

func _PetDetailService_GetPetDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).GetPetDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/GetPetDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).GetPetDetail(ctx, req.(*GetPetDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_SaveOrUpdatePetDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveOrUpdatePetDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).SaveOrUpdatePetDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/SaveOrUpdatePetDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).SaveOrUpdatePetDetails(ctx, req.(*SaveOrUpdatePetDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_CreatePetDetailsForExtraOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetDetailsForExtraOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).CreatePetDetailsForExtraOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/CreatePetDetailsForExtraOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).CreatePetDetailsForExtraOrder(ctx, req.(*CreatePetDetailsForExtraOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_DeletePetDetailForExtraOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetDetailForExtraOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).DeletePetDetailForExtraOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/DeletePetDetailForExtraOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).DeletePetDetailForExtraOrder(ctx, req.(*DeletePetDetailForExtraOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_DeletePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).DeletePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/DeletePet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).DeletePet(ctx, req.(*DeletePetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_DeletePetEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).DeletePetEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/DeletePetEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).DeletePetEvaluation(ctx, req.(*DeletePetEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_UpdateUpcomingPetDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUpcomingPetDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).UpdateUpcomingPetDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/UpdateUpcomingPetDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).UpdateUpcomingPetDetails(ctx, req.(*UpdateUpcomingPetDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_UpdateUpcomingAppointments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUpcomingAppointmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).UpdateUpcomingAppointments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/UpdateUpcomingAppointments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).UpdateUpcomingAppointments(ctx, req.(*UpdateUpcomingAppointmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_GetPetDetailList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetDetailListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).GetPetDetailList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/GetPetDetailList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).GetPetDetailList(ctx, req.(*GetPetDetailListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_UpdatePetDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).UpdatePetDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/UpdatePetDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).UpdatePetDetail(ctx, req.(*UpdatePetDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_GetLastPetDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastPetDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).GetLastPetDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/GetLastPetDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).GetLastPetDetail(ctx, req.(*GetLastPetDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetDetailService_GetStaffPetDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaffPetDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetDetailServiceServer).GetStaffPetDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.PetDetailService/GetStaffPetDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetDetailServiceServer).GetStaffPetDetails(ctx, req.(*GetStaffPetDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PetDetailService_ServiceDesc is the grpc.ServiceDesc for PetDetailService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PetDetailService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.PetDetailService",
	HandlerType: (*PetDetailServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPetDetail",
			Handler:    _PetDetailService_GetPetDetail_Handler,
		},
		{
			MethodName: "SaveOrUpdatePetDetails",
			Handler:    _PetDetailService_SaveOrUpdatePetDetails_Handler,
		},
		{
			MethodName: "CreatePetDetailsForExtraOrder",
			Handler:    _PetDetailService_CreatePetDetailsForExtraOrder_Handler,
		},
		{
			MethodName: "DeletePetDetailForExtraOrder",
			Handler:    _PetDetailService_DeletePetDetailForExtraOrder_Handler,
		},
		{
			MethodName: "DeletePet",
			Handler:    _PetDetailService_DeletePet_Handler,
		},
		{
			MethodName: "DeletePetEvaluation",
			Handler:    _PetDetailService_DeletePetEvaluation_Handler,
		},
		{
			MethodName: "UpdateUpcomingPetDetails",
			Handler:    _PetDetailService_UpdateUpcomingPetDetails_Handler,
		},
		{
			MethodName: "UpdateUpcomingAppointments",
			Handler:    _PetDetailService_UpdateUpcomingAppointments_Handler,
		},
		{
			MethodName: "GetPetDetailList",
			Handler:    _PetDetailService_GetPetDetailList_Handler,
		},
		{
			MethodName: "UpdatePetDetail",
			Handler:    _PetDetailService_UpdatePetDetail_Handler,
		},
		{
			MethodName: "GetLastPetDetail",
			Handler:    _PetDetailService_GetLastPetDetail_Handler,
		},
		{
			MethodName: "GetStaffPetDetails",
			Handler:    _PetDetailService_GetStaffPetDetails_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/pet_detail_service.proto",
}
