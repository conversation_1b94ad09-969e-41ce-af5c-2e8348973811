// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/enterprise/v1/setting_service.proto

package enterprisesvcpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// copy intake form
type CopyIntakeFormsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyIntakeFormsRequest) Reset() {
	*x = CopyIntakeFormsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyIntakeFormsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyIntakeFormsRequest) ProtoMessage() {}

func (x *CopyIntakeFormsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyIntakeFormsRequest.ProtoReflect.Descriptor instead.
func (*CopyIntakeFormsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{0}
}

func (x *CopyIntakeFormsRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyIntakeFormsRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy intake form
type CopyIntakeFormsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyIntakeFormsResponse) Reset() {
	*x = CopyIntakeFormsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyIntakeFormsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyIntakeFormsResponse) ProtoMessage() {}

func (x *CopyIntakeFormsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyIntakeFormsResponse.ProtoReflect.Descriptor instead.
func (*CopyIntakeFormsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{1}
}

// copy discount code
type CopyDiscountCodesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyDiscountCodesRequest) Reset() {
	*x = CopyDiscountCodesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyDiscountCodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyDiscountCodesRequest) ProtoMessage() {}

func (x *CopyDiscountCodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyDiscountCodesRequest.ProtoReflect.Descriptor instead.
func (*CopyDiscountCodesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{2}
}

func (x *CopyDiscountCodesRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyDiscountCodesRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy discount code
type CopyDiscountCodesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyDiscountCodesResponse) Reset() {
	*x = CopyDiscountCodesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyDiscountCodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyDiscountCodesResponse) ProtoMessage() {}

func (x *CopyDiscountCodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyDiscountCodesResponse.ProtoReflect.Descriptor instead.
func (*CopyDiscountCodesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{3}
}

// copy roles
type CopyRolesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyRolesRequest) Reset() {
	*x = CopyRolesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyRolesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyRolesRequest) ProtoMessage() {}

func (x *CopyRolesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyRolesRequest.ProtoReflect.Descriptor instead.
func (*CopyRolesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{4}
}

func (x *CopyRolesRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyRolesRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy roles
type CopyRolesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyRolesResponse) Reset() {
	*x = CopyRolesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyRolesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyRolesResponse) ProtoMessage() {}

func (x *CopyRolesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyRolesResponse.ProtoReflect.Descriptor instead.
func (*CopyRolesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{5}
}

// copy packages
type CopyPackagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyPackagesRequest) Reset() {
	*x = CopyPackagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyPackagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyPackagesRequest) ProtoMessage() {}

func (x *CopyPackagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyPackagesRequest.ProtoReflect.Descriptor instead.
func (*CopyPackagesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{6}
}

func (x *CopyPackagesRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyPackagesRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy packages
type CopyPackagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyPackagesResponse) Reset() {
	*x = CopyPackagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyPackagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyPackagesResponse) ProtoMessage() {}

func (x *CopyPackagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyPackagesResponse.ProtoReflect.Descriptor instead.
func (*CopyPackagesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{7}
}

// copy membership
type CopyMembershipsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source company id
	SourceCompanyId int64 `protobuf:"varint,1,opt,name=source_company_id,json=sourceCompanyId,proto3" json:"source_company_id,omitempty"`
	// target company ids
	TargetCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=target_company_ids,json=targetCompanyIds,proto3" json:"target_company_ids,omitempty"`
}

func (x *CopyMembershipsRequest) Reset() {
	*x = CopyMembershipsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyMembershipsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyMembershipsRequest) ProtoMessage() {}

func (x *CopyMembershipsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyMembershipsRequest.ProtoReflect.Descriptor instead.
func (*CopyMembershipsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{8}
}

func (x *CopyMembershipsRequest) GetSourceCompanyId() int64 {
	if x != nil {
		return x.SourceCompanyId
	}
	return 0
}

func (x *CopyMembershipsRequest) GetTargetCompanyIds() []int64 {
	if x != nil {
		return x.TargetCompanyIds
	}
	return nil
}

// response of copy membership
type CopyMembershipsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CopyMembershipsResponse) Reset() {
	*x = CopyMembershipsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyMembershipsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyMembershipsResponse) ProtoMessage() {}

func (x *CopyMembershipsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_enterprise_v1_setting_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyMembershipsResponse.ProtoReflect.Descriptor instead.
func (*CopyMembershipsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP(), []int{9}
}

var File_moego_service_enterprise_v1_setting_service_proto protoreflect.FileDescriptor

var file_moego_service_enterprise_v1_setting_service_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x01, 0x0a, 0x16, 0x43, 0x6f,
	0x70, 0x79, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x12, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18,
	0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x43, 0x6f, 0x70,
	0x79, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x18, 0x43, 0x6f, 0x70, 0x79, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x33, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x1b, 0x0a, 0x19, 0x43, 0x6f, 0x70, 0x79, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x87, 0x01, 0x0a, 0x10, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x6f, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a,
	0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01,
	0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x13, 0x0a,
	0x11, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x13, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x11, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12,
	0x3e, 0x0a, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d,
	0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22,
	0x16, 0x0a, 0x14, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x16, 0x43, 0x6f, 0x70, 0x79,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x33, 0x0a, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x43, 0x6f, 0x70, 0x79, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x32, 0xf2, 0x04, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x43, 0x6f, 0x70, 0x79, 0x49, 0x6e, 0x74,
	0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x49, 0x6e, 0x74, 0x61, 0x6b,
	0x65, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79,
	0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x70, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x70, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x09, 0x43, 0x6f, 0x70, 0x79,
	0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x0f, 0x43, 0x6f, 0x70,
	0x79, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x12, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x70, 0x79, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x89, 0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x73, 0x76,
	0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_enterprise_v1_setting_service_proto_rawDescOnce sync.Once
	file_moego_service_enterprise_v1_setting_service_proto_rawDescData = file_moego_service_enterprise_v1_setting_service_proto_rawDesc
)

func file_moego_service_enterprise_v1_setting_service_proto_rawDescGZIP() []byte {
	file_moego_service_enterprise_v1_setting_service_proto_rawDescOnce.Do(func() {
		file_moego_service_enterprise_v1_setting_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_enterprise_v1_setting_service_proto_rawDescData)
	})
	return file_moego_service_enterprise_v1_setting_service_proto_rawDescData
}

var file_moego_service_enterprise_v1_setting_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_service_enterprise_v1_setting_service_proto_goTypes = []interface{}{
	(*CopyIntakeFormsRequest)(nil),    // 0: moego.service.enterprise.v1.CopyIntakeFormsRequest
	(*CopyIntakeFormsResponse)(nil),   // 1: moego.service.enterprise.v1.CopyIntakeFormsResponse
	(*CopyDiscountCodesRequest)(nil),  // 2: moego.service.enterprise.v1.CopyDiscountCodesRequest
	(*CopyDiscountCodesResponse)(nil), // 3: moego.service.enterprise.v1.CopyDiscountCodesResponse
	(*CopyRolesRequest)(nil),          // 4: moego.service.enterprise.v1.CopyRolesRequest
	(*CopyRolesResponse)(nil),         // 5: moego.service.enterprise.v1.CopyRolesResponse
	(*CopyPackagesRequest)(nil),       // 6: moego.service.enterprise.v1.CopyPackagesRequest
	(*CopyPackagesResponse)(nil),      // 7: moego.service.enterprise.v1.CopyPackagesResponse
	(*CopyMembershipsRequest)(nil),    // 8: moego.service.enterprise.v1.CopyMembershipsRequest
	(*CopyMembershipsResponse)(nil),   // 9: moego.service.enterprise.v1.CopyMembershipsResponse
}
var file_moego_service_enterprise_v1_setting_service_proto_depIdxs = []int32{
	0, // 0: moego.service.enterprise.v1.SettingService.CopyIntakeForms:input_type -> moego.service.enterprise.v1.CopyIntakeFormsRequest
	2, // 1: moego.service.enterprise.v1.SettingService.CopyDiscountCodes:input_type -> moego.service.enterprise.v1.CopyDiscountCodesRequest
	4, // 2: moego.service.enterprise.v1.SettingService.CopyRoles:input_type -> moego.service.enterprise.v1.CopyRolesRequest
	6, // 3: moego.service.enterprise.v1.SettingService.CopyPackages:input_type -> moego.service.enterprise.v1.CopyPackagesRequest
	8, // 4: moego.service.enterprise.v1.SettingService.CopyMemberships:input_type -> moego.service.enterprise.v1.CopyMembershipsRequest
	1, // 5: moego.service.enterprise.v1.SettingService.CopyIntakeForms:output_type -> moego.service.enterprise.v1.CopyIntakeFormsResponse
	3, // 6: moego.service.enterprise.v1.SettingService.CopyDiscountCodes:output_type -> moego.service.enterprise.v1.CopyDiscountCodesResponse
	5, // 7: moego.service.enterprise.v1.SettingService.CopyRoles:output_type -> moego.service.enterprise.v1.CopyRolesResponse
	7, // 8: moego.service.enterprise.v1.SettingService.CopyPackages:output_type -> moego.service.enterprise.v1.CopyPackagesResponse
	9, // 9: moego.service.enterprise.v1.SettingService.CopyMemberships:output_type -> moego.service.enterprise.v1.CopyMembershipsResponse
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_service_enterprise_v1_setting_service_proto_init() }
func file_moego_service_enterprise_v1_setting_service_proto_init() {
	if File_moego_service_enterprise_v1_setting_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyIntakeFormsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyIntakeFormsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyDiscountCodesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyDiscountCodesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyRolesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyRolesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyPackagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyPackagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyMembershipsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_enterprise_v1_setting_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyMembershipsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_enterprise_v1_setting_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_enterprise_v1_setting_service_proto_goTypes,
		DependencyIndexes: file_moego_service_enterprise_v1_setting_service_proto_depIdxs,
		MessageInfos:      file_moego_service_enterprise_v1_setting_service_proto_msgTypes,
	}.Build()
	File_moego_service_enterprise_v1_setting_service_proto = out.File
	file_moego_service_enterprise_v1_setting_service_proto_rawDesc = nil
	file_moego_service_enterprise_v1_setting_service_proto_goTypes = nil
	file_moego_service_enterprise_v1_setting_service_proto_depIdxs = nil
}
