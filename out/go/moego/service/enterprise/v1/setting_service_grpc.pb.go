// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/enterprise/v1/setting_service.proto

package enterprisesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SettingServiceClient is the client API for SettingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SettingServiceClient interface {
	// copy intake form
	CopyIntakeForms(ctx context.Context, in *CopyIntakeFormsRequest, opts ...grpc.CallOption) (*CopyIntakeFormsResponse, error)
	// copy discount code
	CopyDiscountCodes(ctx context.Context, in *CopyDiscountCodesRequest, opts ...grpc.CallOption) (*CopyDiscountCodesResponse, error)
	// copy roles
	CopyRoles(ctx context.Context, in *CopyRolesRequest, opts ...grpc.CallOption) (*CopyRolesResponse, error)
	// copy packages
	CopyPackages(ctx context.Context, in *CopyPackagesRequest, opts ...grpc.CallOption) (*CopyPackagesResponse, error)
	// copy membership
	CopyMemberships(ctx context.Context, in *CopyMembershipsRequest, opts ...grpc.CallOption) (*CopyMembershipsResponse, error)
}

type settingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSettingServiceClient(cc grpc.ClientConnInterface) SettingServiceClient {
	return &settingServiceClient{cc}
}

func (c *settingServiceClient) CopyIntakeForms(ctx context.Context, in *CopyIntakeFormsRequest, opts ...grpc.CallOption) (*CopyIntakeFormsResponse, error) {
	out := new(CopyIntakeFormsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyIntakeForms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CopyDiscountCodes(ctx context.Context, in *CopyDiscountCodesRequest, opts ...grpc.CallOption) (*CopyDiscountCodesResponse, error) {
	out := new(CopyDiscountCodesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyDiscountCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CopyRoles(ctx context.Context, in *CopyRolesRequest, opts ...grpc.CallOption) (*CopyRolesResponse, error) {
	out := new(CopyRolesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyRoles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CopyPackages(ctx context.Context, in *CopyPackagesRequest, opts ...grpc.CallOption) (*CopyPackagesResponse, error) {
	out := new(CopyPackagesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyPackages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settingServiceClient) CopyMemberships(ctx context.Context, in *CopyMembershipsRequest, opts ...grpc.CallOption) (*CopyMembershipsResponse, error) {
	out := new(CopyMembershipsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.enterprise.v1.SettingService/CopyMemberships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SettingServiceServer is the server API for SettingService service.
// All implementations must embed UnimplementedSettingServiceServer
// for forward compatibility
type SettingServiceServer interface {
	// copy intake form
	CopyIntakeForms(context.Context, *CopyIntakeFormsRequest) (*CopyIntakeFormsResponse, error)
	// copy discount code
	CopyDiscountCodes(context.Context, *CopyDiscountCodesRequest) (*CopyDiscountCodesResponse, error)
	// copy roles
	CopyRoles(context.Context, *CopyRolesRequest) (*CopyRolesResponse, error)
	// copy packages
	CopyPackages(context.Context, *CopyPackagesRequest) (*CopyPackagesResponse, error)
	// copy membership
	CopyMemberships(context.Context, *CopyMembershipsRequest) (*CopyMembershipsResponse, error)
	mustEmbedUnimplementedSettingServiceServer()
}

// UnimplementedSettingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSettingServiceServer struct {
}

func (UnimplementedSettingServiceServer) CopyIntakeForms(context.Context, *CopyIntakeFormsRequest) (*CopyIntakeFormsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyIntakeForms not implemented")
}
func (UnimplementedSettingServiceServer) CopyDiscountCodes(context.Context, *CopyDiscountCodesRequest) (*CopyDiscountCodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyDiscountCodes not implemented")
}
func (UnimplementedSettingServiceServer) CopyRoles(context.Context, *CopyRolesRequest) (*CopyRolesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyRoles not implemented")
}
func (UnimplementedSettingServiceServer) CopyPackages(context.Context, *CopyPackagesRequest) (*CopyPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyPackages not implemented")
}
func (UnimplementedSettingServiceServer) CopyMemberships(context.Context, *CopyMembershipsRequest) (*CopyMembershipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyMemberships not implemented")
}
func (UnimplementedSettingServiceServer) mustEmbedUnimplementedSettingServiceServer() {}

// UnsafeSettingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SettingServiceServer will
// result in compilation errors.
type UnsafeSettingServiceServer interface {
	mustEmbedUnimplementedSettingServiceServer()
}

func RegisterSettingServiceServer(s grpc.ServiceRegistrar, srv SettingServiceServer) {
	s.RegisterService(&SettingService_ServiceDesc, srv)
}

func _SettingService_CopyIntakeForms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyIntakeFormsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyIntakeForms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyIntakeForms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyIntakeForms(ctx, req.(*CopyIntakeFormsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CopyDiscountCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyDiscountCodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyDiscountCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyDiscountCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyDiscountCodes(ctx, req.(*CopyDiscountCodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CopyRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyRolesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyRoles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyRoles(ctx, req.(*CopyRolesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CopyPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyPackages",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyPackages(ctx, req.(*CopyPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettingService_CopyMemberships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyMembershipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettingServiceServer).CopyMemberships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.enterprise.v1.SettingService/CopyMemberships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettingServiceServer).CopyMemberships(ctx, req.(*CopyMembershipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SettingService_ServiceDesc is the grpc.ServiceDesc for SettingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SettingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.enterprise.v1.SettingService",
	HandlerType: (*SettingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CopyIntakeForms",
			Handler:    _SettingService_CopyIntakeForms_Handler,
		},
		{
			MethodName: "CopyDiscountCodes",
			Handler:    _SettingService_CopyDiscountCodes_Handler,
		},
		{
			MethodName: "CopyRoles",
			Handler:    _SettingService_CopyRoles_Handler,
		},
		{
			MethodName: "CopyPackages",
			Handler:    _SettingService_CopyPackages_Handler,
		},
		{
			MethodName: "CopyMemberships",
			Handler:    _SettingService_CopyMemberships_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/enterprise/v1/setting_service.proto",
}
