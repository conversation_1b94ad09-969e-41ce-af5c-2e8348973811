// @since 2024-03-12 11:33:59
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/offering/v1/evaluation_service.proto

package offeringsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create evaluation request
type CreateEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// the evaluation def
	EvaluationDef *v1.EvaluationDef `protobuf:"bytes,2,opt,name=evaluation_def,json=evaluationDef,proto3" json:"evaluation_def,omitempty"`
}

func (x *CreateEvaluationRequest) Reset() {
	*x = CreateEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationRequest) ProtoMessage() {}

func (x *CreateEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationRequest.ProtoReflect.Descriptor instead.
func (*CreateEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateEvaluationRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateEvaluationRequest) GetEvaluationDef() *v1.EvaluationDef {
	if x != nil {
		return x.EvaluationDef
	}
	return nil
}

// create evaluation response
type CreateEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model
	EvaluationModel *v1.EvaluationModel `protobuf:"bytes,1,opt,name=evaluation_model,json=evaluationModel,proto3" json:"evaluation_model,omitempty"`
}

func (x *CreateEvaluationResponse) Reset() {
	*x = CreateEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationResponse) ProtoMessage() {}

func (x *CreateEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationResponse.ProtoReflect.Descriptor instead.
func (*CreateEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateEvaluationResponse) GetEvaluationModel() *v1.EvaluationModel {
	if x != nil {
		return x.EvaluationModel
	}
	return nil
}

// update evaluation request
type UpdateEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the evaluation def
	EvaluationDef *v1.EvaluationDef `protobuf:"bytes,2,opt,name=evaluation_def,json=evaluationDef,proto3" json:"evaluation_def,omitempty"`
	// tenant 直接设置 required true 是个 break change，这次先加上，下次再迭代这里的时候，再把 required set true
	Tenant *v11.Tenant `protobuf:"bytes,3,opt,name=tenant,proto3,oneof" json:"tenant,omitempty"`
}

func (x *UpdateEvaluationRequest) Reset() {
	*x = UpdateEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationRequest) ProtoMessage() {}

func (x *UpdateEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationRequest.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateEvaluationRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateEvaluationRequest) GetEvaluationDef() *v1.EvaluationDef {
	if x != nil {
		return x.EvaluationDef
	}
	return nil
}

func (x *UpdateEvaluationRequest) GetTenant() *v11.Tenant {
	if x != nil {
		return x.Tenant
	}
	return nil
}

// update evaluation response
type UpdateEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model
	EvaluationModel *v1.EvaluationModel `protobuf:"bytes,1,opt,name=evaluation_model,json=evaluationModel,proto3" json:"evaluation_model,omitempty"`
}

func (x *UpdateEvaluationResponse) Reset() {
	*x = UpdateEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationResponse) ProtoMessage() {}

func (x *UpdateEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationResponse.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateEvaluationResponse) GetEvaluationModel() *v1.EvaluationModel {
	if x != nil {
		return x.EvaluationModel
	}
	return nil
}

// get evaluation list request
type GetEvaluationListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetEvaluationListRequest) Reset() {
	*x = GetEvaluationListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationListRequest) ProtoMessage() {}

func (x *GetEvaluationListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationListRequest.ProtoReflect.Descriptor instead.
func (*GetEvaluationListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetEvaluationListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get evaluation list response
type GetEvaluationListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model list
	Evaluations []*v1.EvaluationModel `protobuf:"bytes,1,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *GetEvaluationListResponse) Reset() {
	*x = GetEvaluationListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationListResponse) ProtoMessage() {}

func (x *GetEvaluationListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationListResponse.ProtoReflect.Descriptor instead.
func (*GetEvaluationListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetEvaluationListResponse) GetEvaluations() []*v1.EvaluationModel {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// get evaluation request
type GetEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetEvaluationRequest) Reset() {
	*x = GetEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationRequest) ProtoMessage() {}

func (x *GetEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationRequest.ProtoReflect.Descriptor instead.
func (*GetEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetEvaluationRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get evaluation response
type GetEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model
	EvaluationModel *v1.EvaluationModel `protobuf:"bytes,1,opt,name=evaluation_model,json=evaluationModel,proto3" json:"evaluation_model,omitempty"`
}

func (x *GetEvaluationResponse) Reset() {
	*x = GetEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationResponse) ProtoMessage() {}

func (x *GetEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationResponse.ProtoReflect.Descriptor instead.
func (*GetEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetEvaluationResponse) GetEvaluationModel() *v1.EvaluationModel {
	if x != nil {
		return x.EvaluationModel
	}
	return nil
}

// get applicable evaluation list request
type GetApplicableEvaluationListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// service type
	ServiceItemType *v1.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType,oneof" json:"service_item_type,omitempty"`
	// filter by pet
	FilterByPet *v1.ServiceFilterByPet `protobuf:"bytes,4,opt,name=filter_by_pet,json=filterByPet,proto3,oneof" json:"filter_by_pet,omitempty"`
	// include inactive
	IncludeInactive bool `protobuf:"varint,5,opt,name=include_inactive,json=includeInactive,proto3" json:"include_inactive,omitempty"`
}

func (x *GetApplicableEvaluationListRequest) Reset() {
	*x = GetApplicableEvaluationListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicableEvaluationListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableEvaluationListRequest) ProtoMessage() {}

func (x *GetApplicableEvaluationListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableEvaluationListRequest.ProtoReflect.Descriptor instead.
func (*GetApplicableEvaluationListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetApplicableEvaluationListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetApplicableEvaluationListRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetApplicableEvaluationListRequest) GetServiceItemType() v1.ServiceItemType {
	if x != nil && x.ServiceItemType != nil {
		return *x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetApplicableEvaluationListRequest) GetFilterByPet() *v1.ServiceFilterByPet {
	if x != nil {
		return x.FilterByPet
	}
	return nil
}

func (x *GetApplicableEvaluationListRequest) GetIncludeInactive() bool {
	if x != nil {
		return x.IncludeInactive
	}
	return false
}

// get applicable evaluation list response
type GetApplicableEvaluationListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model list
	Evaluations []*v1.EvaluationBriefView `protobuf:"bytes,1,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *GetApplicableEvaluationListResponse) Reset() {
	*x = GetApplicableEvaluationListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicableEvaluationListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicableEvaluationListResponse) ProtoMessage() {}

func (x *GetApplicableEvaluationListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicableEvaluationListResponse.ProtoReflect.Descriptor instead.
func (*GetApplicableEvaluationListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetApplicableEvaluationListResponse) GetEvaluations() []*v1.EvaluationBriefView {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// get business list with applicable evaluation request
type GetBusinessListWithApplicableEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *GetBusinessListWithApplicableEvaluationRequest) Reset() {
	*x = GetBusinessListWithApplicableEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessListWithApplicableEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessListWithApplicableEvaluationRequest) ProtoMessage() {}

func (x *GetBusinessListWithApplicableEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessListWithApplicableEvaluationRequest.ProtoReflect.Descriptor instead.
func (*GetBusinessListWithApplicableEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetBusinessListWithApplicableEvaluationRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// get business list with applicable evaluation response
type GetBusinessListWithApplicableEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// bool is all location
	IsAllLocation bool `protobuf:"varint,1,opt,name=is_all_location,json=isAllLocation,proto3" json:"is_all_location,omitempty"`
	// the business id list, only return when is_all_location is false
	BusinessIds []int64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
}

func (x *GetBusinessListWithApplicableEvaluationResponse) Reset() {
	*x = GetBusinessListWithApplicableEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusinessListWithApplicableEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusinessListWithApplicableEvaluationResponse) ProtoMessage() {}

func (x *GetBusinessListWithApplicableEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusinessListWithApplicableEvaluationResponse.ProtoReflect.Descriptor instead.
func (*GetBusinessListWithApplicableEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetBusinessListWithApplicableEvaluationResponse) GetIsAllLocation() bool {
	if x != nil {
		return x.IsAllLocation
	}
	return false
}

func (x *GetBusinessListWithApplicableEvaluationResponse) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

// get evaluation list with evaluation ids request
type GetEvaluationListWithEvaluationIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation ids
	EvaluationIds []int64 `protobuf:"varint,1,rep,packed,name=evaluation_ids,json=evaluationIds,proto3" json:"evaluation_ids,omitempty"`
}

func (x *GetEvaluationListWithEvaluationIdsRequest) Reset() {
	*x = GetEvaluationListWithEvaluationIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationListWithEvaluationIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationListWithEvaluationIdsRequest) ProtoMessage() {}

func (x *GetEvaluationListWithEvaluationIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationListWithEvaluationIdsRequest.ProtoReflect.Descriptor instead.
func (*GetEvaluationListWithEvaluationIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetEvaluationListWithEvaluationIdsRequest) GetEvaluationIds() []int64 {
	if x != nil {
		return x.EvaluationIds
	}
	return nil
}

// get evaluation list with evaluation ids response
type GetEvaluationListWithEvaluationIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model list
	Evaluations []*v1.EvaluationBriefView `protobuf:"bytes,1,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *GetEvaluationListWithEvaluationIdsResponse) Reset() {
	*x = GetEvaluationListWithEvaluationIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEvaluationListWithEvaluationIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvaluationListWithEvaluationIdsResponse) ProtoMessage() {}

func (x *GetEvaluationListWithEvaluationIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvaluationListWithEvaluationIdsResponse.ProtoReflect.Descriptor instead.
func (*GetEvaluationListWithEvaluationIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetEvaluationListWithEvaluationIdsResponse) GetEvaluations() []*v1.EvaluationBriefView {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// list evaluation request
type ListEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListEvaluationRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListEvaluationRequest) Reset() {
	*x = ListEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationRequest) ProtoMessage() {}

func (x *ListEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationRequest.ProtoReflect.Descriptor instead.
func (*ListEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{14}
}

func (x *ListEvaluationRequest) GetFilter() *ListEvaluationRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListEvaluationRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list evaluation response
type ListEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the evaluation model filter list
	Evaluations []*v1.EvaluationModel `protobuf:"bytes,1,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListEvaluationResponse) Reset() {
	*x = ListEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationResponse) ProtoMessage() {}

func (x *ListEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationResponse.ProtoReflect.Descriptor instead.
func (*ListEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListEvaluationResponse) GetEvaluations() []*v1.EvaluationModel {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

func (x *ListEvaluationResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// delete evaluation request
type DeleteEvaluationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *DeleteEvaluationRequest) Reset() {
	*x = DeleteEvaluationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationRequest) ProtoMessage() {}

func (x *DeleteEvaluationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationRequest.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteEvaluationRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteEvaluationRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// delete evaluation response
type DeleteEvaluationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteEvaluationResponse) Reset() {
	*x = DeleteEvaluationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEvaluationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvaluationResponse) ProtoMessage() {}

func (x *DeleteEvaluationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvaluationResponse.ProtoReflect.Descriptor instead.
func (*DeleteEvaluationResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{17}
}

// filter
type ListEvaluationRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by the is resettable
	IsResettable *bool `protobuf:"varint,1,opt,name=is_resettable,json=isResettable,proto3,oneof" json:"is_resettable,omitempty"`
	// company ids recommended fields for indexing
	CompanyIds []int64 `protobuf:"varint,2,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
	// evaluation ids
	Ids []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListEvaluationRequest_Filter) Reset() {
	*x = ListEvaluationRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationRequest_Filter) ProtoMessage() {}

func (x *ListEvaluationRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_offering_v1_evaluation_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListEvaluationRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ListEvaluationRequest_Filter) GetIsResettable() bool {
	if x != nil && x.IsResettable != nil {
		return *x.IsResettable
	}
	return false
}

func (x *ListEvaluationRequest_Filter) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

func (x *ListEvaluationRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

var File_moego_service_offering_v1_evaluation_service_proto protoreflect.FileDescriptor

var file_moego_service_offering_v1_evaluation_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9b, 0x01, 0x0a, 0x17, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x58, 0x0a,
	0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x22, 0x7a, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x10, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x22, 0xe4, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x58, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x66, 0x12, 0x4b, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x00, 0x48, 0x00, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42,
	0x09, 0x0a, 0x07, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x22, 0x7a, 0x0a, 0x18, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x10, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x42, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x68, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x2f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x77, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e,
	0x0a, 0x10, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x91,
	0x03, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5a, 0x0a, 0x11,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x48, 0x01, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x55, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x50, 0x65, 0x74, 0x48, 0x02, 0x52,
	0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x50, 0x65, 0x74, 0x88, 0x01, 0x01, 0x12,
	0x29, 0x0a, 0x10, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x6e, 0x63, 0x6c, 0x75,
	0x64, 0x65, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x5f, 0x70,
	0x65, 0x74, 0x22, 0x76, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0b, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x58, 0x0a, 0x2e, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x22, 0x7c, 0x0a, 0x2f, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x61, 0x6c,
	0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x73, 0x22, 0x52, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x7d, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x72, 0x69, 0x65, 0x66, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xc9, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x4f, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x9b, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x28,
	0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x69, 0x73, 0x52, 0x65, 0x73, 0x65, 0x74,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa,
	0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08, 0x00, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x22, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a,
	0x08, 0x00, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x22, 0xa9, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0b,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x65, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5a, 0x0a,
	0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x1a, 0x0a, 0x18, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0x8b, 0x0a, 0x0a, 0x11, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7b, 0x0a, 0x10, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x72, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xc0, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb1, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69,
	0x74, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12,
	0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x0e,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x83, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_offering_v1_evaluation_service_proto_rawDescOnce sync.Once
	file_moego_service_offering_v1_evaluation_service_proto_rawDescData = file_moego_service_offering_v1_evaluation_service_proto_rawDesc
)

func file_moego_service_offering_v1_evaluation_service_proto_rawDescGZIP() []byte {
	file_moego_service_offering_v1_evaluation_service_proto_rawDescOnce.Do(func() {
		file_moego_service_offering_v1_evaluation_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_offering_v1_evaluation_service_proto_rawDescData)
	})
	return file_moego_service_offering_v1_evaluation_service_proto_rawDescData
}

var file_moego_service_offering_v1_evaluation_service_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_moego_service_offering_v1_evaluation_service_proto_goTypes = []interface{}{
	(*CreateEvaluationRequest)(nil),                         // 0: moego.service.offering.v1.CreateEvaluationRequest
	(*CreateEvaluationResponse)(nil),                        // 1: moego.service.offering.v1.CreateEvaluationResponse
	(*UpdateEvaluationRequest)(nil),                         // 2: moego.service.offering.v1.UpdateEvaluationRequest
	(*UpdateEvaluationResponse)(nil),                        // 3: moego.service.offering.v1.UpdateEvaluationResponse
	(*GetEvaluationListRequest)(nil),                        // 4: moego.service.offering.v1.GetEvaluationListRequest
	(*GetEvaluationListResponse)(nil),                       // 5: moego.service.offering.v1.GetEvaluationListResponse
	(*GetEvaluationRequest)(nil),                            // 6: moego.service.offering.v1.GetEvaluationRequest
	(*GetEvaluationResponse)(nil),                           // 7: moego.service.offering.v1.GetEvaluationResponse
	(*GetApplicableEvaluationListRequest)(nil),              // 8: moego.service.offering.v1.GetApplicableEvaluationListRequest
	(*GetApplicableEvaluationListResponse)(nil),             // 9: moego.service.offering.v1.GetApplicableEvaluationListResponse
	(*GetBusinessListWithApplicableEvaluationRequest)(nil),  // 10: moego.service.offering.v1.GetBusinessListWithApplicableEvaluationRequest
	(*GetBusinessListWithApplicableEvaluationResponse)(nil), // 11: moego.service.offering.v1.GetBusinessListWithApplicableEvaluationResponse
	(*GetEvaluationListWithEvaluationIdsRequest)(nil),       // 12: moego.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest
	(*GetEvaluationListWithEvaluationIdsResponse)(nil),      // 13: moego.service.offering.v1.GetEvaluationListWithEvaluationIdsResponse
	(*ListEvaluationRequest)(nil),                           // 14: moego.service.offering.v1.ListEvaluationRequest
	(*ListEvaluationResponse)(nil),                          // 15: moego.service.offering.v1.ListEvaluationResponse
	(*DeleteEvaluationRequest)(nil),                         // 16: moego.service.offering.v1.DeleteEvaluationRequest
	(*DeleteEvaluationResponse)(nil),                        // 17: moego.service.offering.v1.DeleteEvaluationResponse
	(*ListEvaluationRequest_Filter)(nil),                    // 18: moego.service.offering.v1.ListEvaluationRequest.Filter
	(*v1.EvaluationDef)(nil),                                // 19: moego.models.offering.v1.EvaluationDef
	(*v1.EvaluationModel)(nil),                              // 20: moego.models.offering.v1.EvaluationModel
	(*v11.Tenant)(nil),                                      // 21: moego.models.organization.v1.Tenant
	(v1.ServiceItemType)(0),                                 // 22: moego.models.offering.v1.ServiceItemType
	(*v1.ServiceFilterByPet)(nil),                           // 23: moego.models.offering.v1.ServiceFilterByPet
	(*v1.EvaluationBriefView)(nil),                          // 24: moego.models.offering.v1.EvaluationBriefView
	(*v2.PaginationRequest)(nil),                            // 25: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                           // 26: moego.utils.v2.PaginationResponse
}
var file_moego_service_offering_v1_evaluation_service_proto_depIdxs = []int32{
	19, // 0: moego.service.offering.v1.CreateEvaluationRequest.evaluation_def:type_name -> moego.models.offering.v1.EvaluationDef
	20, // 1: moego.service.offering.v1.CreateEvaluationResponse.evaluation_model:type_name -> moego.models.offering.v1.EvaluationModel
	19, // 2: moego.service.offering.v1.UpdateEvaluationRequest.evaluation_def:type_name -> moego.models.offering.v1.EvaluationDef
	21, // 3: moego.service.offering.v1.UpdateEvaluationRequest.tenant:type_name -> moego.models.organization.v1.Tenant
	20, // 4: moego.service.offering.v1.UpdateEvaluationResponse.evaluation_model:type_name -> moego.models.offering.v1.EvaluationModel
	20, // 5: moego.service.offering.v1.GetEvaluationListResponse.evaluations:type_name -> moego.models.offering.v1.EvaluationModel
	20, // 6: moego.service.offering.v1.GetEvaluationResponse.evaluation_model:type_name -> moego.models.offering.v1.EvaluationModel
	22, // 7: moego.service.offering.v1.GetApplicableEvaluationListRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	23, // 8: moego.service.offering.v1.GetApplicableEvaluationListRequest.filter_by_pet:type_name -> moego.models.offering.v1.ServiceFilterByPet
	24, // 9: moego.service.offering.v1.GetApplicableEvaluationListResponse.evaluations:type_name -> moego.models.offering.v1.EvaluationBriefView
	24, // 10: moego.service.offering.v1.GetEvaluationListWithEvaluationIdsResponse.evaluations:type_name -> moego.models.offering.v1.EvaluationBriefView
	18, // 11: moego.service.offering.v1.ListEvaluationRequest.filter:type_name -> moego.service.offering.v1.ListEvaluationRequest.Filter
	25, // 12: moego.service.offering.v1.ListEvaluationRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	20, // 13: moego.service.offering.v1.ListEvaluationResponse.evaluations:type_name -> moego.models.offering.v1.EvaluationModel
	26, // 14: moego.service.offering.v1.ListEvaluationResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	0,  // 15: moego.service.offering.v1.EvaluationService.CreateEvaluation:input_type -> moego.service.offering.v1.CreateEvaluationRequest
	2,  // 16: moego.service.offering.v1.EvaluationService.UpdateEvaluation:input_type -> moego.service.offering.v1.UpdateEvaluationRequest
	16, // 17: moego.service.offering.v1.EvaluationService.DeleteEvaluation:input_type -> moego.service.offering.v1.DeleteEvaluationRequest
	6,  // 18: moego.service.offering.v1.EvaluationService.GetEvaluation:input_type -> moego.service.offering.v1.GetEvaluationRequest
	4,  // 19: moego.service.offering.v1.EvaluationService.GetEvaluationList:input_type -> moego.service.offering.v1.GetEvaluationListRequest
	8,  // 20: moego.service.offering.v1.EvaluationService.GetApplicableEvaluationList:input_type -> moego.service.offering.v1.GetApplicableEvaluationListRequest
	10, // 21: moego.service.offering.v1.EvaluationService.GetBusinessListWithApplicableEvaluation:input_type -> moego.service.offering.v1.GetBusinessListWithApplicableEvaluationRequest
	12, // 22: moego.service.offering.v1.EvaluationService.GetEvaluationListWithEvaluationIds:input_type -> moego.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest
	14, // 23: moego.service.offering.v1.EvaluationService.ListEvaluation:input_type -> moego.service.offering.v1.ListEvaluationRequest
	1,  // 24: moego.service.offering.v1.EvaluationService.CreateEvaluation:output_type -> moego.service.offering.v1.CreateEvaluationResponse
	3,  // 25: moego.service.offering.v1.EvaluationService.UpdateEvaluation:output_type -> moego.service.offering.v1.UpdateEvaluationResponse
	17, // 26: moego.service.offering.v1.EvaluationService.DeleteEvaluation:output_type -> moego.service.offering.v1.DeleteEvaluationResponse
	7,  // 27: moego.service.offering.v1.EvaluationService.GetEvaluation:output_type -> moego.service.offering.v1.GetEvaluationResponse
	5,  // 28: moego.service.offering.v1.EvaluationService.GetEvaluationList:output_type -> moego.service.offering.v1.GetEvaluationListResponse
	9,  // 29: moego.service.offering.v1.EvaluationService.GetApplicableEvaluationList:output_type -> moego.service.offering.v1.GetApplicableEvaluationListResponse
	11, // 30: moego.service.offering.v1.EvaluationService.GetBusinessListWithApplicableEvaluation:output_type -> moego.service.offering.v1.GetBusinessListWithApplicableEvaluationResponse
	13, // 31: moego.service.offering.v1.EvaluationService.GetEvaluationListWithEvaluationIds:output_type -> moego.service.offering.v1.GetEvaluationListWithEvaluationIdsResponse
	15, // 32: moego.service.offering.v1.EvaluationService.ListEvaluation:output_type -> moego.service.offering.v1.ListEvaluationResponse
	24, // [24:33] is the sub-list for method output_type
	15, // [15:24] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_moego_service_offering_v1_evaluation_service_proto_init() }
func file_moego_service_offering_v1_evaluation_service_proto_init() {
	if File_moego_service_offering_v1_evaluation_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicableEvaluationListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicableEvaluationListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessListWithApplicableEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusinessListWithApplicableEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationListWithEvaluationIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEvaluationListWithEvaluationIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEvaluationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_offering_v1_evaluation_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_offering_v1_evaluation_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_evaluation_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_offering_v1_evaluation_service_proto_msgTypes[18].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_offering_v1_evaluation_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_offering_v1_evaluation_service_proto_goTypes,
		DependencyIndexes: file_moego_service_offering_v1_evaluation_service_proto_depIdxs,
		MessageInfos:      file_moego_service_offering_v1_evaluation_service_proto_msgTypes,
	}.Build()
	File_moego_service_offering_v1_evaluation_service_proto = out.File
	file_moego_service_offering_v1_evaluation_service_proto_rawDesc = nil
	file_moego_service_offering_v1_evaluation_service_proto_goTypes = nil
	file_moego_service_offering_v1_evaluation_service_proto_depIdxs = nil
}
