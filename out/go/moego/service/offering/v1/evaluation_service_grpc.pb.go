// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/offering/v1/evaluation_service.proto

package offeringsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EvaluationServiceClient is the client API for EvaluationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EvaluationServiceClient interface {
	// create evaluation
	CreateEvaluation(ctx context.Context, in *CreateEvaluationRequest, opts ...grpc.CallOption) (*CreateEvaluationResponse, error)
	// update evaluation
	UpdateEvaluation(ctx context.Context, in *UpdateEvaluationRequest, opts ...grpc.CallOption) (*UpdateEvaluationResponse, error)
	// delete evaluation
	DeleteEvaluation(ctx context.Context, in *DeleteEvaluationRequest, opts ...grpc.CallOption) (*DeleteEvaluationResponse, error)
	// get evaluation
	GetEvaluation(ctx context.Context, in *GetEvaluationRequest, opts ...grpc.CallOption) (*GetEvaluationResponse, error)
	// get evaluation list
	GetEvaluationList(ctx context.Context, in *GetEvaluationListRequest, opts ...grpc.CallOption) (*GetEvaluationListResponse, error)
	// get applicable evaluation list
	GetApplicableEvaluationList(ctx context.Context, in *GetApplicableEvaluationListRequest, opts ...grpc.CallOption) (*GetApplicableEvaluationListResponse, error)
	// get business list with applicable evaluation
	GetBusinessListWithApplicableEvaluation(ctx context.Context, in *GetBusinessListWithApplicableEvaluationRequest, opts ...grpc.CallOption) (*GetBusinessListWithApplicableEvaluationResponse, error)
	// get evaluation list with evaluation ids
	GetEvaluationListWithEvaluationIds(ctx context.Context, in *GetEvaluationListWithEvaluationIdsRequest, opts ...grpc.CallOption) (*GetEvaluationListWithEvaluationIdsResponse, error)
	// list evaluation
	ListEvaluation(ctx context.Context, in *ListEvaluationRequest, opts ...grpc.CallOption) (*ListEvaluationResponse, error)
}

type evaluationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEvaluationServiceClient(cc grpc.ClientConnInterface) EvaluationServiceClient {
	return &evaluationServiceClient{cc}
}

func (c *evaluationServiceClient) CreateEvaluation(ctx context.Context, in *CreateEvaluationRequest, opts ...grpc.CallOption) (*CreateEvaluationResponse, error) {
	out := new(CreateEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.EvaluationService/CreateEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) UpdateEvaluation(ctx context.Context, in *UpdateEvaluationRequest, opts ...grpc.CallOption) (*UpdateEvaluationResponse, error) {
	out := new(UpdateEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.EvaluationService/UpdateEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) DeleteEvaluation(ctx context.Context, in *DeleteEvaluationRequest, opts ...grpc.CallOption) (*DeleteEvaluationResponse, error) {
	out := new(DeleteEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.EvaluationService/DeleteEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) GetEvaluation(ctx context.Context, in *GetEvaluationRequest, opts ...grpc.CallOption) (*GetEvaluationResponse, error) {
	out := new(GetEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.EvaluationService/GetEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) GetEvaluationList(ctx context.Context, in *GetEvaluationListRequest, opts ...grpc.CallOption) (*GetEvaluationListResponse, error) {
	out := new(GetEvaluationListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.EvaluationService/GetEvaluationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) GetApplicableEvaluationList(ctx context.Context, in *GetApplicableEvaluationListRequest, opts ...grpc.CallOption) (*GetApplicableEvaluationListResponse, error) {
	out := new(GetApplicableEvaluationListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.EvaluationService/GetApplicableEvaluationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) GetBusinessListWithApplicableEvaluation(ctx context.Context, in *GetBusinessListWithApplicableEvaluationRequest, opts ...grpc.CallOption) (*GetBusinessListWithApplicableEvaluationResponse, error) {
	out := new(GetBusinessListWithApplicableEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.EvaluationService/GetBusinessListWithApplicableEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) GetEvaluationListWithEvaluationIds(ctx context.Context, in *GetEvaluationListWithEvaluationIdsRequest, opts ...grpc.CallOption) (*GetEvaluationListWithEvaluationIdsResponse, error) {
	out := new(GetEvaluationListWithEvaluationIdsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.EvaluationService/GetEvaluationListWithEvaluationIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evaluationServiceClient) ListEvaluation(ctx context.Context, in *ListEvaluationRequest, opts ...grpc.CallOption) (*ListEvaluationResponse, error) {
	out := new(ListEvaluationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.offering.v1.EvaluationService/ListEvaluation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EvaluationServiceServer is the server API for EvaluationService service.
// All implementations must embed UnimplementedEvaluationServiceServer
// for forward compatibility
type EvaluationServiceServer interface {
	// create evaluation
	CreateEvaluation(context.Context, *CreateEvaluationRequest) (*CreateEvaluationResponse, error)
	// update evaluation
	UpdateEvaluation(context.Context, *UpdateEvaluationRequest) (*UpdateEvaluationResponse, error)
	// delete evaluation
	DeleteEvaluation(context.Context, *DeleteEvaluationRequest) (*DeleteEvaluationResponse, error)
	// get evaluation
	GetEvaluation(context.Context, *GetEvaluationRequest) (*GetEvaluationResponse, error)
	// get evaluation list
	GetEvaluationList(context.Context, *GetEvaluationListRequest) (*GetEvaluationListResponse, error)
	// get applicable evaluation list
	GetApplicableEvaluationList(context.Context, *GetApplicableEvaluationListRequest) (*GetApplicableEvaluationListResponse, error)
	// get business list with applicable evaluation
	GetBusinessListWithApplicableEvaluation(context.Context, *GetBusinessListWithApplicableEvaluationRequest) (*GetBusinessListWithApplicableEvaluationResponse, error)
	// get evaluation list with evaluation ids
	GetEvaluationListWithEvaluationIds(context.Context, *GetEvaluationListWithEvaluationIdsRequest) (*GetEvaluationListWithEvaluationIdsResponse, error)
	// list evaluation
	ListEvaluation(context.Context, *ListEvaluationRequest) (*ListEvaluationResponse, error)
	mustEmbedUnimplementedEvaluationServiceServer()
}

// UnimplementedEvaluationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEvaluationServiceServer struct {
}

func (UnimplementedEvaluationServiceServer) CreateEvaluation(context.Context, *CreateEvaluationRequest) (*CreateEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) UpdateEvaluation(context.Context, *UpdateEvaluationRequest) (*UpdateEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) DeleteEvaluation(context.Context, *DeleteEvaluationRequest) (*DeleteEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) GetEvaluation(context.Context, *GetEvaluationRequest) (*GetEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) GetEvaluationList(context.Context, *GetEvaluationListRequest) (*GetEvaluationListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvaluationList not implemented")
}
func (UnimplementedEvaluationServiceServer) GetApplicableEvaluationList(context.Context, *GetApplicableEvaluationListRequest) (*GetApplicableEvaluationListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicableEvaluationList not implemented")
}
func (UnimplementedEvaluationServiceServer) GetBusinessListWithApplicableEvaluation(context.Context, *GetBusinessListWithApplicableEvaluationRequest) (*GetBusinessListWithApplicableEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessListWithApplicableEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) GetEvaluationListWithEvaluationIds(context.Context, *GetEvaluationListWithEvaluationIdsRequest) (*GetEvaluationListWithEvaluationIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvaluationListWithEvaluationIds not implemented")
}
func (UnimplementedEvaluationServiceServer) ListEvaluation(context.Context, *ListEvaluationRequest) (*ListEvaluationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEvaluation not implemented")
}
func (UnimplementedEvaluationServiceServer) mustEmbedUnimplementedEvaluationServiceServer() {}

// UnsafeEvaluationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EvaluationServiceServer will
// result in compilation errors.
type UnsafeEvaluationServiceServer interface {
	mustEmbedUnimplementedEvaluationServiceServer()
}

func RegisterEvaluationServiceServer(s grpc.ServiceRegistrar, srv EvaluationServiceServer) {
	s.RegisterService(&EvaluationService_ServiceDesc, srv)
}

func _EvaluationService_CreateEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).CreateEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.EvaluationService/CreateEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).CreateEvaluation(ctx, req.(*CreateEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_UpdateEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).UpdateEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.EvaluationService/UpdateEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).UpdateEvaluation(ctx, req.(*UpdateEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_DeleteEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).DeleteEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.EvaluationService/DeleteEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).DeleteEvaluation(ctx, req.(*DeleteEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_GetEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).GetEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.EvaluationService/GetEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).GetEvaluation(ctx, req.(*GetEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_GetEvaluationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvaluationListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).GetEvaluationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.EvaluationService/GetEvaluationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).GetEvaluationList(ctx, req.(*GetEvaluationListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_GetApplicableEvaluationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicableEvaluationListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).GetApplicableEvaluationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.EvaluationService/GetApplicableEvaluationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).GetApplicableEvaluationList(ctx, req.(*GetApplicableEvaluationListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_GetBusinessListWithApplicableEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessListWithApplicableEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).GetBusinessListWithApplicableEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.EvaluationService/GetBusinessListWithApplicableEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).GetBusinessListWithApplicableEvaluation(ctx, req.(*GetBusinessListWithApplicableEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_GetEvaluationListWithEvaluationIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvaluationListWithEvaluationIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).GetEvaluationListWithEvaluationIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.EvaluationService/GetEvaluationListWithEvaluationIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).GetEvaluationListWithEvaluationIds(ctx, req.(*GetEvaluationListWithEvaluationIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvaluationService_ListEvaluation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEvaluationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvaluationServiceServer).ListEvaluation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.offering.v1.EvaluationService/ListEvaluation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvaluationServiceServer).ListEvaluation(ctx, req.(*ListEvaluationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EvaluationService_ServiceDesc is the grpc.ServiceDesc for EvaluationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EvaluationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.offering.v1.EvaluationService",
	HandlerType: (*EvaluationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateEvaluation",
			Handler:    _EvaluationService_CreateEvaluation_Handler,
		},
		{
			MethodName: "UpdateEvaluation",
			Handler:    _EvaluationService_UpdateEvaluation_Handler,
		},
		{
			MethodName: "DeleteEvaluation",
			Handler:    _EvaluationService_DeleteEvaluation_Handler,
		},
		{
			MethodName: "GetEvaluation",
			Handler:    _EvaluationService_GetEvaluation_Handler,
		},
		{
			MethodName: "GetEvaluationList",
			Handler:    _EvaluationService_GetEvaluationList_Handler,
		},
		{
			MethodName: "GetApplicableEvaluationList",
			Handler:    _EvaluationService_GetApplicableEvaluationList_Handler,
		},
		{
			MethodName: "GetBusinessListWithApplicableEvaluation",
			Handler:    _EvaluationService_GetBusinessListWithApplicableEvaluation_Handler,
		},
		{
			MethodName: "GetEvaluationListWithEvaluationIds",
			Handler:    _EvaluationService_GetEvaluationListWithEvaluationIds_Handler,
		},
		{
			MethodName: "ListEvaluation",
			Handler:    _EvaluationService_ListEvaluation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/offering/v1/evaluation_service.proto",
}
