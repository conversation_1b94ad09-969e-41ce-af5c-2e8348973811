// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/online_booking/v1/booking_request_service.proto

package onlinebookingsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BookingRequestServiceClient is the client API for BookingRequestService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BookingRequestServiceClient interface {
	// Create a record, return inserted id.
	CreateBookingRequest(ctx context.Context, in *CreateBookingRequestRequest, opts ...grpc.CallOption) (*wrapperspb.Int64Value, error)
	// Get a record by id, not include deleted record.
	GetBookingRequest(ctx context.Context, in *GetBookingRequestRequest, opts ...grpc.CallOption) (*GetBookingRequestResponse, error)
	// List booking requests<br>
	// Supports filtering, sorting, paging, and returning data by associated model array<br>
	// filters: business_id, customer_id, status, start_date, end_date, created_at<br>
	// sorts: id, start_date, created_at<br>
	// associated model array: services, add-on, feeding, medication
	ListBookingRequests(ctx context.Context, in *ListBookingRequestsRequest, opts ...grpc.CallOption) (*ListBookingRequestsResponse, error)
	// list waitlists
	ListWaitlists(ctx context.Context, in *ListWaitlistsRequest, opts ...grpc.CallOption) (*ListWaitlistsResponse, error)
	// Create a grooming only booking request
	CreateGroomingOnly(ctx context.Context, in *CreateGroomingOnlyRequest, opts ...grpc.CallOption) (*CreateGroomingOnlyResponse, error)
	// Update booking request status
	// submitted -> wait_list / scheduled / declined / payment_failed
	// wait_list -> scheduled / deleted
	UpdateBookingRequestStatus(ctx context.Context, in *UpdateBookingRequestStatusRequest, opts ...grpc.CallOption) (*UpdateBookingRequestStatusResponse, error)
	// Update booking request
	UpdateBookingRequest(ctx context.Context, in *UpdateBookingRequestRequest, opts ...grpc.CallOption) (*UpdateBookingRequestResponse, error)
	// replace booking reqeust
	ReplaceBookingRequest(ctx context.Context, in *ReplaceBookingRequestRequest, opts ...grpc.CallOption) (*ReplaceBookingRequestResponse, error)
	// Retry failed events
	RetryFailedEvents(ctx context.Context, in *RetryFailedEventsRequest, opts ...grpc.CallOption) (*RetryFailedEventsResponse, error)
	// Deprecated: Do not use.
	// Get auto assign room
	// deprecated by Freeman since 2025/3/6, use AutoAssign instead
	GetAutoAssign(ctx context.Context, in *GetAutoAssignRequest, opts ...grpc.CallOption) (*GetAutoAssignResponse, error)
	// Auto assign
	AutoAssign(ctx context.Context, in *AutoAssignRequest, opts ...grpc.CallOption) (*AutoAssignResponse, error)
	// Deprecated: Do not use.
	// Accept booking request
	// deprecated by Freeman since 2025/3/5, 这个接口的参数设计和实现都很难看懂，难以扩展，use AcceptBookingRequestV2 instead
	AcceptBookingRequest(ctx context.Context, in *AcceptBookingRequestRequest, opts ...grpc.CallOption) (*AcceptBookingRequestResponse, error)
	// Accept booking request v2
	// 没放在 v2 包下面的原因是这个接口会大量复用 AcceptBookingRequest 的逻辑，需要在一个类里面
	AcceptBookingRequestV2(ctx context.Context, in *AcceptBookingRequestV2Request, opts ...grpc.CallOption) (*AcceptBookingRequestV2Response, error)
	// Decline booking request
	DeclineBookingRequest(ctx context.Context, in *DeclineBookingRequestRequest, opts ...grpc.CallOption) (*DeclineBookingRequestResponse, error)
	// Count booking requests
	CountBookingRequests(ctx context.Context, in *CountBookingRequestsRequest, opts ...grpc.CallOption) (*CountBookingRequestsResponse, error)
	// List booking request ids by appointment id
	ListBookingRequestId(ctx context.Context, in *ListBookingRequestIdRequest, opts ...grpc.CallOption) (*ListBookingRequestIdResponse, error)
	// Sync BookingRequest from appointment
	// 这个接口用于同步老的 BookingRequest 数据（保存在 moe_grooming_appointment 表）到新的 booking_request 表
	SyncBookingRequestFromAppointment(ctx context.Context, in *SyncBookingRequestFromAppointmentRequest, opts ...grpc.CallOption) (*SyncBookingRequestFromAppointmentResponse, error)
	// online booking 的 auto accept 逻辑，这个接口会处理 accept BookingRequest，capture PaymentIntent，send notification 等逻辑。
	// 这个接口的调用场景有两个：
	// 1. 在不需要 payment 的 submit BookingRequest 场景里，在 client-api-v1 直接调用。
	// 2. 在需要 payment 的 submit BookingRequest 场景里，在 confirm PaymentIntent 的回调里调用。
	TriggerBookingRequestAutoAccepted(ctx context.Context, in *TriggerBookingRequestAutoAcceptedRequest, opts ...grpc.CallOption) (*TriggerBookingRequestAutoAcceptedResponse, error)
	// waitlist available check task
	CheckWaitlistAvailableTask(ctx context.Context, in *CheckWaitlistAvailableTaskRequest, opts ...grpc.CallOption) (*CheckWaitlistAvailableTaskResponse, error)
	// Move booking request to waitlist
	MoveBookingRequestToWaitlist(ctx context.Context, in *MoveBookingRequestToWaitlistRequest, opts ...grpc.CallOption) (*MoveBookingRequestToWaitlistResponse, error)
	// check service id is use
	CountBookingRequestByFilter(ctx context.Context, in *CountBookingRequestByFilterRequest, opts ...grpc.CallOption) (*CountBookingRequestByFilterResponse, error)
	// Preview pricing of booking request
	PreviewBookingRequestPricing(ctx context.Context, in *PreviewBookingRequestPricingRequest, opts ...grpc.CallOption) (*PreviewBookingRequestPricingResponse, error)
}

type bookingRequestServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBookingRequestServiceClient(cc grpc.ClientConnInterface) BookingRequestServiceClient {
	return &bookingRequestServiceClient{cc}
}

func (c *bookingRequestServiceClient) CreateBookingRequest(ctx context.Context, in *CreateBookingRequestRequest, opts ...grpc.CallOption) (*wrapperspb.Int64Value, error) {
	out := new(wrapperspb.Int64Value)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/CreateBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) GetBookingRequest(ctx context.Context, in *GetBookingRequestRequest, opts ...grpc.CallOption) (*GetBookingRequestResponse, error) {
	out := new(GetBookingRequestResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/GetBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) ListBookingRequests(ctx context.Context, in *ListBookingRequestsRequest, opts ...grpc.CallOption) (*ListBookingRequestsResponse, error) {
	out := new(ListBookingRequestsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/ListBookingRequests", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) ListWaitlists(ctx context.Context, in *ListWaitlistsRequest, opts ...grpc.CallOption) (*ListWaitlistsResponse, error) {
	out := new(ListWaitlistsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/ListWaitlists", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) CreateGroomingOnly(ctx context.Context, in *CreateGroomingOnlyRequest, opts ...grpc.CallOption) (*CreateGroomingOnlyResponse, error) {
	out := new(CreateGroomingOnlyResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/CreateGroomingOnly", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) UpdateBookingRequestStatus(ctx context.Context, in *UpdateBookingRequestStatusRequest, opts ...grpc.CallOption) (*UpdateBookingRequestStatusResponse, error) {
	out := new(UpdateBookingRequestStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/UpdateBookingRequestStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) UpdateBookingRequest(ctx context.Context, in *UpdateBookingRequestRequest, opts ...grpc.CallOption) (*UpdateBookingRequestResponse, error) {
	out := new(UpdateBookingRequestResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/UpdateBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) ReplaceBookingRequest(ctx context.Context, in *ReplaceBookingRequestRequest, opts ...grpc.CallOption) (*ReplaceBookingRequestResponse, error) {
	out := new(ReplaceBookingRequestResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/ReplaceBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) RetryFailedEvents(ctx context.Context, in *RetryFailedEventsRequest, opts ...grpc.CallOption) (*RetryFailedEventsResponse, error) {
	out := new(RetryFailedEventsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/RetryFailedEvents", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *bookingRequestServiceClient) GetAutoAssign(ctx context.Context, in *GetAutoAssignRequest, opts ...grpc.CallOption) (*GetAutoAssignResponse, error) {
	out := new(GetAutoAssignResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/GetAutoAssign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) AutoAssign(ctx context.Context, in *AutoAssignRequest, opts ...grpc.CallOption) (*AutoAssignResponse, error) {
	out := new(AutoAssignResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/AutoAssign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *bookingRequestServiceClient) AcceptBookingRequest(ctx context.Context, in *AcceptBookingRequestRequest, opts ...grpc.CallOption) (*AcceptBookingRequestResponse, error) {
	out := new(AcceptBookingRequestResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/AcceptBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) AcceptBookingRequestV2(ctx context.Context, in *AcceptBookingRequestV2Request, opts ...grpc.CallOption) (*AcceptBookingRequestV2Response, error) {
	out := new(AcceptBookingRequestV2Response)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/AcceptBookingRequestV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) DeclineBookingRequest(ctx context.Context, in *DeclineBookingRequestRequest, opts ...grpc.CallOption) (*DeclineBookingRequestResponse, error) {
	out := new(DeclineBookingRequestResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/DeclineBookingRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) CountBookingRequests(ctx context.Context, in *CountBookingRequestsRequest, opts ...grpc.CallOption) (*CountBookingRequestsResponse, error) {
	out := new(CountBookingRequestsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/CountBookingRequests", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) ListBookingRequestId(ctx context.Context, in *ListBookingRequestIdRequest, opts ...grpc.CallOption) (*ListBookingRequestIdResponse, error) {
	out := new(ListBookingRequestIdResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/ListBookingRequestId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) SyncBookingRequestFromAppointment(ctx context.Context, in *SyncBookingRequestFromAppointmentRequest, opts ...grpc.CallOption) (*SyncBookingRequestFromAppointmentResponse, error) {
	out := new(SyncBookingRequestFromAppointmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/SyncBookingRequestFromAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) TriggerBookingRequestAutoAccepted(ctx context.Context, in *TriggerBookingRequestAutoAcceptedRequest, opts ...grpc.CallOption) (*TriggerBookingRequestAutoAcceptedResponse, error) {
	out := new(TriggerBookingRequestAutoAcceptedResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/TriggerBookingRequestAutoAccepted", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) CheckWaitlistAvailableTask(ctx context.Context, in *CheckWaitlistAvailableTaskRequest, opts ...grpc.CallOption) (*CheckWaitlistAvailableTaskResponse, error) {
	out := new(CheckWaitlistAvailableTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/CheckWaitlistAvailableTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) MoveBookingRequestToWaitlist(ctx context.Context, in *MoveBookingRequestToWaitlistRequest, opts ...grpc.CallOption) (*MoveBookingRequestToWaitlistResponse, error) {
	out := new(MoveBookingRequestToWaitlistResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/MoveBookingRequestToWaitlist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) CountBookingRequestByFilter(ctx context.Context, in *CountBookingRequestByFilterRequest, opts ...grpc.CallOption) (*CountBookingRequestByFilterResponse, error) {
	out := new(CountBookingRequestByFilterResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/CountBookingRequestByFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingRequestServiceClient) PreviewBookingRequestPricing(ctx context.Context, in *PreviewBookingRequestPricingRequest, opts ...grpc.CallOption) (*PreviewBookingRequestPricingResponse, error) {
	out := new(PreviewBookingRequestPricingResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.BookingRequestService/PreviewBookingRequestPricing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookingRequestServiceServer is the server API for BookingRequestService service.
// All implementations must embed UnimplementedBookingRequestServiceServer
// for forward compatibility
type BookingRequestServiceServer interface {
	// Create a record, return inserted id.
	CreateBookingRequest(context.Context, *CreateBookingRequestRequest) (*wrapperspb.Int64Value, error)
	// Get a record by id, not include deleted record.
	GetBookingRequest(context.Context, *GetBookingRequestRequest) (*GetBookingRequestResponse, error)
	// List booking requests<br>
	// Supports filtering, sorting, paging, and returning data by associated model array<br>
	// filters: business_id, customer_id, status, start_date, end_date, created_at<br>
	// sorts: id, start_date, created_at<br>
	// associated model array: services, add-on, feeding, medication
	ListBookingRequests(context.Context, *ListBookingRequestsRequest) (*ListBookingRequestsResponse, error)
	// list waitlists
	ListWaitlists(context.Context, *ListWaitlistsRequest) (*ListWaitlistsResponse, error)
	// Create a grooming only booking request
	CreateGroomingOnly(context.Context, *CreateGroomingOnlyRequest) (*CreateGroomingOnlyResponse, error)
	// Update booking request status
	// submitted -> wait_list / scheduled / declined / payment_failed
	// wait_list -> scheduled / deleted
	UpdateBookingRequestStatus(context.Context, *UpdateBookingRequestStatusRequest) (*UpdateBookingRequestStatusResponse, error)
	// Update booking request
	UpdateBookingRequest(context.Context, *UpdateBookingRequestRequest) (*UpdateBookingRequestResponse, error)
	// replace booking reqeust
	ReplaceBookingRequest(context.Context, *ReplaceBookingRequestRequest) (*ReplaceBookingRequestResponse, error)
	// Retry failed events
	RetryFailedEvents(context.Context, *RetryFailedEventsRequest) (*RetryFailedEventsResponse, error)
	// Deprecated: Do not use.
	// Get auto assign room
	// deprecated by Freeman since 2025/3/6, use AutoAssign instead
	GetAutoAssign(context.Context, *GetAutoAssignRequest) (*GetAutoAssignResponse, error)
	// Auto assign
	AutoAssign(context.Context, *AutoAssignRequest) (*AutoAssignResponse, error)
	// Deprecated: Do not use.
	// Accept booking request
	// deprecated by Freeman since 2025/3/5, 这个接口的参数设计和实现都很难看懂，难以扩展，use AcceptBookingRequestV2 instead
	AcceptBookingRequest(context.Context, *AcceptBookingRequestRequest) (*AcceptBookingRequestResponse, error)
	// Accept booking request v2
	// 没放在 v2 包下面的原因是这个接口会大量复用 AcceptBookingRequest 的逻辑，需要在一个类里面
	AcceptBookingRequestV2(context.Context, *AcceptBookingRequestV2Request) (*AcceptBookingRequestV2Response, error)
	// Decline booking request
	DeclineBookingRequest(context.Context, *DeclineBookingRequestRequest) (*DeclineBookingRequestResponse, error)
	// Count booking requests
	CountBookingRequests(context.Context, *CountBookingRequestsRequest) (*CountBookingRequestsResponse, error)
	// List booking request ids by appointment id
	ListBookingRequestId(context.Context, *ListBookingRequestIdRequest) (*ListBookingRequestIdResponse, error)
	// Sync BookingRequest from appointment
	// 这个接口用于同步老的 BookingRequest 数据（保存在 moe_grooming_appointment 表）到新的 booking_request 表
	SyncBookingRequestFromAppointment(context.Context, *SyncBookingRequestFromAppointmentRequest) (*SyncBookingRequestFromAppointmentResponse, error)
	// online booking 的 auto accept 逻辑，这个接口会处理 accept BookingRequest，capture PaymentIntent，send notification 等逻辑。
	// 这个接口的调用场景有两个：
	// 1. 在不需要 payment 的 submit BookingRequest 场景里，在 client-api-v1 直接调用。
	// 2. 在需要 payment 的 submit BookingRequest 场景里，在 confirm PaymentIntent 的回调里调用。
	TriggerBookingRequestAutoAccepted(context.Context, *TriggerBookingRequestAutoAcceptedRequest) (*TriggerBookingRequestAutoAcceptedResponse, error)
	// waitlist available check task
	CheckWaitlistAvailableTask(context.Context, *CheckWaitlistAvailableTaskRequest) (*CheckWaitlistAvailableTaskResponse, error)
	// Move booking request to waitlist
	MoveBookingRequestToWaitlist(context.Context, *MoveBookingRequestToWaitlistRequest) (*MoveBookingRequestToWaitlistResponse, error)
	// check service id is use
	CountBookingRequestByFilter(context.Context, *CountBookingRequestByFilterRequest) (*CountBookingRequestByFilterResponse, error)
	// Preview pricing of booking request
	PreviewBookingRequestPricing(context.Context, *PreviewBookingRequestPricingRequest) (*PreviewBookingRequestPricingResponse, error)
	mustEmbedUnimplementedBookingRequestServiceServer()
}

// UnimplementedBookingRequestServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBookingRequestServiceServer struct {
}

func (UnimplementedBookingRequestServiceServer) CreateBookingRequest(context.Context, *CreateBookingRequestRequest) (*wrapperspb.Int64Value, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) GetBookingRequest(context.Context, *GetBookingRequestRequest) (*GetBookingRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) ListBookingRequests(context.Context, *ListBookingRequestsRequest) (*ListBookingRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBookingRequests not implemented")
}
func (UnimplementedBookingRequestServiceServer) ListWaitlists(context.Context, *ListWaitlistsRequest) (*ListWaitlistsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWaitlists not implemented")
}
func (UnimplementedBookingRequestServiceServer) CreateGroomingOnly(context.Context, *CreateGroomingOnlyRequest) (*CreateGroomingOnlyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGroomingOnly not implemented")
}
func (UnimplementedBookingRequestServiceServer) UpdateBookingRequestStatus(context.Context, *UpdateBookingRequestStatusRequest) (*UpdateBookingRequestStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBookingRequestStatus not implemented")
}
func (UnimplementedBookingRequestServiceServer) UpdateBookingRequest(context.Context, *UpdateBookingRequestRequest) (*UpdateBookingRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) ReplaceBookingRequest(context.Context, *ReplaceBookingRequestRequest) (*ReplaceBookingRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReplaceBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) RetryFailedEvents(context.Context, *RetryFailedEventsRequest) (*RetryFailedEventsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetryFailedEvents not implemented")
}
func (UnimplementedBookingRequestServiceServer) GetAutoAssign(context.Context, *GetAutoAssignRequest) (*GetAutoAssignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAutoAssign not implemented")
}
func (UnimplementedBookingRequestServiceServer) AutoAssign(context.Context, *AutoAssignRequest) (*AutoAssignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AutoAssign not implemented")
}
func (UnimplementedBookingRequestServiceServer) AcceptBookingRequest(context.Context, *AcceptBookingRequestRequest) (*AcceptBookingRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcceptBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) AcceptBookingRequestV2(context.Context, *AcceptBookingRequestV2Request) (*AcceptBookingRequestV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AcceptBookingRequestV2 not implemented")
}
func (UnimplementedBookingRequestServiceServer) DeclineBookingRequest(context.Context, *DeclineBookingRequestRequest) (*DeclineBookingRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeclineBookingRequest not implemented")
}
func (UnimplementedBookingRequestServiceServer) CountBookingRequests(context.Context, *CountBookingRequestsRequest) (*CountBookingRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountBookingRequests not implemented")
}
func (UnimplementedBookingRequestServiceServer) ListBookingRequestId(context.Context, *ListBookingRequestIdRequest) (*ListBookingRequestIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBookingRequestId not implemented")
}
func (UnimplementedBookingRequestServiceServer) SyncBookingRequestFromAppointment(context.Context, *SyncBookingRequestFromAppointmentRequest) (*SyncBookingRequestFromAppointmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncBookingRequestFromAppointment not implemented")
}
func (UnimplementedBookingRequestServiceServer) TriggerBookingRequestAutoAccepted(context.Context, *TriggerBookingRequestAutoAcceptedRequest) (*TriggerBookingRequestAutoAcceptedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerBookingRequestAutoAccepted not implemented")
}
func (UnimplementedBookingRequestServiceServer) CheckWaitlistAvailableTask(context.Context, *CheckWaitlistAvailableTaskRequest) (*CheckWaitlistAvailableTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckWaitlistAvailableTask not implemented")
}
func (UnimplementedBookingRequestServiceServer) MoveBookingRequestToWaitlist(context.Context, *MoveBookingRequestToWaitlistRequest) (*MoveBookingRequestToWaitlistResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MoveBookingRequestToWaitlist not implemented")
}
func (UnimplementedBookingRequestServiceServer) CountBookingRequestByFilter(context.Context, *CountBookingRequestByFilterRequest) (*CountBookingRequestByFilterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountBookingRequestByFilter not implemented")
}
func (UnimplementedBookingRequestServiceServer) PreviewBookingRequestPricing(context.Context, *PreviewBookingRequestPricingRequest) (*PreviewBookingRequestPricingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewBookingRequestPricing not implemented")
}
func (UnimplementedBookingRequestServiceServer) mustEmbedUnimplementedBookingRequestServiceServer() {}

// UnsafeBookingRequestServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookingRequestServiceServer will
// result in compilation errors.
type UnsafeBookingRequestServiceServer interface {
	mustEmbedUnimplementedBookingRequestServiceServer()
}

func RegisterBookingRequestServiceServer(s grpc.ServiceRegistrar, srv BookingRequestServiceServer) {
	s.RegisterService(&BookingRequestService_ServiceDesc, srv)
}

func _BookingRequestService_CreateBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBookingRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).CreateBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/CreateBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).CreateBookingRequest(ctx, req.(*CreateBookingRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_GetBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookingRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).GetBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/GetBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).GetBookingRequest(ctx, req.(*GetBookingRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_ListBookingRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBookingRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).ListBookingRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/ListBookingRequests",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).ListBookingRequests(ctx, req.(*ListBookingRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_ListWaitlists_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWaitlistsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).ListWaitlists(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/ListWaitlists",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).ListWaitlists(ctx, req.(*ListWaitlistsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_CreateGroomingOnly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGroomingOnlyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).CreateGroomingOnly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/CreateGroomingOnly",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).CreateGroomingOnly(ctx, req.(*CreateGroomingOnlyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_UpdateBookingRequestStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBookingRequestStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).UpdateBookingRequestStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/UpdateBookingRequestStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).UpdateBookingRequestStatus(ctx, req.(*UpdateBookingRequestStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_UpdateBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBookingRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).UpdateBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/UpdateBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).UpdateBookingRequest(ctx, req.(*UpdateBookingRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_ReplaceBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReplaceBookingRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).ReplaceBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/ReplaceBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).ReplaceBookingRequest(ctx, req.(*ReplaceBookingRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_RetryFailedEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetryFailedEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).RetryFailedEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/RetryFailedEvents",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).RetryFailedEvents(ctx, req.(*RetryFailedEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_GetAutoAssign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAutoAssignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).GetAutoAssign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/GetAutoAssign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).GetAutoAssign(ctx, req.(*GetAutoAssignRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_AutoAssign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AutoAssignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).AutoAssign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/AutoAssign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).AutoAssign(ctx, req.(*AutoAssignRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_AcceptBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptBookingRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).AcceptBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/AcceptBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).AcceptBookingRequest(ctx, req.(*AcceptBookingRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_AcceptBookingRequestV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptBookingRequestV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).AcceptBookingRequestV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/AcceptBookingRequestV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).AcceptBookingRequestV2(ctx, req.(*AcceptBookingRequestV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_DeclineBookingRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeclineBookingRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).DeclineBookingRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/DeclineBookingRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).DeclineBookingRequest(ctx, req.(*DeclineBookingRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_CountBookingRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountBookingRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).CountBookingRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/CountBookingRequests",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).CountBookingRequests(ctx, req.(*CountBookingRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_ListBookingRequestId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBookingRequestIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).ListBookingRequestId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/ListBookingRequestId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).ListBookingRequestId(ctx, req.(*ListBookingRequestIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_SyncBookingRequestFromAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncBookingRequestFromAppointmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).SyncBookingRequestFromAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/SyncBookingRequestFromAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).SyncBookingRequestFromAppointment(ctx, req.(*SyncBookingRequestFromAppointmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_TriggerBookingRequestAutoAccepted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerBookingRequestAutoAcceptedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).TriggerBookingRequestAutoAccepted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/TriggerBookingRequestAutoAccepted",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).TriggerBookingRequestAutoAccepted(ctx, req.(*TriggerBookingRequestAutoAcceptedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_CheckWaitlistAvailableTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckWaitlistAvailableTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).CheckWaitlistAvailableTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/CheckWaitlistAvailableTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).CheckWaitlistAvailableTask(ctx, req.(*CheckWaitlistAvailableTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_MoveBookingRequestToWaitlist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoveBookingRequestToWaitlistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).MoveBookingRequestToWaitlist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/MoveBookingRequestToWaitlist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).MoveBookingRequestToWaitlist(ctx, req.(*MoveBookingRequestToWaitlistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_CountBookingRequestByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountBookingRequestByFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).CountBookingRequestByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/CountBookingRequestByFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).CountBookingRequestByFilter(ctx, req.(*CountBookingRequestByFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingRequestService_PreviewBookingRequestPricing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewBookingRequestPricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingRequestServiceServer).PreviewBookingRequestPricing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.BookingRequestService/PreviewBookingRequestPricing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingRequestServiceServer).PreviewBookingRequestPricing(ctx, req.(*PreviewBookingRequestPricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BookingRequestService_ServiceDesc is the grpc.ServiceDesc for BookingRequestService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BookingRequestService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.online_booking.v1.BookingRequestService",
	HandlerType: (*BookingRequestServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBookingRequest",
			Handler:    _BookingRequestService_CreateBookingRequest_Handler,
		},
		{
			MethodName: "GetBookingRequest",
			Handler:    _BookingRequestService_GetBookingRequest_Handler,
		},
		{
			MethodName: "ListBookingRequests",
			Handler:    _BookingRequestService_ListBookingRequests_Handler,
		},
		{
			MethodName: "ListWaitlists",
			Handler:    _BookingRequestService_ListWaitlists_Handler,
		},
		{
			MethodName: "CreateGroomingOnly",
			Handler:    _BookingRequestService_CreateGroomingOnly_Handler,
		},
		{
			MethodName: "UpdateBookingRequestStatus",
			Handler:    _BookingRequestService_UpdateBookingRequestStatus_Handler,
		},
		{
			MethodName: "UpdateBookingRequest",
			Handler:    _BookingRequestService_UpdateBookingRequest_Handler,
		},
		{
			MethodName: "ReplaceBookingRequest",
			Handler:    _BookingRequestService_ReplaceBookingRequest_Handler,
		},
		{
			MethodName: "RetryFailedEvents",
			Handler:    _BookingRequestService_RetryFailedEvents_Handler,
		},
		{
			MethodName: "GetAutoAssign",
			Handler:    _BookingRequestService_GetAutoAssign_Handler,
		},
		{
			MethodName: "AutoAssign",
			Handler:    _BookingRequestService_AutoAssign_Handler,
		},
		{
			MethodName: "AcceptBookingRequest",
			Handler:    _BookingRequestService_AcceptBookingRequest_Handler,
		},
		{
			MethodName: "AcceptBookingRequestV2",
			Handler:    _BookingRequestService_AcceptBookingRequestV2_Handler,
		},
		{
			MethodName: "DeclineBookingRequest",
			Handler:    _BookingRequestService_DeclineBookingRequest_Handler,
		},
		{
			MethodName: "CountBookingRequests",
			Handler:    _BookingRequestService_CountBookingRequests_Handler,
		},
		{
			MethodName: "ListBookingRequestId",
			Handler:    _BookingRequestService_ListBookingRequestId_Handler,
		},
		{
			MethodName: "SyncBookingRequestFromAppointment",
			Handler:    _BookingRequestService_SyncBookingRequestFromAppointment_Handler,
		},
		{
			MethodName: "TriggerBookingRequestAutoAccepted",
			Handler:    _BookingRequestService_TriggerBookingRequestAutoAccepted_Handler,
		},
		{
			MethodName: "CheckWaitlistAvailableTask",
			Handler:    _BookingRequestService_CheckWaitlistAvailableTask_Handler,
		},
		{
			MethodName: "MoveBookingRequestToWaitlist",
			Handler:    _BookingRequestService_MoveBookingRequestToWaitlist_Handler,
		},
		{
			MethodName: "CountBookingRequestByFilter",
			Handler:    _BookingRequestService_CountBookingRequestByFilter_Handler,
		},
		{
			MethodName: "PreviewBookingRequestPricing",
			Handler:    _BookingRequestService_PreviewBookingRequestPricing_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/online_booking/v1/booking_request_service.proto",
}
