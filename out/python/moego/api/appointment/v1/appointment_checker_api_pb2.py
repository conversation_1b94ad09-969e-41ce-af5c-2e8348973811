# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/appointment/v1/appointment_checker_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/appointment/v1/appointment_checker_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n6moego/api/appointment/v1/appointment_checker_api.proto\x12\x18moego.api.appointment.v1\x1a\x16google/type/date.proto\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x17validate/validate.proto\"\xf2\x02\n\x1a\x43heckSaveAppointmentParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12:\n\nstart_date\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\tstartDate\x12\x36\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07\x65ndDate\x12(\n\x10lodging_unit_ids\x18\x04 \x03(\x03R\x0elodgingUnitIds\x12\x17\n\x07pet_ids\x18\x05 \x03(\x03R\x06petIds\x12$\n\x0b\x63ustomer_id\x18\x06 \x01(\x03H\x00R\ncustomerId\x88\x01\x01\x12*\n\x0e\x61ppointment_id\x18\x07 \x01(\x03H\x01R\rappointmentId\x88\x01\x01\x42\x0e\n\x0c_customer_idB\x11\n\x0f_appointment_id\"\xba\x03\n\x1a\x43heckSaveAppointmentResult\x12\x84\x01\n\"lodging_over_capacity_check_result\x18\x01 \x01(\x0b\x32\x38.moego.api.appointment.v1.LodgingOverCapacityCheckResultR\x1elodgingOverCapacityCheckResult\x12\x90\x01\n&appointment_date_conflict_check_result\x18\x02 \x01(\x0b\x32<.moego.api.appointment.v1.AppointmentDateConflictCheckResultR\"appointmentDateConflictCheckResult\x12\x81\x01\n!business_closed_date_check_result\x18\x03 \x01(\x0b\x32\x37.moego.api.appointment.v1.BusinessClosedDateCheckResultR\x1d\x62usinessClosedDateCheckResult\"\xc8\x01\n\x1eLodgingOverCapacityCheckResult\x12R\n\rlodging_units\x18\x01 \x03(\x0b\x32-.moego.api.appointment.v1.LodgingUnitOverviewR\x0clodgingUnits\x12R\n\rlodging_types\x18\x02 \x03(\x0b\x32-.moego.api.appointment.v1.LodgingTypeOverviewR\x0clodgingTypes\"a\n\x13LodgingUnitOverview\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12&\n\x0flodging_type_id\x18\x03 \x01(\x03R\rlodgingTypeId\"9\n\x13LodgingTypeOverview\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\"\x8c\x01\n\"AppointmentDateConflictCheckResult\x12\x66\n\x15\x63onflict_appointments\x18\x01 \x03(\x0b\x32\x31.moego.api.appointment.v1.PetAppointmentsOverviewR\x14\x63onflictAppointments\"\xa5\x01\n\x17PetAppointmentsOverview\x12\x37\n\x03pet\x18\x01 \x01(\x0b\x32%.moego.api.appointment.v1.PetOverviewR\x03pet\x12Q\n\x0c\x61ppointments\x18\x02 \x03(\x0b\x32-.moego.api.appointment.v1.AppointmentOverviewR\x0c\x61ppointments\"@\n\x1d\x42usinessClosedDateCheckResult\x12\x1f\n\x0b\x63losed_date\x18\x01 \x03(\tR\nclosedDate\"\xe2\x01\n\x0bPetOverview\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n\x08pet_name\x18\x02 \x01(\tR\x07petName\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPath\x12\x16\n\x06weight\x18\x04 \x01(\tR\x06weight\x12\x1b\n\tcoat_type\x18\x05 \x01(\tR\x08\x63oatType\x12\x14\n\x05\x62reed\x18\x06 \x01(\tR\x05\x62reed\x12<\n\x08pet_type\x18\x07 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\"\xcb\x02\n\x13\x41ppointmentOverview\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12\x30\n\nstart_date\x18\x02 \x01(\x0b\x32\x11.google.type.DateR\tstartDate\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateR\x07\x65ndDate\x12\x45\n\x08services\x18\x04 \x03(\x0b\x32).moego.api.appointment.v1.ServiceOverviewR\x08services\x12\x34\n\x16\x61ppointment_start_time\x18\x05 \x01(\x05R\x14\x61ppointmentStartTime\x12\x30\n\x14\x61ppointment_end_time\x18\x06 \x01(\x05R\x12\x61ppointmentEndTime\"\x8a\x02\n\x0fServiceOverview\x12\x1d\n\nservice_id\x18\x01 \x01(\x03R\tserviceId\x12!\n\x0cservice_name\x18\x02 \x01(\tR\x0bserviceName\x12\x30\n\nstart_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateR\tstartDate\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateR\x07\x65ndDate\x12U\n\x11service_item_type\x18\x05 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType2\xa0\x01\n\x19\x41ppointmentCheckerService\x12\x82\x01\n\x14\x43heckSaveAppointment\x12\x34.moego.api.appointment.v1.CheckSaveAppointmentParams\x1a\x34.moego.api.appointment.v1.CheckSaveAppointmentResultB\x84\x01\n com.moego.idl.api.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.appointment.v1.appointment_checker_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.api.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb'
  _globals['_CHECKSAVEAPPOINTMENTPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_CHECKSAVEAPPOINTMENTPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CHECKSAVEAPPOINTMENTPARAMS'].fields_by_name['start_date']._loaded_options = None
  _globals['_CHECKSAVEAPPOINTMENTPARAMS'].fields_by_name['start_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CHECKSAVEAPPOINTMENTPARAMS'].fields_by_name['end_date']._loaded_options = None
  _globals['_CHECKSAVEAPPOINTMENTPARAMS'].fields_by_name['end_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CHECKSAVEAPPOINTMENTPARAMS']._serialized_start=230
  _globals['_CHECKSAVEAPPOINTMENTPARAMS']._serialized_end=600
  _globals['_CHECKSAVEAPPOINTMENTRESULT']._serialized_start=603
  _globals['_CHECKSAVEAPPOINTMENTRESULT']._serialized_end=1045
  _globals['_LODGINGOVERCAPACITYCHECKRESULT']._serialized_start=1048
  _globals['_LODGINGOVERCAPACITYCHECKRESULT']._serialized_end=1248
  _globals['_LODGINGUNITOVERVIEW']._serialized_start=1250
  _globals['_LODGINGUNITOVERVIEW']._serialized_end=1347
  _globals['_LODGINGTYPEOVERVIEW']._serialized_start=1349
  _globals['_LODGINGTYPEOVERVIEW']._serialized_end=1406
  _globals['_APPOINTMENTDATECONFLICTCHECKRESULT']._serialized_start=1409
  _globals['_APPOINTMENTDATECONFLICTCHECKRESULT']._serialized_end=1549
  _globals['_PETAPPOINTMENTSOVERVIEW']._serialized_start=1552
  _globals['_PETAPPOINTMENTSOVERVIEW']._serialized_end=1717
  _globals['_BUSINESSCLOSEDDATECHECKRESULT']._serialized_start=1719
  _globals['_BUSINESSCLOSEDDATECHECKRESULT']._serialized_end=1783
  _globals['_PETOVERVIEW']._serialized_start=1786
  _globals['_PETOVERVIEW']._serialized_end=2012
  _globals['_APPOINTMENTOVERVIEW']._serialized_start=2015
  _globals['_APPOINTMENTOVERVIEW']._serialized_end=2346
  _globals['_SERVICEOVERVIEW']._serialized_start=2349
  _globals['_SERVICEOVERVIEW']._serialized_end=2615
  _globals['_APPOINTMENTCHECKERSERVICE']._serialized_start=2618
  _globals['_APPOINTMENTCHECKERSERVICE']._serialized_end=2778
# @@protoc_insertion_point(module_scope)
