from google.type import date_pb2 as _date_pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as _customer_pet_enums_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CheckSaveAppointmentParams(_message.Message):
    __slots__ = ("business_id", "start_date", "end_date", "lodging_unit_ids", "pet_ids", "customer_id", "appointment_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    LODGING_UNIT_IDS_FIELD_NUMBER: _ClassVar[int]
    PET_IDS_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    lodging_unit_ids: _containers.RepeatedScalarFieldContainer[int]
    pet_ids: _containers.RepeatedScalarFieldContainer[int]
    customer_id: int
    appointment_id: int
    def __init__(self, business_id: _Optional[int] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., lodging_unit_ids: _Optional[_Iterable[int]] = ..., pet_ids: _Optional[_Iterable[int]] = ..., customer_id: _Optional[int] = ..., appointment_id: _Optional[int] = ...) -> None: ...

class CheckSaveAppointmentResult(_message.Message):
    __slots__ = ("lodging_over_capacity_check_result", "appointment_date_conflict_check_result", "business_closed_date_check_result")
    LODGING_OVER_CAPACITY_CHECK_RESULT_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_DATE_CONFLICT_CHECK_RESULT_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_CLOSED_DATE_CHECK_RESULT_FIELD_NUMBER: _ClassVar[int]
    lodging_over_capacity_check_result: LodgingOverCapacityCheckResult
    appointment_date_conflict_check_result: AppointmentDateConflictCheckResult
    business_closed_date_check_result: BusinessClosedDateCheckResult
    def __init__(self, lodging_over_capacity_check_result: _Optional[_Union[LodgingOverCapacityCheckResult, _Mapping]] = ..., appointment_date_conflict_check_result: _Optional[_Union[AppointmentDateConflictCheckResult, _Mapping]] = ..., business_closed_date_check_result: _Optional[_Union[BusinessClosedDateCheckResult, _Mapping]] = ...) -> None: ...

class LodgingOverCapacityCheckResult(_message.Message):
    __slots__ = ("lodging_units", "lodging_types")
    LODGING_UNITS_FIELD_NUMBER: _ClassVar[int]
    LODGING_TYPES_FIELD_NUMBER: _ClassVar[int]
    lodging_units: _containers.RepeatedCompositeFieldContainer[LodgingUnitOverview]
    lodging_types: _containers.RepeatedCompositeFieldContainer[LodgingTypeOverview]
    def __init__(self, lodging_units: _Optional[_Iterable[_Union[LodgingUnitOverview, _Mapping]]] = ..., lodging_types: _Optional[_Iterable[_Union[LodgingTypeOverview, _Mapping]]] = ...) -> None: ...

class LodgingUnitOverview(_message.Message):
    __slots__ = ("id", "name", "lodging_type_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    LODGING_TYPE_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    lodging_type_id: int
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., lodging_type_id: _Optional[int] = ...) -> None: ...

class LodgingTypeOverview(_message.Message):
    __slots__ = ("id", "name")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ...) -> None: ...

class AppointmentDateConflictCheckResult(_message.Message):
    __slots__ = ("conflict_appointments",)
    CONFLICT_APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    conflict_appointments: _containers.RepeatedCompositeFieldContainer[PetAppointmentsOverview]
    def __init__(self, conflict_appointments: _Optional[_Iterable[_Union[PetAppointmentsOverview, _Mapping]]] = ...) -> None: ...

class PetAppointmentsOverview(_message.Message):
    __slots__ = ("pet", "appointments")
    PET_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    pet: PetOverview
    appointments: _containers.RepeatedCompositeFieldContainer[AppointmentOverview]
    def __init__(self, pet: _Optional[_Union[PetOverview, _Mapping]] = ..., appointments: _Optional[_Iterable[_Union[AppointmentOverview, _Mapping]]] = ...) -> None: ...

class BusinessClosedDateCheckResult(_message.Message):
    __slots__ = ("closed_date",)
    CLOSED_DATE_FIELD_NUMBER: _ClassVar[int]
    closed_date: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, closed_date: _Optional[_Iterable[str]] = ...) -> None: ...

class PetOverview(_message.Message):
    __slots__ = ("id", "pet_name", "avatar_path", "weight", "coat_type", "breed", "pet_type")
    ID_FIELD_NUMBER: _ClassVar[int]
    PET_NAME_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    WEIGHT_FIELD_NUMBER: _ClassVar[int]
    COAT_TYPE_FIELD_NUMBER: _ClassVar[int]
    BREED_FIELD_NUMBER: _ClassVar[int]
    PET_TYPE_FIELD_NUMBER: _ClassVar[int]
    id: int
    pet_name: str
    avatar_path: str
    weight: str
    coat_type: str
    breed: str
    pet_type: _customer_pet_enums_pb2.PetType
    def __init__(self, id: _Optional[int] = ..., pet_name: _Optional[str] = ..., avatar_path: _Optional[str] = ..., weight: _Optional[str] = ..., coat_type: _Optional[str] = ..., breed: _Optional[str] = ..., pet_type: _Optional[_Union[_customer_pet_enums_pb2.PetType, str]] = ...) -> None: ...

class AppointmentOverview(_message.Message):
    __slots__ = ("appointment_id", "start_date", "end_date", "services", "appointment_start_time", "appointment_end_time")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_START_TIME_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_END_TIME_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    services: _containers.RepeatedCompositeFieldContainer[ServiceOverview]
    appointment_start_time: int
    appointment_end_time: int
    def __init__(self, appointment_id: _Optional[int] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., services: _Optional[_Iterable[_Union[ServiceOverview, _Mapping]]] = ..., appointment_start_time: _Optional[int] = ..., appointment_end_time: _Optional[int] = ...) -> None: ...

class ServiceOverview(_message.Message):
    __slots__ = ("service_id", "service_name", "start_date", "end_date", "service_item_type")
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    service_id: int
    service_name: str
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    service_item_type: _service_enum_pb2.ServiceItemType
    def __init__(self, service_id: _Optional[int] = ..., service_name: _Optional[str] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ...) -> None: ...
