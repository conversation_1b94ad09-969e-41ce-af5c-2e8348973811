# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/appointment/v1/daily_report_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/appointment/v1/daily_report_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.models.appointment.v1 import daily_report_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_daily__report__defs__pb2
from moego.models.appointment.v1 import daily_report_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_daily__report__enums__pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/moego/api/appointment/v1/daily_report_api.proto\x12\x18moego.api.appointment.v1\x1a\x16google/type/date.proto\x1a\x33moego/models/appointment/v1/daily_report_defs.proto\x1a\x34moego/models/appointment/v1/daily_report_enums.proto\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a\x17validate/validate.proto\"\xac\x01\n\x1aGetDailyReportConfigParams\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12>\n\x0cservice_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0bserviceDate\"\xb3\x01\n\x1aGetDailyReportConfigResult\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12>\n\x06report\x18\x02 \x01(\x0b\x32&.moego.models.appointment.v1.ReportDefR\x06report\x12\x45\n\x06status\x18\x03 \x01(\x0e\x32-.moego.models.appointment.v1.ReportCardStatusR\x06status\"\x90\x01\n\x1eGetDailyReportSentResultParams\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12>\n\x0cservice_date\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0bserviceDate\"o\n\x1eGetDailyReportSentResultResult\x12M\n\x0csent_results\x18\x01 \x03(\x0b\x32*.moego.models.appointment.v1.SentResultDefR\x0bsentResults\"\xa3\x02\n\x1dUpsertDailyReportConfigParams\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12(\n\x0b\x63ustomer_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\ncustomerId\x12>\n\x0cservice_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0bserviceDate\x12H\n\x06report\x18\x05 \x01(\x0b\x32&.moego.models.appointment.v1.ReportDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x06report\"C\n\x1dUpsertDailyReportConfigResult\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04uuid\x18\x02 \x01(\tR\x04uuid\"q\n\x1fGetDailyReportSentHistoryParams\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\"\x86\x01\n\x1fGetDailyReportSentHistoryResult\x12\x63\n\x14sent_history_records\x18\x01 \x03(\x0b\x32\x31.moego.models.appointment.v1.SentHistoryRecordDefR\x12sentHistoryRecords\">\n\x1fGetDailyReportForCustomerParams\x12\x1b\n\x04uuid\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\x04uuid\"\xd4\x04\n\x1fGetDailyReportForCustomerResult\x12>\n\x06report\x18\x01 \x01(\x0b\x32&.moego.models.appointment.v1.ReportDefR\x06report\x12\x34\n\x0cservice_date\x18\x02 \x01(\x0b\x32\x11.google.type.DateR\x0bserviceDate\x12O\n\x03pet\x18\x03 \x01(\x0b\x32=.moego.api.appointment.v1.GetDailyReportForCustomerResult.PetR\x03pet\x12^\n\x08\x62usiness\x18\x04 \x01(\x0b\x32\x42.moego.api.appointment.v1.GetDailyReportForCustomerResult.BusinessR\x08\x62usiness\x1a\x96\x01\n\x03Pet\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x19\n\x08pet_name\x18\x02 \x01(\tR\x07petName\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPath\x12<\n\x08pet_type\x18\x04 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\x1aq\n\x08\x42usiness\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12#\n\rbusiness_name\x18\x02 \x01(\tR\x0c\x62usinessName\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPath\"\xae\x01\n\x1cGenerateMessageContentParams\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12>\n\x0cservice_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0bserviceDate\"8\n\x1cGenerateMessageContentResult\x12\x18\n\x07message\x18\x01 \x01(\tR\x07message\",\n\x11SendMessageParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"+\n\x11SendMessageResult\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\"\xc4\x01\n\x1bListDailyReportConfigParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12{\n\x18list_daily_report_config\x18\x02 \x01(\x0b\x32\x38.moego.models.appointment.v1.ListDailyReportConfigFilterB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x15listDailyReportConfig\"\xdf\x02\n\x1bListDailyReportConfigResult\x12X\n\x0ereport_configs\x18\x01 \x03(\x0b\x32\x31.moego.models.appointment.v1.DailyReportConfigDefR\rreportConfigs\x12M\n\x04pets\x18\x03 \x03(\x0b\x32\x39.moego.api.appointment.v1.ListDailyReportConfigResult.PetR\x04pets\x1a\x96\x01\n\x03Pet\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x19\n\x08pet_name\x18\x02 \x01(\tR\x07petName\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPath\x12<\n\x08pet_type\x18\x04 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\"\xdb\x01\n\x1f\x42\x61tchSendDailyDraftReportParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x38\n\x10\x64\x61ily_report_ids\x18\x02 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\x0e\x64\x61ilyReportIds\x12T\n\x0bsend_method\x18\x03 \x01(\x0e\x32\'.moego.models.appointment.v1.SendMethodB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\nsendMethod\"!\n\x1f\x42\x61tchSendDailyDraftReportResult\"\x88\x01\n\"BatchDeleteDailyReportConfigParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x38\n\x10\x64\x61ily_report_ids\x18\x02 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\x0e\x64\x61ilyReportIds\"$\n\"BatchDeleteDailyReportConfigResult2\x8d\x0b\n\x12\x44\x61ilyReportService\x12\x82\x01\n\x14GetDailyReportConfig\x12\x34.moego.api.appointment.v1.GetDailyReportConfigParams\x1a\x34.moego.api.appointment.v1.GetDailyReportConfigResult\x12\x8e\x01\n\x18GetDailyReportSentResult\x12\x38.moego.api.appointment.v1.GetDailyReportSentResultParams\x1a\x38.moego.api.appointment.v1.GetDailyReportSentResultResult\x12\x8b\x01\n\x17UpsertDailyReportConfig\x12\x37.moego.api.appointment.v1.UpsertDailyReportConfigParams\x1a\x37.moego.api.appointment.v1.UpsertDailyReportConfigResult\x12\x91\x01\n\x19GetDailyReportSentHistory\x12\x39.moego.api.appointment.v1.GetDailyReportSentHistoryParams\x1a\x39.moego.api.appointment.v1.GetDailyReportSentHistoryResult\x12\x91\x01\n\x19GetDailyReportForCustomer\x12\x39.moego.api.appointment.v1.GetDailyReportForCustomerParams\x1a\x39.moego.api.appointment.v1.GetDailyReportForCustomerResult\x12\x88\x01\n\x16GenerateMessageContent\x12\x36.moego.api.appointment.v1.GenerateMessageContentParams\x1a\x36.moego.api.appointment.v1.GenerateMessageContentResult\x12g\n\x0bSendMessage\x12+.moego.api.appointment.v1.SendMessageParams\x1a+.moego.api.appointment.v1.SendMessageResult\x12\x85\x01\n\x15ListDailyReportConfig\x12\x35.moego.api.appointment.v1.ListDailyReportConfigParams\x1a\x35.moego.api.appointment.v1.ListDailyReportConfigResult\x12\x91\x01\n\x19\x42\x61tchSendDailyDraftReport\x12\x39.moego.api.appointment.v1.BatchSendDailyDraftReportParams\x1a\x39.moego.api.appointment.v1.BatchSendDailyDraftReportResult\x12\x9a\x01\n\x1c\x42\x61tchDeleteDailyReportConfig\x12<.moego.api.appointment.v1.BatchDeleteDailyReportConfigParams\x1a<.moego.api.appointment.v1.BatchDeleteDailyReportConfigResultB\x84\x01\n com.moego.idl.api.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.appointment.v1.daily_report_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.api.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb'
  _globals['_GETDAILYREPORTCONFIGPARAMS'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GETDAILYREPORTCONFIGPARAMS'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTCONFIGPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_GETDAILYREPORTCONFIGPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTCONFIGPARAMS'].fields_by_name['service_date']._loaded_options = None
  _globals['_GETDAILYREPORTCONFIGPARAMS'].fields_by_name['service_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETDAILYREPORTSENTRESULTPARAMS'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GETDAILYREPORTSENTRESULTPARAMS'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTSENTRESULTPARAMS'].fields_by_name['service_date']._loaded_options = None
  _globals['_GETDAILYREPORTSENTRESULTPARAMS'].fields_by_name['service_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['customer_id']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['service_date']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['service_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['report']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS'].fields_by_name['report']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETDAILYREPORTSENTHISTORYPARAMS'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GETDAILYREPORTSENTHISTORYPARAMS'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTSENTHISTORYPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_GETDAILYREPORTSENTHISTORYPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTFORCUSTOMERPARAMS'].fields_by_name['uuid']._loaded_options = None
  _globals['_GETDAILYREPORTFORCUSTOMERPARAMS'].fields_by_name['uuid']._serialized_options = b'\372B\004r\002\0302'
  _globals['_GENERATEMESSAGECONTENTPARAMS'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GENERATEMESSAGECONTENTPARAMS'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GENERATEMESSAGECONTENTPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_GENERATEMESSAGECONTENTPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GENERATEMESSAGECONTENTPARAMS'].fields_by_name['service_date']._loaded_options = None
  _globals['_GENERATEMESSAGECONTENTPARAMS'].fields_by_name['service_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_SENDMESSAGEPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_SENDMESSAGEPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTDAILYREPORTCONFIGPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTDAILYREPORTCONFIGPARAMS'].fields_by_name['list_daily_report_config']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGPARAMS'].fields_by_name['list_daily_report_config']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_BATCHSENDDAILYDRAFTREPORTPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_BATCHSENDDAILYDRAFTREPORTPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHSENDDAILYDRAFTREPORTPARAMS'].fields_by_name['daily_report_ids']._loaded_options = None
  _globals['_BATCHSENDDAILYDRAFTREPORTPARAMS'].fields_by_name['daily_report_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_BATCHSENDDAILYDRAFTREPORTPARAMS'].fields_by_name['send_method']._loaded_options = None
  _globals['_BATCHSENDDAILYDRAFTREPORTPARAMS'].fields_by_name['send_method']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_BATCHDELETEDAILYREPORTCONFIGPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_BATCHDELETEDAILYREPORTCONFIGPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHDELETEDAILYREPORTCONFIGPARAMS'].fields_by_name['daily_report_ids']._loaded_options = None
  _globals['_BATCHDELETEDAILYREPORTCONFIGPARAMS'].fields_by_name['daily_report_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_GETDAILYREPORTCONFIGPARAMS']._serialized_start=285
  _globals['_GETDAILYREPORTCONFIGPARAMS']._serialized_end=457
  _globals['_GETDAILYREPORTCONFIGRESULT']._serialized_start=460
  _globals['_GETDAILYREPORTCONFIGRESULT']._serialized_end=639
  _globals['_GETDAILYREPORTSENTRESULTPARAMS']._serialized_start=642
  _globals['_GETDAILYREPORTSENTRESULTPARAMS']._serialized_end=786
  _globals['_GETDAILYREPORTSENTRESULTRESULT']._serialized_start=788
  _globals['_GETDAILYREPORTSENTRESULTRESULT']._serialized_end=899
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS']._serialized_start=902
  _globals['_UPSERTDAILYREPORTCONFIGPARAMS']._serialized_end=1193
  _globals['_UPSERTDAILYREPORTCONFIGRESULT']._serialized_start=1195
  _globals['_UPSERTDAILYREPORTCONFIGRESULT']._serialized_end=1262
  _globals['_GETDAILYREPORTSENTHISTORYPARAMS']._serialized_start=1264
  _globals['_GETDAILYREPORTSENTHISTORYPARAMS']._serialized_end=1377
  _globals['_GETDAILYREPORTSENTHISTORYRESULT']._serialized_start=1380
  _globals['_GETDAILYREPORTSENTHISTORYRESULT']._serialized_end=1514
  _globals['_GETDAILYREPORTFORCUSTOMERPARAMS']._serialized_start=1516
  _globals['_GETDAILYREPORTFORCUSTOMERPARAMS']._serialized_end=1578
  _globals['_GETDAILYREPORTFORCUSTOMERRESULT']._serialized_start=1581
  _globals['_GETDAILYREPORTFORCUSTOMERRESULT']._serialized_end=2177
  _globals['_GETDAILYREPORTFORCUSTOMERRESULT_PET']._serialized_start=1912
  _globals['_GETDAILYREPORTFORCUSTOMERRESULT_PET']._serialized_end=2062
  _globals['_GETDAILYREPORTFORCUSTOMERRESULT_BUSINESS']._serialized_start=2064
  _globals['_GETDAILYREPORTFORCUSTOMERRESULT_BUSINESS']._serialized_end=2177
  _globals['_GENERATEMESSAGECONTENTPARAMS']._serialized_start=2180
  _globals['_GENERATEMESSAGECONTENTPARAMS']._serialized_end=2354
  _globals['_GENERATEMESSAGECONTENTRESULT']._serialized_start=2356
  _globals['_GENERATEMESSAGECONTENTRESULT']._serialized_end=2412
  _globals['_SENDMESSAGEPARAMS']._serialized_start=2414
  _globals['_SENDMESSAGEPARAMS']._serialized_end=2458
  _globals['_SENDMESSAGERESULT']._serialized_start=2460
  _globals['_SENDMESSAGERESULT']._serialized_end=2503
  _globals['_LISTDAILYREPORTCONFIGPARAMS']._serialized_start=2506
  _globals['_LISTDAILYREPORTCONFIGPARAMS']._serialized_end=2702
  _globals['_LISTDAILYREPORTCONFIGRESULT']._serialized_start=2705
  _globals['_LISTDAILYREPORTCONFIGRESULT']._serialized_end=3056
  _globals['_LISTDAILYREPORTCONFIGRESULT_PET']._serialized_start=1912
  _globals['_LISTDAILYREPORTCONFIGRESULT_PET']._serialized_end=2062
  _globals['_BATCHSENDDAILYDRAFTREPORTPARAMS']._serialized_start=3059
  _globals['_BATCHSENDDAILYDRAFTREPORTPARAMS']._serialized_end=3278
  _globals['_BATCHSENDDAILYDRAFTREPORTRESULT']._serialized_start=3280
  _globals['_BATCHSENDDAILYDRAFTREPORTRESULT']._serialized_end=3313
  _globals['_BATCHDELETEDAILYREPORTCONFIGPARAMS']._serialized_start=3316
  _globals['_BATCHDELETEDAILYREPORTCONFIGPARAMS']._serialized_end=3452
  _globals['_BATCHDELETEDAILYREPORTCONFIGRESULT']._serialized_start=3454
  _globals['_BATCHDELETEDAILYREPORTCONFIGRESULT']._serialized_end=3490
  _globals['_DAILYREPORTSERVICE']._serialized_start=3493
  _globals['_DAILYREPORTSERVICE']._serialized_end=4914
# @@protoc_insertion_point(module_scope)
