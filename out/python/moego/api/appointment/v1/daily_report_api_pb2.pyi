from google.type import date_pb2 as _date_pb2
from moego.models.appointment.v1 import daily_report_defs_pb2 as _daily_report_defs_pb2
from moego.models.appointment.v1 import daily_report_enums_pb2 as _daily_report_enums_pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as _customer_pet_enums_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetDailyReportConfigParams(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "service_date")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    service_date: _date_pb2.Date
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ...) -> None: ...

class GetDailyReportConfigResult(_message.Message):
    __slots__ = ("id", "report", "status")
    ID_FIELD_NUMBER: _ClassVar[int]
    REPORT_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    id: int
    report: _daily_report_defs_pb2.ReportDef
    status: _daily_report_enums_pb2.ReportCardStatus
    def __init__(self, id: _Optional[int] = ..., report: _Optional[_Union[_daily_report_defs_pb2.ReportDef, _Mapping]] = ..., status: _Optional[_Union[_daily_report_enums_pb2.ReportCardStatus, str]] = ...) -> None: ...

class GetDailyReportSentResultParams(_message.Message):
    __slots__ = ("appointment_id", "service_date")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    service_date: _date_pb2.Date
    def __init__(self, appointment_id: _Optional[int] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ...) -> None: ...

class GetDailyReportSentResultResult(_message.Message):
    __slots__ = ("sent_results",)
    SENT_RESULTS_FIELD_NUMBER: _ClassVar[int]
    sent_results: _containers.RepeatedCompositeFieldContainer[_daily_report_defs_pb2.SentResultDef]
    def __init__(self, sent_results: _Optional[_Iterable[_Union[_daily_report_defs_pb2.SentResultDef, _Mapping]]] = ...) -> None: ...

class UpsertDailyReportConfigParams(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "customer_id", "service_date", "report")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    REPORT_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    customer_id: int
    service_date: _date_pb2.Date
    report: _daily_report_defs_pb2.ReportDef
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., report: _Optional[_Union[_daily_report_defs_pb2.ReportDef, _Mapping]] = ...) -> None: ...

class UpsertDailyReportConfigResult(_message.Message):
    __slots__ = ("id", "uuid")
    ID_FIELD_NUMBER: _ClassVar[int]
    UUID_FIELD_NUMBER: _ClassVar[int]
    id: int
    uuid: str
    def __init__(self, id: _Optional[int] = ..., uuid: _Optional[str] = ...) -> None: ...

class GetDailyReportSentHistoryParams(_message.Message):
    __slots__ = ("appointment_id", "pet_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ...) -> None: ...

class GetDailyReportSentHistoryResult(_message.Message):
    __slots__ = ("sent_history_records",)
    SENT_HISTORY_RECORDS_FIELD_NUMBER: _ClassVar[int]
    sent_history_records: _containers.RepeatedCompositeFieldContainer[_daily_report_defs_pb2.SentHistoryRecordDef]
    def __init__(self, sent_history_records: _Optional[_Iterable[_Union[_daily_report_defs_pb2.SentHistoryRecordDef, _Mapping]]] = ...) -> None: ...

class GetDailyReportForCustomerParams(_message.Message):
    __slots__ = ("uuid",)
    UUID_FIELD_NUMBER: _ClassVar[int]
    uuid: str
    def __init__(self, uuid: _Optional[str] = ...) -> None: ...

class GetDailyReportForCustomerResult(_message.Message):
    __slots__ = ("report", "service_date", "pet", "business")
    class Pet(_message.Message):
        __slots__ = ("pet_id", "pet_name", "avatar_path", "pet_type")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        PET_NAME_FIELD_NUMBER: _ClassVar[int]
        AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
        PET_TYPE_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        pet_name: str
        avatar_path: str
        pet_type: _customer_pet_enums_pb2.PetType
        def __init__(self, pet_id: _Optional[int] = ..., pet_name: _Optional[str] = ..., avatar_path: _Optional[str] = ..., pet_type: _Optional[_Union[_customer_pet_enums_pb2.PetType, str]] = ...) -> None: ...
    class Business(_message.Message):
        __slots__ = ("business_id", "business_name", "avatar_path")
        BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
        BUSINESS_NAME_FIELD_NUMBER: _ClassVar[int]
        AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
        business_id: int
        business_name: str
        avatar_path: str
        def __init__(self, business_id: _Optional[int] = ..., business_name: _Optional[str] = ..., avatar_path: _Optional[str] = ...) -> None: ...
    REPORT_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    PET_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_FIELD_NUMBER: _ClassVar[int]
    report: _daily_report_defs_pb2.ReportDef
    service_date: _date_pb2.Date
    pet: GetDailyReportForCustomerResult.Pet
    business: GetDailyReportForCustomerResult.Business
    def __init__(self, report: _Optional[_Union[_daily_report_defs_pb2.ReportDef, _Mapping]] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., pet: _Optional[_Union[GetDailyReportForCustomerResult.Pet, _Mapping]] = ..., business: _Optional[_Union[GetDailyReportForCustomerResult.Business, _Mapping]] = ...) -> None: ...

class GenerateMessageContentParams(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "service_date")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    service_date: _date_pb2.Date
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ...) -> None: ...

class GenerateMessageContentResult(_message.Message):
    __slots__ = ("message",)
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    message: str
    def __init__(self, message: _Optional[str] = ...) -> None: ...

class SendMessageParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class SendMessageResult(_message.Message):
    __slots__ = ("result",)
    RESULT_FIELD_NUMBER: _ClassVar[int]
    result: bool
    def __init__(self, result: bool = ...) -> None: ...

class ListDailyReportConfigParams(_message.Message):
    __slots__ = ("business_id", "list_daily_report_config")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    LIST_DAILY_REPORT_CONFIG_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    list_daily_report_config: _daily_report_defs_pb2.ListDailyReportConfigFilter
    def __init__(self, business_id: _Optional[int] = ..., list_daily_report_config: _Optional[_Union[_daily_report_defs_pb2.ListDailyReportConfigFilter, _Mapping]] = ...) -> None: ...

class ListDailyReportConfigResult(_message.Message):
    __slots__ = ("report_configs", "pets")
    class Pet(_message.Message):
        __slots__ = ("pet_id", "pet_name", "avatar_path", "pet_type")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        PET_NAME_FIELD_NUMBER: _ClassVar[int]
        AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
        PET_TYPE_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        pet_name: str
        avatar_path: str
        pet_type: _customer_pet_enums_pb2.PetType
        def __init__(self, pet_id: _Optional[int] = ..., pet_name: _Optional[str] = ..., avatar_path: _Optional[str] = ..., pet_type: _Optional[_Union[_customer_pet_enums_pb2.PetType, str]] = ...) -> None: ...
    REPORT_CONFIGS_FIELD_NUMBER: _ClassVar[int]
    PETS_FIELD_NUMBER: _ClassVar[int]
    report_configs: _containers.RepeatedCompositeFieldContainer[_daily_report_defs_pb2.DailyReportConfigDef]
    pets: _containers.RepeatedCompositeFieldContainer[ListDailyReportConfigResult.Pet]
    def __init__(self, report_configs: _Optional[_Iterable[_Union[_daily_report_defs_pb2.DailyReportConfigDef, _Mapping]]] = ..., pets: _Optional[_Iterable[_Union[ListDailyReportConfigResult.Pet, _Mapping]]] = ...) -> None: ...

class BatchSendDailyDraftReportParams(_message.Message):
    __slots__ = ("business_id", "daily_report_ids", "send_method")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    DAILY_REPORT_IDS_FIELD_NUMBER: _ClassVar[int]
    SEND_METHOD_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    daily_report_ids: _containers.RepeatedScalarFieldContainer[int]
    send_method: _daily_report_enums_pb2.SendMethod
    def __init__(self, business_id: _Optional[int] = ..., daily_report_ids: _Optional[_Iterable[int]] = ..., send_method: _Optional[_Union[_daily_report_enums_pb2.SendMethod, str]] = ...) -> None: ...

class BatchSendDailyDraftReportResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class BatchDeleteDailyReportConfigParams(_message.Message):
    __slots__ = ("business_id", "daily_report_ids")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    DAILY_REPORT_IDS_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    daily_report_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, business_id: _Optional[int] = ..., daily_report_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class BatchDeleteDailyReportConfigResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
