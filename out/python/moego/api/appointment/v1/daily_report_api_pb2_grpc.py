# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.appointment.v1 import daily_report_api_pb2 as moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2


class DailyReportServiceStub(object):
    """the daily report service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetDailyReportConfig = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/GetDailyReportConfig',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportConfigParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportConfigResult.FromString,
                _registered_method=True)
        self.GetDailyReportSentResult = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/GetDailyReportSentResult',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentResultParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentResultResult.FromString,
                _registered_method=True)
        self.UpsertDailyReportConfig = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/UpsertDailyReportConfig',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.UpsertDailyReportConfigParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.UpsertDailyReportConfigResult.FromString,
                _registered_method=True)
        self.GetDailyReportSentHistory = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/GetDailyReportSentHistory',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentHistoryParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentHistoryResult.FromString,
                _registered_method=True)
        self.GetDailyReportForCustomer = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/GetDailyReportForCustomer',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportForCustomerParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportForCustomerResult.FromString,
                _registered_method=True)
        self.GenerateMessageContent = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/GenerateMessageContent',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GenerateMessageContentParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GenerateMessageContentResult.FromString,
                _registered_method=True)
        self.SendMessage = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/SendMessage',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.SendMessageParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.SendMessageResult.FromString,
                _registered_method=True)
        self.ListDailyReportConfig = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/ListDailyReportConfig',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.ListDailyReportConfigParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.ListDailyReportConfigResult.FromString,
                _registered_method=True)
        self.BatchSendDailyDraftReport = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/BatchSendDailyDraftReport',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchSendDailyDraftReportParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchSendDailyDraftReportResult.FromString,
                _registered_method=True)
        self.BatchDeleteDailyReportConfig = channel.unary_unary(
                '/moego.api.appointment.v1.DailyReportService/BatchDeleteDailyReportConfig',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchDeleteDailyReportConfigParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchDeleteDailyReportConfigResult.FromString,
                _registered_method=True)


class DailyReportServiceServicer(object):
    """the daily report service
    """

    def GetDailyReportConfig(self, request, context):
        """get daily report config
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDailyReportSentResult(self, request, context):
        """get daily report config sent result
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpsertDailyReportConfig(self, request, context):
        """upsert daily report config
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDailyReportSentHistory(self, request, context):
        """get daily report sent history
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDailyReportForCustomer(self, request, context):
        """get daily report for customer
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateMessageContent(self, request, context):
        """generate message content
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendMessage(self, request, context):
        """send message
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListDailyReportConfig(self, request, context):
        """get daily report list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchSendDailyDraftReport(self, request, context):
        """batch send daily draft report
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchDeleteDailyReportConfig(self, request, context):
        """batch delete daily report config
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DailyReportServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetDailyReportConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDailyReportConfig,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportConfigParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportConfigResult.SerializeToString,
            ),
            'GetDailyReportSentResult': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDailyReportSentResult,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentResultParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentResultResult.SerializeToString,
            ),
            'UpsertDailyReportConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.UpsertDailyReportConfig,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.UpsertDailyReportConfigParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.UpsertDailyReportConfigResult.SerializeToString,
            ),
            'GetDailyReportSentHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDailyReportSentHistory,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentHistoryParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentHistoryResult.SerializeToString,
            ),
            'GetDailyReportForCustomer': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDailyReportForCustomer,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportForCustomerParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportForCustomerResult.SerializeToString,
            ),
            'GenerateMessageContent': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateMessageContent,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GenerateMessageContentParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GenerateMessageContentResult.SerializeToString,
            ),
            'SendMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.SendMessage,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.SendMessageParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.SendMessageResult.SerializeToString,
            ),
            'ListDailyReportConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.ListDailyReportConfig,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.ListDailyReportConfigParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.ListDailyReportConfigResult.SerializeToString,
            ),
            'BatchSendDailyDraftReport': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchSendDailyDraftReport,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchSendDailyDraftReportParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchSendDailyDraftReportResult.SerializeToString,
            ),
            'BatchDeleteDailyReportConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchDeleteDailyReportConfig,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchDeleteDailyReportConfigParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchDeleteDailyReportConfigResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.appointment.v1.DailyReportService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.appointment.v1.DailyReportService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class DailyReportService(object):
    """the daily report service
    """

    @staticmethod
    def GetDailyReportConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/GetDailyReportConfig',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportConfigParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportConfigResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDailyReportSentResult(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/GetDailyReportSentResult',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentResultParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentResultResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpsertDailyReportConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/UpsertDailyReportConfig',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.UpsertDailyReportConfigParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.UpsertDailyReportConfigResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDailyReportSentHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/GetDailyReportSentHistory',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentHistoryParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportSentHistoryResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDailyReportForCustomer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/GetDailyReportForCustomer',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportForCustomerParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GetDailyReportForCustomerResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GenerateMessageContent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/GenerateMessageContent',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GenerateMessageContentParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.GenerateMessageContentResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SendMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/SendMessage',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.SendMessageParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.SendMessageResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListDailyReportConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/ListDailyReportConfig',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.ListDailyReportConfigParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.ListDailyReportConfigResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchSendDailyDraftReport(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/BatchSendDailyDraftReport',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchSendDailyDraftReportParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchSendDailyDraftReportResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchDeleteDailyReportConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.DailyReportService/BatchDeleteDailyReportConfig',
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchDeleteDailyReportConfigParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_daily__report__api__pb2.BatchDeleteDailyReportConfigResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
