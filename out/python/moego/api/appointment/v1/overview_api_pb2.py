# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/appointment/v1/overview_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/appointment/v1/overview_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import date_pb2 as google_dot_type_dot_date__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2
from moego.api.appointment.v1 import appointment_view_pb2 as moego_dot_api_dot_appointment_dot_v1_dot_appointment__view__pb2
from moego.models.appointment.v1 import appointment_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__enums__pb2
from moego.models.appointment.v1 import appointment_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__models__pb2
from moego.models.appointment.v1 import appointment_note_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__note__models__pb2
from moego.models.appointment.v1 import invoice_deposit_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_invoice__deposit__models__pb2
from moego.models.appointment.v1 import overview_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_overview__enums__pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__enums__pb2
from moego.models.appointment.v1 import wait_list_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_wait__list__models__pb2
from moego.models.business_customer.v1 import business_customer_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__models__pb2
from moego.models.business_customer.v1 import business_customer_pet_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__pet__models__pb2
from moego.models.business_customer.v1 import business_pet_code_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__code__models__pb2
from moego.models.business_customer.v1 import business_pet_evaluation_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__evaluation__models__pb2
from moego.models.business_customer.v1 import business_pet_incident_report_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__incident__report__models__pb2
from moego.models.business_customer.v1 import business_pet_note_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__note__models__pb2
from moego.models.membership.v1 import subscription_models_pb2 as moego_dot_models_dot_membership_dot_v1_dot_subscription__models__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.order.v1 import invoice_models_pb2 as moego_dot_models_dot_order_dot_v1_dot_invoice__models__pb2
from moego.models.payment.v1 import pre_auth_models_pb2 as moego_dot_models_dot_payment_dot_v1_dot_pre__auth__models__pb2
from moego.utils.v2 import condition_messages_pb2 as moego_dot_utils_dot_v2_dot_condition__messages__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+moego/api/appointment/v1/overview_api.proto\x12\x18moego.api.appointment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x16google/type/date.proto\x1a\x17google/type/money.proto\x1a/moego/api/appointment/v1/appointment_view.proto\x1a\x33moego/models/appointment/v1/appointment_enums.proto\x1a\x34moego/models/appointment/v1/appointment_models.proto\x1a\x39moego/models/appointment/v1/appointment_note_models.proto\x1a\x38moego/models/appointment/v1/invoice_deposit_models.proto\x1a\x30moego/models/appointment/v1/overview_enums.proto\x1a\x32moego/models/appointment/v1/pet_detail_enums.proto\x1a\x32moego/models/appointment/v1/wait_list_models.proto\x1a@moego/models/business_customer/v1/business_customer_models.proto\x1a\x44moego/models/business_customer/v1/business_customer_pet_models.proto\x1a@moego/models/business_customer/v1/business_pet_code_models.proto\x1a\x46moego/models/business_customer/v1/business_pet_evaluation_models.proto\x1aKmoego/models/business_customer/v1/business_pet_incident_report_models.proto\x1a@moego/models/business_customer/v1/business_pet_note_models.proto\x1a\x34moego/models/membership/v1/subscription_models.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a*moego/models/order/v1/invoice_models.proto\x1a-moego/models/payment/v1/pre_auth_models.proto\x1a\'moego/utils/v2/condition_messages.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xf6\x06\n\x15GetOverviewListParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12.\n\x04\x64\x61te\x18\x02 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\x04\x64\x61te\x12&\n\x07keyword\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x00R\x07keyword\x88\x01\x01\x12h\n\x12service_item_types\x18\x04 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x10serviceItemTypes\x12V\n\tdate_type\x18\x05 \x01(\x0e\x32-.moego.models.appointment.v1.OverviewDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x08\x64\x61teType\x12S\n\x06\x66ilter\x18\x06 \x01(\x0b\x32\x36.moego.api.appointment.v1.GetOverviewListParams.FilterH\x01R\x06\x66ilter\x88\x01\x01\x12\x34\n\torder_bys\x18\x07 \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBys\x12\x65\n\x0fselected_status\x18\x08 \x01(\x0e\x32+.moego.models.appointment.v1.OverviewStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x02R\x0eselectedStatus\x88\x01\x01\x1a\xfb\x01\n\x06\x46ilter\x12g\n\rreport_status\x18\x01 \x01(\x0e\x32\x31.moego.models.appointment.v1.OverviewReportStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x0creportStatus\x88\x01\x01\x12v\n\x14\x61ppointment_statuses\x18\x02 \x03(\x0e\x32..moego.models.appointment.v1.AppointmentStatusB\x13\xfa\x42\x10\x92\x01\r\x10\x06\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x13\x61ppointmentStatusesB\x10\n\x0e_report_statusB\n\n\x08_keywordB\t\n\x07_filterB\x12\n\x10_selected_status\"\xfd\x07\n\x0cOverviewItem\x12R\n\x0b\x61ppointment\x18\x01 \x01(\x0b\x32\x30.moego.models.appointment.v1.AppointmentOverviewR\x0b\x61ppointment\x12V\n\x0eservice_detail\x18\x02 \x03(\x0b\x32/.moego.api.appointment.v1.ServiceDetailOverviewR\rserviceDetail\x12G\n\x05notes\x18\x03 \x03(\x0b\x32\x31.moego.models.appointment.v1.AppointmentNoteModelR\x05notes\x12O\n\x08\x63ustomer\x18\x04 \x01(\x0b\x32\x33.moego.api.appointment.v1.CustomerCompositeOverviewR\x08\x63ustomer\x12N\n\twait_list\x18\x05 \x01(\x0b\x32\x31.moego.models.appointment.v1.WaitListCalendarViewR\x08waitList\x12W\n\x12service_item_types\x18\x06 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypes\x12G\n\x08pre_auth\x18\x15 \x01(\x0b\x32,.moego.models.payment.v1.PreAuthCalendarViewR\x07preAuth\x12L\n\x08\x64\x65posits\x18\x16 \x01(\x0b\x32\x30.moego.models.appointment.v1.InvoiceDepositModelR\x08\x64\x65posits\x12\x44\n\x07invoice\x18\x17 \x01(\x0b\x32*.moego.models.order.v1.InvoiceCalendarViewR\x07invoice\x12X\n\x0fno_show_invoice\x18\x18 \x01(\x0b\x32\x30.moego.models.order.v1.NoShowInvoiceCalendarViewR\rnoShowInvoice\x12O\n\x0freport_statuses\x18\x19 \x03(\x0b\x32&.moego.api.appointment.v1.ReportStatusR\x0ereportStatuses\x12v\n\x18membership_subscriptions\x18\x1a \x01(\x0b\x32;.moego.models.membership.v1.MembershipSubscriptionListModelR\x17membershipSubscriptions\"p\n\x0cReportStatus\x12\x15\n\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12I\n\x06status\x18\x01 \x01(\x0e\x32\x31.moego.models.appointment.v1.OverviewReportStatusR\x06status\"\xfe\x07\n\x15ServiceDetailOverview\x12U\n\x03pet\x18\x01 \x01(\x0b\x32\x43.moego.models.business_customer.v1.BusinessCustomerPetModelOverviewR\x03pet\x12N\n\x08services\x18\x02 \x03(\x0b\x32\x32.moego.api.appointment.v1.ServiceCompositeOverviewR\x08services\x12I\n\x07\x61\x64\x64_ons\x18\x03 \x03(\x0b\x32\x30.moego.api.appointment.v1.AddOnCompositeOverviewR\x06\x61\x64\x64Ons\x12U\n\x0b\x65valuations\x18\x04 \x03(\x0b\x32\x33.moego.api.appointment.v1.EvaluationServiceOverviewR\x0b\x65valuations\x12M\n\x05\x63odes\x18\x05 \x03(\x0b\x32\x37.moego.models.business_customer.v1.BusinessPetCodeModelR\x05\x63odes\x12M\n\x05notes\x18\x06 \x03(\x0b\x32\x37.moego.models.business_customer.v1.BusinessPetNoteModelR\x05notes\x12\x46\n\x08vaccines\x18\x07 \x03(\x0b\x32*.moego.api.appointment.v1.VaccineCompositeR\x08vaccines\x12S\n\x08\x62indings\x18\x08 \x03(\x0b\x32\x37.moego.api.appointment.v1.ServiceDetailOverview.BindingR\x08\x62indings\x12l\n\x10incident_reports\x18\t \x03(\x0b\x32\x41.moego.models.business_customer.v1.BusinessPetIncidentReportModelR\x0fincidentReports\x12^\n\x0fpet_evaluations\x18\n \x03(\x0b\x32\x35.moego.models.business_customer.v1.PetEvaluationModelR\x0epetEvaluations\x1a\x92\x01\n\x07\x42inding\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x17\n\x07\x63ode_id\x18\x02 \x01(\x03R\x06\x63odeId\x12\x18\n\x07\x63omment\x18\x03 \x01(\tR\x07\x63omment\x12=\n\x0c\x62inding_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0b\x62indingTime\"\xfc\x01\n\x10VaccineComposite\x12,\n\x12vaccine_binding_id\x18\x01 \x01(\x03R\x10vaccineBindingId\x12\x1d\n\nvaccine_id\x18\x03 \x01(\x03R\tvaccineId\x12?\n\x0f\x65xpiration_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateH\x00R\x0e\x65xpirationDate\x88\x01\x01\x12#\n\rdocument_urls\x18\x05 \x03(\tR\x0c\x64ocumentUrls\x12!\n\x0cvaccine_name\x18\x06 \x01(\tR\x0bvaccineNameB\x12\n\x10_expiration_date\"\xc3\x07\n\x18ServiceCompositeOverview\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12\x19\n\x08staff_id\x18\x04 \x01(\x03R\x07staffId\x12\x1d\n\nservice_id\x18\x05 \x01(\x03R\tserviceId\x12)\n\x10\x65nable_operation\x18\x15 \x01(\x08R\x0f\x65nableOperation\x12\x42\n\twork_mode\x18\x16 \x01(\x0e\x32%.moego.models.appointment.v1.WorkModeR\x08workMode\x12,\n\x12service_color_code\x18\x17 \x01(\tR\x10serviceColorCode\x12\x1d\n\nstart_date\x18\x18 \x01(\tR\tstartDate\x12\x19\n\x08\x65nd_date\x18\x19 \x01(\tR\x07\x65ndDate\x12U\n\x11service_item_type\x18\x1a \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12\x1d\n\nlodging_id\x18\x1b \x01(\x03R\tlodgingId\x12!\n\x0cservice_name\x18\x1c \x01(\tR\x0bserviceName\x12*\n\x11lodging_unit_name\x18\x1d \x01(\tR\x0flodgingUnitName\x12*\n\x11lodging_type_name\x18\x1e \x01(\tR\x0flodgingTypeName\x12\x1d\n\nstaff_name\x18\x1f \x01(\tR\tstaffName\x12!\n\x0cmax_duration\x18  \x01(\x05R\x0bmaxDuration\x12I\n\nprice_unit\x18! \x01(\x0e\x32*.moego.models.offering.v1.ServicePriceUnitR\tpriceUnit\x12\x63\n\rlodging_infos\x18\" \x03(\x0b\x32>.moego.api.appointment.v1.ServiceCompositeOverview.LodgingInfoR\x0clodgingInfos\x1a\x65\n\x0bLodgingInfo\x12*\n\x11lodging_unit_name\x18\x01 \x01(\tR\x0flodgingUnitName\x12*\n\x11lodging_type_name\x18\x02 \x01(\tR\x0flodgingTypeName\"\x84\x05\n\x16\x41\x64\x64OnCompositeOverview\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12\x19\n\x08staff_id\x18\x04 \x01(\x03R\x07staffId\x12\x1d\n\nservice_id\x18\x05 \x01(\x03R\tserviceId\x12)\n\x10\x65nable_operation\x18\x15 \x01(\x08R\x0f\x65nableOperation\x12\x42\n\twork_mode\x18\x16 \x01(\x0e\x32%.moego.models.appointment.v1.WorkModeR\x08workMode\x12,\n\x12service_color_code\x18\x17 \x01(\tR\x10serviceColorCode\x12\x1d\n\nstart_date\x18\x18 \x01(\tR\tstartDate\x12\x19\n\x08\x65nd_date\x18\x19 \x01(\tR\x07\x65ndDate\x12U\n\x11service_item_type\x18\x1a \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12!\n\x0cservice_name\x18\x1b \x01(\tR\x0bserviceName\x12\x1d\n\nstaff_name\x18\x1c \x01(\tR\tstaffName\x12K\n\tdate_type\x18\x1d \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeR\x08\x64\x61teType\x12%\n\x0especific_dates\x18\x1e \x03(\tR\rspecificDates\"\x97\x05\n\x19\x45valuationServiceOverview\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12\x1d\n\nservice_id\x18\x04 \x01(\x03R\tserviceId\x12\x1d\n\nstart_date\x18\x05 \x01(\tR\tstartDate\x12\x19\n\x08\x65nd_date\x18\x06 \x01(\tR\x07\x65ndDate\x12U\n\x11service_item_type\x18\x07 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12!\n\x0cservice_name\x18\x08 \x01(\tR\x0bserviceName\x12\x1e\n\x08staff_id\x18\t \x01(\x03H\x00R\x07staffId\x88\x01\x01\x12\"\n\nstaff_name\x18\n \x01(\tH\x01R\tstaffName\x88\x01\x01\x12\"\n\nlodging_id\x18\x0b \x01(\x03H\x02R\tlodgingId\x88\x01\x01\x12/\n\x11lodging_unit_name\x18\x0c \x01(\tH\x03R\x0flodgingUnitName\x88\x01\x01\x12/\n\x11lodging_type_name\x18\r \x01(\tH\x04R\x0flodgingTypeName\x88\x01\x01\x12\x1d\n\nstart_time\x18\x0e \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_time\x18\x0f \x01(\x05R\x07\x65ndTimeB\x0b\n\t_staff_idB\r\n\x0b_staff_nameB\r\n\x0b_lodging_idB\x14\n\x12_lodging_unit_nameB\x14\n\x12_lodging_type_name\"\xde\x04\n\x19\x43ustomerCompositeOverview\x12j\n\x10\x63ustomer_profile\x18\x01 \x01(\x0b\x32?.moego.models.business_customer.v1.BusinessCustomerCalendarViewR\x0f\x63ustomerProfile\x12&\n\x0fis_new_customer\x18\x02 \x01(\x08R\risNewCustomer\x12#\n\rrequired_sign\x18\x03 \x01(\x08R\x0crequiredSign\x12.\n\x13review_booster_sent\x18\x04 \x01(\x08R\x11reviewBoosterSent\x12Z\n\x11\x63ustomer_packages\x18\x05 \x03(\x0b\x32-.moego.api.appointment.v1.CustomerPackageViewR\x10\x63ustomerPackages\x12\x37\n\runpaid_amount\x18\x06 \x01(\x0b\x32\x12.google.type.MoneyR\x0cunpaidAmount\x12\\\n\ncof_status\x18\x07 \x01(\x0e\x32=.moego.api.appointment.v1.CustomerCompositeOverview.COFStatusR\tcofStatus\"e\n\tCOFStatus\x12\x1a\n\x16\x43OF_STATUS_UNSPECIFIED\x10\x00\x12\x0e\n\nAUTHORIZED\x10\x01\x12\x0b\n\x07PENDING\x10\x02\x12\n\n\x06\x46\x41ILED\x10\x03\x12\x13\n\x0fNO_CARD_ON_FILE\x10\x04\"\xb5\x03\n\rOverviewEntry\x12\x43\n\x06status\x18\x01 \x01(\x0e\x32+.moego.models.appointment.v1.OverviewStatusR\x06status\x12\x14\n\x05\x63ount\x18\x02 \x01(\x03R\x05\x63ount\x12<\n\x05items\x18\x03 \x03(\x0b\x32&.moego.api.appointment.v1.OverviewItemR\x05items\x12\x90\x01\n\'appointment_count_by_service_item_types\x18\x04 \x03(\x0b\x32;.moego.api.appointment.v1.AppointmentCountByServiceItemTypeR\"appointmentCountByServiceItemTypes\x12x\n\x1fpet_count_by_service_item_types\x18\x05 \x03(\x0b\x32\x33.moego.api.appointment.v1.PetCountByServiceItemTypeR\x1apetCountByServiceItemTypes\"\x90\x01\n!AppointmentCountByServiceItemType\x12U\n\x11service_item_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12\x14\n\x05\x63ount\x18\x02 \x01(\x03R\x05\x63ount\"\x88\x01\n\x19PetCountByServiceItemType\x12U\n\x11service_item_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12\x14\n\x05\x63ount\x18\x02 \x01(\x03R\x05\x63ount\"Z\n\x15GetOverviewListResult\x12\x41\n\x07\x65ntries\x18\x01 \x03(\x0b\x32\'.moego.api.appointment.v1.OverviewEntryR\x07\x65ntries\"\xb1\x07\n\x1dListOverviewAppointmentParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12/\n\x04\x64\x61te\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x04\x64\x61te\x12V\n\tdate_type\x18\x03 \x01(\x0e\x32-.moego.models.appointment.v1.OverviewDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x08\x64\x61teType\x12`\n\x0foverview_status\x18\x04 \x01(\x0e\x32+.moego.models.appointment.v1.OverviewStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0eoverviewStatus\x12[\n\x06\x66ilter\x18\x05 \x01(\x0b\x32>.moego.api.appointment.v1.ListOverviewAppointmentParams.FilterH\x00R\x06\x66ilter\x88\x01\x01\x12\x41\n\npagination\x18\t \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npagination\x12\x34\n\torder_bys\x18\n \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBys\x1a\x99\x03\n\x06\x46ilter\x12h\n\x12service_item_types\x18\x01 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x10serviceItemTypes\x12v\n\x14\x61ppointment_statuses\x18\x02 \x03(\x0e\x32..moego.models.appointment.v1.AppointmentStatusB\x13\xfa\x42\x10\x92\x01\r\x10\x06\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x13\x61ppointmentStatuses\x12&\n\x07keyword\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x00R\x07keyword\x88\x01\x01\x12g\n\rreport_status\x18\x04 \x01(\x0e\x32\x31.moego.models.appointment.v1.OverviewReportStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x0creportStatus\x88\x01\x01\x42\n\n\x08_keywordB\x10\n\x0e_report_statusB\t\n\x07_filter\"\x98\x04\n\x1dListOverviewAppointmentResult\x12<\n\x05items\x18\x01 \x03(\x0b\x32&.moego.api.appointment.v1.OverviewItemR\x05items\x12\x46\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseB\x02\x18\x01R\npagination\x12G\n\rpagination_v2\x18\x06 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\x0cpaginationV2\x12\x90\x01\n\'appointment_count_by_service_item_types\x18\x03 \x03(\x0b\x32;.moego.api.appointment.v1.AppointmentCountByServiceItemTypeR\"appointmentCountByServiceItemTypes\x12x\n\x1fpet_count_by_service_item_types\x18\x04 \x03(\x0b\x32\x33.moego.api.appointment.v1.PetCountByServiceItemTypeR\x1apetCountByServiceItemTypes\x12\x1b\n\tpet_count\x18\x05 \x01(\x05R\x08petCount\"\xcf\x05\n\x1e\x43ountOverviewAppointmentParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12/\n\x04\x64\x61te\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x04\x64\x61te\x12V\n\tdate_type\x18\x03 \x01(\x0e\x32-.moego.models.appointment.v1.OverviewDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x08\x64\x61teType\x12g\n\x0foverview_status\x18\x04 \x01(\x0e\x32+.moego.models.appointment.v1.OverviewStatusB\x0c\x18\x01\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x0eoverviewStatus\x88\x01\x01\x12\\\n\x06\x66ilter\x18\x05 \x01(\x0b\x32?.moego.api.appointment.v1.CountOverviewAppointmentParams.FilterH\x01R\x06\x66ilter\x88\x01\x01\x12k\n\x11overview_statuses\x18\x06 \x03(\x0e\x32+.moego.models.appointment.v1.OverviewStatusB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x10overviewStatuses\x1a\xa6\x01\n\x06\x46ilter\x12h\n\x12service_item_types\x18\x01 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x10serviceItemTypes\x12&\n\x07keyword\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x00R\x07keyword\x88\x01\x01\x42\n\n\x08_keywordB\x12\n\x10_overview_statusB\t\n\x07_filter\"\xd2\x02\n\x1e\x43ountOverviewAppointmentResult\x12\x18\n\x05\x63ount\x18\x01 \x01(\x03\x42\x02\x18\x01R\x05\x63ount\x12\x8d\x01\n\x1a\x63ount_with_overview_status\x18\x02 \x03(\x0b\x32P.moego.api.appointment.v1.CountOverviewAppointmentResult.CountWithOverviewStatusR\x17\x63ountWithOverviewStatus\x1a\x85\x01\n\x17\x43ountWithOverviewStatus\x12T\n\x0foverview_status\x18\x01 \x01(\x0e\x32+.moego.models.appointment.v1.OverviewStatusR\x0eoverviewStatus\x12\x14\n\x05\x63ount\x18\x02 \x01(\x03R\x05\x63ount2\xa5\x03\n\x0fOverviewService\x12s\n\x0fGetOverviewList\x12/.moego.api.appointment.v1.GetOverviewListParams\x1a/.moego.api.appointment.v1.GetOverviewListResult\x12\x8b\x01\n\x17ListOverviewAppointment\x12\x37.moego.api.appointment.v1.ListOverviewAppointmentParams\x1a\x37.moego.api.appointment.v1.ListOverviewAppointmentResult\x12\x8e\x01\n\x18\x43ountOverviewAppointment\x12\x38.moego.api.appointment.v1.CountOverviewAppointmentParams\x1a\x38.moego.api.appointment.v1.CountOverviewAppointmentResultB\x84\x01\n com.moego.idl.api.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.appointment.v1.overview_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.api.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb'
  _globals['_GETOVERVIEWLISTPARAMS_FILTER'].fields_by_name['report_status']._loaded_options = None
  _globals['_GETOVERVIEWLISTPARAMS_FILTER'].fields_by_name['report_status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETOVERVIEWLISTPARAMS_FILTER'].fields_by_name['appointment_statuses']._loaded_options = None
  _globals['_GETOVERVIEWLISTPARAMS_FILTER'].fields_by_name['appointment_statuses']._serialized_options = b'\372B\020\222\001\r\020\006\030\001\"\007\202\001\004\020\001 \000'
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['date']._loaded_options = None
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['keyword']._loaded_options = None
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['keyword']._serialized_options = b'\372B\004r\002\0302'
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['service_item_types']._loaded_options = None
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['service_item_types']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['date_type']._loaded_options = None
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['selected_status']._loaded_options = None
  _globals['_GETOVERVIEWLISTPARAMS'].fields_by_name['selected_status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['service_item_types']._loaded_options = None
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['service_item_types']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['appointment_statuses']._loaded_options = None
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['appointment_statuses']._serialized_options = b'\372B\020\222\001\r\020\006\030\001\"\007\202\001\004\020\001 \000'
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['keyword']._loaded_options = None
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['keyword']._serialized_options = b'\372B\004r\002\0302'
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['report_status']._loaded_options = None
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['report_status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['date']._loaded_options = None
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['date_type']._loaded_options = None
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['overview_status']._loaded_options = None
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['overview_status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTOVERVIEWAPPOINTMENTRESULT'].fields_by_name['pagination']._loaded_options = None
  _globals['_LISTOVERVIEWAPPOINTMENTRESULT'].fields_by_name['pagination']._serialized_options = b'\030\001'
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['service_item_types']._loaded_options = None
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['service_item_types']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['keyword']._loaded_options = None
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS_FILTER'].fields_by_name['keyword']._serialized_options = b'\372B\004r\002\0302'
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['date']._loaded_options = None
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['date_type']._loaded_options = None
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['overview_status']._loaded_options = None
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['overview_status']._serialized_options = b'\030\001\372B\007\202\001\004\020\001 \000'
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['overview_statuses']._loaded_options = None
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS'].fields_by_name['overview_statuses']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_COUNTOVERVIEWAPPOINTMENTRESULT'].fields_by_name['count']._loaded_options = None
  _globals['_COUNTOVERVIEWAPPOINTMENTRESULT'].fields_by_name['count']._serialized_options = b'\030\001'
  _globals['_GETOVERVIEWLISTPARAMS']._serialized_start=1298
  _globals['_GETOVERVIEWLISTPARAMS']._serialized_end=2184
  _globals['_GETOVERVIEWLISTPARAMS_FILTER']._serialized_start=1890
  _globals['_GETOVERVIEWLISTPARAMS_FILTER']._serialized_end=2141
  _globals['_OVERVIEWITEM']._serialized_start=2187
  _globals['_OVERVIEWITEM']._serialized_end=3208
  _globals['_REPORTSTATUS']._serialized_start=3210
  _globals['_REPORTSTATUS']._serialized_end=3322
  _globals['_SERVICEDETAILOVERVIEW']._serialized_start=3325
  _globals['_SERVICEDETAILOVERVIEW']._serialized_end=4347
  _globals['_SERVICEDETAILOVERVIEW_BINDING']._serialized_start=4201
  _globals['_SERVICEDETAILOVERVIEW_BINDING']._serialized_end=4347
  _globals['_VACCINECOMPOSITE']._serialized_start=4350
  _globals['_VACCINECOMPOSITE']._serialized_end=4602
  _globals['_SERVICECOMPOSITEOVERVIEW']._serialized_start=4605
  _globals['_SERVICECOMPOSITEOVERVIEW']._serialized_end=5568
  _globals['_SERVICECOMPOSITEOVERVIEW_LODGINGINFO']._serialized_start=5467
  _globals['_SERVICECOMPOSITEOVERVIEW_LODGINGINFO']._serialized_end=5568
  _globals['_ADDONCOMPOSITEOVERVIEW']._serialized_start=5571
  _globals['_ADDONCOMPOSITEOVERVIEW']._serialized_end=6215
  _globals['_EVALUATIONSERVICEOVERVIEW']._serialized_start=6218
  _globals['_EVALUATIONSERVICEOVERVIEW']._serialized_end=6881
  _globals['_CUSTOMERCOMPOSITEOVERVIEW']._serialized_start=6884
  _globals['_CUSTOMERCOMPOSITEOVERVIEW']._serialized_end=7490
  _globals['_CUSTOMERCOMPOSITEOVERVIEW_COFSTATUS']._serialized_start=7389
  _globals['_CUSTOMERCOMPOSITEOVERVIEW_COFSTATUS']._serialized_end=7490
  _globals['_OVERVIEWENTRY']._serialized_start=7493
  _globals['_OVERVIEWENTRY']._serialized_end=7930
  _globals['_APPOINTMENTCOUNTBYSERVICEITEMTYPE']._serialized_start=7933
  _globals['_APPOINTMENTCOUNTBYSERVICEITEMTYPE']._serialized_end=8077
  _globals['_PETCOUNTBYSERVICEITEMTYPE']._serialized_start=8080
  _globals['_PETCOUNTBYSERVICEITEMTYPE']._serialized_end=8216
  _globals['_GETOVERVIEWLISTRESULT']._serialized_start=8218
  _globals['_GETOVERVIEWLISTRESULT']._serialized_end=8308
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS']._serialized_start=8311
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS']._serialized_end=9256
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER']._serialized_start=8836
  _globals['_LISTOVERVIEWAPPOINTMENTPARAMS_FILTER']._serialized_end=9245
  _globals['_LISTOVERVIEWAPPOINTMENTRESULT']._serialized_start=9259
  _globals['_LISTOVERVIEWAPPOINTMENTRESULT']._serialized_end=9795
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS']._serialized_start=9798
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS']._serialized_end=10517
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS_FILTER']._serialized_start=10320
  _globals['_COUNTOVERVIEWAPPOINTMENTPARAMS_FILTER']._serialized_end=10486
  _globals['_COUNTOVERVIEWAPPOINTMENTRESULT']._serialized_start=10520
  _globals['_COUNTOVERVIEWAPPOINTMENTRESULT']._serialized_end=10858
  _globals['_COUNTOVERVIEWAPPOINTMENTRESULT_COUNTWITHOVERVIEWSTATUS']._serialized_start=10725
  _globals['_COUNTOVERVIEWAPPOINTMENTRESULT_COUNTWITHOVERVIEWSTATUS']._serialized_end=10858
  _globals['_OVERVIEWSERVICE']._serialized_start=10861
  _globals['_OVERVIEWSERVICE']._serialized_end=11282
# @@protoc_insertion_point(module_scope)
