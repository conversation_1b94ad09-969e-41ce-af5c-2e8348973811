from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import date_pb2 as _date_pb2
from google.type import money_pb2 as _money_pb2
from moego.api.appointment.v1 import appointment_view_pb2 as _appointment_view_pb2
from moego.models.appointment.v1 import appointment_enums_pb2 as _appointment_enums_pb2
from moego.models.appointment.v1 import appointment_models_pb2 as _appointment_models_pb2
from moego.models.appointment.v1 import appointment_note_models_pb2 as _appointment_note_models_pb2
from moego.models.appointment.v1 import invoice_deposit_models_pb2 as _invoice_deposit_models_pb2
from moego.models.appointment.v1 import overview_enums_pb2 as _overview_enums_pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as _pet_detail_enums_pb2
from moego.models.appointment.v1 import wait_list_models_pb2 as _wait_list_models_pb2
from moego.models.business_customer.v1 import business_customer_models_pb2 as _business_customer_models_pb2
from moego.models.business_customer.v1 import business_customer_pet_models_pb2 as _business_customer_pet_models_pb2
from moego.models.business_customer.v1 import business_pet_code_models_pb2 as _business_pet_code_models_pb2
from moego.models.business_customer.v1 import business_pet_evaluation_models_pb2 as _business_pet_evaluation_models_pb2
from moego.models.business_customer.v1 import business_pet_incident_report_models_pb2 as _business_pet_incident_report_models_pb2
from moego.models.business_customer.v1 import business_pet_note_models_pb2 as _business_pet_note_models_pb2
from moego.models.membership.v1 import subscription_models_pb2 as _subscription_models_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.order.v1 import invoice_models_pb2 as _invoice_models_pb2
from moego.models.payment.v1 import pre_auth_models_pb2 as _pre_auth_models_pb2
from moego.utils.v2 import condition_messages_pb2 as _condition_messages_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetOverviewListParams(_message.Message):
    __slots__ = ("business_id", "date", "keyword", "service_item_types", "date_type", "filter", "order_bys", "selected_status")
    class Filter(_message.Message):
        __slots__ = ("report_status", "appointment_statuses")
        REPORT_STATUS_FIELD_NUMBER: _ClassVar[int]
        APPOINTMENT_STATUSES_FIELD_NUMBER: _ClassVar[int]
        report_status: _overview_enums_pb2.OverviewReportStatus
        appointment_statuses: _containers.RepeatedScalarFieldContainer[_appointment_enums_pb2.AppointmentStatus]
        def __init__(self, report_status: _Optional[_Union[_overview_enums_pb2.OverviewReportStatus, str]] = ..., appointment_statuses: _Optional[_Iterable[_Union[_appointment_enums_pb2.AppointmentStatus, str]]] = ...) -> None: ...
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    KEYWORD_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    SELECTED_STATUS_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    date: str
    keyword: str
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    date_type: _overview_enums_pb2.OverviewDateType
    filter: GetOverviewListParams.Filter
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2.OrderBy]
    selected_status: _overview_enums_pb2.OverviewStatus
    def __init__(self, business_id: _Optional[int] = ..., date: _Optional[str] = ..., keyword: _Optional[str] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., date_type: _Optional[_Union[_overview_enums_pb2.OverviewDateType, str]] = ..., filter: _Optional[_Union[GetOverviewListParams.Filter, _Mapping]] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2.OrderBy, _Mapping]]] = ..., selected_status: _Optional[_Union[_overview_enums_pb2.OverviewStatus, str]] = ...) -> None: ...

class OverviewItem(_message.Message):
    __slots__ = ("appointment", "service_detail", "notes", "customer", "wait_list", "service_item_types", "pre_auth", "deposits", "invoice", "no_show_invoice", "report_statuses", "membership_subscriptions")
    APPOINTMENT_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAIL_FIELD_NUMBER: _ClassVar[int]
    NOTES_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_FIELD_NUMBER: _ClassVar[int]
    WAIT_LIST_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    PRE_AUTH_FIELD_NUMBER: _ClassVar[int]
    DEPOSITS_FIELD_NUMBER: _ClassVar[int]
    INVOICE_FIELD_NUMBER: _ClassVar[int]
    NO_SHOW_INVOICE_FIELD_NUMBER: _ClassVar[int]
    REPORT_STATUSES_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_SUBSCRIPTIONS_FIELD_NUMBER: _ClassVar[int]
    appointment: _appointment_models_pb2.AppointmentOverview
    service_detail: _containers.RepeatedCompositeFieldContainer[ServiceDetailOverview]
    notes: _containers.RepeatedCompositeFieldContainer[_appointment_note_models_pb2.AppointmentNoteModel]
    customer: CustomerCompositeOverview
    wait_list: _wait_list_models_pb2.WaitListCalendarView
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    pre_auth: _pre_auth_models_pb2.PreAuthCalendarView
    deposits: _invoice_deposit_models_pb2.InvoiceDepositModel
    invoice: _invoice_models_pb2.InvoiceCalendarView
    no_show_invoice: _invoice_models_pb2.NoShowInvoiceCalendarView
    report_statuses: _containers.RepeatedCompositeFieldContainer[ReportStatus]
    membership_subscriptions: _subscription_models_pb2.MembershipSubscriptionListModel
    def __init__(self, appointment: _Optional[_Union[_appointment_models_pb2.AppointmentOverview, _Mapping]] = ..., service_detail: _Optional[_Iterable[_Union[ServiceDetailOverview, _Mapping]]] = ..., notes: _Optional[_Iterable[_Union[_appointment_note_models_pb2.AppointmentNoteModel, _Mapping]]] = ..., customer: _Optional[_Union[CustomerCompositeOverview, _Mapping]] = ..., wait_list: _Optional[_Union[_wait_list_models_pb2.WaitListCalendarView, _Mapping]] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., pre_auth: _Optional[_Union[_pre_auth_models_pb2.PreAuthCalendarView, _Mapping]] = ..., deposits: _Optional[_Union[_invoice_deposit_models_pb2.InvoiceDepositModel, _Mapping]] = ..., invoice: _Optional[_Union[_invoice_models_pb2.InvoiceCalendarView, _Mapping]] = ..., no_show_invoice: _Optional[_Union[_invoice_models_pb2.NoShowInvoiceCalendarView, _Mapping]] = ..., report_statuses: _Optional[_Iterable[_Union[ReportStatus, _Mapping]]] = ..., membership_subscriptions: _Optional[_Union[_subscription_models_pb2.MembershipSubscriptionListModel, _Mapping]] = ...) -> None: ...

class ReportStatus(_message.Message):
    __slots__ = ("pet_id", "status")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    status: _overview_enums_pb2.OverviewReportStatus
    def __init__(self, pet_id: _Optional[int] = ..., status: _Optional[_Union[_overview_enums_pb2.OverviewReportStatus, str]] = ...) -> None: ...

class ServiceDetailOverview(_message.Message):
    __slots__ = ("pet", "services", "add_ons", "evaluations", "codes", "notes", "vaccines", "bindings", "incident_reports", "pet_evaluations")
    class Binding(_message.Message):
        __slots__ = ("pet_id", "code_id", "comment", "binding_time")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        CODE_ID_FIELD_NUMBER: _ClassVar[int]
        COMMENT_FIELD_NUMBER: _ClassVar[int]
        BINDING_TIME_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        code_id: int
        comment: str
        binding_time: _timestamp_pb2.Timestamp
        def __init__(self, pet_id: _Optional[int] = ..., code_id: _Optional[int] = ..., comment: _Optional[str] = ..., binding_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...
    PET_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    ADD_ONS_FIELD_NUMBER: _ClassVar[int]
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    CODES_FIELD_NUMBER: _ClassVar[int]
    NOTES_FIELD_NUMBER: _ClassVar[int]
    VACCINES_FIELD_NUMBER: _ClassVar[int]
    BINDINGS_FIELD_NUMBER: _ClassVar[int]
    INCIDENT_REPORTS_FIELD_NUMBER: _ClassVar[int]
    PET_EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    pet: _business_customer_pet_models_pb2.BusinessCustomerPetModelOverview
    services: _containers.RepeatedCompositeFieldContainer[ServiceCompositeOverview]
    add_ons: _containers.RepeatedCompositeFieldContainer[AddOnCompositeOverview]
    evaluations: _containers.RepeatedCompositeFieldContainer[EvaluationServiceOverview]
    codes: _containers.RepeatedCompositeFieldContainer[_business_pet_code_models_pb2.BusinessPetCodeModel]
    notes: _containers.RepeatedCompositeFieldContainer[_business_pet_note_models_pb2.BusinessPetNoteModel]
    vaccines: _containers.RepeatedCompositeFieldContainer[VaccineComposite]
    bindings: _containers.RepeatedCompositeFieldContainer[ServiceDetailOverview.Binding]
    incident_reports: _containers.RepeatedCompositeFieldContainer[_business_pet_incident_report_models_pb2.BusinessPetIncidentReportModel]
    pet_evaluations: _containers.RepeatedCompositeFieldContainer[_business_pet_evaluation_models_pb2.PetEvaluationModel]
    def __init__(self, pet: _Optional[_Union[_business_customer_pet_models_pb2.BusinessCustomerPetModelOverview, _Mapping]] = ..., services: _Optional[_Iterable[_Union[ServiceCompositeOverview, _Mapping]]] = ..., add_ons: _Optional[_Iterable[_Union[AddOnCompositeOverview, _Mapping]]] = ..., evaluations: _Optional[_Iterable[_Union[EvaluationServiceOverview, _Mapping]]] = ..., codes: _Optional[_Iterable[_Union[_business_pet_code_models_pb2.BusinessPetCodeModel, _Mapping]]] = ..., notes: _Optional[_Iterable[_Union[_business_pet_note_models_pb2.BusinessPetNoteModel, _Mapping]]] = ..., vaccines: _Optional[_Iterable[_Union[VaccineComposite, _Mapping]]] = ..., bindings: _Optional[_Iterable[_Union[ServiceDetailOverview.Binding, _Mapping]]] = ..., incident_reports: _Optional[_Iterable[_Union[_business_pet_incident_report_models_pb2.BusinessPetIncidentReportModel, _Mapping]]] = ..., pet_evaluations: _Optional[_Iterable[_Union[_business_pet_evaluation_models_pb2.PetEvaluationModel, _Mapping]]] = ...) -> None: ...

class VaccineComposite(_message.Message):
    __slots__ = ("vaccine_binding_id", "vaccine_id", "expiration_date", "document_urls", "vaccine_name")
    VACCINE_BINDING_ID_FIELD_NUMBER: _ClassVar[int]
    VACCINE_ID_FIELD_NUMBER: _ClassVar[int]
    EXPIRATION_DATE_FIELD_NUMBER: _ClassVar[int]
    DOCUMENT_URLS_FIELD_NUMBER: _ClassVar[int]
    VACCINE_NAME_FIELD_NUMBER: _ClassVar[int]
    vaccine_binding_id: int
    vaccine_id: int
    expiration_date: _date_pb2.Date
    document_urls: _containers.RepeatedScalarFieldContainer[str]
    vaccine_name: str
    def __init__(self, vaccine_binding_id: _Optional[int] = ..., vaccine_id: _Optional[int] = ..., expiration_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., document_urls: _Optional[_Iterable[str]] = ..., vaccine_name: _Optional[str] = ...) -> None: ...

class ServiceCompositeOverview(_message.Message):
    __slots__ = ("id", "appointment_id", "pet_id", "staff_id", "service_id", "enable_operation", "work_mode", "service_color_code", "start_date", "end_date", "service_item_type", "lodging_id", "service_name", "lodging_unit_name", "lodging_type_name", "staff_name", "max_duration", "price_unit", "lodging_infos")
    class LodgingInfo(_message.Message):
        __slots__ = ("lodging_unit_name", "lodging_type_name")
        LODGING_UNIT_NAME_FIELD_NUMBER: _ClassVar[int]
        LODGING_TYPE_NAME_FIELD_NUMBER: _ClassVar[int]
        lodging_unit_name: str
        lodging_type_name: str
        def __init__(self, lodging_unit_name: _Optional[str] = ..., lodging_type_name: _Optional[str] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    ENABLE_OPERATION_FIELD_NUMBER: _ClassVar[int]
    WORK_MODE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    LODGING_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    LODGING_UNIT_NAME_FIELD_NUMBER: _ClassVar[int]
    LODGING_TYPE_NAME_FIELD_NUMBER: _ClassVar[int]
    STAFF_NAME_FIELD_NUMBER: _ClassVar[int]
    MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
    PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    LODGING_INFOS_FIELD_NUMBER: _ClassVar[int]
    id: int
    appointment_id: int
    pet_id: int
    staff_id: int
    service_id: int
    enable_operation: bool
    work_mode: _pet_detail_enums_pb2.WorkMode
    service_color_code: str
    start_date: str
    end_date: str
    service_item_type: _service_enum_pb2.ServiceItemType
    lodging_id: int
    service_name: str
    lodging_unit_name: str
    lodging_type_name: str
    staff_name: str
    max_duration: int
    price_unit: _service_enum_pb2.ServicePriceUnit
    lodging_infos: _containers.RepeatedCompositeFieldContainer[ServiceCompositeOverview.LodgingInfo]
    def __init__(self, id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., service_id: _Optional[int] = ..., enable_operation: bool = ..., work_mode: _Optional[_Union[_pet_detail_enums_pb2.WorkMode, str]] = ..., service_color_code: _Optional[str] = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., lodging_id: _Optional[int] = ..., service_name: _Optional[str] = ..., lodging_unit_name: _Optional[str] = ..., lodging_type_name: _Optional[str] = ..., staff_name: _Optional[str] = ..., max_duration: _Optional[int] = ..., price_unit: _Optional[_Union[_service_enum_pb2.ServicePriceUnit, str]] = ..., lodging_infos: _Optional[_Iterable[_Union[ServiceCompositeOverview.LodgingInfo, _Mapping]]] = ...) -> None: ...

class AddOnCompositeOverview(_message.Message):
    __slots__ = ("id", "appointment_id", "pet_id", "staff_id", "service_id", "enable_operation", "work_mode", "service_color_code", "start_date", "end_date", "service_item_type", "service_name", "staff_name", "date_type", "specific_dates")
    ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    ENABLE_OPERATION_FIELD_NUMBER: _ClassVar[int]
    WORK_MODE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    STAFF_NAME_FIELD_NUMBER: _ClassVar[int]
    DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    id: int
    appointment_id: int
    pet_id: int
    staff_id: int
    service_id: int
    enable_operation: bool
    work_mode: _pet_detail_enums_pb2.WorkMode
    service_color_code: str
    start_date: str
    end_date: str
    service_item_type: _service_enum_pb2.ServiceItemType
    service_name: str
    staff_name: str
    date_type: _pet_detail_enums_pb2.PetDetailDateType
    specific_dates: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., service_id: _Optional[int] = ..., enable_operation: bool = ..., work_mode: _Optional[_Union[_pet_detail_enums_pb2.WorkMode, str]] = ..., service_color_code: _Optional[str] = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., service_name: _Optional[str] = ..., staff_name: _Optional[str] = ..., date_type: _Optional[_Union[_pet_detail_enums_pb2.PetDetailDateType, str]] = ..., specific_dates: _Optional[_Iterable[str]] = ...) -> None: ...

class EvaluationServiceOverview(_message.Message):
    __slots__ = ("id", "appointment_id", "pet_id", "service_id", "start_date", "end_date", "service_item_type", "service_name", "staff_id", "staff_name", "lodging_id", "lodging_unit_name", "lodging_type_name", "start_time", "end_time")
    ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_NAME_FIELD_NUMBER: _ClassVar[int]
    LODGING_ID_FIELD_NUMBER: _ClassVar[int]
    LODGING_UNIT_NAME_FIELD_NUMBER: _ClassVar[int]
    LODGING_TYPE_NAME_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    id: int
    appointment_id: int
    pet_id: int
    service_id: int
    start_date: str
    end_date: str
    service_item_type: _service_enum_pb2.ServiceItemType
    service_name: str
    staff_id: int
    staff_name: str
    lodging_id: int
    lodging_unit_name: str
    lodging_type_name: str
    start_time: int
    end_time: int
    def __init__(self, id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., service_id: _Optional[int] = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., service_name: _Optional[str] = ..., staff_id: _Optional[int] = ..., staff_name: _Optional[str] = ..., lodging_id: _Optional[int] = ..., lodging_unit_name: _Optional[str] = ..., lodging_type_name: _Optional[str] = ..., start_time: _Optional[int] = ..., end_time: _Optional[int] = ...) -> None: ...

class CustomerCompositeOverview(_message.Message):
    __slots__ = ("customer_profile", "is_new_customer", "required_sign", "review_booster_sent", "customer_packages", "unpaid_amount", "cof_status")
    class COFStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        COF_STATUS_UNSPECIFIED: _ClassVar[CustomerCompositeOverview.COFStatus]
        AUTHORIZED: _ClassVar[CustomerCompositeOverview.COFStatus]
        PENDING: _ClassVar[CustomerCompositeOverview.COFStatus]
        FAILED: _ClassVar[CustomerCompositeOverview.COFStatus]
        NO_CARD_ON_FILE: _ClassVar[CustomerCompositeOverview.COFStatus]
    COF_STATUS_UNSPECIFIED: CustomerCompositeOverview.COFStatus
    AUTHORIZED: CustomerCompositeOverview.COFStatus
    PENDING: CustomerCompositeOverview.COFStatus
    FAILED: CustomerCompositeOverview.COFStatus
    NO_CARD_ON_FILE: CustomerCompositeOverview.COFStatus
    CUSTOMER_PROFILE_FIELD_NUMBER: _ClassVar[int]
    IS_NEW_CUSTOMER_FIELD_NUMBER: _ClassVar[int]
    REQUIRED_SIGN_FIELD_NUMBER: _ClassVar[int]
    REVIEW_BOOSTER_SENT_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_PACKAGES_FIELD_NUMBER: _ClassVar[int]
    UNPAID_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    COF_STATUS_FIELD_NUMBER: _ClassVar[int]
    customer_profile: _business_customer_models_pb2.BusinessCustomerCalendarView
    is_new_customer: bool
    required_sign: bool
    review_booster_sent: bool
    customer_packages: _containers.RepeatedCompositeFieldContainer[_appointment_view_pb2.CustomerPackageView]
    unpaid_amount: _money_pb2.Money
    cof_status: CustomerCompositeOverview.COFStatus
    def __init__(self, customer_profile: _Optional[_Union[_business_customer_models_pb2.BusinessCustomerCalendarView, _Mapping]] = ..., is_new_customer: bool = ..., required_sign: bool = ..., review_booster_sent: bool = ..., customer_packages: _Optional[_Iterable[_Union[_appointment_view_pb2.CustomerPackageView, _Mapping]]] = ..., unpaid_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., cof_status: _Optional[_Union[CustomerCompositeOverview.COFStatus, str]] = ...) -> None: ...

class OverviewEntry(_message.Message):
    __slots__ = ("status", "count", "items", "appointment_count_by_service_item_types", "pet_count_by_service_item_types")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    COUNT_FIELD_NUMBER: _ClassVar[int]
    ITEMS_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_COUNT_BY_SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    PET_COUNT_BY_SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    status: _overview_enums_pb2.OverviewStatus
    count: int
    items: _containers.RepeatedCompositeFieldContainer[OverviewItem]
    appointment_count_by_service_item_types: _containers.RepeatedCompositeFieldContainer[AppointmentCountByServiceItemType]
    pet_count_by_service_item_types: _containers.RepeatedCompositeFieldContainer[PetCountByServiceItemType]
    def __init__(self, status: _Optional[_Union[_overview_enums_pb2.OverviewStatus, str]] = ..., count: _Optional[int] = ..., items: _Optional[_Iterable[_Union[OverviewItem, _Mapping]]] = ..., appointment_count_by_service_item_types: _Optional[_Iterable[_Union[AppointmentCountByServiceItemType, _Mapping]]] = ..., pet_count_by_service_item_types: _Optional[_Iterable[_Union[PetCountByServiceItemType, _Mapping]]] = ...) -> None: ...

class AppointmentCountByServiceItemType(_message.Message):
    __slots__ = ("service_item_type", "count")
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    COUNT_FIELD_NUMBER: _ClassVar[int]
    service_item_type: _service_enum_pb2.ServiceItemType
    count: int
    def __init__(self, service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., count: _Optional[int] = ...) -> None: ...

class PetCountByServiceItemType(_message.Message):
    __slots__ = ("service_item_type", "count")
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    COUNT_FIELD_NUMBER: _ClassVar[int]
    service_item_type: _service_enum_pb2.ServiceItemType
    count: int
    def __init__(self, service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., count: _Optional[int] = ...) -> None: ...

class GetOverviewListResult(_message.Message):
    __slots__ = ("entries",)
    ENTRIES_FIELD_NUMBER: _ClassVar[int]
    entries: _containers.RepeatedCompositeFieldContainer[OverviewEntry]
    def __init__(self, entries: _Optional[_Iterable[_Union[OverviewEntry, _Mapping]]] = ...) -> None: ...

class ListOverviewAppointmentParams(_message.Message):
    __slots__ = ("business_id", "date", "date_type", "overview_status", "filter", "pagination", "order_bys")
    class Filter(_message.Message):
        __slots__ = ("service_item_types", "appointment_statuses", "keyword", "report_status")
        SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
        APPOINTMENT_STATUSES_FIELD_NUMBER: _ClassVar[int]
        KEYWORD_FIELD_NUMBER: _ClassVar[int]
        REPORT_STATUS_FIELD_NUMBER: _ClassVar[int]
        service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
        appointment_statuses: _containers.RepeatedScalarFieldContainer[_appointment_enums_pb2.AppointmentStatus]
        keyword: str
        report_status: _overview_enums_pb2.OverviewReportStatus
        def __init__(self, service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., appointment_statuses: _Optional[_Iterable[_Union[_appointment_enums_pb2.AppointmentStatus, str]]] = ..., keyword: _Optional[str] = ..., report_status: _Optional[_Union[_overview_enums_pb2.OverviewReportStatus, str]] = ...) -> None: ...
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
    OVERVIEW_STATUS_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    date: _date_pb2.Date
    date_type: _overview_enums_pb2.OverviewDateType
    overview_status: _overview_enums_pb2.OverviewStatus
    filter: ListOverviewAppointmentParams.Filter
    pagination: _pagination_messages_pb2.PaginationRequest
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2.OrderBy]
    def __init__(self, business_id: _Optional[int] = ..., date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., date_type: _Optional[_Union[_overview_enums_pb2.OverviewDateType, str]] = ..., overview_status: _Optional[_Union[_overview_enums_pb2.OverviewStatus, str]] = ..., filter: _Optional[_Union[ListOverviewAppointmentParams.Filter, _Mapping]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2.OrderBy, _Mapping]]] = ...) -> None: ...

class ListOverviewAppointmentResult(_message.Message):
    __slots__ = ("items", "pagination", "pagination_v2", "appointment_count_by_service_item_types", "pet_count_by_service_item_types", "pet_count")
    ITEMS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_V2_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_COUNT_BY_SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    PET_COUNT_BY_SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    PET_COUNT_FIELD_NUMBER: _ClassVar[int]
    items: _containers.RepeatedCompositeFieldContainer[OverviewItem]
    pagination: _pagination_messages_pb2.PaginationResponse
    pagination_v2: _pagination_messages_pb2.PaginationResponse
    appointment_count_by_service_item_types: _containers.RepeatedCompositeFieldContainer[AppointmentCountByServiceItemType]
    pet_count_by_service_item_types: _containers.RepeatedCompositeFieldContainer[PetCountByServiceItemType]
    pet_count: int
    def __init__(self, items: _Optional[_Iterable[_Union[OverviewItem, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., pagination_v2: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., appointment_count_by_service_item_types: _Optional[_Iterable[_Union[AppointmentCountByServiceItemType, _Mapping]]] = ..., pet_count_by_service_item_types: _Optional[_Iterable[_Union[PetCountByServiceItemType, _Mapping]]] = ..., pet_count: _Optional[int] = ...) -> None: ...

class CountOverviewAppointmentParams(_message.Message):
    __slots__ = ("business_id", "date", "date_type", "overview_status", "filter", "overview_statuses")
    class Filter(_message.Message):
        __slots__ = ("service_item_types", "keyword")
        SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
        KEYWORD_FIELD_NUMBER: _ClassVar[int]
        service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
        keyword: str
        def __init__(self, service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., keyword: _Optional[str] = ...) -> None: ...
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
    OVERVIEW_STATUS_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    OVERVIEW_STATUSES_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    date: _date_pb2.Date
    date_type: _overview_enums_pb2.OverviewDateType
    overview_status: _overview_enums_pb2.OverviewStatus
    filter: CountOverviewAppointmentParams.Filter
    overview_statuses: _containers.RepeatedScalarFieldContainer[_overview_enums_pb2.OverviewStatus]
    def __init__(self, business_id: _Optional[int] = ..., date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., date_type: _Optional[_Union[_overview_enums_pb2.OverviewDateType, str]] = ..., overview_status: _Optional[_Union[_overview_enums_pb2.OverviewStatus, str]] = ..., filter: _Optional[_Union[CountOverviewAppointmentParams.Filter, _Mapping]] = ..., overview_statuses: _Optional[_Iterable[_Union[_overview_enums_pb2.OverviewStatus, str]]] = ...) -> None: ...

class CountOverviewAppointmentResult(_message.Message):
    __slots__ = ("count", "count_with_overview_status")
    class CountWithOverviewStatus(_message.Message):
        __slots__ = ("overview_status", "count")
        OVERVIEW_STATUS_FIELD_NUMBER: _ClassVar[int]
        COUNT_FIELD_NUMBER: _ClassVar[int]
        overview_status: _overview_enums_pb2.OverviewStatus
        count: int
        def __init__(self, overview_status: _Optional[_Union[_overview_enums_pb2.OverviewStatus, str]] = ..., count: _Optional[int] = ...) -> None: ...
    COUNT_FIELD_NUMBER: _ClassVar[int]
    COUNT_WITH_OVERVIEW_STATUS_FIELD_NUMBER: _ClassVar[int]
    count: int
    count_with_overview_status: _containers.RepeatedCompositeFieldContainer[CountOverviewAppointmentResult.CountWithOverviewStatus]
    def __init__(self, count: _Optional[int] = ..., count_with_overview_status: _Optional[_Iterable[_Union[CountOverviewAppointmentResult.CountWithOverviewStatus, _Mapping]]] = ...) -> None: ...
