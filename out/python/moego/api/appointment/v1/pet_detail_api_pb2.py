# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/appointment/v1/pet_detail_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/appointment/v1/pet_detail_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.appointment.v1 import pet_detail_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__defs__pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__enums__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-moego/api/appointment/v1/pet_detail_api.proto\x12\x18moego.api.appointment.v1\x1a\x31moego/models/appointment/v1/pet_detail_defs.proto\x1a\x32moego/models/appointment/v1/pet_detail_enums.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x17validate/validate.proto\"\xbe\x03\n\x1cSaveOrUpdatePetDetailsParams\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12Q\n\npet_detail\x18\x02 \x01(\x0b\x32).moego.models.appointment.v1.PetDetailDefB\x02\x18\x01H\x00R\tpetDetail\x88\x01\x01\x12T\n\x0bpet_details\x18\x04 \x03(\x0b\x32).moego.models.appointment.v1.PetDetailDefB\x08\xfa\x42\x05\x92\x01\x02\x10\x64R\npetDetails\x12\x91\x01\n\x1frepeat_appointment_modify_scope\x18\x03 \x01(\x0e\x32\x39.moego.models.appointment.v1.RepeatAppointmentModifyScopeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x1crepeatAppointmentModifyScope\x88\x01\x01\x42\r\n\x0b_pet_detailB\"\n _repeat_appointment_modify_scope\"\x1e\n\x1cSaveOrUpdatePetDetailsResult\"\x99\x02\n\x0f\x44\x65letePetParams\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12\x91\x01\n\x1frepeat_appointment_modify_scope\x18\x03 \x01(\x0e\x32\x39.moego.models.appointment.v1.RepeatAppointmentModifyScopeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x1crepeatAppointmentModifyScope\x88\x01\x01\x42\"\n _repeat_appointment_modify_scope\"\x11\n\x0f\x44\x65letePetResult\"\x95\x01\n\x19\x44\x65letePetEvaluationParams\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12H\n\x1c\x65valuation_service_detail_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x19\x65valuationServiceDetailId\"\x1b\n\x19\x44\x65letePetEvaluationResult\"\xb5\x01\n%PreCreateEvaluationServiceCheckParams\x12-\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12>\n\nstart_date\x18\x02 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\tstartDate\x88\x01\x01\x42\x0e\n\x0c_business_idB\r\n\x0b_start_date\"|\n%PreCreateEvaluationServiceCheckResult\x12\x38\n\x16pet_num_for_evaluation\x18\x01 \x01(\x05H\x00R\x13petNumForEvaluation\x88\x01\x01\x42\x19\n\x17_pet_num_for_evaluation\"\xae\x02\n\x14\x43ountPetDetailParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x39\n\nstart_date\x18\x02 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\tstartDate\x12\x35\n\x08\x65nd_date\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\x07\x65ndDate\x12\x64\n\x11service_item_type\x18\x04 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x00R\x0fserviceItemType\x88\x01\x01\x42\x14\n\x12_service_item_type\",\n\x14\x43ountPetDetailResult\x12\x14\n\x05\x63ount\x18\x01 \x01(\x05R\x05\x63ount2\x99\x05\n\x10PetDetailService\x12\x88\x01\n\x16SaveOrUpdatePetDetails\x12\x36.moego.api.appointment.v1.SaveOrUpdatePetDetailsParams\x1a\x36.moego.api.appointment.v1.SaveOrUpdatePetDetailsResult\x12\x61\n\tDeletePet\x12).moego.api.appointment.v1.DeletePetParams\x1a).moego.api.appointment.v1.DeletePetResult\x12\x7f\n\x13\x44\x65letePetEvaluation\x12\x33.moego.api.appointment.v1.DeletePetEvaluationParams\x1a\x33.moego.api.appointment.v1.DeletePetEvaluationResult\x12\xa3\x01\n\x1fPreCreateEvaluationServiceCheck\x12?.moego.api.appointment.v1.PreCreateEvaluationServiceCheckParams\x1a?.moego.api.appointment.v1.PreCreateEvaluationServiceCheckResult\x12p\n\x0e\x43ountPetDetail\x12..moego.api.appointment.v1.CountPetDetailParams\x1a..moego.api.appointment.v1.CountPetDetailResultB\x84\x01\n com.moego.idl.api.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.appointment.v1.pet_detail_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.api.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb'
  _globals['_SAVEORUPDATEPETDETAILSPARAMS'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSPARAMS'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SAVEORUPDATEPETDETAILSPARAMS'].fields_by_name['pet_detail']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSPARAMS'].fields_by_name['pet_detail']._serialized_options = b'\030\001'
  _globals['_SAVEORUPDATEPETDETAILSPARAMS'].fields_by_name['pet_details']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSPARAMS'].fields_by_name['pet_details']._serialized_options = b'\372B\005\222\001\002\020d'
  _globals['_SAVEORUPDATEPETDETAILSPARAMS'].fields_by_name['repeat_appointment_modify_scope']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSPARAMS'].fields_by_name['repeat_appointment_modify_scope']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_DELETEPETPARAMS'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_DELETEPETPARAMS'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_DELETEPETPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETPARAMS'].fields_by_name['repeat_appointment_modify_scope']._loaded_options = None
  _globals['_DELETEPETPARAMS'].fields_by_name['repeat_appointment_modify_scope']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_DELETEPETEVALUATIONPARAMS'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_DELETEPETEVALUATIONPARAMS'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETEVALUATIONPARAMS'].fields_by_name['evaluation_service_detail_id']._loaded_options = None
  _globals['_DELETEPETEVALUATIONPARAMS'].fields_by_name['evaluation_service_detail_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PRECREATEEVALUATIONSERVICECHECKPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_PRECREATEEVALUATIONSERVICECHECKPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PRECREATEEVALUATIONSERVICECHECKPARAMS'].fields_by_name['start_date']._loaded_options = None
  _globals['_PRECREATEEVALUATIONSERVICECHECKPARAMS'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_COUNTPETDETAILPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_COUNTPETDETAILPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COUNTPETDETAILPARAMS'].fields_by_name['start_date']._loaded_options = None
  _globals['_COUNTPETDETAILPARAMS'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_COUNTPETDETAILPARAMS'].fields_by_name['end_date']._loaded_options = None
  _globals['_COUNTPETDETAILPARAMS'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_COUNTPETDETAILPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_COUNTPETDETAILPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_SAVEORUPDATEPETDETAILSPARAMS']._serialized_start=249
  _globals['_SAVEORUPDATEPETDETAILSPARAMS']._serialized_end=695
  _globals['_SAVEORUPDATEPETDETAILSRESULT']._serialized_start=697
  _globals['_SAVEORUPDATEPETDETAILSRESULT']._serialized_end=727
  _globals['_DELETEPETPARAMS']._serialized_start=730
  _globals['_DELETEPETPARAMS']._serialized_end=1011
  _globals['_DELETEPETRESULT']._serialized_start=1013
  _globals['_DELETEPETRESULT']._serialized_end=1030
  _globals['_DELETEPETEVALUATIONPARAMS']._serialized_start=1033
  _globals['_DELETEPETEVALUATIONPARAMS']._serialized_end=1182
  _globals['_DELETEPETEVALUATIONRESULT']._serialized_start=1184
  _globals['_DELETEPETEVALUATIONRESULT']._serialized_end=1211
  _globals['_PRECREATEEVALUATIONSERVICECHECKPARAMS']._serialized_start=1214
  _globals['_PRECREATEEVALUATIONSERVICECHECKPARAMS']._serialized_end=1395
  _globals['_PRECREATEEVALUATIONSERVICECHECKRESULT']._serialized_start=1397
  _globals['_PRECREATEEVALUATIONSERVICECHECKRESULT']._serialized_end=1521
  _globals['_COUNTPETDETAILPARAMS']._serialized_start=1524
  _globals['_COUNTPETDETAILPARAMS']._serialized_end=1826
  _globals['_COUNTPETDETAILRESULT']._serialized_start=1828
  _globals['_COUNTPETDETAILRESULT']._serialized_end=1872
  _globals['_PETDETAILSERVICE']._serialized_start=1875
  _globals['_PETDETAILSERVICE']._serialized_end=2540
# @@protoc_insertion_point(module_scope)
