from moego.models.appointment.v1 import pet_detail_defs_pb2 as _pet_detail_defs_pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as _pet_detail_enums_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class SaveOrUpdatePetDetailsParams(_message.Message):
    __slots__ = ("appointment_id", "pet_detail", "pet_details", "repeat_appointment_modify_scope")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_DETAIL_FIELD_NUMBER: _ClassVar[int]
    PET_DETAILS_FIELD_NUMBER: _ClassVar[int]
    REPEAT_APPOINTMENT_MODIFY_SCOPE_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_detail: _pet_detail_defs_pb2.PetDetailDef
    pet_details: _containers.RepeatedCompositeFieldContainer[_pet_detail_defs_pb2.PetDetailDef]
    repeat_appointment_modify_scope: _pet_detail_enums_pb2.RepeatAppointmentModifyScope
    def __init__(self, appointment_id: _Optional[int] = ..., pet_detail: _Optional[_Union[_pet_detail_defs_pb2.PetDetailDef, _Mapping]] = ..., pet_details: _Optional[_Iterable[_Union[_pet_detail_defs_pb2.PetDetailDef, _Mapping]]] = ..., repeat_appointment_modify_scope: _Optional[_Union[_pet_detail_enums_pb2.RepeatAppointmentModifyScope, str]] = ...) -> None: ...

class SaveOrUpdatePetDetailsResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeletePetParams(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "repeat_appointment_modify_scope")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    REPEAT_APPOINTMENT_MODIFY_SCOPE_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    repeat_appointment_modify_scope: _pet_detail_enums_pb2.RepeatAppointmentModifyScope
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., repeat_appointment_modify_scope: _Optional[_Union[_pet_detail_enums_pb2.RepeatAppointmentModifyScope, str]] = ...) -> None: ...

class DeletePetResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeletePetEvaluationParams(_message.Message):
    __slots__ = ("appointment_id", "evaluation_service_detail_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    evaluation_service_detail_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., evaluation_service_detail_id: _Optional[int] = ...) -> None: ...

class DeletePetEvaluationResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class PreCreateEvaluationServiceCheckParams(_message.Message):
    __slots__ = ("business_id", "start_date")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    start_date: str
    def __init__(self, business_id: _Optional[int] = ..., start_date: _Optional[str] = ...) -> None: ...

class PreCreateEvaluationServiceCheckResult(_message.Message):
    __slots__ = ("pet_num_for_evaluation",)
    PET_NUM_FOR_EVALUATION_FIELD_NUMBER: _ClassVar[int]
    pet_num_for_evaluation: int
    def __init__(self, pet_num_for_evaluation: _Optional[int] = ...) -> None: ...

class CountPetDetailParams(_message.Message):
    __slots__ = ("business_id", "start_date", "end_date", "service_item_type")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    start_date: str
    end_date: str
    service_item_type: _service_enum_pb2.ServiceItemType
    def __init__(self, business_id: _Optional[int] = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ...) -> None: ...

class CountPetDetailResult(_message.Message):
    __slots__ = ("count",)
    COUNT_FIELD_NUMBER: _ClassVar[int]
    count: int
    def __init__(self, count: _Optional[int] = ...) -> None: ...
