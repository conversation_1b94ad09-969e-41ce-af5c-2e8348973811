# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.appointment.v1 import pet_detail_api_pb2 as moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2


class PetDetailServiceStub(object):
    """Appointment pet detail service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SaveOrUpdatePetDetails = channel.unary_unary(
                '/moego.api.appointment.v1.PetDetailService/SaveOrUpdatePetDetails',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.SaveOrUpdatePetDetailsParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.SaveOrUpdatePetDetailsResult.FromString,
                _registered_method=True)
        self.DeletePet = channel.unary_unary(
                '/moego.api.appointment.v1.PetDetailService/DeletePet',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetResult.FromString,
                _registered_method=True)
        self.DeletePetEvaluation = channel.unary_unary(
                '/moego.api.appointment.v1.PetDetailService/DeletePetEvaluation',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetEvaluationParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetEvaluationResult.FromString,
                _registered_method=True)
        self.PreCreateEvaluationServiceCheck = channel.unary_unary(
                '/moego.api.appointment.v1.PetDetailService/PreCreateEvaluationServiceCheck',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.PreCreateEvaluationServiceCheckParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.PreCreateEvaluationServiceCheckResult.FromString,
                _registered_method=True)
        self.CountPetDetail = channel.unary_unary(
                '/moego.api.appointment.v1.PetDetailService/CountPetDetail',
                request_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.CountPetDetailParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.CountPetDetailResult.FromString,
                _registered_method=True)


class PetDetailServiceServicer(object):
    """Appointment pet detail service
    """

    def SaveOrUpdatePetDetails(self, request, context):
        """Save or update pet's selected services
        If there is no pet, the selected services will be saved directly.
        If there is a pet, it will delete the original services selected for the pet, then save the newly selected services again.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePet(self, request, context):
        """Delete selected pet
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePetEvaluation(self, request, context):
        """Delete selected pet evaluation service detail
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PreCreateEvaluationServiceCheck(self, request, context):
        """pre check for create evaluation service
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CountPetDetail(self, request, context):
        """Count pet detail
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PetDetailServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SaveOrUpdatePetDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveOrUpdatePetDetails,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.SaveOrUpdatePetDetailsParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.SaveOrUpdatePetDetailsResult.SerializeToString,
            ),
            'DeletePet': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePet,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetResult.SerializeToString,
            ),
            'DeletePetEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePetEvaluation,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetEvaluationParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetEvaluationResult.SerializeToString,
            ),
            'PreCreateEvaluationServiceCheck': grpc.unary_unary_rpc_method_handler(
                    servicer.PreCreateEvaluationServiceCheck,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.PreCreateEvaluationServiceCheckParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.PreCreateEvaluationServiceCheckResult.SerializeToString,
            ),
            'CountPetDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.CountPetDetail,
                    request_deserializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.CountPetDetailParams.FromString,
                    response_serializer=moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.CountPetDetailResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.appointment.v1.PetDetailService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.appointment.v1.PetDetailService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PetDetailService(object):
    """Appointment pet detail service
    """

    @staticmethod
    def SaveOrUpdatePetDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.PetDetailService/SaveOrUpdatePetDetails',
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.SaveOrUpdatePetDetailsParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.SaveOrUpdatePetDetailsResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.PetDetailService/DeletePet',
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePetEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.PetDetailService/DeletePetEvaluation',
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetEvaluationParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.DeletePetEvaluationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PreCreateEvaluationServiceCheck(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.PetDetailService/PreCreateEvaluationServiceCheck',
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.PreCreateEvaluationServiceCheckParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.PreCreateEvaluationServiceCheckResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CountPetDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.appointment.v1.PetDetailService/CountPetDetail',
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.CountPetDetailParams.SerializeToString,
            moego_dot_api_dot_appointment_dot_v1_dot_pet__detail__api__pb2.CountPetDetailResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
