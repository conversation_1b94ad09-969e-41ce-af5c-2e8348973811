# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/business_customer/v1/business_pet_evaluation_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/business_customer/v1/business_pet_evaluation_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.business_customer.v1 import business_pet_evaluation_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__evaluation__models__pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.offering.v1 import evaluation_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_evaluation__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n@moego/api/business_customer/v1/business_pet_evaluation_api.proto\x12\x1emoego.api.business_customer.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x46moego/models/business_customer/v1/business_pet_evaluation_models.proto\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a\x30moego/models/offering/v1/evaluation_models.proto\x1a\x17validate/validate.proto\"@\n\x1eListPetEvaluationHistoryParams\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\"\xd6\x01\n\x1eListPetEvaluationHistoryResult\x12h\n\x14\x65valuation_histories\x18\x01 \x03(\x0b\x32\x35.moego.api.business_customer.v1.EvaluationHistoryViewR\x13\x65valuationHistories\x12J\n\x0b\x65valuations\x18\x02 \x03(\x0b\x32(.moego.models.offering.v1.EvaluationViewR\x0b\x65valuations\"\x90\x03\n\x15\x45valuationHistoryView\x12h\n\x0b\x61\x63tion_type\x18\x01 \x01(\x0e\x32G.moego.models.business_customer.v1.PetEvaluationHistoryModel.ActionTypeR\nactionType\x12\x44\n\x08operator\x18\x02 \x01(\x0b\x32(.moego.api.business_customer.v1.OperatorR\x08operator\x12=\n\x0coperate_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0boperateDate\x12Z\n\x10\x65valuation_extra\x18\x04 \x01(\x0b\x32/.moego.api.business_customer.v1.EvaluationExtraR\x0f\x65valuationExtra\x12,\n\revaluation_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0c\x65valuationId\"\xe1\x01\n\x0f\x45valuationExtra\x12.\n\x13reset_interval_days\x18\x01 \x01(\x05R\x11resetIntervalDays\x12S\n\x0foriginal_status\x18\x02 \x01(\x0e\x32*.moego.models.customer.v1.EvaluationStatusR\x0eoriginalStatus\x12I\n\nnew_status\x18\x03 \x01(\x0e\x32*.moego.models.customer.v1.EvaluationStatusR\tnewStatus\".\n\x08Operator\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\"9\n\x17ListPetEvaluationParams\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\"\xc5\x01\n\x17ListPetEvaluationResult\x12^\n\x0fpet_evaluations\x18\x01 \x03(\x0b\x32\x35.moego.models.business_customer.v1.PetEvaluationModelR\x0epetEvaluations\x12J\n\x0b\x65valuations\x18\x02 \x03(\x0b\x32(.moego.models.offering.v1.EvaluationViewR\x0b\x65valuations\"\xac\x02\n\x19UpdatePetEvaluationParams\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12,\n\revaluation_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0c\x65valuationId\x12W\n\x11\x65valuation_status\x18\x04 \x01(\x0e\x32*.moego.models.customer.v1.EvaluationStatusR\x10\x65valuationStatus\x12h\n\x0b\x61\x63tion_type\x18\x05 \x01(\x0e\x32G.moego.models.business_customer.v1.PetEvaluationHistoryModel.ActionTypeR\nactionType\"\x1b\n\x19UpdatePetEvaluationResult\"\x94\x02\n&ListEvaluationStatusByPetServiceParams\x12\x8b\x01\n\x0fpet_service_ids\x18\x01 \x03(\x0b\x32Y.moego.api.business_customer.v1.ListEvaluationStatusByPetServiceParams.PetServiceIdParamsB\x08\xfa\x42\x05\x92\x01\x02\x08\x01R\rpetServiceIds\x1a\\\n\x12PetServiceIdParams\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12&\n\nservice_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\"\x96\x04\n&ListEvaluationStatusByPetServiceResult\x12\x99\x01\n\x19\x65valuation_status_results\x18\x01 \x03(\x0b\x32].moego.api.business_customer.v1.ListEvaluationStatusByPetServiceResult.EvaluationStatusResultR\x17\x65valuationStatusResults\x1a\xcf\x02\n\x16\x45valuationStatusResult\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x1d\n\nservice_id\x18\x02 \x01(\x03R\tserviceId\x12\x62\n\x17\x66inal_evaluation_status\x18\x03 \x01(\x0e\x32*.moego.models.customer.v1.EvaluationStatusR\x15\x66inalEvaluationStatus\x12\x34\n\x16is_evaluation_required\x18\x06 \x01(\x08R\x14isEvaluationRequired\x12@\n\x1dis_evaluation_required_for_ob\x18\x07 \x01(\x08R\x19isEvaluationRequiredForOb\x12#\n\revaluation_id\x18\x08 \x01(\x03R\x0c\x65valuationId2\x86\x05\n\x1c\x42usinessPetEvaluationService\x12\x9a\x01\n\x18ListPetEvaluationHistory\x12>.moego.api.business_customer.v1.ListPetEvaluationHistoryParams\x1a>.moego.api.business_customer.v1.ListPetEvaluationHistoryResult\x12\x85\x01\n\x11ListPetEvaluation\x12\x37.moego.api.business_customer.v1.ListPetEvaluationParams\x1a\x37.moego.api.business_customer.v1.ListPetEvaluationResult\x12\x8b\x01\n\x13UpdatePetEvaluation\x12\x39.moego.api.business_customer.v1.UpdatePetEvaluationParams\x1a\x39.moego.api.business_customer.v1.UpdatePetEvaluationResult\x12\xb2\x01\n ListEvaluationStatusByPetService\x12\x46.moego.api.business_customer.v1.ListEvaluationStatusByPetServiceParams\x1a\x46.moego.api.business_customer.v1.ListEvaluationStatusByPetServiceResultB\x95\x01\n&com.moego.idl.api.business_customer.v1P\x01Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.business_customer.v1.business_pet_evaluation_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.api.business_customer.v1P\001Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb'
  _globals['_LISTPETEVALUATIONHISTORYPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_LISTPETEVALUATIONHISTORYPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_EVALUATIONHISTORYVIEW'].fields_by_name['evaluation_id']._loaded_options = None
  _globals['_EVALUATIONHISTORYVIEW'].fields_by_name['evaluation_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTPETEVALUATIONPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_LISTPETEVALUATIONPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETEVALUATIONPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_UPDATEPETEVALUATIONPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETEVALUATIONPARAMS'].fields_by_name['evaluation_id']._loaded_options = None
  _globals['_UPDATEPETEVALUATIONPARAMS'].fields_by_name['evaluation_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS_PETSERVICEIDPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS_PETSERVICEIDPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS_PETSERVICEIDPARAMS'].fields_by_name['service_id']._loaded_options = None
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS_PETSERVICEIDPARAMS'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS'].fields_by_name['pet_service_ids']._loaded_options = None
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS'].fields_by_name['pet_service_ids']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_LISTPETEVALUATIONHISTORYPARAMS']._serialized_start=331
  _globals['_LISTPETEVALUATIONHISTORYPARAMS']._serialized_end=395
  _globals['_LISTPETEVALUATIONHISTORYRESULT']._serialized_start=398
  _globals['_LISTPETEVALUATIONHISTORYRESULT']._serialized_end=612
  _globals['_EVALUATIONHISTORYVIEW']._serialized_start=615
  _globals['_EVALUATIONHISTORYVIEW']._serialized_end=1015
  _globals['_EVALUATIONEXTRA']._serialized_start=1018
  _globals['_EVALUATIONEXTRA']._serialized_end=1243
  _globals['_OPERATOR']._serialized_start=1245
  _globals['_OPERATOR']._serialized_end=1291
  _globals['_LISTPETEVALUATIONPARAMS']._serialized_start=1293
  _globals['_LISTPETEVALUATIONPARAMS']._serialized_end=1350
  _globals['_LISTPETEVALUATIONRESULT']._serialized_start=1353
  _globals['_LISTPETEVALUATIONRESULT']._serialized_end=1550
  _globals['_UPDATEPETEVALUATIONPARAMS']._serialized_start=1553
  _globals['_UPDATEPETEVALUATIONPARAMS']._serialized_end=1853
  _globals['_UPDATEPETEVALUATIONRESULT']._serialized_start=1855
  _globals['_UPDATEPETEVALUATIONRESULT']._serialized_end=1882
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS']._serialized_start=1885
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS']._serialized_end=2161
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS_PETSERVICEIDPARAMS']._serialized_start=2069
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICEPARAMS_PETSERVICEIDPARAMS']._serialized_end=2161
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICERESULT']._serialized_start=2164
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICERESULT']._serialized_end=2698
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICERESULT_EVALUATIONSTATUSRESULT']._serialized_start=2363
  _globals['_LISTEVALUATIONSTATUSBYPETSERVICERESULT_EVALUATIONSTATUSRESULT']._serialized_end=2698
  _globals['_BUSINESSPETEVALUATIONSERVICE']._serialized_start=2701
  _globals['_BUSINESSPETEVALUATIONSERVICE']._serialized_end=3347
# @@protoc_insertion_point(module_scope)
