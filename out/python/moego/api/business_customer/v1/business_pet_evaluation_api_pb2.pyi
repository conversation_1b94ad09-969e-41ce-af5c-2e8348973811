from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.business_customer.v1 import business_pet_evaluation_models_pb2 as _business_pet_evaluation_models_pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as _customer_pet_enums_pb2
from moego.models.offering.v1 import evaluation_models_pb2 as _evaluation_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ListPetEvaluationHistoryParams(_message.Message):
    __slots__ = ("pet_id",)
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    def __init__(self, pet_id: _Optional[int] = ...) -> None: ...

class ListPetEvaluationHistoryResult(_message.Message):
    __slots__ = ("evaluation_histories", "evaluations")
    EVALUATION_HISTORIES_FIELD_NUMBER: _ClassVar[int]
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    evaluation_histories: _containers.RepeatedCompositeFieldContainer[EvaluationHistoryView]
    evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationView]
    def __init__(self, evaluation_histories: _Optional[_Iterable[_Union[EvaluationHistoryView, _Mapping]]] = ..., evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationView, _Mapping]]] = ...) -> None: ...

class EvaluationHistoryView(_message.Message):
    __slots__ = ("action_type", "operator", "operate_date", "evaluation_extra", "evaluation_id")
    ACTION_TYPE_FIELD_NUMBER: _ClassVar[int]
    OPERATOR_FIELD_NUMBER: _ClassVar[int]
    OPERATE_DATE_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_EXTRA_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_ID_FIELD_NUMBER: _ClassVar[int]
    action_type: _business_pet_evaluation_models_pb2.PetEvaluationHistoryModel.ActionType
    operator: Operator
    operate_date: _timestamp_pb2.Timestamp
    evaluation_extra: EvaluationExtra
    evaluation_id: int
    def __init__(self, action_type: _Optional[_Union[_business_pet_evaluation_models_pb2.PetEvaluationHistoryModel.ActionType, str]] = ..., operator: _Optional[_Union[Operator, _Mapping]] = ..., operate_date: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., evaluation_extra: _Optional[_Union[EvaluationExtra, _Mapping]] = ..., evaluation_id: _Optional[int] = ...) -> None: ...

class EvaluationExtra(_message.Message):
    __slots__ = ("reset_interval_days", "original_status", "new_status")
    RESET_INTERVAL_DAYS_FIELD_NUMBER: _ClassVar[int]
    ORIGINAL_STATUS_FIELD_NUMBER: _ClassVar[int]
    NEW_STATUS_FIELD_NUMBER: _ClassVar[int]
    reset_interval_days: int
    original_status: _customer_pet_enums_pb2.EvaluationStatus
    new_status: _customer_pet_enums_pb2.EvaluationStatus
    def __init__(self, reset_interval_days: _Optional[int] = ..., original_status: _Optional[_Union[_customer_pet_enums_pb2.EvaluationStatus, str]] = ..., new_status: _Optional[_Union[_customer_pet_enums_pb2.EvaluationStatus, str]] = ...) -> None: ...

class Operator(_message.Message):
    __slots__ = ("id", "name")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ...) -> None: ...

class ListPetEvaluationParams(_message.Message):
    __slots__ = ("pet_id",)
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    def __init__(self, pet_id: _Optional[int] = ...) -> None: ...

class ListPetEvaluationResult(_message.Message):
    __slots__ = ("pet_evaluations", "evaluations")
    PET_EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    pet_evaluations: _containers.RepeatedCompositeFieldContainer[_business_pet_evaluation_models_pb2.PetEvaluationModel]
    evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationView]
    def __init__(self, pet_evaluations: _Optional[_Iterable[_Union[_business_pet_evaluation_models_pb2.PetEvaluationModel, _Mapping]]] = ..., evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationView, _Mapping]]] = ...) -> None: ...

class UpdatePetEvaluationParams(_message.Message):
    __slots__ = ("pet_id", "evaluation_id", "evaluation_status", "action_type")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_ID_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_STATUS_FIELD_NUMBER: _ClassVar[int]
    ACTION_TYPE_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    evaluation_id: int
    evaluation_status: _customer_pet_enums_pb2.EvaluationStatus
    action_type: _business_pet_evaluation_models_pb2.PetEvaluationHistoryModel.ActionType
    def __init__(self, pet_id: _Optional[int] = ..., evaluation_id: _Optional[int] = ..., evaluation_status: _Optional[_Union[_customer_pet_enums_pb2.EvaluationStatus, str]] = ..., action_type: _Optional[_Union[_business_pet_evaluation_models_pb2.PetEvaluationHistoryModel.ActionType, str]] = ...) -> None: ...

class UpdatePetEvaluationResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListEvaluationStatusByPetServiceParams(_message.Message):
    __slots__ = ("pet_service_ids",)
    class PetServiceIdParams(_message.Message):
        __slots__ = ("pet_id", "service_id")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        service_id: int
        def __init__(self, pet_id: _Optional[int] = ..., service_id: _Optional[int] = ...) -> None: ...
    PET_SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
    pet_service_ids: _containers.RepeatedCompositeFieldContainer[ListEvaluationStatusByPetServiceParams.PetServiceIdParams]
    def __init__(self, pet_service_ids: _Optional[_Iterable[_Union[ListEvaluationStatusByPetServiceParams.PetServiceIdParams, _Mapping]]] = ...) -> None: ...

class ListEvaluationStatusByPetServiceResult(_message.Message):
    __slots__ = ("evaluation_status_results",)
    class EvaluationStatusResult(_message.Message):
        __slots__ = ("pet_id", "service_id", "final_evaluation_status", "is_evaluation_required", "is_evaluation_required_for_ob", "evaluation_id")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        FINAL_EVALUATION_STATUS_FIELD_NUMBER: _ClassVar[int]
        IS_EVALUATION_REQUIRED_FIELD_NUMBER: _ClassVar[int]
        IS_EVALUATION_REQUIRED_FOR_OB_FIELD_NUMBER: _ClassVar[int]
        EVALUATION_ID_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        service_id: int
        final_evaluation_status: _customer_pet_enums_pb2.EvaluationStatus
        is_evaluation_required: bool
        is_evaluation_required_for_ob: bool
        evaluation_id: int
        def __init__(self, pet_id: _Optional[int] = ..., service_id: _Optional[int] = ..., final_evaluation_status: _Optional[_Union[_customer_pet_enums_pb2.EvaluationStatus, str]] = ..., is_evaluation_required: bool = ..., is_evaluation_required_for_ob: bool = ..., evaluation_id: _Optional[int] = ...) -> None: ...
    EVALUATION_STATUS_RESULTS_FIELD_NUMBER: _ClassVar[int]
    evaluation_status_results: _containers.RepeatedCompositeFieldContainer[ListEvaluationStatusByPetServiceResult.EvaluationStatusResult]
    def __init__(self, evaluation_status_results: _Optional[_Iterable[_Union[ListEvaluationStatusByPetServiceResult.EvaluationStatusResult, _Mapping]]] = ...) -> None: ...
