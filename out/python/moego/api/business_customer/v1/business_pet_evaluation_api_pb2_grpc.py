# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.business_customer.v1 import business_pet_evaluation_api_pb2 as moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2


class BusinessPetEvaluationServiceStub(object):
    """API for business pet evaluation
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ListPetEvaluationHistory = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluationHistory',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationHistoryParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationHistoryResult.FromString,
                _registered_method=True)
        self.ListPetEvaluation = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluation',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationResult.FromString,
                _registered_method=True)
        self.UpdatePetEvaluation = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessPetEvaluationService/UpdatePetEvaluation',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.UpdatePetEvaluationParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.UpdatePetEvaluationResult.FromString,
                _registered_method=True)
        self.ListEvaluationStatusByPetService = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessPetEvaluationService/ListEvaluationStatusByPetService',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListEvaluationStatusByPetServiceParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListEvaluationStatusByPetServiceResult.FromString,
                _registered_method=True)


class BusinessPetEvaluationServiceServicer(object):
    """API for business pet evaluation
    """

    def ListPetEvaluationHistory(self, request, context):
        """List pet evaluation history
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListPetEvaluation(self, request, context):
        """List pet evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdatePetEvaluation(self, request, context):
        """update pet evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListEvaluationStatusByPetService(self, request, context):
        """List pet service 的 evaluation status
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BusinessPetEvaluationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ListPetEvaluationHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPetEvaluationHistory,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationHistoryParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationHistoryResult.SerializeToString,
            ),
            'ListPetEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPetEvaluation,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationResult.SerializeToString,
            ),
            'UpdatePetEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdatePetEvaluation,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.UpdatePetEvaluationParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.UpdatePetEvaluationResult.SerializeToString,
            ),
            'ListEvaluationStatusByPetService': grpc.unary_unary_rpc_method_handler(
                    servicer.ListEvaluationStatusByPetService,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListEvaluationStatusByPetServiceParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListEvaluationStatusByPetServiceResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.business_customer.v1.BusinessPetEvaluationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.business_customer.v1.BusinessPetEvaluationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class BusinessPetEvaluationService(object):
    """API for business pet evaluation
    """

    @staticmethod
    def ListPetEvaluationHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluationHistory',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationHistoryParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationHistoryResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListPetEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessPetEvaluationService/ListPetEvaluation',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListPetEvaluationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdatePetEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessPetEvaluationService/UpdatePetEvaluation',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.UpdatePetEvaluationParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.UpdatePetEvaluationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListEvaluationStatusByPetService(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessPetEvaluationService/ListEvaluationStatusByPetService',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListEvaluationStatusByPetServiceParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__pet__evaluation__api__pb2.ListEvaluationStatusByPetServiceResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
