# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/fulfillment/v1/fulfillment_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/fulfillment/v1/fulfillment_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.fulfillment.v1 import fulfillment_defs_pb2 as moego_dot_models_dot_fulfillment_dot_v1_dot_fulfillment__defs__pb2
from moego.models.fulfillment.v1 import fulfillment_enums_pb2 as moego_dot_models_dot_fulfillment_dot_v1_dot_fulfillment__enums__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.moego/api/fulfillment/v1/fulfillment_api.proto\x12\x18moego.api.fulfillment.v1\x1a\x32moego/models/fulfillment/v1/fulfillment_defs.proto\x1a\x33moego/models/fulfillment/v1/fulfillment_enums.proto\x1a\x17validate/validate.proto\"\xce\x01\n\x0f\x45nrollPetParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12(\n\x0binstance_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\ninstanceId\x12\x1e\n\x06pet_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12G\n\x06source\x18\x04 \x01(\x0e\x32#.moego.models.fulfillment.v1.SourceB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x06source\",\n\x0f\x45nrollPetResult\x12\x19\n\x08order_id\x18\x01 \x01(\x03R\x07orderId\"\xd5\x01\n\x1e\x43heckInGroupClassSessionParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12+\n\x07pet_ids\x18\x02 \x03(\x03\x42\x12\xfa\x42\x0f\x92\x01\x0c\x08\x01\x10\x64\x18\x01\"\x04\"\x02 \x00R\x06petIds\x12\x41\n\x16group_class_session_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x13groupClassSessionId\x88\x01\x01\x42\x19\n\x17_group_class_session_id\"}\n\x1e\x43heckInGroupClassSessionResult\x12$\n\x0e\x63heck_in_count\x18\x01 \x01(\x05R\x0c\x63heckInCount\x12\x35\n\x17\x63hecked_in_instance_ids\x18\x02 \x03(\x03R\x14\x63heckedInInstanceIds\"6\n\x1bPreviewOrderLineItemsParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\xb5\x01\n\x1bPreviewOrderLineItemsResult\x12J\n\x0cpet_services\x18\x01 \x03(\x0b\x32\'.moego.models.fulfillment.v1.PetServiceR\x0bpetServices\x12J\n\nsurcharges\x18\x02 \x03(\x0b\x32*.moego.models.fulfillment.v1.SurchargeItemR\nsurcharges2\x90\x03\n\x12\x46ulfillmentService\x12\x61\n\tEnrollPet\x12).moego.api.fulfillment.v1.EnrollPetParams\x1a).moego.api.fulfillment.v1.EnrollPetResult\x12\x8e\x01\n\x18\x43heckInGroupClassSession\x12\x38.moego.api.fulfillment.v1.CheckInGroupClassSessionParams\x1a\x38.moego.api.fulfillment.v1.CheckInGroupClassSessionResult\x12\x85\x01\n\x15PreviewOrderLineItems\x12\x35.moego.api.fulfillment.v1.PreviewOrderLineItemsParams\x1a\x35.moego.api.fulfillment.v1.PreviewOrderLineItemsResultB\x84\x01\n com.moego.idl.api.fulfillment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/fulfillment/v1;fulfillmentapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.fulfillment.v1.fulfillment_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.api.fulfillment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/fulfillment/v1;fulfillmentapipb'
  _globals['_ENROLLPETPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_ENROLLPETPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ENROLLPETPARAMS'].fields_by_name['instance_id']._loaded_options = None
  _globals['_ENROLLPETPARAMS'].fields_by_name['instance_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ENROLLPETPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_ENROLLPETPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ENROLLPETPARAMS'].fields_by_name['source']._loaded_options = None
  _globals['_ENROLLPETPARAMS'].fields_by_name['source']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_CHECKINGROUPCLASSSESSIONPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_CHECKINGROUPCLASSSESSIONPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CHECKINGROUPCLASSSESSIONPARAMS'].fields_by_name['pet_ids']._loaded_options = None
  _globals['_CHECKINGROUPCLASSSESSIONPARAMS'].fields_by_name['pet_ids']._serialized_options = b'\372B\017\222\001\014\010\001\020d\030\001\"\004\"\002 \000'
  _globals['_CHECKINGROUPCLASSSESSIONPARAMS'].fields_by_name['group_class_session_id']._loaded_options = None
  _globals['_CHECKINGROUPCLASSSESSIONPARAMS'].fields_by_name['group_class_session_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PREVIEWORDERLINEITEMSPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_PREVIEWORDERLINEITEMSPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ENROLLPETPARAMS']._serialized_start=207
  _globals['_ENROLLPETPARAMS']._serialized_end=413
  _globals['_ENROLLPETRESULT']._serialized_start=415
  _globals['_ENROLLPETRESULT']._serialized_end=459
  _globals['_CHECKINGROUPCLASSSESSIONPARAMS']._serialized_start=462
  _globals['_CHECKINGROUPCLASSSESSIONPARAMS']._serialized_end=675
  _globals['_CHECKINGROUPCLASSSESSIONRESULT']._serialized_start=677
  _globals['_CHECKINGROUPCLASSSESSIONRESULT']._serialized_end=802
  _globals['_PREVIEWORDERLINEITEMSPARAMS']._serialized_start=804
  _globals['_PREVIEWORDERLINEITEMSPARAMS']._serialized_end=858
  _globals['_PREVIEWORDERLINEITEMSRESULT']._serialized_start=861
  _globals['_PREVIEWORDERLINEITEMSRESULT']._serialized_end=1042
  _globals['_FULFILLMENTSERVICE']._serialized_start=1045
  _globals['_FULFILLMENTSERVICE']._serialized_end=1445
# @@protoc_insertion_point(module_scope)
