from moego.models.fulfillment.v1 import fulfillment_defs_pb2 as _fulfillment_defs_pb2
from moego.models.fulfillment.v1 import fulfillment_enums_pb2 as _fulfillment_enums_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EnrollPetParams(_message.Message):
    __slots__ = ("business_id", "instance_id", "pet_id", "source")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    INSTANCE_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    instance_id: int
    pet_id: int
    source: _fulfillment_enums_pb2.Source
    def __init__(self, business_id: _Optional[int] = ..., instance_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., source: _Optional[_Union[_fulfillment_enums_pb2.Source, str]] = ...) -> None: ...

class EnrollPetResult(_message.Message):
    __slots__ = ("order_id",)
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    order_id: int
    def __init__(self, order_id: _Optional[int] = ...) -> None: ...

class CheckInGroupClassSessionParams(_message.Message):
    __slots__ = ("business_id", "pet_ids", "group_class_session_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    PET_IDS_FIELD_NUMBER: _ClassVar[int]
    GROUP_CLASS_SESSION_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    pet_ids: _containers.RepeatedScalarFieldContainer[int]
    group_class_session_id: int
    def __init__(self, business_id: _Optional[int] = ..., pet_ids: _Optional[_Iterable[int]] = ..., group_class_session_id: _Optional[int] = ...) -> None: ...

class CheckInGroupClassSessionResult(_message.Message):
    __slots__ = ("check_in_count", "checked_in_instance_ids")
    CHECK_IN_COUNT_FIELD_NUMBER: _ClassVar[int]
    CHECKED_IN_INSTANCE_IDS_FIELD_NUMBER: _ClassVar[int]
    check_in_count: int
    checked_in_instance_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, check_in_count: _Optional[int] = ..., checked_in_instance_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class PreviewOrderLineItemsParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class PreviewOrderLineItemsResult(_message.Message):
    __slots__ = ("pet_services", "surcharges")
    PET_SERVICES_FIELD_NUMBER: _ClassVar[int]
    SURCHARGES_FIELD_NUMBER: _ClassVar[int]
    pet_services: _containers.RepeatedCompositeFieldContainer[_fulfillment_defs_pb2.PetService]
    surcharges: _containers.RepeatedCompositeFieldContainer[_fulfillment_defs_pb2.SurchargeItem]
    def __init__(self, pet_services: _Optional[_Iterable[_Union[_fulfillment_defs_pb2.PetService, _Mapping]]] = ..., surcharges: _Optional[_Iterable[_Union[_fulfillment_defs_pb2.SurchargeItem, _Mapping]]] = ...) -> None: ...
