# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/grooming/v1/grooming_report_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/grooming/v1/grooming_report_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.grooming.v1 import grooming_report_enums_pb2 as moego_dot_models_dot_grooming_dot_v1_dot_grooming__report__enums__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/moego/api/grooming/v1/grooming_report_api.proto\x12\x15moego.api.grooming.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x16google/type/date.proto\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a\x34moego/models/grooming/v1/grooming_report_enums.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xd7\x03\n\x1cListGroomingReportCardParams\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x35\n\nstart_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateH\x00R\tstartDate\x88\x01\x01\x12\x31\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateH\x01R\x07\x65ndDate\x88\x01\x01\x12W\n\x06status\x18\x05 \x01(\x0e\x32..moego.models.grooming.v1.GroomingReportStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x02R\x06status\x88\x01\x01\x12#\n\x06pet_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x03R\x05petId\x88\x01\x01\x12K\n\npagination\x18\x07 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npaginationB\r\n\x0b_start_dateB\x0b\n\t_end_dateB\t\n\x07_statusB\t\n\x07_pet_id\"\xc4\x01\n\x1cListGroomingReportCardResult\x12`\n\x15grooming_report_cards\x18\x01 \x03(\x0b\x32,.moego.api.grooming.v1.GroomingReportCardDefR\x13groomingReportCards\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"\xd5\x02\n\x15GroomingReportCardDef\x12$\n\x0ereport_card_id\x18\x01 \x01(\x03R\x0creportCardId\x12(\n\x10report_card_type\x18\x02 \x01(\tR\x0ereportCardType\x12O\n\x0cpet_overview\x18\x03 \x01(\x0b\x32,.moego.api.grooming.v1.ReportCardPetOverviewR\x0bpetOverview\x12;\n\x0bupdate_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nupdateTime\x12\x37\n\tsend_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x08sendTime\x12%\n\x0e\x61ppointment_id\x18\x06 \x01(\x03R\rappointmentId\"\xa8\x01\n\x15ReportCardPetOverview\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x19\n\x08pet_name\x18\x02 \x01(\tR\x07petName\x12<\n\x08pet_type\x18\x03 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\x12\x1f\n\x0b\x61vatar_path\x18\x04 \x01(\tR\navatarPath\"\x9f\x01\n#BatchDeleteGroomingReportCardParams\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12&\n\x0freport_card_ids\x18\x03 \x03(\x03R\rreportCardIds\"%\n#BatchDeleteGroomingReportCardResult\"\xfe\x01\n!BatchSendGroomingReportCardParams\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12&\n\x0freport_card_ids\x18\x03 \x03(\x03R\rreportCardIds\x12_\n\x0bsend_method\x18\x04 \x01(\x0e\x32\x32.moego.models.grooming.v1.GroomingReportSendMethodB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\nsendMethod\"#\n!BatchSendGroomingReportCardResult2\xca\x03\n\x15GroomingReportService\x12\x82\x01\n\x16ListGroomingReportCard\x12\x33.moego.api.grooming.v1.ListGroomingReportCardParams\x1a\x33.moego.api.grooming.v1.ListGroomingReportCardResult\x12\x97\x01\n\x1d\x42\x61tchDeleteGroomingReportCard\x12:.moego.api.grooming.v1.BatchDeleteGroomingReportCardParams\x1a:.moego.api.grooming.v1.BatchDeleteGroomingReportCardResult\x12\x91\x01\n\x1b\x42\x61tchSendGroomingReportCard\x12\x38.moego.api.grooming.v1.BatchSendGroomingReportCardParams\x1a\x38.moego.api.grooming.v1.BatchSendGroomingReportCardResultB{\n\x1d\x63om.moego.idl.api.grooming.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/grooming/v1;groomingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.grooming.v1.grooming_report_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\035com.moego.idl.api.grooming.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/grooming/v1;groomingapipb'
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['status']._loaded_options = None
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['pagination']._loaded_options = None
  _globals['_LISTGROOMINGREPORTCARDPARAMS'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_BATCHDELETEGROOMINGREPORTCARDPARAMS'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHDELETEGROOMINGREPORTCARDPARAMS'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHDELETEGROOMINGREPORTCARDPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_BATCHDELETEGROOMINGREPORTCARDPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHSENDGROOMINGREPORTCARDPARAMS'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHSENDGROOMINGREPORTCARDPARAMS'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHSENDGROOMINGREPORTCARDPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_BATCHSENDGROOMINGREPORTCARDPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHSENDGROOMINGREPORTCARDPARAMS'].fields_by_name['send_method']._loaded_options = None
  _globals['_BATCHSENDGROOMINGREPORTCARDPARAMS'].fields_by_name['send_method']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTGROOMINGREPORTCARDPARAMS']._serialized_start=304
  _globals['_LISTGROOMINGREPORTCARDPARAMS']._serialized_end=775
  _globals['_LISTGROOMINGREPORTCARDRESULT']._serialized_start=778
  _globals['_LISTGROOMINGREPORTCARDRESULT']._serialized_end=974
  _globals['_GROOMINGREPORTCARDDEF']._serialized_start=977
  _globals['_GROOMINGREPORTCARDDEF']._serialized_end=1318
  _globals['_REPORTCARDPETOVERVIEW']._serialized_start=1321
  _globals['_REPORTCARDPETOVERVIEW']._serialized_end=1489
  _globals['_BATCHDELETEGROOMINGREPORTCARDPARAMS']._serialized_start=1492
  _globals['_BATCHDELETEGROOMINGREPORTCARDPARAMS']._serialized_end=1651
  _globals['_BATCHDELETEGROOMINGREPORTCARDRESULT']._serialized_start=1653
  _globals['_BATCHDELETEGROOMINGREPORTCARDRESULT']._serialized_end=1690
  _globals['_BATCHSENDGROOMINGREPORTCARDPARAMS']._serialized_start=1693
  _globals['_BATCHSENDGROOMINGREPORTCARDPARAMS']._serialized_end=1947
  _globals['_BATCHSENDGROOMINGREPORTCARDRESULT']._serialized_start=1949
  _globals['_BATCHSENDGROOMINGREPORTCARDRESULT']._serialized_end=1984
  _globals['_GROOMINGREPORTSERVICE']._serialized_start=1987
  _globals['_GROOMINGREPORTSERVICE']._serialized_end=2445
# @@protoc_insertion_point(module_scope)
