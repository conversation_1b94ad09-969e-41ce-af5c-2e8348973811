from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import date_pb2 as _date_pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as _customer_pet_enums_pb2
from moego.models.grooming.v1 import grooming_report_enums_pb2 as _grooming_report_enums_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ListGroomingReportCardParams(_message.Message):
    __slots__ = ("company_id", "business_id", "start_date", "end_date", "status", "pet_id", "pagination")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    status: _grooming_report_enums_pb2.GroomingReportStatus
    pet_id: int
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., status: _Optional[_Union[_grooming_report_enums_pb2.GroomingReportStatus, str]] = ..., pet_id: _Optional[int] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class ListGroomingReportCardResult(_message.Message):
    __slots__ = ("grooming_report_cards", "pagination")
    GROOMING_REPORT_CARDS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    grooming_report_cards: _containers.RepeatedCompositeFieldContainer[GroomingReportCardDef]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, grooming_report_cards: _Optional[_Iterable[_Union[GroomingReportCardDef, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class GroomingReportCardDef(_message.Message):
    __slots__ = ("report_card_id", "pet_overview", "update_time", "send_time", "appointment_id", "send_method")
    REPORT_CARD_ID_FIELD_NUMBER: _ClassVar[int]
    PET_OVERVIEW_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    SEND_TIME_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    SEND_METHOD_FIELD_NUMBER: _ClassVar[int]
    report_card_id: int
    pet_overview: ReportCardPetOverview
    update_time: _timestamp_pb2.Timestamp
    send_time: _timestamp_pb2.Timestamp
    appointment_id: int
    send_method: _grooming_report_enums_pb2.GroomingReportSendMethod
    def __init__(self, report_card_id: _Optional[int] = ..., pet_overview: _Optional[_Union[ReportCardPetOverview, _Mapping]] = ..., update_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., send_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., appointment_id: _Optional[int] = ..., send_method: _Optional[_Union[_grooming_report_enums_pb2.GroomingReportSendMethod, str]] = ...) -> None: ...

class ReportCardPetOverview(_message.Message):
    __slots__ = ("pet_id", "pet_name", "pet_type", "avatar_path")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PET_NAME_FIELD_NUMBER: _ClassVar[int]
    PET_TYPE_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    pet_name: str
    pet_type: _customer_pet_enums_pb2.PetType
    avatar_path: str
    def __init__(self, pet_id: _Optional[int] = ..., pet_name: _Optional[str] = ..., pet_type: _Optional[_Union[_customer_pet_enums_pb2.PetType, str]] = ..., avatar_path: _Optional[str] = ...) -> None: ...

class BatchDeleteGroomingReportCardParams(_message.Message):
    __slots__ = ("company_id", "business_id", "report_card_ids")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    REPORT_CARD_IDS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    report_card_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., report_card_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class BatchDeleteGroomingReportCardResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class BatchSendGroomingReportCardParams(_message.Message):
    __slots__ = ("company_id", "business_id", "report_card_ids", "send_method", "staff_id")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    REPORT_CARD_IDS_FIELD_NUMBER: _ClassVar[int]
    SEND_METHOD_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    report_card_ids: _containers.RepeatedScalarFieldContainer[int]
    send_method: _grooming_report_enums_pb2.GroomingReportSendMethod
    staff_id: int
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., report_card_ids: _Optional[_Iterable[int]] = ..., send_method: _Optional[_Union[_grooming_report_enums_pb2.GroomingReportSendMethod, str]] = ..., staff_id: _Optional[int] = ...) -> None: ...

class BatchSendGroomingReportCardResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
