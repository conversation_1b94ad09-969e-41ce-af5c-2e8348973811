# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.grooming.v1 import grooming_report_api_pb2 as moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2


class GroomingReportServiceStub(object):
    """service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ListGroomingReportCard = channel.unary_unary(
                '/moego.api.grooming.v1.GroomingReportService/ListGroomingReportCard',
                request_serializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.ListGroomingReportCardParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.ListGroomingReportCardResult.FromString,
                _registered_method=True)
        self.BatchDeleteGroomingReportCard = channel.unary_unary(
                '/moego.api.grooming.v1.GroomingReportService/BatchDeleteGroomingReportCard',
                request_serializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchDeleteGroomingReportCardParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchDeleteGroomingReportCardResult.FromString,
                _registered_method=True)
        self.BatchSendGroomingReportCard = channel.unary_unary(
                '/moego.api.grooming.v1.GroomingReportService/BatchSendGroomingReportCard',
                request_serializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchSendGroomingReportCardParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchSendGroomingReportCardResult.FromString,
                _registered_method=True)


class GroomingReportServiceServicer(object):
    """service
    """

    def ListGroomingReportCard(self, request, context):
        """list grooming report card by date range
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchDeleteGroomingReportCard(self, request, context):
        """batch delete grooming report
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchSendGroomingReportCard(self, request, context):
        """batch send grooming report
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GroomingReportServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ListGroomingReportCard': grpc.unary_unary_rpc_method_handler(
                    servicer.ListGroomingReportCard,
                    request_deserializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.ListGroomingReportCardParams.FromString,
                    response_serializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.ListGroomingReportCardResult.SerializeToString,
            ),
            'BatchDeleteGroomingReportCard': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchDeleteGroomingReportCard,
                    request_deserializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchDeleteGroomingReportCardParams.FromString,
                    response_serializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchDeleteGroomingReportCardResult.SerializeToString,
            ),
            'BatchSendGroomingReportCard': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchSendGroomingReportCard,
                    request_deserializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchSendGroomingReportCardParams.FromString,
                    response_serializer=moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchSendGroomingReportCardResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.grooming.v1.GroomingReportService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.grooming.v1.GroomingReportService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class GroomingReportService(object):
    """service
    """

    @staticmethod
    def ListGroomingReportCard(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.grooming.v1.GroomingReportService/ListGroomingReportCard',
            moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.ListGroomingReportCardParams.SerializeToString,
            moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.ListGroomingReportCardResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchDeleteGroomingReportCard(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.grooming.v1.GroomingReportService/BatchDeleteGroomingReportCard',
            moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchDeleteGroomingReportCardParams.SerializeToString,
            moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchDeleteGroomingReportCardResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchSendGroomingReportCard(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.grooming.v1.GroomingReportService/BatchSendGroomingReportCard',
            moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchSendGroomingReportCardParams.SerializeToString,
            moego_dot_api_dot_grooming_dot_v1_dot_grooming__report__api__pb2.BatchSendGroomingReportCardResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
