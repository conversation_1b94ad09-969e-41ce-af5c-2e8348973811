# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/offering/v1/evaluation_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/offering/v1/evaluation_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.offering.v1 import evaluation_defs_pb2 as moego_dot_models_dot_offering_dot_v1_dot_evaluation__defs__pb2
from moego.models.offering.v1 import evaluation_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_evaluation__models__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.organization.v1 import location_models_pb2 as moego_dot_models_dot_organization_dot_v1_dot_location__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*moego/api/offering/v1/evaluation_api.proto\x12\x15moego.api.offering.v1\x1a.moego/models/offering/v1/evaluation_defs.proto\x1a\x30moego/models/offering/v1/evaluation_models.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x32moego/models/organization/v1/location_models.proto\x1a\x17validate/validate.proto\"r\n\x16\x43reateEvaluationParams\x12X\n\x0e\x65valuation_def\x18\x01 \x01(\x0b\x32\'.moego.models.offering.v1.EvaluationDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\revaluationDef\"n\n\x16\x43reateEvaluationResult\x12T\n\x10\x65valuation_model\x18\x01 \x01(\x0b\x32).moego.models.offering.v1.EvaluationModelR\x0f\x65valuationModel\"\x8b\x01\n\x16UpdateEvaluationParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12X\n\x0e\x65valuation_def\x18\x02 \x01(\x0b\x32\'.moego.models.offering.v1.EvaluationDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\revaluationDef\"n\n\x16UpdateEvaluationResult\x12T\n\x10\x65valuation_model\x18\x01 \x01(\x0b\x32).moego.models.offering.v1.EvaluationModelR\x0f\x65valuationModel\"1\n\x16\x44\x65leteEvaluationParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\x18\n\x16\x44\x65leteEvaluationResult\".\n\x13GetEvaluationParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"`\n\x13GetEvaluationResult\x12I\n\nevaluation\x18\x01 \x01(\x0b\x32).moego.models.offering.v1.EvaluationModelR\nevaluation\"\x19\n\x17GetEvaluationListParams\"f\n\x17GetEvaluationListResult\x12K\n\x0b\x65valuations\x18\x01 \x03(\x0b\x32).moego.models.offering.v1.EvaluationModelR\x0b\x65valuations\"\xd4\x01\n!GetApplicableEvaluationListParams\x12-\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12Z\n\x11service_item_type\x18\x02 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeH\x01R\x0fserviceItemType\x88\x01\x01\x42\x0e\n\x0c_business_idB\x14\n\x12_service_item_type\"t\n!GetApplicableEvaluationListResult\x12O\n\x0b\x65valuations\x18\x01 \x03(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewR\x0b\x65valuations\"/\n-GetBusinessListWithApplicableEvaluationParams\"\x80\x01\n-GetBusinessListWithApplicableEvaluationResult\x12O\n\nbusinesses\x18\x01 \x03(\x0b\x32/.moego.models.organization.v1.LocationBriefViewR\nbusinesses2\xa1\x07\n\x11\x45valuationService\x12r\n\x10\x43reateEvaluation\x12-.moego.api.offering.v1.CreateEvaluationParams\x1a-.moego.api.offering.v1.CreateEvaluationResult\"\x00\x12r\n\x10UpdateEvaluation\x12-.moego.api.offering.v1.UpdateEvaluationParams\x1a-.moego.api.offering.v1.UpdateEvaluationResult\"\x00\x12r\n\x10\x44\x65leteEvaluation\x12-.moego.api.offering.v1.DeleteEvaluationParams\x1a-.moego.api.offering.v1.DeleteEvaluationResult\"\x00\x12i\n\rGetEvaluation\x12*.moego.api.offering.v1.GetEvaluationParams\x1a*.moego.api.offering.v1.GetEvaluationResult\"\x00\x12u\n\x11GetEvaluationList\x12..moego.api.offering.v1.GetEvaluationListParams\x1a..moego.api.offering.v1.GetEvaluationListResult\"\x00\x12\x93\x01\n\x1bGetApplicableEvaluationList\x12\x38.moego.api.offering.v1.GetApplicableEvaluationListParams\x1a\x38.moego.api.offering.v1.GetApplicableEvaluationListResult\"\x00\x12\xb7\x01\n\'GetBusinessListWithApplicableEvaluation\x12\x44.moego.api.offering.v1.GetBusinessListWithApplicableEvaluationParams\x1a\x44.moego.api.offering.v1.GetBusinessListWithApplicableEvaluationResult\"\x00\x42{\n\x1d\x63om.moego.idl.api.offering.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.offering.v1.evaluation_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\035com.moego.idl.api.offering.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb'
  _globals['_CREATEEVALUATIONPARAMS'].fields_by_name['evaluation_def']._loaded_options = None
  _globals['_CREATEEVALUATIONPARAMS'].fields_by_name['evaluation_def']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATEEVALUATIONPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEEVALUATIONPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEEVALUATIONPARAMS'].fields_by_name['evaluation_def']._loaded_options = None
  _globals['_UPDATEEVALUATIONPARAMS'].fields_by_name['evaluation_def']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_DELETEEVALUATIONPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_DELETEEVALUATIONPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETEVALUATIONPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_GETEVALUATIONPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPLICABLEEVALUATIONLISTPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETAPPLICABLEEVALUATIONLISTPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEEVALUATIONPARAMS']._serialized_start=289
  _globals['_CREATEEVALUATIONPARAMS']._serialized_end=403
  _globals['_CREATEEVALUATIONRESULT']._serialized_start=405
  _globals['_CREATEEVALUATIONRESULT']._serialized_end=515
  _globals['_UPDATEEVALUATIONPARAMS']._serialized_start=518
  _globals['_UPDATEEVALUATIONPARAMS']._serialized_end=657
  _globals['_UPDATEEVALUATIONRESULT']._serialized_start=659
  _globals['_UPDATEEVALUATIONRESULT']._serialized_end=769
  _globals['_DELETEEVALUATIONPARAMS']._serialized_start=771
  _globals['_DELETEEVALUATIONPARAMS']._serialized_end=820
  _globals['_DELETEEVALUATIONRESULT']._serialized_start=822
  _globals['_DELETEEVALUATIONRESULT']._serialized_end=846
  _globals['_GETEVALUATIONPARAMS']._serialized_start=848
  _globals['_GETEVALUATIONPARAMS']._serialized_end=894
  _globals['_GETEVALUATIONRESULT']._serialized_start=896
  _globals['_GETEVALUATIONRESULT']._serialized_end=992
  _globals['_GETEVALUATIONLISTPARAMS']._serialized_start=994
  _globals['_GETEVALUATIONLISTPARAMS']._serialized_end=1019
  _globals['_GETEVALUATIONLISTRESULT']._serialized_start=1021
  _globals['_GETEVALUATIONLISTRESULT']._serialized_end=1123
  _globals['_GETAPPLICABLEEVALUATIONLISTPARAMS']._serialized_start=1126
  _globals['_GETAPPLICABLEEVALUATIONLISTPARAMS']._serialized_end=1338
  _globals['_GETAPPLICABLEEVALUATIONLISTRESULT']._serialized_start=1340
  _globals['_GETAPPLICABLEEVALUATIONLISTRESULT']._serialized_end=1456
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONPARAMS']._serialized_start=1458
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONPARAMS']._serialized_end=1505
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONRESULT']._serialized_start=1508
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONRESULT']._serialized_end=1636
  _globals['_EVALUATIONSERVICE']._serialized_start=1639
  _globals['_EVALUATIONSERVICE']._serialized_end=2568
# @@protoc_insertion_point(module_scope)
