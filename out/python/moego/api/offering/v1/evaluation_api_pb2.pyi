from moego.models.offering.v1 import evaluation_defs_pb2 as _evaluation_defs_pb2
from moego.models.offering.v1 import evaluation_models_pb2 as _evaluation_models_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.organization.v1 import location_models_pb2 as _location_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateEvaluationParams(_message.Message):
    __slots__ = ("evaluation_def",)
    EVALUATION_DEF_FIELD_NUMBER: _ClassVar[int]
    evaluation_def: _evaluation_defs_pb2.EvaluationDef
    def __init__(self, evaluation_def: _Optional[_Union[_evaluation_defs_pb2.EvaluationDef, _Mapping]] = ...) -> None: ...

class CreateEvaluationResult(_message.Message):
    __slots__ = ("evaluation_model",)
    EVALUATION_MODEL_FIELD_NUMBER: _ClassVar[int]
    evaluation_model: _evaluation_models_pb2.EvaluationModel
    def __init__(self, evaluation_model: _Optional[_Union[_evaluation_models_pb2.EvaluationModel, _Mapping]] = ...) -> None: ...

class UpdateEvaluationParams(_message.Message):
    __slots__ = ("id", "evaluation_def")
    ID_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_DEF_FIELD_NUMBER: _ClassVar[int]
    id: int
    evaluation_def: _evaluation_defs_pb2.EvaluationDef
    def __init__(self, id: _Optional[int] = ..., evaluation_def: _Optional[_Union[_evaluation_defs_pb2.EvaluationDef, _Mapping]] = ...) -> None: ...

class UpdateEvaluationResult(_message.Message):
    __slots__ = ("evaluation_model",)
    EVALUATION_MODEL_FIELD_NUMBER: _ClassVar[int]
    evaluation_model: _evaluation_models_pb2.EvaluationModel
    def __init__(self, evaluation_model: _Optional[_Union[_evaluation_models_pb2.EvaluationModel, _Mapping]] = ...) -> None: ...

class DeleteEvaluationParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class DeleteEvaluationResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetEvaluationParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class GetEvaluationResult(_message.Message):
    __slots__ = ("evaluation",)
    EVALUATION_FIELD_NUMBER: _ClassVar[int]
    evaluation: _evaluation_models_pb2.EvaluationModel
    def __init__(self, evaluation: _Optional[_Union[_evaluation_models_pb2.EvaluationModel, _Mapping]] = ...) -> None: ...

class GetEvaluationListParams(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetEvaluationListResult(_message.Message):
    __slots__ = ("evaluations",)
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationModel]
    def __init__(self, evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationModel, _Mapping]]] = ...) -> None: ...

class GetApplicableEvaluationListParams(_message.Message):
    __slots__ = ("business_id", "service_item_type")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    service_item_type: _service_enum_pb2.ServiceItemType
    def __init__(self, business_id: _Optional[int] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ...) -> None: ...

class GetApplicableEvaluationListResult(_message.Message):
    __slots__ = ("evaluations",)
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationBriefView]
    def __init__(self, evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]]] = ...) -> None: ...

class GetBusinessListWithApplicableEvaluationParams(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetBusinessListWithApplicableEvaluationResult(_message.Message):
    __slots__ = ("businesses",)
    BUSINESSES_FIELD_NUMBER: _ClassVar[int]
    businesses: _containers.RepeatedCompositeFieldContainer[_location_models_pb2.LocationBriefView]
    def __init__(self, businesses: _Optional[_Iterable[_Union[_location_models_pb2.LocationBriefView, _Mapping]]] = ...) -> None: ...
