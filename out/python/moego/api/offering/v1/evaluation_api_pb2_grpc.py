# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.offering.v1 import evaluation_api_pb2 as moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2


class EvaluationServiceStub(object):
    """the evaluation service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateEvaluation = channel.unary_unary(
                '/moego.api.offering.v1.EvaluationService/CreateEvaluation',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.CreateEvaluationParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.CreateEvaluationResult.FromString,
                _registered_method=True)
        self.UpdateEvaluation = channel.unary_unary(
                '/moego.api.offering.v1.EvaluationService/UpdateEvaluation',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.UpdateEvaluationParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.UpdateEvaluationResult.FromString,
                _registered_method=True)
        self.DeleteEvaluation = channel.unary_unary(
                '/moego.api.offering.v1.EvaluationService/DeleteEvaluation',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.DeleteEvaluationParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.DeleteEvaluationResult.FromString,
                _registered_method=True)
        self.GetEvaluation = channel.unary_unary(
                '/moego.api.offering.v1.EvaluationService/GetEvaluation',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationResult.FromString,
                _registered_method=True)
        self.GetEvaluationList = channel.unary_unary(
                '/moego.api.offering.v1.EvaluationService/GetEvaluationList',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationListParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationListResult.FromString,
                _registered_method=True)
        self.GetApplicableEvaluationList = channel.unary_unary(
                '/moego.api.offering.v1.EvaluationService/GetApplicableEvaluationList',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetApplicableEvaluationListParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetApplicableEvaluationListResult.FromString,
                _registered_method=True)
        self.GetBusinessListWithApplicableEvaluation = channel.unary_unary(
                '/moego.api.offering.v1.EvaluationService/GetBusinessListWithApplicableEvaluation',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetBusinessListWithApplicableEvaluationParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetBusinessListWithApplicableEvaluationResult.FromString,
                _registered_method=True)


class EvaluationServiceServicer(object):
    """the evaluation service
    """

    def CreateEvaluation(self, request, context):
        """create evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateEvaluation(self, request, context):
        """update evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteEvaluation(self, request, context):
        """delete evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetEvaluation(self, request, context):
        """get evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetEvaluationList(self, request, context):
        """get evaluation list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetApplicableEvaluationList(self, request, context):
        """get applicable evaluation list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBusinessListWithApplicableEvaluation(self, request, context):
        """get business list with applicable evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_EvaluationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateEvaluation,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.CreateEvaluationParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.CreateEvaluationResult.SerializeToString,
            ),
            'UpdateEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateEvaluation,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.UpdateEvaluationParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.UpdateEvaluationResult.SerializeToString,
            ),
            'DeleteEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteEvaluation,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.DeleteEvaluationParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.DeleteEvaluationResult.SerializeToString,
            ),
            'GetEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEvaluation,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationResult.SerializeToString,
            ),
            'GetEvaluationList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEvaluationList,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationListParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationListResult.SerializeToString,
            ),
            'GetApplicableEvaluationList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetApplicableEvaluationList,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetApplicableEvaluationListParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetApplicableEvaluationListResult.SerializeToString,
            ),
            'GetBusinessListWithApplicableEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBusinessListWithApplicableEvaluation,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetBusinessListWithApplicableEvaluationParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetBusinessListWithApplicableEvaluationResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.offering.v1.EvaluationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.offering.v1.EvaluationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class EvaluationService(object):
    """the evaluation service
    """

    @staticmethod
    def CreateEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.EvaluationService/CreateEvaluation',
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.CreateEvaluationParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.CreateEvaluationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.EvaluationService/UpdateEvaluation',
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.UpdateEvaluationParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.UpdateEvaluationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.EvaluationService/DeleteEvaluation',
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.DeleteEvaluationParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.DeleteEvaluationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.EvaluationService/GetEvaluation',
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetEvaluationList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.EvaluationService/GetEvaluationList',
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationListParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetEvaluationListResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetApplicableEvaluationList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.EvaluationService/GetApplicableEvaluationList',
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetApplicableEvaluationListParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetApplicableEvaluationListResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetBusinessListWithApplicableEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.EvaluationService/GetBusinessListWithApplicableEvaluation',
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetBusinessListWithApplicableEvaluationParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_evaluation__api__pb2.GetBusinessListWithApplicableEvaluationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
