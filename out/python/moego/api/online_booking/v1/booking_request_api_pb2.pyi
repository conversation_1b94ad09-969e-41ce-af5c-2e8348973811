from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import date_pb2 as _date_pb2
from moego.api.appointment.v1 import appointment_view_pb2 as _appointment_view_pb2
from moego.models.appointment.v1 import appointment_pet_medication_schedule_defs_pb2 as _appointment_pet_medication_schedule_defs_pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as _pet_detail_enums_pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as _customer_pet_enums_pb2
from moego.models.membership.v1 import membership_models_pb2 as _membership_models_pb2
from moego.models.membership.v1 import subscription_models_pb2 as _subscription_models_pb2
from moego.models.offering.v1 import evaluation_models_pb2 as _evaluation_models_pb2
from moego.models.offering.v1 import group_class_models_pb2 as _group_class_models_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.offering.v1 import service_models_pb2 as _service_models_pb2
from moego.models.online_booking.v1 import boarding_add_on_detail_models_pb2 as _boarding_add_on_detail_models_pb2
from moego.models.online_booking.v1 import boarding_service_detail_models_pb2 as _boarding_service_detail_models_pb2
from moego.models.online_booking.v1 import booking_request_enums_pb2 as _booking_request_enums_pb2
from moego.models.online_booking.v1 import booking_request_models_pb2 as _booking_request_models_pb2
from moego.models.online_booking.v1 import daycare_add_on_detail_models_pb2 as _daycare_add_on_detail_models_pb2
from moego.models.online_booking.v1 import daycare_service_detail_models_pb2 as _daycare_service_detail_models_pb2
from moego.models.online_booking.v1 import evaluation_test_detail_models_pb2 as _evaluation_test_detail_models_pb2
from moego.models.online_booking.v1 import feeding_models_pb2 as _feeding_models_pb2
from moego.models.online_booking.v1 import grooming_add_on_detail_models_pb2 as _grooming_add_on_detail_models_pb2
from moego.models.online_booking.v1 import grooming_service_detail_models_pb2 as _grooming_service_detail_models_pb2
from moego.models.online_booking.v1 import medication_models_pb2 as _medication_models_pb2
from moego.service.online_booking.v1 import booking_request_service_pb2 as _booking_request_service_pb2
from moego.utils.v2 import condition_messages_pb2 as _condition_messages_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetBookingRequestListRequest(_message.Message):
    __slots__ = ("business_id", "pagination", "keyword", "order_bys")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    KEYWORD_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    pagination: _pagination_messages_pb2.PaginationRequest
    keyword: str
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2.OrderBy]
    def __init__(self, business_id: _Optional[int] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., keyword: _Optional[str] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2.OrderBy, _Mapping]]] = ...) -> None: ...

class CustomerDetail(_message.Message):
    __slots__ = ("first_name", "last_name", "phone_number", "business_id", "customer_id", "email", "avatar_path", "client_color", "referral_source_id", "preferred_groomer_id", "preferred_frequency_type", "preferred_frequency_day", "preferred_day", "preferred_time", "is_new_customer", "primary_address", "new_address", "question_answers", "last_alert_note", "out_of_service_area", "has_pet_parent_app_account", "emergency_contact", "pickup_contact")
    class EmergencyContact(_message.Message):
        __slots__ = ("first_name", "last_name", "phone_number")
        FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
        LAST_NAME_FIELD_NUMBER: _ClassVar[int]
        PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
        first_name: str
        last_name: str
        phone_number: str
        def __init__(self, first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., phone_number: _Optional[str] = ...) -> None: ...
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    CLIENT_COLOR_FIELD_NUMBER: _ClassVar[int]
    REFERRAL_SOURCE_ID_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_GROOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_FREQUENCY_TYPE_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_FREQUENCY_DAY_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_DAY_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_TIME_FIELD_NUMBER: _ClassVar[int]
    IS_NEW_CUSTOMER_FIELD_NUMBER: _ClassVar[int]
    PRIMARY_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    NEW_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    QUESTION_ANSWERS_FIELD_NUMBER: _ClassVar[int]
    LAST_ALERT_NOTE_FIELD_NUMBER: _ClassVar[int]
    OUT_OF_SERVICE_AREA_FIELD_NUMBER: _ClassVar[int]
    HAS_PET_PARENT_APP_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    EMERGENCY_CONTACT_FIELD_NUMBER: _ClassVar[int]
    PICKUP_CONTACT_FIELD_NUMBER: _ClassVar[int]
    first_name: str
    last_name: str
    phone_number: str
    business_id: int
    customer_id: int
    email: str
    avatar_path: str
    client_color: str
    referral_source_id: int
    preferred_groomer_id: int
    preferred_frequency_type: int
    preferred_frequency_day: int
    preferred_day: _containers.RepeatedScalarFieldContainer[int]
    preferred_time: _containers.RepeatedScalarFieldContainer[int]
    is_new_customer: bool
    primary_address: AddressDetail
    new_address: _containers.RepeatedCompositeFieldContainer[AddressDetail]
    question_answers: _containers.RepeatedCompositeFieldContainer[QuestionAnswerDetail]
    last_alert_note: str
    out_of_service_area: bool
    has_pet_parent_app_account: bool
    emergency_contact: CustomerDetail.EmergencyContact
    pickup_contact: CustomerDetail.EmergencyContact
    def __init__(self, first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., phone_number: _Optional[str] = ..., business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., email: _Optional[str] = ..., avatar_path: _Optional[str] = ..., client_color: _Optional[str] = ..., referral_source_id: _Optional[int] = ..., preferred_groomer_id: _Optional[int] = ..., preferred_frequency_type: _Optional[int] = ..., preferred_frequency_day: _Optional[int] = ..., preferred_day: _Optional[_Iterable[int]] = ..., preferred_time: _Optional[_Iterable[int]] = ..., is_new_customer: bool = ..., primary_address: _Optional[_Union[AddressDetail, _Mapping]] = ..., new_address: _Optional[_Iterable[_Union[AddressDetail, _Mapping]]] = ..., question_answers: _Optional[_Iterable[_Union[QuestionAnswerDetail, _Mapping]]] = ..., last_alert_note: _Optional[str] = ..., out_of_service_area: bool = ..., has_pet_parent_app_account: bool = ..., emergency_contact: _Optional[_Union[CustomerDetail.EmergencyContact, _Mapping]] = ..., pickup_contact: _Optional[_Union[CustomerDetail.EmergencyContact, _Mapping]] = ...) -> None: ...

class GetBookingRequestListResponse(_message.Message):
    __slots__ = ("booking_request_items", "pagination")
    BOOKING_REQUEST_ITEMS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    booking_request_items: _containers.RepeatedCompositeFieldContainer[GetBookingRequestItem]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, booking_request_items: _Optional[_Iterable[_Union[GetBookingRequestItem, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class GetBookingRequestRequest(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class GetBookingRequestItem(_message.Message):
    __slots__ = ("booking_request", "service_details", "customer_detail", "pay", "has_request_update", "assign_require", "membership_subscriptions", "customer_packages", "incomplete_details", "related_memberships")
    BOOKING_REQUEST_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAILS_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_DETAIL_FIELD_NUMBER: _ClassVar[int]
    PAY_FIELD_NUMBER: _ClassVar[int]
    HAS_REQUEST_UPDATE_FIELD_NUMBER: _ClassVar[int]
    ASSIGN_REQUIRE_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_SUBSCRIPTIONS_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_PACKAGES_FIELD_NUMBER: _ClassVar[int]
    INCOMPLETE_DETAILS_FIELD_NUMBER: _ClassVar[int]
    RELATED_MEMBERSHIPS_FIELD_NUMBER: _ClassVar[int]
    booking_request: BookingRequestDetail
    service_details: _containers.RepeatedCompositeFieldContainer[ServiceDetail]
    customer_detail: CustomerDetail
    pay: PayBookingRequestView
    has_request_update: bool
    assign_require: AssignRequire
    membership_subscriptions: _subscription_models_pb2.MembershipSubscriptionListModel
    customer_packages: _containers.RepeatedCompositeFieldContainer[_appointment_view_pb2.CustomerPackageView]
    incomplete_details: IncompleteDetails
    related_memberships: _containers.RepeatedCompositeFieldContainer[_membership_models_pb2.MembershipModelPublicView]
    def __init__(self, booking_request: _Optional[_Union[BookingRequestDetail, _Mapping]] = ..., service_details: _Optional[_Iterable[_Union[ServiceDetail, _Mapping]]] = ..., customer_detail: _Optional[_Union[CustomerDetail, _Mapping]] = ..., pay: _Optional[_Union[PayBookingRequestView, _Mapping]] = ..., has_request_update: bool = ..., assign_require: _Optional[_Union[AssignRequire, _Mapping]] = ..., membership_subscriptions: _Optional[_Union[_subscription_models_pb2.MembershipSubscriptionListModel, _Mapping]] = ..., customer_packages: _Optional[_Iterable[_Union[_appointment_view_pb2.CustomerPackageView, _Mapping]]] = ..., incomplete_details: _Optional[_Union[IncompleteDetails, _Mapping]] = ..., related_memberships: _Optional[_Iterable[_Union[_membership_models_pb2.MembershipModelPublicView, _Mapping]]] = ...) -> None: ...

class ServiceDetail(_message.Message):
    __slots__ = ("pet_detail", "services")
    class Service(_message.Message):
        __slots__ = ("grooming", "boarding", "daycare", "evaluation", "dog_walking", "group_class", "service_item_type")
        GROOMING_FIELD_NUMBER: _ClassVar[int]
        BOARDING_FIELD_NUMBER: _ClassVar[int]
        DAYCARE_FIELD_NUMBER: _ClassVar[int]
        EVALUATION_FIELD_NUMBER: _ClassVar[int]
        DOG_WALKING_FIELD_NUMBER: _ClassVar[int]
        GROUP_CLASS_FIELD_NUMBER: _ClassVar[int]
        SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
        grooming: GroomingService
        boarding: BoardingService
        daycare: DaycareService
        evaluation: EvaluationService
        dog_walking: DogWalkingService
        group_class: GroupClassService
        service_item_type: _service_enum_pb2.ServiceItemType
        def __init__(self, grooming: _Optional[_Union[GroomingService, _Mapping]] = ..., boarding: _Optional[_Union[BoardingService, _Mapping]] = ..., daycare: _Optional[_Union[DaycareService, _Mapping]] = ..., evaluation: _Optional[_Union[EvaluationService, _Mapping]] = ..., dog_walking: _Optional[_Union[DogWalkingService, _Mapping]] = ..., group_class: _Optional[_Union[GroupClassService, _Mapping]] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ...) -> None: ...
    PET_DETAIL_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    pet_detail: PetDetail
    services: _containers.RepeatedCompositeFieldContainer[ServiceDetail.Service]
    def __init__(self, pet_detail: _Optional[_Union[PetDetail, _Mapping]] = ..., services: _Optional[_Iterable[_Union[ServiceDetail.Service, _Mapping]]] = ...) -> None: ...

class BookingRequestDetail(_message.Message):
    __slots__ = ("id", "business_id", "customer_id", "appointment_id", "start_date", "start_time", "end_date", "end_time", "status", "is_prepaid", "additional_note", "source_platform", "service_type_include", "created_at", "no_start_time", "service_item_types", "staff_name", "staff_id", "specific_dates", "source", "source_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    IS_PREPAID_FIELD_NUMBER: _ClassVar[int]
    ADDITIONAL_NOTE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_PLATFORM_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_INCLUDE_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    NO_START_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    STAFF_NAME_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    customer_id: int
    appointment_id: int
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    status: _booking_request_enums_pb2.BookingRequestStatus
    is_prepaid: bool
    additional_note: str
    source_platform: _booking_request_enums_pb2.BookingRequestSourcePlatform
    service_type_include: int
    created_at: _timestamp_pb2.Timestamp
    no_start_time: bool
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    staff_name: str
    staff_id: int
    specific_dates: _containers.RepeatedScalarFieldContainer[str]
    source: _booking_request_models_pb2.BookingRequestModel.Source
    source_id: int
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., status: _Optional[_Union[_booking_request_enums_pb2.BookingRequestStatus, str]] = ..., is_prepaid: bool = ..., additional_note: _Optional[str] = ..., source_platform: _Optional[_Union[_booking_request_enums_pb2.BookingRequestSourcePlatform, str]] = ..., service_type_include: _Optional[int] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., no_start_time: bool = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., staff_name: _Optional[str] = ..., staff_id: _Optional[int] = ..., specific_dates: _Optional[_Iterable[str]] = ..., source: _Optional[_Union[_booking_request_models_pb2.BookingRequestModel.Source, str]] = ..., source_id: _Optional[int] = ...) -> None: ...

class GetBookingRequestResponse(_message.Message):
    __slots__ = ("booking_request", "service_details", "customer_detail", "pay", "has_request_update", "address", "assign_require", "order", "incomplete_details")
    BOOKING_REQUEST_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAILS_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_DETAIL_FIELD_NUMBER: _ClassVar[int]
    PAY_FIELD_NUMBER: _ClassVar[int]
    HAS_REQUEST_UPDATE_FIELD_NUMBER: _ClassVar[int]
    ADDRESS_FIELD_NUMBER: _ClassVar[int]
    ASSIGN_REQUIRE_FIELD_NUMBER: _ClassVar[int]
    ORDER_FIELD_NUMBER: _ClassVar[int]
    INCOMPLETE_DETAILS_FIELD_NUMBER: _ClassVar[int]
    booking_request: BookingRequestDetail
    service_details: _containers.RepeatedCompositeFieldContainer[ServiceDetail]
    customer_detail: CustomerDetail
    pay: PayBookingRequestView
    has_request_update: bool
    address: AddressDetail
    assign_require: AssignRequire
    order: OrderBookingRequestView
    incomplete_details: IncompleteDetails
    def __init__(self, booking_request: _Optional[_Union[BookingRequestDetail, _Mapping]] = ..., service_details: _Optional[_Iterable[_Union[ServiceDetail, _Mapping]]] = ..., customer_detail: _Optional[_Union[CustomerDetail, _Mapping]] = ..., pay: _Optional[_Union[PayBookingRequestView, _Mapping]] = ..., has_request_update: bool = ..., address: _Optional[_Union[AddressDetail, _Mapping]] = ..., assign_require: _Optional[_Union[AssignRequire, _Mapping]] = ..., order: _Optional[_Union[OrderBookingRequestView, _Mapping]] = ..., incomplete_details: _Optional[_Union[IncompleteDetails, _Mapping]] = ...) -> None: ...

class GroomingService(_message.Message):
    __slots__ = ("service", "addons", "auto_assign")
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    ADDONS_FIELD_NUMBER: _ClassVar[int]
    AUTO_ASSIGN_FIELD_NUMBER: _ClassVar[int]
    service: GroomingServiceDetail
    addons: _containers.RepeatedCompositeFieldContainer[GroomingAddOnDetail]
    auto_assign: GroomingAutoAssignDetail
    def __init__(self, service: _Optional[_Union[GroomingServiceDetail, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[GroomingAddOnDetail, _Mapping]]] = ..., auto_assign: _Optional[_Union[GroomingAutoAssignDetail, _Mapping]] = ...) -> None: ...

class GroomingServiceDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "pet_id", "staff_id", "service_id", "service_time", "service_price", "start_date", "start_time", "end_date", "end_time", "service_name", "staff_name", "price_override_type", "duration_override_type")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    STAFF_NAME_FIELD_NUMBER: _ClassVar[int]
    PRICE_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    DURATION_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    pet_id: int
    staff_id: int
    service_id: int
    service_time: int
    service_price: float
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    service_name: str
    staff_name: str
    price_override_type: _service_enum_pb2.ServiceOverrideType
    duration_override_type: _service_enum_pb2.ServiceOverrideType
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., service_id: _Optional[int] = ..., service_time: _Optional[int] = ..., service_price: _Optional[float] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., service_name: _Optional[str] = ..., staff_name: _Optional[str] = ..., price_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ..., duration_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ...) -> None: ...

class GroomingAddOnDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "service_detail_id", "pet_id", "staff_id", "add_on_id", "service_time", "service_price", "start_date", "start_time", "end_date", "end_time", "service_name", "staff_name")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    ADD_ON_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    STAFF_NAME_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    service_detail_id: int
    pet_id: int
    staff_id: int
    add_on_id: int
    service_time: int
    service_price: float
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    service_name: str
    staff_name: str
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., service_detail_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., add_on_id: _Optional[int] = ..., service_time: _Optional[int] = ..., service_price: _Optional[float] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., service_name: _Optional[str] = ..., staff_name: _Optional[str] = ...) -> None: ...

class BoardingService(_message.Message):
    __slots__ = ("service", "addons", "feeding", "medication", "auto_assign", "feedings", "medications")
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    ADDONS_FIELD_NUMBER: _ClassVar[int]
    FEEDING_FIELD_NUMBER: _ClassVar[int]
    MEDICATION_FIELD_NUMBER: _ClassVar[int]
    AUTO_ASSIGN_FIELD_NUMBER: _ClassVar[int]
    FEEDINGS_FIELD_NUMBER: _ClassVar[int]
    MEDICATIONS_FIELD_NUMBER: _ClassVar[int]
    service: BoardingServiceDetail
    addons: _containers.RepeatedCompositeFieldContainer[BoardingAddOnDetail]
    feeding: FeedingDetail
    medication: MedicationDetail
    auto_assign: BoardingAutoAssignDetail
    feedings: _containers.RepeatedCompositeFieldContainer[FeedingDetail]
    medications: _containers.RepeatedCompositeFieldContainer[MedicationDetail]
    def __init__(self, service: _Optional[_Union[BoardingServiceDetail, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[BoardingAddOnDetail, _Mapping]]] = ..., feeding: _Optional[_Union[FeedingDetail, _Mapping]] = ..., medication: _Optional[_Union[MedicationDetail, _Mapping]] = ..., auto_assign: _Optional[_Union[BoardingAutoAssignDetail, _Mapping]] = ..., feedings: _Optional[_Iterable[_Union[FeedingDetail, _Mapping]]] = ..., medications: _Optional[_Iterable[_Union[MedicationDetail, _Mapping]]] = ...) -> None: ...

class BoardingServiceDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "pet_id", "lodging_id", "service_id", "specific_dates", "service_price", "tax_id", "start_date", "start_time", "end_date", "end_time", "service_name", "lodging_unit_name", "lodging_type_name", "price_unit")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    LODGING_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    LODGING_UNIT_NAME_FIELD_NUMBER: _ClassVar[int]
    LODGING_TYPE_NAME_FIELD_NUMBER: _ClassVar[int]
    PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    pet_id: int
    lodging_id: int
    service_id: int
    specific_dates: _containers.RepeatedScalarFieldContainer[str]
    service_price: float
    tax_id: int
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    service_name: str
    lodging_unit_name: str
    lodging_type_name: str
    price_unit: _service_enum_pb2.ServicePriceUnit
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., lodging_id: _Optional[int] = ..., service_id: _Optional[int] = ..., specific_dates: _Optional[_Iterable[str]] = ..., service_price: _Optional[float] = ..., tax_id: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., service_name: _Optional[str] = ..., lodging_unit_name: _Optional[str] = ..., lodging_type_name: _Optional[str] = ..., price_unit: _Optional[_Union[_service_enum_pb2.ServicePriceUnit, str]] = ...) -> None: ...

class BoardingAddOnDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "service_detail_id", "pet_id", "add_on_id", "specific_dates", "is_everyday", "service_price", "tax_id", "duration", "service_name", "quantity_per_day", "require_dedicated_staff", "date_type", "start_date")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    ADD_ON_ID_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    IS_EVERYDAY_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_PER_DAY_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_STAFF_FIELD_NUMBER: _ClassVar[int]
    DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    service_detail_id: int
    pet_id: int
    add_on_id: int
    specific_dates: _containers.RepeatedScalarFieldContainer[str]
    is_everyday: bool
    service_price: float
    tax_id: int
    duration: int
    service_name: str
    quantity_per_day: int
    require_dedicated_staff: bool
    date_type: _pet_detail_enums_pb2.PetDetailDateType
    start_date: str
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., service_detail_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., add_on_id: _Optional[int] = ..., specific_dates: _Optional[_Iterable[str]] = ..., is_everyday: bool = ..., service_price: _Optional[float] = ..., tax_id: _Optional[int] = ..., duration: _Optional[int] = ..., service_name: _Optional[str] = ..., quantity_per_day: _Optional[int] = ..., require_dedicated_staff: bool = ..., date_type: _Optional[_Union[_pet_detail_enums_pb2.PetDetailDateType, str]] = ..., start_date: _Optional[str] = ...) -> None: ...

class FeedingDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "service_detail_id", "time", "amount", "unit", "food_type", "food_source", "instruction", "note", "amount_str")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_FIELD_NUMBER: _ClassVar[int]
    UNIT_FIELD_NUMBER: _ClassVar[int]
    FOOD_TYPE_FIELD_NUMBER: _ClassVar[int]
    FOOD_SOURCE_FIELD_NUMBER: _ClassVar[int]
    INSTRUCTION_FIELD_NUMBER: _ClassVar[int]
    NOTE_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_STR_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    service_detail_id: int
    time: _containers.RepeatedCompositeFieldContainer[_feeding_models_pb2.FeedingModel.FeedingSchedule]
    amount: float
    unit: str
    food_type: _containers.RepeatedScalarFieldContainer[str]
    food_source: str
    instruction: str
    note: str
    amount_str: str
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., service_detail_id: _Optional[int] = ..., time: _Optional[_Iterable[_Union[_feeding_models_pb2.FeedingModel.FeedingSchedule, _Mapping]]] = ..., amount: _Optional[float] = ..., unit: _Optional[str] = ..., food_type: _Optional[_Iterable[str]] = ..., food_source: _Optional[str] = ..., instruction: _Optional[str] = ..., note: _Optional[str] = ..., amount_str: _Optional[str] = ...) -> None: ...

class MedicationDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "service_detail_id", "time", "amount", "unit", "medication_name", "notes", "amount_str", "selected_date")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    TIME_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_FIELD_NUMBER: _ClassVar[int]
    UNIT_FIELD_NUMBER: _ClassVar[int]
    MEDICATION_NAME_FIELD_NUMBER: _ClassVar[int]
    NOTES_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_STR_FIELD_NUMBER: _ClassVar[int]
    SELECTED_DATE_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    service_detail_id: int
    time: _containers.RepeatedCompositeFieldContainer[_medication_models_pb2.MedicationModel.MedicationSchedule]
    amount: float
    unit: str
    medication_name: str
    notes: str
    amount_str: str
    selected_date: _appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef.SelectedDateDef
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., service_detail_id: _Optional[int] = ..., time: _Optional[_Iterable[_Union[_medication_models_pb2.MedicationModel.MedicationSchedule, _Mapping]]] = ..., amount: _Optional[float] = ..., unit: _Optional[str] = ..., medication_name: _Optional[str] = ..., notes: _Optional[str] = ..., amount_str: _Optional[str] = ..., selected_date: _Optional[_Union[_appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef.SelectedDateDef, _Mapping]] = ...) -> None: ...

class DaycareService(_message.Message):
    __slots__ = ("service", "addons", "feeding", "medication", "feedings", "medications")
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    ADDONS_FIELD_NUMBER: _ClassVar[int]
    FEEDING_FIELD_NUMBER: _ClassVar[int]
    MEDICATION_FIELD_NUMBER: _ClassVar[int]
    FEEDINGS_FIELD_NUMBER: _ClassVar[int]
    MEDICATIONS_FIELD_NUMBER: _ClassVar[int]
    service: DaycareServiceDetail
    addons: _containers.RepeatedCompositeFieldContainer[DaycareAddOnDetail]
    feeding: FeedingDetail
    medication: MedicationDetail
    feedings: _containers.RepeatedCompositeFieldContainer[FeedingDetail]
    medications: _containers.RepeatedCompositeFieldContainer[MedicationDetail]
    def __init__(self, service: _Optional[_Union[DaycareServiceDetail, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[DaycareAddOnDetail, _Mapping]]] = ..., feeding: _Optional[_Union[FeedingDetail, _Mapping]] = ..., medication: _Optional[_Union[MedicationDetail, _Mapping]] = ..., feedings: _Optional[_Iterable[_Union[FeedingDetail, _Mapping]]] = ..., medications: _Optional[_Iterable[_Union[MedicationDetail, _Mapping]]] = ...) -> None: ...

class DaycareServiceDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "pet_id", "service_id", "specific_dates", "service_price", "tax_id", "max_duration", "start_date", "start_time", "end_date", "end_time", "service_name")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    pet_id: int
    service_id: int
    specific_dates: _containers.RepeatedScalarFieldContainer[str]
    service_price: float
    tax_id: int
    max_duration: int
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    service_name: str
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., service_id: _Optional[int] = ..., specific_dates: _Optional[_Iterable[str]] = ..., service_price: _Optional[float] = ..., tax_id: _Optional[int] = ..., max_duration: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., service_name: _Optional[str] = ...) -> None: ...

class DaycareAddOnDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "service_detail_id", "pet_id", "add_on_id", "specific_dates", "is_everyday", "service_price", "tax_id", "duration", "service_name", "quantity_per_day", "require_dedicated_staff")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    ADD_ON_ID_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    IS_EVERYDAY_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_PER_DAY_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_STAFF_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    service_detail_id: int
    pet_id: int
    add_on_id: int
    specific_dates: _containers.RepeatedScalarFieldContainer[str]
    is_everyday: bool
    service_price: float
    tax_id: int
    duration: int
    service_name: str
    quantity_per_day: int
    require_dedicated_staff: bool
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., service_detail_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., add_on_id: _Optional[int] = ..., specific_dates: _Optional[_Iterable[str]] = ..., is_everyday: bool = ..., service_price: _Optional[float] = ..., tax_id: _Optional[int] = ..., duration: _Optional[int] = ..., service_name: _Optional[str] = ..., quantity_per_day: _Optional[int] = ..., require_dedicated_staff: bool = ...) -> None: ...

class EvaluationService(_message.Message):
    __slots__ = ("service",)
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    service: EvaluationTestDetail
    def __init__(self, service: _Optional[_Union[EvaluationTestDetail, _Mapping]] = ...) -> None: ...

class EvaluationTestDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "pet_id", "evaluation_id", "service_price", "duration", "start_date", "start_time", "end_date", "end_time", "service_name", "evaluation_name", "service_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    pet_id: int
    evaluation_id: int
    service_price: float
    duration: int
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    service_name: str
    evaluation_name: str
    service_id: int
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., evaluation_id: _Optional[int] = ..., service_price: _Optional[float] = ..., duration: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., service_name: _Optional[str] = ..., evaluation_name: _Optional[str] = ..., service_id: _Optional[int] = ...) -> None: ...

class DogWalkingService(_message.Message):
    __slots__ = ("service",)
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    service: DogWalkingServiceDetail
    def __init__(self, service: _Optional[_Union[DogWalkingServiceDetail, _Mapping]] = ...) -> None: ...

class DogWalkingServiceDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "pet_id", "staff_id", "service_id", "service_time", "service_price", "start_date", "start_time", "end_date", "end_time", "service_name", "staff_name", "price_override_type", "duration_override_type")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    STAFF_NAME_FIELD_NUMBER: _ClassVar[int]
    PRICE_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    DURATION_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    pet_id: int
    staff_id: int
    service_id: int
    service_time: int
    service_price: float
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    service_name: str
    staff_name: str
    price_override_type: _service_enum_pb2.ServiceOverrideType
    duration_override_type: _service_enum_pb2.ServiceOverrideType
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., service_id: _Optional[int] = ..., service_time: _Optional[int] = ..., service_price: _Optional[float] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., service_name: _Optional[str] = ..., staff_name: _Optional[str] = ..., price_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ..., duration_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ...) -> None: ...

class GroupClassService(_message.Message):
    __slots__ = ("service",)
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    service: GroupClassServiceDetail
    def __init__(self, service: _Optional[_Union[GroupClassServiceDetail, _Mapping]] = ...) -> None: ...

class GroupClassServiceDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "pet_id", "class_instance_id", "staff_id", "service_id", "specific_dates", "start_time", "end_time", "duration_per_session", "service_name", "staff_name", "instance_name", "num_sessions", "occurrence")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    CLASS_INSTANCE_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    DURATION_PER_SESSION_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    STAFF_NAME_FIELD_NUMBER: _ClassVar[int]
    INSTANCE_NAME_FIELD_NUMBER: _ClassVar[int]
    NUM_SESSIONS_FIELD_NUMBER: _ClassVar[int]
    OCCURRENCE_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    pet_id: int
    class_instance_id: int
    staff_id: int
    service_id: int
    specific_dates: _containers.RepeatedScalarFieldContainer[str]
    start_time: int
    end_time: int
    duration_per_session: int
    service_name: str
    staff_name: str
    instance_name: str
    num_sessions: int
    occurrence: _group_class_models_pb2.GroupClassInstance.Occurrence
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., class_instance_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., service_id: _Optional[int] = ..., specific_dates: _Optional[_Iterable[str]] = ..., start_time: _Optional[int] = ..., end_time: _Optional[int] = ..., duration_per_session: _Optional[int] = ..., service_name: _Optional[str] = ..., staff_name: _Optional[str] = ..., instance_name: _Optional[str] = ..., num_sessions: _Optional[int] = ..., occurrence: _Optional[_Union[_group_class_models_pb2.GroupClassInstance.Occurrence, _Mapping]] = ...) -> None: ...

class PetDetail(_message.Message):
    __slots__ = ("id", "pet_name", "avatar_path", "pet_type", "breed", "breed_mixed", "gender", "weight", "coat_type", "fixed", "behavior", "birthday", "passed_away", "deleted", "expiry_notification", "vet_name", "vet_phone_number", "vet_address", "emergency_contact_name", "emergency_contact_phone_number", "health_issues", "evaluation_status", "pet_codes", "question_answers", "pet_vaccines")
    ID_FIELD_NUMBER: _ClassVar[int]
    PET_NAME_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    PET_TYPE_FIELD_NUMBER: _ClassVar[int]
    BREED_FIELD_NUMBER: _ClassVar[int]
    BREED_MIXED_FIELD_NUMBER: _ClassVar[int]
    GENDER_FIELD_NUMBER: _ClassVar[int]
    WEIGHT_FIELD_NUMBER: _ClassVar[int]
    COAT_TYPE_FIELD_NUMBER: _ClassVar[int]
    FIXED_FIELD_NUMBER: _ClassVar[int]
    BEHAVIOR_FIELD_NUMBER: _ClassVar[int]
    BIRTHDAY_FIELD_NUMBER: _ClassVar[int]
    PASSED_AWAY_FIELD_NUMBER: _ClassVar[int]
    DELETED_FIELD_NUMBER: _ClassVar[int]
    EXPIRY_NOTIFICATION_FIELD_NUMBER: _ClassVar[int]
    VET_NAME_FIELD_NUMBER: _ClassVar[int]
    VET_PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    VET_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    EMERGENCY_CONTACT_NAME_FIELD_NUMBER: _ClassVar[int]
    EMERGENCY_CONTACT_PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    HEALTH_ISSUES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_STATUS_FIELD_NUMBER: _ClassVar[int]
    PET_CODES_FIELD_NUMBER: _ClassVar[int]
    QUESTION_ANSWERS_FIELD_NUMBER: _ClassVar[int]
    PET_VACCINES_FIELD_NUMBER: _ClassVar[int]
    id: int
    pet_name: str
    avatar_path: str
    pet_type: _customer_pet_enums_pb2.PetType
    breed: str
    breed_mixed: bool
    gender: _customer_pet_enums_pb2.PetGender
    weight: str
    coat_type: str
    fixed: str
    behavior: str
    birthday: _date_pb2.Date
    passed_away: bool
    deleted: bool
    expiry_notification: bool
    vet_name: str
    vet_phone_number: str
    vet_address: str
    emergency_contact_name: str
    emergency_contact_phone_number: str
    health_issues: str
    evaluation_status: _customer_pet_enums_pb2.EvaluationStatus
    pet_codes: _containers.RepeatedCompositeFieldContainer[PetCodeComposite]
    question_answers: _containers.RepeatedCompositeFieldContainer[QuestionAnswerDetail]
    pet_vaccines: _containers.RepeatedCompositeFieldContainer[PetVaccineComposite]
    def __init__(self, id: _Optional[int] = ..., pet_name: _Optional[str] = ..., avatar_path: _Optional[str] = ..., pet_type: _Optional[_Union[_customer_pet_enums_pb2.PetType, str]] = ..., breed: _Optional[str] = ..., breed_mixed: bool = ..., gender: _Optional[_Union[_customer_pet_enums_pb2.PetGender, str]] = ..., weight: _Optional[str] = ..., coat_type: _Optional[str] = ..., fixed: _Optional[str] = ..., behavior: _Optional[str] = ..., birthday: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., passed_away: bool = ..., deleted: bool = ..., expiry_notification: bool = ..., vet_name: _Optional[str] = ..., vet_phone_number: _Optional[str] = ..., vet_address: _Optional[str] = ..., emergency_contact_name: _Optional[str] = ..., emergency_contact_phone_number: _Optional[str] = ..., health_issues: _Optional[str] = ..., evaluation_status: _Optional[_Union[_customer_pet_enums_pb2.EvaluationStatus, str]] = ..., pet_codes: _Optional[_Iterable[_Union[PetCodeComposite, _Mapping]]] = ..., question_answers: _Optional[_Iterable[_Union[QuestionAnswerDetail, _Mapping]]] = ..., pet_vaccines: _Optional[_Iterable[_Union[PetVaccineComposite, _Mapping]]] = ...) -> None: ...

class PetVaccineComposite(_message.Message):
    __slots__ = ("vaccine_binding_id", "vaccine_id", "expiration_date", "document_urls", "vaccine_name")
    VACCINE_BINDING_ID_FIELD_NUMBER: _ClassVar[int]
    VACCINE_ID_FIELD_NUMBER: _ClassVar[int]
    EXPIRATION_DATE_FIELD_NUMBER: _ClassVar[int]
    DOCUMENT_URLS_FIELD_NUMBER: _ClassVar[int]
    VACCINE_NAME_FIELD_NUMBER: _ClassVar[int]
    vaccine_binding_id: int
    vaccine_id: int
    expiration_date: _date_pb2.Date
    document_urls: _containers.RepeatedScalarFieldContainer[str]
    vaccine_name: str
    def __init__(self, vaccine_binding_id: _Optional[int] = ..., vaccine_id: _Optional[int] = ..., expiration_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., document_urls: _Optional[_Iterable[str]] = ..., vaccine_name: _Optional[str] = ...) -> None: ...

class PetCodeComposite(_message.Message):
    __slots__ = ("id", "abbreviation", "description", "color", "sort", "deleted")
    ID_FIELD_NUMBER: _ClassVar[int]
    ABBREVIATION_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    COLOR_FIELD_NUMBER: _ClassVar[int]
    SORT_FIELD_NUMBER: _ClassVar[int]
    DELETED_FIELD_NUMBER: _ClassVar[int]
    id: int
    abbreviation: str
    description: str
    color: str
    sort: int
    deleted: bool
    def __init__(self, id: _Optional[int] = ..., abbreviation: _Optional[str] = ..., description: _Optional[str] = ..., color: _Optional[str] = ..., sort: _Optional[int] = ..., deleted: bool = ...) -> None: ...

class PayBookingRequestView(_message.Message):
    __slots__ = ("paid_amount", "pre_pay_amount", "refund_amount", "pre_pay_status", "pre_pay_rate", "pre_auth_enable")
    PAID_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    PRE_PAY_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    REFUND_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    PRE_PAY_STATUS_FIELD_NUMBER: _ClassVar[int]
    PRE_PAY_RATE_FIELD_NUMBER: _ClassVar[int]
    PRE_AUTH_ENABLE_FIELD_NUMBER: _ClassVar[int]
    paid_amount: float
    pre_pay_amount: float
    refund_amount: float
    pre_pay_status: int
    pre_pay_rate: float
    pre_auth_enable: bool
    def __init__(self, paid_amount: _Optional[float] = ..., pre_pay_amount: _Optional[float] = ..., refund_amount: _Optional[float] = ..., pre_pay_status: _Optional[int] = ..., pre_pay_rate: _Optional[float] = ..., pre_auth_enable: bool = ...) -> None: ...

class AddressDetail(_message.Message):
    __slots__ = ("address_id", "address1", "address2", "city", "state", "zipcode", "country", "lat", "lng", "is_primary", "is_profile_request_address", "customer_id")
    ADDRESS_ID_FIELD_NUMBER: _ClassVar[int]
    ADDRESS1_FIELD_NUMBER: _ClassVar[int]
    ADDRESS2_FIELD_NUMBER: _ClassVar[int]
    CITY_FIELD_NUMBER: _ClassVar[int]
    STATE_FIELD_NUMBER: _ClassVar[int]
    ZIPCODE_FIELD_NUMBER: _ClassVar[int]
    COUNTRY_FIELD_NUMBER: _ClassVar[int]
    LAT_FIELD_NUMBER: _ClassVar[int]
    LNG_FIELD_NUMBER: _ClassVar[int]
    IS_PRIMARY_FIELD_NUMBER: _ClassVar[int]
    IS_PROFILE_REQUEST_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    address_id: int
    address1: str
    address2: str
    city: str
    state: str
    zipcode: str
    country: str
    lat: str
    lng: str
    is_primary: bool
    is_profile_request_address: bool
    customer_id: int
    def __init__(self, address_id: _Optional[int] = ..., address1: _Optional[str] = ..., address2: _Optional[str] = ..., city: _Optional[str] = ..., state: _Optional[str] = ..., zipcode: _Optional[str] = ..., country: _Optional[str] = ..., lat: _Optional[str] = ..., lng: _Optional[str] = ..., is_primary: bool = ..., is_profile_request_address: bool = ..., customer_id: _Optional[int] = ...) -> None: ...

class QuestionAnswerDetail(_message.Message):
    __slots__ = ("key", "question", "answer")
    KEY_FIELD_NUMBER: _ClassVar[int]
    QUESTION_FIELD_NUMBER: _ClassVar[int]
    ANSWER_FIELD_NUMBER: _ClassVar[int]
    key: str
    question: str
    answer: str
    def __init__(self, key: _Optional[str] = ..., question: _Optional[str] = ..., answer: _Optional[str] = ...) -> None: ...

class GroomingAutoAssignDetail(_message.Message):
    __slots__ = ("id", "appointment_id", "staff_id", "appointment_time")
    ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_TIME_FIELD_NUMBER: _ClassVar[int]
    id: int
    appointment_id: int
    staff_id: int
    appointment_time: int
    def __init__(self, id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., appointment_time: _Optional[int] = ...) -> None: ...

class BoardingAutoAssignDetail(_message.Message):
    __slots__ = ("id", "booking_request_id", "boarding_service_detail_id", "lodging_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    BOARDING_SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    LODGING_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    boarding_service_detail_id: int
    lodging_id: int
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., boarding_service_detail_id: _Optional[int] = ..., lodging_id: _Optional[int] = ...) -> None: ...

class AssignRequire(_message.Message):
    __slots__ = ("room_assign_requires", "staff_assign_require")
    ROOM_ASSIGN_REQUIRES_FIELD_NUMBER: _ClassVar[int]
    STAFF_ASSIGN_REQUIRE_FIELD_NUMBER: _ClassVar[int]
    room_assign_requires: _containers.RepeatedCompositeFieldContainer[RoomAssignRequire]
    staff_assign_require: _containers.RepeatedCompositeFieldContainer[StaffAssignRequire]
    def __init__(self, room_assign_requires: _Optional[_Iterable[_Union[RoomAssignRequire, _Mapping]]] = ..., staff_assign_require: _Optional[_Iterable[_Union[StaffAssignRequire, _Mapping]]] = ...) -> None: ...

class IncompleteDetails(_message.Message):
    __slots__ = ("grooming_services", "boarding_services", "daycare_services", "evaluation_services", "grooming_addons", "boarding_addons", "daycare_addons")
    class GroomingService(_message.Message):
        __slots__ = ("service_detail", "service")
        SERVICE_DETAIL_FIELD_NUMBER: _ClassVar[int]
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        service_detail: _grooming_service_detail_models_pb2.GroomingServiceDetailModel
        service: _service_models_pb2.ServiceBriefView
        def __init__(self, service_detail: _Optional[_Union[_grooming_service_detail_models_pb2.GroomingServiceDetailModel, _Mapping]] = ..., service: _Optional[_Union[_service_models_pb2.ServiceBriefView, _Mapping]] = ...) -> None: ...
    class BoardingService(_message.Message):
        __slots__ = ("service_detail", "service", "missing_evaluation")
        SERVICE_DETAIL_FIELD_NUMBER: _ClassVar[int]
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        MISSING_EVALUATION_FIELD_NUMBER: _ClassVar[int]
        service_detail: _boarding_service_detail_models_pb2.BoardingServiceDetailModel
        service: _service_models_pb2.ServiceBriefView
        missing_evaluation: _evaluation_models_pb2.EvaluationBriefView
        def __init__(self, service_detail: _Optional[_Union[_boarding_service_detail_models_pb2.BoardingServiceDetailModel, _Mapping]] = ..., service: _Optional[_Union[_service_models_pb2.ServiceBriefView, _Mapping]] = ..., missing_evaluation: _Optional[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]] = ...) -> None: ...
    class DaycareService(_message.Message):
        __slots__ = ("service_detail", "service", "missing_evaluation")
        SERVICE_DETAIL_FIELD_NUMBER: _ClassVar[int]
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        MISSING_EVALUATION_FIELD_NUMBER: _ClassVar[int]
        service_detail: _daycare_service_detail_models_pb2.DaycareServiceDetailModel
        service: _service_models_pb2.ServiceBriefView
        missing_evaluation: _evaluation_models_pb2.EvaluationBriefView
        def __init__(self, service_detail: _Optional[_Union[_daycare_service_detail_models_pb2.DaycareServiceDetailModel, _Mapping]] = ..., service: _Optional[_Union[_service_models_pb2.ServiceBriefView, _Mapping]] = ..., missing_evaluation: _Optional[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]] = ...) -> None: ...
    class EvaluationService(_message.Message):
        __slots__ = ("service_detail", "evaluation")
        SERVICE_DETAIL_FIELD_NUMBER: _ClassVar[int]
        EVALUATION_FIELD_NUMBER: _ClassVar[int]
        service_detail: _evaluation_test_detail_models_pb2.EvaluationTestDetailModel
        evaluation: _evaluation_models_pb2.EvaluationBriefView
        def __init__(self, service_detail: _Optional[_Union[_evaluation_test_detail_models_pb2.EvaluationTestDetailModel, _Mapping]] = ..., evaluation: _Optional[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]] = ...) -> None: ...
    class GroomingAddon(_message.Message):
        __slots__ = ("addon_detail", "service")
        ADDON_DETAIL_FIELD_NUMBER: _ClassVar[int]
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        addon_detail: _grooming_add_on_detail_models_pb2.GroomingAddOnDetailModel
        service: _service_models_pb2.ServiceBriefView
        def __init__(self, addon_detail: _Optional[_Union[_grooming_add_on_detail_models_pb2.GroomingAddOnDetailModel, _Mapping]] = ..., service: _Optional[_Union[_service_models_pb2.ServiceBriefView, _Mapping]] = ...) -> None: ...
    class BoardingAddon(_message.Message):
        __slots__ = ("addon_detail", "service")
        ADDON_DETAIL_FIELD_NUMBER: _ClassVar[int]
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        addon_detail: _boarding_add_on_detail_models_pb2.BoardingAddOnDetailModel
        service: _service_models_pb2.ServiceBriefView
        def __init__(self, addon_detail: _Optional[_Union[_boarding_add_on_detail_models_pb2.BoardingAddOnDetailModel, _Mapping]] = ..., service: _Optional[_Union[_service_models_pb2.ServiceBriefView, _Mapping]] = ...) -> None: ...
    class DaycareAddon(_message.Message):
        __slots__ = ("addon_detail", "service")
        ADDON_DETAIL_FIELD_NUMBER: _ClassVar[int]
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        addon_detail: _daycare_add_on_detail_models_pb2.DaycareAddOnDetailModel
        service: _service_models_pb2.ServiceBriefView
        def __init__(self, addon_detail: _Optional[_Union[_daycare_add_on_detail_models_pb2.DaycareAddOnDetailModel, _Mapping]] = ..., service: _Optional[_Union[_service_models_pb2.ServiceBriefView, _Mapping]] = ...) -> None: ...
    GROOMING_SERVICES_FIELD_NUMBER: _ClassVar[int]
    BOARDING_SERVICES_FIELD_NUMBER: _ClassVar[int]
    DAYCARE_SERVICES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_SERVICES_FIELD_NUMBER: _ClassVar[int]
    GROOMING_ADDONS_FIELD_NUMBER: _ClassVar[int]
    BOARDING_ADDONS_FIELD_NUMBER: _ClassVar[int]
    DAYCARE_ADDONS_FIELD_NUMBER: _ClassVar[int]
    grooming_services: _containers.RepeatedCompositeFieldContainer[IncompleteDetails.GroomingService]
    boarding_services: _containers.RepeatedCompositeFieldContainer[IncompleteDetails.BoardingService]
    daycare_services: _containers.RepeatedCompositeFieldContainer[IncompleteDetails.DaycareService]
    evaluation_services: _containers.RepeatedCompositeFieldContainer[IncompleteDetails.EvaluationService]
    grooming_addons: _containers.RepeatedCompositeFieldContainer[IncompleteDetails.GroomingAddon]
    boarding_addons: _containers.RepeatedCompositeFieldContainer[IncompleteDetails.BoardingAddon]
    daycare_addons: _containers.RepeatedCompositeFieldContainer[IncompleteDetails.DaycareAddon]
    def __init__(self, grooming_services: _Optional[_Iterable[_Union[IncompleteDetails.GroomingService, _Mapping]]] = ..., boarding_services: _Optional[_Iterable[_Union[IncompleteDetails.BoardingService, _Mapping]]] = ..., daycare_services: _Optional[_Iterable[_Union[IncompleteDetails.DaycareService, _Mapping]]] = ..., evaluation_services: _Optional[_Iterable[_Union[IncompleteDetails.EvaluationService, _Mapping]]] = ..., grooming_addons: _Optional[_Iterable[_Union[IncompleteDetails.GroomingAddon, _Mapping]]] = ..., boarding_addons: _Optional[_Iterable[_Union[IncompleteDetails.BoardingAddon, _Mapping]]] = ..., daycare_addons: _Optional[_Iterable[_Union[IncompleteDetails.DaycareAddon, _Mapping]]] = ...) -> None: ...

class RoomAssignRequire(_message.Message):
    __slots__ = ("pet_id", "pet_name", "service_id", "start_date", "end_date")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PET_NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    pet_name: str
    service_id: int
    start_date: str
    end_date: str
    def __init__(self, pet_id: _Optional[int] = ..., pet_name: _Optional[str] = ..., service_id: _Optional[int] = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ...) -> None: ...

class StaffAssignRequire(_message.Message):
    __slots__ = ("pet_id", "pet_name", "service_id", "duration", "service_name", "specific_dates")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PET_NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    pet_name: str
    service_id: int
    duration: int
    service_name: str
    specific_dates: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, pet_id: _Optional[int] = ..., pet_name: _Optional[str] = ..., service_id: _Optional[int] = ..., duration: _Optional[int] = ..., service_name: _Optional[str] = ..., specific_dates: _Optional[_Iterable[str]] = ...) -> None: ...

class OrderBookingRequestView(_message.Message):
    __slots__ = ("discount_code_name",)
    DISCOUNT_CODE_NAME_FIELD_NUMBER: _ClassVar[int]
    discount_code_name: str
    def __init__(self, discount_code_name: _Optional[str] = ...) -> None: ...

class AcceptBookingRequestRequest(_message.Message):
    __slots__ = ("id", "pet_to_lodgings", "pet_to_staffs", "pet_to_services", "evaluation_pet_to_staffs")
    ID_FIELD_NUMBER: _ClassVar[int]
    PET_TO_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    PET_TO_STAFFS_FIELD_NUMBER: _ClassVar[int]
    PET_TO_SERVICES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_PET_TO_STAFFS_FIELD_NUMBER: _ClassVar[int]
    id: int
    pet_to_lodgings: _containers.RepeatedCompositeFieldContainer[PetToLodging]
    pet_to_staffs: _containers.RepeatedCompositeFieldContainer[PetToStaff]
    pet_to_services: _containers.RepeatedCompositeFieldContainer[PetToService]
    evaluation_pet_to_staffs: _containers.RepeatedCompositeFieldContainer[PetToStaff]
    def __init__(self, id: _Optional[int] = ..., pet_to_lodgings: _Optional[_Iterable[_Union[PetToLodging, _Mapping]]] = ..., pet_to_staffs: _Optional[_Iterable[_Union[PetToStaff, _Mapping]]] = ..., pet_to_services: _Optional[_Iterable[_Union[PetToService, _Mapping]]] = ..., evaluation_pet_to_staffs: _Optional[_Iterable[_Union[PetToStaff, _Mapping]]] = ...) -> None: ...

class PetToLodging(_message.Message):
    __slots__ = ("pet_id", "lodging_unit_id")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    LODGING_UNIT_ID_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    lodging_unit_id: int
    def __init__(self, pet_id: _Optional[int] = ..., lodging_unit_id: _Optional[int] = ...) -> None: ...

class PetToStaff(_message.Message):
    __slots__ = ("pet_id", "service_id", "staff_id", "start_time")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    service_id: int
    staff_id: int
    start_time: int
    def __init__(self, pet_id: _Optional[int] = ..., service_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., start_time: _Optional[int] = ...) -> None: ...

class PetToService(_message.Message):
    __slots__ = ("pet_id", "from_evaluation_service_id", "to_evaluation_service_id")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    FROM_EVALUATION_SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    TO_EVALUATION_SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    from_evaluation_service_id: int
    to_evaluation_service_id: int
    def __init__(self, pet_id: _Optional[int] = ..., from_evaluation_service_id: _Optional[int] = ..., to_evaluation_service_id: _Optional[int] = ...) -> None: ...

class AutoAssignRequest(_message.Message):
    __slots__ = ("booking_request_id",)
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    booking_request_id: int
    def __init__(self, booking_request_id: _Optional[int] = ...) -> None: ...

class AutoAssignResponse(_message.Message):
    __slots__ = ("boarding_assign_requires", "pet_to_lodgings", "evaluation_assign_requires", "evaluation_pet_to_staffs", "lodgings", "staffs")
    class AssignRequire(_message.Message):
        __slots__ = ("pet_id", "service_id", "start_date", "end_date")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        END_DATE_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        service_id: int
        start_date: str
        end_date: str
        def __init__(self, pet_id: _Optional[int] = ..., service_id: _Optional[int] = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ...) -> None: ...
    class LodgingDetail(_message.Message):
        __slots__ = ("lodging_id", "lodging_unit_name", "lodging_type_name")
        LODGING_ID_FIELD_NUMBER: _ClassVar[int]
        LODGING_UNIT_NAME_FIELD_NUMBER: _ClassVar[int]
        LODGING_TYPE_NAME_FIELD_NUMBER: _ClassVar[int]
        lodging_id: int
        lodging_unit_name: str
        lodging_type_name: str
        def __init__(self, lodging_id: _Optional[int] = ..., lodging_unit_name: _Optional[str] = ..., lodging_type_name: _Optional[str] = ...) -> None: ...
    class StaffDetail(_message.Message):
        __slots__ = ("id", "first_name", "last_name")
        ID_FIELD_NUMBER: _ClassVar[int]
        FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
        LAST_NAME_FIELD_NUMBER: _ClassVar[int]
        id: int
        first_name: str
        last_name: str
        def __init__(self, id: _Optional[int] = ..., first_name: _Optional[str] = ..., last_name: _Optional[str] = ...) -> None: ...
    BOARDING_ASSIGN_REQUIRES_FIELD_NUMBER: _ClassVar[int]
    PET_TO_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_ASSIGN_REQUIRES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_PET_TO_STAFFS_FIELD_NUMBER: _ClassVar[int]
    LODGINGS_FIELD_NUMBER: _ClassVar[int]
    STAFFS_FIELD_NUMBER: _ClassVar[int]
    boarding_assign_requires: _containers.RepeatedCompositeFieldContainer[AutoAssignResponse.AssignRequire]
    pet_to_lodgings: _containers.RepeatedCompositeFieldContainer[PetToLodging]
    evaluation_assign_requires: _containers.RepeatedCompositeFieldContainer[AutoAssignResponse.AssignRequire]
    evaluation_pet_to_staffs: _containers.RepeatedCompositeFieldContainer[PetToStaff]
    lodgings: _containers.RepeatedCompositeFieldContainer[AutoAssignResponse.LodgingDetail]
    staffs: _containers.RepeatedCompositeFieldContainer[AutoAssignResponse.StaffDetail]
    def __init__(self, boarding_assign_requires: _Optional[_Iterable[_Union[AutoAssignResponse.AssignRequire, _Mapping]]] = ..., pet_to_lodgings: _Optional[_Iterable[_Union[PetToLodging, _Mapping]]] = ..., evaluation_assign_requires: _Optional[_Iterable[_Union[AutoAssignResponse.AssignRequire, _Mapping]]] = ..., evaluation_pet_to_staffs: _Optional[_Iterable[_Union[PetToStaff, _Mapping]]] = ..., lodgings: _Optional[_Iterable[_Union[AutoAssignResponse.LodgingDetail, _Mapping]]] = ..., staffs: _Optional[_Iterable[_Union[AutoAssignResponse.StaffDetail, _Mapping]]] = ...) -> None: ...

class AcceptBookingRequestResponse(_message.Message):
    __slots__ = ("result", "appointment_id")
    RESULT_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    result: bool
    appointment_id: int
    def __init__(self, result: bool = ..., appointment_id: _Optional[int] = ...) -> None: ...

class DeclineBookingRequestRequest(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class DeclineBookingRequestResponse(_message.Message):
    __slots__ = ("result", "appointment_id")
    RESULT_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    result: bool
    appointment_id: int
    def __init__(self, result: bool = ..., appointment_id: _Optional[int] = ...) -> None: ...

class AcceptBookingRequestV2Request(_message.Message):
    __slots__ = ("id", "grooming_services", "boarding_services", "daycare_services", "evaluation_services", "grooming_addons", "boarding_addons", "daycare_addons", "create_evaluation_requests")
    ID_FIELD_NUMBER: _ClassVar[int]
    GROOMING_SERVICES_FIELD_NUMBER: _ClassVar[int]
    BOARDING_SERVICES_FIELD_NUMBER: _ClassVar[int]
    DAYCARE_SERVICES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_SERVICES_FIELD_NUMBER: _ClassVar[int]
    GROOMING_ADDONS_FIELD_NUMBER: _ClassVar[int]
    BOARDING_ADDONS_FIELD_NUMBER: _ClassVar[int]
    DAYCARE_ADDONS_FIELD_NUMBER: _ClassVar[int]
    CREATE_EVALUATION_REQUESTS_FIELD_NUMBER: _ClassVar[int]
    id: int
    grooming_services: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AcceptBookingRequestV2Request.GroomingService]
    boarding_services: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AcceptBookingRequestV2Request.BoardingService]
    daycare_services: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AcceptBookingRequestV2Request.DaycareService]
    evaluation_services: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AcceptBookingRequestV2Request.EvaluationService]
    grooming_addons: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AcceptBookingRequestV2Request.GroomingAddon]
    boarding_addons: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AcceptBookingRequestV2Request.BoardingAddon]
    daycare_addons: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AcceptBookingRequestV2Request.DaycareAddon]
    create_evaluation_requests: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AcceptBookingRequestV2Request.CreateEvaluationRequest]
    def __init__(self, id: _Optional[int] = ..., grooming_services: _Optional[_Iterable[_Union[_booking_request_service_pb2.AcceptBookingRequestV2Request.GroomingService, _Mapping]]] = ..., boarding_services: _Optional[_Iterable[_Union[_booking_request_service_pb2.AcceptBookingRequestV2Request.BoardingService, _Mapping]]] = ..., daycare_services: _Optional[_Iterable[_Union[_booking_request_service_pb2.AcceptBookingRequestV2Request.DaycareService, _Mapping]]] = ..., evaluation_services: _Optional[_Iterable[_Union[_booking_request_service_pb2.AcceptBookingRequestV2Request.EvaluationService, _Mapping]]] = ..., grooming_addons: _Optional[_Iterable[_Union[_booking_request_service_pb2.AcceptBookingRequestV2Request.GroomingAddon, _Mapping]]] = ..., boarding_addons: _Optional[_Iterable[_Union[_booking_request_service_pb2.AcceptBookingRequestV2Request.BoardingAddon, _Mapping]]] = ..., daycare_addons: _Optional[_Iterable[_Union[_booking_request_service_pb2.AcceptBookingRequestV2Request.DaycareAddon, _Mapping]]] = ..., create_evaluation_requests: _Optional[_Iterable[_Union[_booking_request_service_pb2.AcceptBookingRequestV2Request.CreateEvaluationRequest, _Mapping]]] = ...) -> None: ...

class AcceptBookingRequestV2Response(_message.Message):
    __slots__ = ("result", "appointment_id")
    RESULT_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    result: bool
    appointment_id: int
    def __init__(self, result: bool = ..., appointment_id: _Optional[int] = ...) -> None: ...

class AutoAssignV2Request(_message.Message):
    __slots__ = ("booking_request_id",)
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    booking_request_id: int
    def __init__(self, booking_request_id: _Optional[int] = ...) -> None: ...

class AutoAssignV2Response(_message.Message):
    __slots__ = ("boarding_services", "evaluation_services")
    BOARDING_SERVICES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_SERVICES_FIELD_NUMBER: _ClassVar[int]
    boarding_services: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AutoAssignResponse.BoardingService]
    evaluation_services: _containers.RepeatedCompositeFieldContainer[_booking_request_service_pb2.AutoAssignResponse.EvaluationService]
    def __init__(self, boarding_services: _Optional[_Iterable[_Union[_booking_request_service_pb2.AutoAssignResponse.BoardingService, _Mapping]]] = ..., evaluation_services: _Optional[_Iterable[_Union[_booking_request_service_pb2.AutoAssignResponse.EvaluationService, _Mapping]]] = ...) -> None: ...

class MoveBookingRequestToWaitlistParams(_message.Message):
    __slots__ = ("business_id", "booking_request_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    booking_request_id: int
    def __init__(self, business_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ...) -> None: ...

class MoveBookingRequestToWaitlistResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
