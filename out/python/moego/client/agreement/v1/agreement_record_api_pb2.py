# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/agreement/v1/agreement_record_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/agreement/v1/agreement_record_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.agreement.v1 import agreement_enums_pb2 as moego_dot_models_dot_agreement_dot_v1_dot_agreement__enums__pb2
from moego.models.agreement.v1 import agreement_models_pb2 as moego_dot_models_dot_agreement_dot_v1_dot_agreement__models__pb2
from moego.models.agreement.v1 import agreement_record_models_pb2 as moego_dot_models_dot_agreement_dot_v1_dot_agreement__record__models__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4moego/client/agreement/v1/agreement_record_api.proto\x12\x19moego.client.agreement.v1\x1a/moego/models/agreement/v1/agreement_enums.proto\x1a\x30moego/models/agreement/v1/agreement_models.proto\x1a\x37moego/models/agreement/v1/agreement_record_models.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xe2\x02\n\x13GetRecordListParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12]\n\rsigned_status\x18\x03 \x01(\x0e\x32\'.moego.models.agreement.v1.SignedStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x0csignedStatus\x88\x01\x01\x12K\n\npagination\x18\x04 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npagination\x12\x33\n\x13list_company_record\x18\x05 \x01(\x08H\x02R\x11listCompanyRecord\x88\x01\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x10\n\x0e_signed_statusB\x16\n\x14_list_company_record\"\xb9\x02\n\x13GetRecordListResult\x12u\n\x1c\x61greement_record_simple_view\x18\x01 \x03(\x0b\x32\x34.moego.models.agreement.v1.AgreementRecordSimpleViewR\x19\x61greementRecordSimpleView\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x12g\n\x15\x61greement_simple_view\x18\x03 \x03(\x0b\x32\x33.moego.models.agreement.v1.AgreementModelSimpleViewR\x13\x61greementSimpleView2\x89\x01\n\x16\x41greementRecordService\x12o\n\rGetRecordList\x12..moego.client.agreement.v1.GetRecordListParams\x1a..moego.client.agreement.v1.GetRecordListResultB\x84\x01\n!com.moego.idl.client.agreement.v1P\x01Z]github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/agreement/v1;agreementapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.agreement.v1.agreement_record_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.client.agreement.v1P\001Z]github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/agreement/v1;agreementapipb'
  _globals['_GETRECORDLISTPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETRECORDLISTPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETRECORDLISTPARAMS'].fields_by_name['signed_status']._loaded_options = None
  _globals['_GETRECORDLISTPARAMS'].fields_by_name['signed_status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECORDLISTPARAMS'].fields_by_name['pagination']._loaded_options = None
  _globals['_GETRECORDLISTPARAMS'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETRECORDLISTPARAMS']._serialized_start=307
  _globals['_GETRECORDLISTPARAMS']._serialized_end=661
  _globals['_GETRECORDLISTRESULT']._serialized_start=664
  _globals['_GETRECORDLISTRESULT']._serialized_end=977
  _globals['_AGREEMENTRECORDSERVICE']._serialized_start=980
  _globals['_AGREEMENTRECORDSERVICE']._serialized_end=1117
# @@protoc_insertion_point(module_scope)
