from moego.models.agreement.v1 import agreement_enums_pb2 as _agreement_enums_pb2
from moego.models.agreement.v1 import agreement_models_pb2 as _agreement_models_pb2
from moego.models.agreement.v1 import agreement_record_models_pb2 as _agreement_record_models_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetRecordListParams(_message.Message):
    __slots__ = ("name", "domain", "signed_status", "pagination", "list_company_record")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    SIGNED_STATUS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    LIST_COMPANY_RECORD_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    signed_status: _agreement_enums_pb2.SignedStatus
    pagination: _pagination_messages_pb2.PaginationRequest
    list_company_record: bool
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., signed_status: _Optional[_Union[_agreement_enums_pb2.SignedStatus, str]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., list_company_record: bool = ...) -> None: ...

class GetRecordListResult(_message.Message):
    __slots__ = ("agreement_record_simple_view", "pagination", "agreement_simple_view")
    AGREEMENT_RECORD_SIMPLE_VIEW_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_SIMPLE_VIEW_FIELD_NUMBER: _ClassVar[int]
    agreement_record_simple_view: _containers.RepeatedCompositeFieldContainer[_agreement_record_models_pb2.AgreementRecordSimpleView]
    pagination: _pagination_messages_pb2.PaginationResponse
    agreement_simple_view: _containers.RepeatedCompositeFieldContainer[_agreement_models_pb2.AgreementModelSimpleView]
    def __init__(self, agreement_record_simple_view: _Optional[_Iterable[_Union[_agreement_record_models_pb2.AgreementRecordSimpleView, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., agreement_simple_view: _Optional[_Iterable[_Union[_agreement_models_pb2.AgreementModelSimpleView, _Mapping]]] = ...) -> None: ...
