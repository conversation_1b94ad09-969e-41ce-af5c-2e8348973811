# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/online_booking/v1/appointment_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/online_booking/v1/appointment_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.api.offering.v1 import group_class_api_pb2 as moego_dot_api_dot_offering_dot_v1_dot_group__class__api__pb2
from moego.models.appointment.v1 import appointment_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__enums__pb2
from moego.models.appointment.v1 import appointment_pet_feeding_schedule_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__pet__feeding__schedule__defs__pb2
from moego.models.appointment.v1 import appointment_pet_medication_schedule_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__pet__medication__schedule__defs__pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__enums__pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.offering.v1 import service_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__models__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4moego/client/online_booking/v1/appointment_api.proto\x12\x1emoego.client.online_booking.v1\x1a\x16google/type/date.proto\x1a+moego/api/offering/v1/group_class_api.proto\x1a\x33moego/models/appointment/v1/appointment_enums.proto\x1aGmoego/models/appointment/v1/appointment_pet_feeding_schedule_defs.proto\x1aJmoego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto\x1a\x32moego/models/appointment/v1/pet_detail_enums.proto\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a-moego/models/offering/v1/service_models.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"d\n GetPriorityAppointmentCardParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omainB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\x81\x01\n GetPriorityAppointmentCardResult\x12G\n\x04\x63\x61rd\x18\x01 \x01(\x0b\x32\x33.moego.client.online_booking.v1.AppointmentCardItemR\x04\x63\x61rd\x12\x14\n\x05\x63ount\x18\x02 \x01(\x05R\x05\x63ount\"\xa6\x02\n\x13\x41ppointmentCardItem\x12P\n\tcard_type\x18\x01 \x01(\x0e\x32\x33.moego.client.online_booking.v1.AppointmentCardTypeR\x08\x63\x61rdType\x12X\n\x0b\x61ppointment\x18\x02 \x01(\x0b\x32\x36.moego.client.online_booking.v1.AppointmentSummaryItemR\x0b\x61ppointment\x12\x63\n\x10pet_and_services\x18\x03 \x03(\x0b\x32\x39.moego.client.online_booking.v1.PetAndServicesSummaryItemR\x0epetAndServices\"\xb6\x03\n\x16\x41ppointmentSummaryItem\x12.\n\x12\x62ooking_request_id\x18\x01 \x01(\x03H\x00R\x10\x62ookingRequestId\x12\'\n\x0e\x61ppointment_id\x18\x02 \x01(\x03H\x00R\rappointmentId\x12\"\n\nstart_date\x18\x03 \x01(\tH\x01R\tstartDate\x88\x01\x01\x12\"\n\nstart_time\x18\x04 \x01(\x05H\x02R\tstartTime\x88\x01\x01\x12\x1e\n\x08\x65nd_date\x18\x05 \x01(\tH\x03R\x07\x65ndDate\x88\x01\x01\x12\x1e\n\x08\x65nd_time\x18\x06 \x01(\x05H\x04R\x07\x65ndTime\x88\x01\x01\x12O\n\x0emain_care_type\x18\x07 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0cmainCareType\x12,\n\x12is_booking_request\x18\x08 \x01(\x08R\x10isBookingRequestB\x04\n\x02idB\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x0b\n\t_end_dateB\x0b\n\t_end_time\"\xda\x05\n\x19PetAndServicesSummaryItem\x12S\n\x03pet\x18\x01 \x01(\x0b\x32\x41.moego.client.online_booking.v1.PetAndServicesSummaryItem.PetItemR\x03pet\x12\x61\n\x08services\x18\x02 \x03(\x0b\x32\x45.moego.client.online_booking.v1.PetAndServicesSummaryItem.ServiceItemR\x08services\x1a\x93\x01\n\x07PetItem\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n\x08pet_name\x18\x02 \x01(\tR\x07petName\x12<\n\x08pet_type\x18\x03 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\x12\x1f\n\x0b\x61vatar_path\x18\x04 \x01(\tR\navatarPath\x1a\xee\x02\n\x0bServiceItem\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12!\n\x0cservice_name\x18\x02 \x01(\tR\x0bserviceName\x12\x46\n\tcare_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x08\x63\x61reType\x12\x35\n\nstart_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateH\x00R\tstartDate\x88\x01\x01\x12\"\n\nstart_time\x18\x05 \x01(\x05H\x01R\tstartTime\x88\x01\x01\x12\x31\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x11.google.type.DateH\x02R\x07\x65ndDate\x88\x01\x01\x12\x1e\n\x08\x65nd_time\x18\x07 \x01(\x05H\x03R\x07\x65ndTime\x88\x01\x01\x42\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x0b\n\t_end_dateB\x0b\n\t_end_time\"\x96\x02\n\x16\x41ppointmentPaymentItem\x12\x32\n\x15\x65stimated_total_price\x18\x01 \x01(\x01R\x13\x65stimatedTotalPrice\x12!\n\x0ctotal_amount\x18\x02 \x01(\x01R\x0btotalAmount\x12\\\n\x0epayment_status\x18\x03 \x01(\x0e\x32\x35.moego.models.appointment.v1.AppointmentPaymentStatusR\rpaymentStatus\x12G\n evaluation_estimated_total_price\x18\x04 \x01(\x01R\x1d\x65valuationEstimatedTotalPrice\"\xa2\x06\n\x16ListAppointmentsParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12U\n\x06\x66ilter\x18\x03 \x01(\x0b\x32=.moego.client.online_booking.v1.ListAppointmentsParams.FilterR\x06\x66ilter\x12\x41\n\npagination\x18\x04 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npagination\x12_\n\x05sorts\x18\x05 \x03(\x0b\x32I.moego.client.online_booking.v1.ListAppointmentsParams.AppointmentSortDefR\x05sorts\x1a\x87\x01\n\x06\x46ilter\x12}\n\x10\x61ppointment_type\x18\x01 \x01(\x0e\x32\x46.moego.client.online_booking.v1.ListAppointmentsParams.AppointmentTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0f\x61ppointmentType\x1a\x89\x01\n\x12\x41ppointmentSortDef\x12\x61\n\x05\x66ield\x18\x01 \x01(\x0e\x32K.moego.client.online_booking.v1.ListAppointmentsParams.AppointmentSortFieldR\x05\x66ield\x12\x10\n\x03\x61sc\x18\x02 \x01(\x08R\x03\x61sc\"f\n\x0f\x41ppointmentType\x12 \n\x1c\x41PPOINTMENT_TYPE_UNSPECIFIED\x10\x00\x12\x0b\n\x07PENDING\x10\x01\x12\x0c\n\x08UPCOMING\x10\x02\x12\x08\n\x04PAST\x10\x03\x12\x0c\n\x08\x43\x41NCELED\x10\x04\"M\n\x14\x41ppointmentSortField\x12&\n\"APPOINTMENT_SORT_FIELD_UNSPECIFIED\x10\x00\x12\r\n\tDATE_TIME\x10\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\xf5\x03\n\x16ListAppointmentsResult\x12n\n\x0c\x61ppointments\x18\x01 \x03(\x0b\x32J.moego.client.online_booking.v1.ListAppointmentsResult.AppointmentListItemR\x0c\x61ppointments\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x1a\xa6\x02\n\x13\x41ppointmentListItem\x12X\n\x0b\x61ppointment\x18\x01 \x01(\x0b\x32\x36.moego.client.online_booking.v1.AppointmentSummaryItemR\x0b\x61ppointment\x12\x63\n\x10pet_and_services\x18\x02 \x03(\x0b\x32\x39.moego.client.online_booking.v1.PetAndServicesSummaryItemR\x0epetAndServices\x12P\n\x07payment\x18\x03 \x01(\x0b\x32\x36.moego.client.online_booking.v1.AppointmentPaymentItemR\x07payment\"\xbd\x01\n\x1aGetAppointmentDetailParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\'\n\x0e\x61ppointment_id\x18\x03 \x01(\x03H\x01R\rappointmentId\x12.\n\x12\x62ooking_request_id\x18\x04 \x01(\x03H\x01R\x10\x62ookingRequestIdB\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x04\n\x02id\"\x95\x1d\n\x1aGetAppointmentDetailResult\x12l\n\x0b\x61ppointment\x18\x01 \x01(\x0b\x32J.moego.client.online_booking.v1.GetAppointmentDetailResult.AppointmentItemR\x0b\x61ppointment\x12}\n\x10pet_and_services\x18\x02 \x03(\x0b\x32S.moego.client.online_booking.v1.GetAppointmentDetailResult.PetAndServicesDetailItemR\x0epetAndServices\x12`\n\x07payment\x18\x03 \x01(\x0b\x32\x46.moego.client.online_booking.v1.GetAppointmentDetailResult.PaymentItemR\x07payment\x1a\xe4\x04\n\x0f\x41ppointmentItem\x12\'\n\x0e\x61ppointment_id\x18\x01 \x01(\x03H\x00R\rappointmentId\x12.\n\x12\x62ooking_request_id\x18\x02 \x01(\x03H\x00R\x10\x62ookingRequestId\x12\x1d\n\nstart_date\x18\x03 \x01(\tR\tstartDate\x12\x1d\n\nstart_time\x18\x04 \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_date\x18\x05 \x01(\tR\x07\x65ndDate\x12\x19\n\x08\x65nd_time\x18\x06 \x01(\x05R\x07\x65ndTime\x12O\n\x0emain_care_type\x18\x07 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0cmainCareType\x12,\n\x12is_booking_request\x18\x08 \x01(\x08R\x10isBookingRequest\x12\x83\x01\n\x17\x63heck_in_out_date_times\x18\t \x03(\x0b\x32M.moego.client.online_booking.v1.GetAppointmentDetailResult.CheckInOutDateTimeR\x13\x63heckInOutDateTimes\x12\x62\n\x12\x61ppointment_status\x18\n \x01(\x0e\x32..moego.models.appointment.v1.AppointmentStatusH\x01R\x11\x61ppointmentStatus\x88\x01\x01\x42\x04\n\x02idB\x15\n\x13_appointment_status\x1a\xa8\x01\n\x12\x43heckInOutDateTime\x12\"\n\rcheck_in_date\x18\x01 \x01(\tR\x0b\x63heckInDate\x12\"\n\rcheck_in_time\x18\x02 \x01(\x05R\x0b\x63heckInTime\x12$\n\x0e\x63heck_out_date\x18\x03 \x01(\tR\x0c\x63heckOutDate\x12$\n\x0e\x63heck_out_time\x18\x04 \x01(\x05R\x0c\x63heckOutTime\x1a\xbf\x02\n\x18PetAndServicesDetailItem\x12T\n\x03pet\x18\x01 \x01(\x0b\x32\x42.moego.client.online_booking.v1.GetAppointmentDetailResult.PetItemR\x03pet\x12h\n\x08services\x18\x02 \x03(\x0b\x32L.moego.client.online_booking.v1.GetAppointmentDetailResult.ServiceDetailItemR\x08services\x12\x63\n\x07\x61\x64\x64_ons\x18\x03 \x03(\x0b\x32J.moego.client.online_booking.v1.GetAppointmentDetailResult.AddOnDetailItemR\x06\x61\x64\x64Ons\x1a\x8a\x03\n\x07PetItem\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n\x08pet_name\x18\x02 \x01(\tR\x07petName\x12<\n\x08pet_type\x18\x03 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\x12\x14\n\x05\x62reed\x18\x04 \x01(\tR\x05\x62reed\x12\x1f\n\x0b\x61vatar_path\x18\x05 \x01(\tR\navatarPath\x12\x19\n\x08vet_name\x18\x06 \x01(\tR\x07vetName\x12(\n\x10vet_phone_number\x18\x07 \x01(\tR\x0evetPhoneNumber\x12\x1f\n\x0bvet_address\x18\x08 \x01(\tR\nvetAddress\x12\x34\n\x16\x65mergency_contact_name\x18\t \x01(\tR\x14\x65mergencyContactName\x12\x43\n\x1e\x65mergency_contact_phone_number\x18\n \x01(\tR\x1b\x65mergencyContactPhoneNumber\x1a\x97\x06\n\x11ServiceDetailItem\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12!\n\x0cservice_name\x18\x02 \x01(\tR\x0bserviceName\x12\x46\n\tcare_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x08\x63\x61reType\x12\"\n\rpet_detail_id\x18\x04 \x01(\x03R\x0bpetDetailId\x12K\n\tdate_type\x18\x05 \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeR\x08\x64\x61teType\x12%\n\x0especific_dates\x18\x06 \x03(\tR\rspecificDates\x12\"\n\nstart_date\x18\x07 \x01(\tH\x00R\tstartDate\x88\x01\x01\x12\"\n\nstart_time\x18\x08 \x01(\x05H\x01R\tstartTime\x88\x01\x01\x12\x1e\n\x08\x65nd_date\x18\t \x01(\tH\x02R\x07\x65ndDate\x88\x01\x01\x12\x1e\n\x08\x65nd_time\x18\n \x01(\x05H\x03R\x07\x65ndTime\x88\x01\x01\x12Y\n\x08\x66\x65\x65\x64ings\x18\x0b \x03(\x0b\x32=.moego.models.appointment.v1.AppointmentPetFeedingScheduleDefR\x08\x66\x65\x65\x64ings\x12\x62\n\x0bmedications\x18\x0c \x03(\x0b\<EMAIL>\x0bmedications\x12&\n\x0cmax_duration\x18\r \x01(\x05H\x04R\x0bmaxDuration\x88\x01\x01\x12&\n\x0cservice_time\x18\x0e \x01(\x05H\x05R\x0bserviceTime\x88\x01\x01\x42\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x0b\n\t_end_dateB\x0b\n\t_end_timeB\x0f\n\r_max_durationB\x0f\n\r_service_time\x1a\xe9\x04\n\x0f\x41\x64\x64OnDetailItem\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n\x0b\x61\x64\x64_on_name\x18\x02 \x01(\tR\taddOnName\x12K\n\tdate_type\x18\x03 \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeR\x08\x64\x61teType\x12%\n\x0especific_dates\x18\x04 \x03(\tR\rspecificDates\x12\x46\n\tcare_type\x18\x05 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x08\x63\x61reType\x12\"\n\rpet_detail_id\x18\x06 \x01(\x03R\x0bpetDetailId\x12\"\n\nstart_time\x18\x07 \x01(\x05H\x00R\tstartTime\x88\x01\x01\x12\x1e\n\x08\x65nd_time\x18\x08 \x01(\x05H\x01R\x07\x65ndTime\x88\x01\x01\x12-\n\x10quantity_per_day\x18\t \x01(\x05H\x02R\x0equantityPerDay\x88\x01\x01\x12&\n\x0cservice_time\x18\n \x01(\x05H\x03R\x0bserviceTime\x88\x01\x01\x12\x36\n\x17require_dedicated_staff\x18\x0b \x01(\x08R\x15requireDedicatedStaff\x12\"\n\nstart_date\x18\x0c \x01(\tH\x04R\tstartDate\x88\x01\x01\x42\r\n\x0b_start_timeB\x0b\n\t_end_timeB\x13\n\x11_quantity_per_dayB\x0f\n\r_service_timeB\r\n\x0b_start_date\x1a\xc0\x03\n\x0bPaymentItem\x12\x32\n\x15\x65stimated_total_price\x18\x01 \x01(\x01R\x13\x65stimatedTotalPrice\x12!\n\x0ctotal_amount\x18\x02 \x01(\x01R\x0btotalAmount\x12\\\n\x0epayment_status\x18\x03 \x01(\x0e\x32\x35.moego.models.appointment.v1.AppointmentPaymentStatusR\rpaymentStatus\x12$\n\x0bpaid_amount\x18\x04 \x01(\x01H\x00R\npaidAmount\x88\x01\x01\x12)\n\x0epre_pay_amount\x18\x05 \x01(\x01H\x01R\x0cprePayAmount\x88\x01\x01\x12+\n\x0fpre_auth_enable\x18\x06 \x01(\x08H\x02R\rpreAuthEnable\x88\x01\x01\x12G\n evaluation_estimated_total_price\x18\x07 \x01(\x01R\x1d\x65valuationEstimatedTotalPriceB\x0e\n\x0c_paid_amountB\x11\n\x0f_pre_pay_amountB\x12\n\x10_pre_auth_enable\"\xbc\x01\n\x19GetGroupClassDetailParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\'\n\x0e\x61ppointment_id\x18\x03 \x01(\x03H\x01R\rappointmentId\x12.\n\x12\x62ooking_request_id\x18\x04 \x01(\x03H\x01R\x10\x62ookingRequestIdB\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x04\n\x02id\"\x9f\x02\n\x19GetGroupClassDetailResult\x12T\n\x03pet\x18\x01 \x01(\x0b\x32\x42.moego.client.online_booking.v1.GetAppointmentDetailResult.PetItemR\x03pet\x12_\n\x14group_class_instance\x18\x02 \x01(\x0b\x32-.moego.api.offering.v1.GroupClassInstanceViewR\x12groupClassInstance\x12K\n\rservice_model\x18\x03 \x01(\x0b\x32&.moego.models.offering.v1.ServiceModelR\x0cserviceModel\"\xc7\x05\n$ReschedulePetFeedingMedicationParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\'\n\x0e\x61ppointment_id\x18\x03 \x01(\x03H\x01R\rappointmentId\x12.\n\x12\x62ooking_request_id\x18\x04 \x01(\x03H\x01R\x10\x62ookingRequestId\x12\x84\x01\n\tschedules\x18\x05 \x03(\x0b\x32S.moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams.PetScheduleDefB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\x10\x64\"\x05\x8a\x01\x02\x10\x01R\tschedules\x1a\xf6\x02\n\x0ePetScheduleDef\x12+\n\rpet_detail_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0bpetDetailId\x12R\n\tcare_type\x18\x02 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x08\x63\x61reType\x12l\n\x08\x66\x65\x65\x64ings\x18\x03 \x03(\x0b\x32=.moego.models.appointment.v1.AppointmentPetFeedingScheduleDefB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x08\x66\x65\x65\x64ings\x12u\n\x0bmedications\x18\x04 \x03(\x0b\<EMAIL>\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x0bmedicationsB\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x04\n\x02id\"&\n$ReschedulePetFeedingMedicationResult\"\xba\x01\n\x17\x43\x61ncelAppointmentParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\'\n\x0e\x61ppointment_id\x18\x03 \x01(\x03H\x01R\rappointmentId\x12.\n\x12\x62ooking_request_id\x18\x04 \x01(\x03H\x01R\x10\x62ookingRequestIdB\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x04\n\x02id\"\x19\n\x17\x43\x61ncelAppointmentResult\"\xed\x0e\n\x17UpdateAppointmentParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\'\n\x0e\x61ppointment_id\x18\x03 \x01(\x03H\x01R\rappointmentId\x12.\n\x12\x62ooking_request_id\x18\x04 \x01(\x03H\x01R\x10\x62ookingRequestId\x12~\n\x10pet_and_services\x18\x05 \x03(\x0b\x32T.moego.client.online_booking.v1.UpdateAppointmentParams.UpdatePetServiceDetailParamsR\x0epetAndServices\x1a\x97\x02\n\x1cUpdatePetServiceDetailParams\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12m\n\x08services\x18\x02 \x03(\x0b\x32Q.moego.client.online_booking.v1.UpdateAppointmentParams.UpdateServiceDetailParamsR\x08services\x12h\n\x07\x61\x64\x64_ons\x18\x03 \x03(\x0b\x32O.moego.client.online_booking.v1.UpdateAppointmentParams.UpdateAddOnDetailParamsR\x06\x61\x64\x64Ons\x1a\xaf\x05\n\x19UpdateServiceDetailParams\x12R\n\tcare_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x08\x63\x61reType\x12+\n\rpet_detail_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0bpetDetailId\x12\\\n\tdate_type\x18\x03 \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x08\x64\x61teType\x88\x01\x01\x12H\n\x0especific_dates\x18\x04 \x03(\tB!\xfa\x42\x1e\x92\x01\x1b\x10\x64\"\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\rspecificDates\x12>\n\nstart_date\x18\x05 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\tstartDate\x88\x01\x01\x12.\n\nstart_time\x18\x06 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00H\x02R\tstartTime\x88\x01\x01\x12:\n\x08\x65nd_date\x18\x07 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x03R\x07\x65ndDate\x88\x01\x01\x12*\n\x08\x65nd_time\x18\x08 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00H\x04R\x07\x65ndTime\x88\x01\x01\x12\x36\n\x10quantity_per_day\x18\t \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x05R\x0equantityPerDay\x88\x01\x01\x42\x0c\n\n_date_typeB\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x0b\n\t_end_dateB\x0b\n\t_end_timeB\x13\n\x11_quantity_per_day\x1a\xe4\x04\n\x17UpdateAddOnDetailParams\x12R\n\tcare_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x08\x63\x61reType\x12+\n\rpet_detail_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0bpetDetailId\x12\\\n\tdate_type\x18\x03 \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x08\x64\x61teType\x88\x01\x01\x12H\n\x0especific_dates\x18\x04 \x03(\tB!\xfa\x42\x1e\x92\x01\x1b\x10\x64\"\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\rspecificDates\x12.\n\nstart_time\x18\x05 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00H\x01R\tstartTime\x88\x01\x01\x12*\n\x08\x65nd_time\x18\x06 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00H\x02R\x07\x65ndTime\x88\x01\x01\x12\x36\n\x10quantity_per_day\x18\x07 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x03R\x0equantityPerDay\x88\x01\x01\x12>\n\nstart_date\x18\x08 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x04R\tstartDate\x88\x01\x01\x42\x0c\n\n_date_typeB\r\n\x0b_start_timeB\x0b\n\t_end_timeB\x13\n\x11_quantity_per_dayB\r\n\x0b_start_dateB\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x04\n\x02id\"\x19\n\x17UpdateAppointmentResult\"\xc0\x03\n\x1eIsAvailableForRescheduleParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\'\n\x0e\x61ppointment_id\x18\x03 \x01(\x03H\x01R\rappointmentId\x12.\n\x12\x62ooking_request_id\x18\x04 \x01(\x03H\x01R\x10\x62ookingRequestId\x12\x61\n\x11service_item_type\x18\x05 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0fserviceItemType\x12\x30\n\nstart_date\x18\x06 \x01(\x0b\x32\x11.google.type.DateR\tstartDate\x12,\n\x08\x65nd_date\x18\x07 \x01(\x0b\x32\x11.google.type.DateR\x07\x65ndDate\x12+\n\nservice_id\x18\x08 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x02R\tserviceId\x88\x01\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x04\n\x02idB\r\n\x0b_service_id\"C\n\x1eIsAvailableForRescheduleResult\x12!\n\x0cis_available\x18\x01 \x01(\x08R\x0bisAvailable\"\x9c\x01\n\x15ListEvaluationsParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x41\n\npagination\x18\x03 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npaginationB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\xb7\x01\n\x15ListEvaluationsResult\x12Z\n\x0c\x61ppointments\x18\x01 \x03(\x0b\x32\x36.moego.client.online_booking.v1.AppointmentSummaryItemR\x0c\x61ppointments\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination*r\n\x13\x41ppointmentCardType\x12%\n!APPOINTMENT_CARD_TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04LAST\x10\x02\x12\x0b\n\x07PENDING\x10\x04\x12\x0c\n\x08UPCOMING\x10\x06\x12\x0f\n\x0bIN_PROGRESS\x10\x08\x32\xb8\n\n\x12\x41ppointmentService\x12\xa0\x01\n\x1aGetPriorityAppointmentCard\<EMAIL>.online_booking.v1.GetPriorityAppointmentCardParams\<EMAIL>.online_booking.v1.GetPriorityAppointmentCardResult\x12\x82\x01\n\x10ListAppointments\x12\x36.moego.client.online_booking.v1.ListAppointmentsParams\x1a\x36.moego.client.online_booking.v1.ListAppointmentsResult\x12\x8e\x01\n\x14GetAppointmentDetail\x12:.moego.client.online_booking.v1.GetAppointmentDetailParams\x1a:.moego.client.online_booking.v1.GetAppointmentDetailResult\x12\x8b\x01\n\x13GetGroupClassDetail\x12\x39.moego.client.online_booking.v1.GetGroupClassDetailParams\x1a\x39.moego.client.online_booking.v1.GetGroupClassDetailResult\x12\xac\x01\n\x1eReschedulePetFeedingMedication\x12\x44.moego.client.online_booking.v1.ReschedulePetFeedingMedicationParams\x1a\x44.moego.client.online_booking.v1.ReschedulePetFeedingMedicationResult\x12\x85\x01\n\x11\x43\x61ncelAppointment\x12\x37.moego.client.online_booking.v1.CancelAppointmentParams\x1a\x37.moego.client.online_booking.v1.CancelAppointmentResult\x12\x85\x01\n\x11UpdateAppointment\x12\x37.moego.client.online_booking.v1.UpdateAppointmentParams\x1a\x37.moego.client.online_booking.v1.UpdateAppointmentResult\x12\x9a\x01\n\x18IsAvailableForReschedule\x12>.moego.client.online_booking.v1.IsAvailableForRescheduleParams\x1a>.moego.client.online_booking.v1.IsAvailableForRescheduleResult\x12\x7f\n\x0fListEvaluations\x12\x35.moego.client.online_booking.v1.ListEvaluationsParams\x1a\x35.moego.client.online_booking.v1.ListEvaluationsResultB\x92\x01\n&com.moego.idl.client.online_booking.v1P\x01Zfgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.online_booking.v1.appointment_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.client.online_booking.v1P\001Zfgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb'
  _globals['_GETPRIORITYAPPOINTMENTCARDPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETPRIORITYAPPOINTMENTCARDPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_LISTAPPOINTMENTSPARAMS_FILTER'].fields_by_name['appointment_type']._loaded_options = None
  _globals['_LISTAPPOINTMENTSPARAMS_FILTER'].fields_by_name['appointment_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTAPPOINTMENTSPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_LISTAPPOINTMENTSPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETAPPOINTMENTDETAILPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETAPPOINTMENTDETAILPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETGROUPCLASSDETAILPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETGROUPCLASSDETAILPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF'].fields_by_name['pet_detail_id']._loaded_options = None
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF'].fields_by_name['pet_detail_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF'].fields_by_name['care_type']._loaded_options = None
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF'].fields_by_name['care_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF'].fields_by_name['feedings']._loaded_options = None
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF'].fields_by_name['feedings']._serialized_options = b'\372B\016\222\001\013\010\000\020d\"\005\212\001\002\020\001'
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF'].fields_by_name['medications']._loaded_options = None
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF'].fields_by_name['medications']._serialized_options = b'\372B\016\222\001\013\010\000\020d\"\005\212\001\002\020\001'
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS'].fields_by_name['schedules']._loaded_options = None
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS'].fields_by_name['schedules']._serialized_options = b'\372B\016\222\001\013\010\000\020d\"\005\212\001\002\020\001'
  _globals['_CANCELAPPOINTMENTPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_CANCELAPPOINTMENTPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEPETSERVICEDETAILPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEPETSERVICEDETAILPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['care_type']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['care_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['pet_detail_id']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['pet_detail_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['date_type']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['specific_dates']._serialized_options = b'\372B\036\222\001\033\020d\"\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['start_date']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['start_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['end_date']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['end_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['quantity_per_day']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS'].fields_by_name['quantity_per_day']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['care_type']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['care_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['pet_detail_id']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['pet_detail_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['date_type']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['specific_dates']._serialized_options = b'\372B\036\222\001\033\020d\"\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['start_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['end_time']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['quantity_per_day']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['quantity_per_day']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['start_date']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEAPPOINTMENTPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_UPDATEAPPOINTMENTPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_ISAVAILABLEFORRESCHEDULEPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_ISAVAILABLEFORRESCHEDULEPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_ISAVAILABLEFORRESCHEDULEPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_ISAVAILABLEFORRESCHEDULEPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_ISAVAILABLEFORRESCHEDULEPARAMS'].fields_by_name['service_id']._loaded_options = None
  _globals['_ISAVAILABLEFORRESCHEDULEPARAMS'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTEVALUATIONSPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_LISTEVALUATIONSPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_APPOINTMENTCARDTYPE']._serialized_start=12091
  _globals['_APPOINTMENTCARDTYPE']._serialized_end=12205
  _globals['_GETPRIORITYAPPOINTMENTCARDPARAMS']._serialized_start=621
  _globals['_GETPRIORITYAPPOINTMENTCARDPARAMS']._serialized_end=721
  _globals['_GETPRIORITYAPPOINTMENTCARDRESULT']._serialized_start=724
  _globals['_GETPRIORITYAPPOINTMENTCARDRESULT']._serialized_end=853
  _globals['_APPOINTMENTCARDITEM']._serialized_start=856
  _globals['_APPOINTMENTCARDITEM']._serialized_end=1150
  _globals['_APPOINTMENTSUMMARYITEM']._serialized_start=1153
  _globals['_APPOINTMENTSUMMARYITEM']._serialized_end=1591
  _globals['_PETANDSERVICESSUMMARYITEM']._serialized_start=1594
  _globals['_PETANDSERVICESSUMMARYITEM']._serialized_end=2324
  _globals['_PETANDSERVICESSUMMARYITEM_PETITEM']._serialized_start=1808
  _globals['_PETANDSERVICESSUMMARYITEM_PETITEM']._serialized_end=1955
  _globals['_PETANDSERVICESSUMMARYITEM_SERVICEITEM']._serialized_start=1958
  _globals['_PETANDSERVICESSUMMARYITEM_SERVICEITEM']._serialized_end=2324
  _globals['_APPOINTMENTPAYMENTITEM']._serialized_start=2327
  _globals['_APPOINTMENTPAYMENTITEM']._serialized_end=2605
  _globals['_LISTAPPOINTMENTSPARAMS']._serialized_start=2608
  _globals['_LISTAPPOINTMENTSPARAMS']._serialized_end=3410
  _globals['_LISTAPPOINTMENTSPARAMS_FILTER']._serialized_start=2934
  _globals['_LISTAPPOINTMENTSPARAMS_FILTER']._serialized_end=3069
  _globals['_LISTAPPOINTMENTSPARAMS_APPOINTMENTSORTDEF']._serialized_start=3072
  _globals['_LISTAPPOINTMENTSPARAMS_APPOINTMENTSORTDEF']._serialized_end=3209
  _globals['_LISTAPPOINTMENTSPARAMS_APPOINTMENTTYPE']._serialized_start=3211
  _globals['_LISTAPPOINTMENTSPARAMS_APPOINTMENTTYPE']._serialized_end=3313
  _globals['_LISTAPPOINTMENTSPARAMS_APPOINTMENTSORTFIELD']._serialized_start=3315
  _globals['_LISTAPPOINTMENTSPARAMS_APPOINTMENTSORTFIELD']._serialized_end=3392
  _globals['_LISTAPPOINTMENTSRESULT']._serialized_start=3413
  _globals['_LISTAPPOINTMENTSRESULT']._serialized_end=3914
  _globals['_LISTAPPOINTMENTSRESULT_APPOINTMENTLISTITEM']._serialized_start=3620
  _globals['_LISTAPPOINTMENTSRESULT_APPOINTMENTLISTITEM']._serialized_end=3914
  _globals['_GETAPPOINTMENTDETAILPARAMS']._serialized_start=3917
  _globals['_GETAPPOINTMENTDETAILPARAMS']._serialized_end=4106
  _globals['_GETAPPOINTMENTDETAILRESULT']._serialized_start=4109
  _globals['_GETAPPOINTMENTDETAILRESULT']._serialized_end=7842
  _globals['_GETAPPOINTMENTDETAILRESULT_APPOINTMENTITEM']._serialized_start=4475
  _globals['_GETAPPOINTMENTDETAILRESULT_APPOINTMENTITEM']._serialized_end=5087
  _globals['_GETAPPOINTMENTDETAILRESULT_CHECKINOUTDATETIME']._serialized_start=5090
  _globals['_GETAPPOINTMENTDETAILRESULT_CHECKINOUTDATETIME']._serialized_end=5258
  _globals['_GETAPPOINTMENTDETAILRESULT_PETANDSERVICESDETAILITEM']._serialized_start=5261
  _globals['_GETAPPOINTMENTDETAILRESULT_PETANDSERVICESDETAILITEM']._serialized_end=5580
  _globals['_GETAPPOINTMENTDETAILRESULT_PETITEM']._serialized_start=5583
  _globals['_GETAPPOINTMENTDETAILRESULT_PETITEM']._serialized_end=5977
  _globals['_GETAPPOINTMENTDETAILRESULT_SERVICEDETAILITEM']._serialized_start=5980
  _globals['_GETAPPOINTMENTDETAILRESULT_SERVICEDETAILITEM']._serialized_end=6771
  _globals['_GETAPPOINTMENTDETAILRESULT_ADDONDETAILITEM']._serialized_start=6774
  _globals['_GETAPPOINTMENTDETAILRESULT_ADDONDETAILITEM']._serialized_end=7391
  _globals['_GETAPPOINTMENTDETAILRESULT_PAYMENTITEM']._serialized_start=7394
  _globals['_GETAPPOINTMENTDETAILRESULT_PAYMENTITEM']._serialized_end=7842
  _globals['_GETGROUPCLASSDETAILPARAMS']._serialized_start=7845
  _globals['_GETGROUPCLASSDETAILPARAMS']._serialized_end=8033
  _globals['_GETGROUPCLASSDETAILRESULT']._serialized_start=8036
  _globals['_GETGROUPCLASSDETAILRESULT']._serialized_end=8323
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS']._serialized_start=8326
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS']._serialized_end=9037
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF']._serialized_start=8639
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONPARAMS_PETSCHEDULEDEF']._serialized_end=9013
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONRESULT']._serialized_start=9039
  _globals['_RESCHEDULEPETFEEDINGMEDICATIONRESULT']._serialized_end=9077
  _globals['_CANCELAPPOINTMENTPARAMS']._serialized_start=9080
  _globals['_CANCELAPPOINTMENTPARAMS']._serialized_end=9266
  _globals['_CANCELAPPOINTMENTRESULT']._serialized_start=9268
  _globals['_CANCELAPPOINTMENTRESULT']._serialized_end=9293
  _globals['_UPDATEAPPOINTMENTPARAMS']._serialized_start=9296
  _globals['_UPDATEAPPOINTMENTPARAMS']._serialized_end=11197
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEPETSERVICEDETAILPARAMS']._serialized_start=9589
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEPETSERVICEDETAILPARAMS']._serialized_end=9868
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS']._serialized_start=9871
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATESERVICEDETAILPARAMS']._serialized_end=10558
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS']._serialized_start=10561
  _globals['_UPDATEAPPOINTMENTPARAMS_UPDATEADDONDETAILPARAMS']._serialized_end=11173
  _globals['_UPDATEAPPOINTMENTRESULT']._serialized_start=11199
  _globals['_UPDATEAPPOINTMENTRESULT']._serialized_end=11224
  _globals['_ISAVAILABLEFORRESCHEDULEPARAMS']._serialized_start=11227
  _globals['_ISAVAILABLEFORRESCHEDULEPARAMS']._serialized_end=11675
  _globals['_ISAVAILABLEFORRESCHEDULERESULT']._serialized_start=11677
  _globals['_ISAVAILABLEFORRESCHEDULERESULT']._serialized_end=11744
  _globals['_LISTEVALUATIONSPARAMS']._serialized_start=11747
  _globals['_LISTEVALUATIONSPARAMS']._serialized_end=11903
  _globals['_LISTEVALUATIONSRESULT']._serialized_start=11906
  _globals['_LISTEVALUATIONSRESULT']._serialized_end=12089
  _globals['_APPOINTMENTSERVICE']._serialized_start=12208
  _globals['_APPOINTMENTSERVICE']._serialized_end=13544
# @@protoc_insertion_point(module_scope)
