from google.type import date_pb2 as _date_pb2
from moego.api.offering.v1 import group_class_api_pb2 as _group_class_api_pb2
from moego.models.appointment.v1 import appointment_enums_pb2 as _appointment_enums_pb2
from moego.models.appointment.v1 import appointment_pet_feeding_schedule_defs_pb2 as _appointment_pet_feeding_schedule_defs_pb2
from moego.models.appointment.v1 import appointment_pet_medication_schedule_defs_pb2 as _appointment_pet_medication_schedule_defs_pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as _pet_detail_enums_pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as _customer_pet_enums_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.offering.v1 import service_models_pb2 as _service_models_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class AppointmentCardType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    APPOINTMENT_CARD_TYPE_UNSPECIFIED: _ClassVar[AppointmentCardType]
    LAST: _ClassVar[AppointmentCardType]
    PENDING: _ClassVar[AppointmentCardType]
    UPCOMING: _ClassVar[AppointmentCardType]
    IN_PROGRESS: _ClassVar[AppointmentCardType]
APPOINTMENT_CARD_TYPE_UNSPECIFIED: AppointmentCardType
LAST: AppointmentCardType
PENDING: AppointmentCardType
UPCOMING: AppointmentCardType
IN_PROGRESS: AppointmentCardType

class GetPriorityAppointmentCardParams(_message.Message):
    __slots__ = ("name", "domain")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ...) -> None: ...

class GetPriorityAppointmentCardResult(_message.Message):
    __slots__ = ("card", "count")
    CARD_FIELD_NUMBER: _ClassVar[int]
    COUNT_FIELD_NUMBER: _ClassVar[int]
    card: AppointmentCardItem
    count: int
    def __init__(self, card: _Optional[_Union[AppointmentCardItem, _Mapping]] = ..., count: _Optional[int] = ...) -> None: ...

class AppointmentCardItem(_message.Message):
    __slots__ = ("card_type", "appointment", "pet_and_services")
    CARD_TYPE_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_FIELD_NUMBER: _ClassVar[int]
    PET_AND_SERVICES_FIELD_NUMBER: _ClassVar[int]
    card_type: AppointmentCardType
    appointment: AppointmentSummaryItem
    pet_and_services: _containers.RepeatedCompositeFieldContainer[PetAndServicesSummaryItem]
    def __init__(self, card_type: _Optional[_Union[AppointmentCardType, str]] = ..., appointment: _Optional[_Union[AppointmentSummaryItem, _Mapping]] = ..., pet_and_services: _Optional[_Iterable[_Union[PetAndServicesSummaryItem, _Mapping]]] = ...) -> None: ...

class AppointmentSummaryItem(_message.Message):
    __slots__ = ("booking_request_id", "appointment_id", "start_date", "start_time", "end_date", "end_time", "main_care_type", "is_booking_request")
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    MAIN_CARE_TYPE_FIELD_NUMBER: _ClassVar[int]
    IS_BOOKING_REQUEST_FIELD_NUMBER: _ClassVar[int]
    booking_request_id: int
    appointment_id: int
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    main_care_type: _service_enum_pb2.ServiceItemType
    is_booking_request: bool
    def __init__(self, booking_request_id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., main_care_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., is_booking_request: bool = ...) -> None: ...

class PetAndServicesSummaryItem(_message.Message):
    __slots__ = ("pet", "services")
    class PetItem(_message.Message):
        __slots__ = ("id", "pet_name", "pet_type", "avatar_path")
        ID_FIELD_NUMBER: _ClassVar[int]
        PET_NAME_FIELD_NUMBER: _ClassVar[int]
        PET_TYPE_FIELD_NUMBER: _ClassVar[int]
        AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
        id: int
        pet_name: str
        pet_type: _customer_pet_enums_pb2.PetType
        avatar_path: str
        def __init__(self, id: _Optional[int] = ..., pet_name: _Optional[str] = ..., pet_type: _Optional[_Union[_customer_pet_enums_pb2.PetType, str]] = ..., avatar_path: _Optional[str] = ...) -> None: ...
    class ServiceItem(_message.Message):
        __slots__ = ("id", "service_name", "care_type", "start_date", "start_time", "end_date", "end_time")
        ID_FIELD_NUMBER: _ClassVar[int]
        SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
        CARE_TYPE_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        END_DATE_FIELD_NUMBER: _ClassVar[int]
        END_TIME_FIELD_NUMBER: _ClassVar[int]
        id: int
        service_name: str
        care_type: _service_enum_pb2.ServiceItemType
        start_date: _date_pb2.Date
        start_time: int
        end_date: _date_pb2.Date
        end_time: int
        def __init__(self, id: _Optional[int] = ..., service_name: _Optional[str] = ..., care_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., start_time: _Optional[int] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_time: _Optional[int] = ...) -> None: ...
    PET_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    pet: PetAndServicesSummaryItem.PetItem
    services: _containers.RepeatedCompositeFieldContainer[PetAndServicesSummaryItem.ServiceItem]
    def __init__(self, pet: _Optional[_Union[PetAndServicesSummaryItem.PetItem, _Mapping]] = ..., services: _Optional[_Iterable[_Union[PetAndServicesSummaryItem.ServiceItem, _Mapping]]] = ...) -> None: ...

class AppointmentPaymentItem(_message.Message):
    __slots__ = ("estimated_total_price", "total_amount", "payment_status", "evaluation_estimated_total_price")
    ESTIMATED_TOTAL_PRICE_FIELD_NUMBER: _ClassVar[int]
    TOTAL_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_STATUS_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_ESTIMATED_TOTAL_PRICE_FIELD_NUMBER: _ClassVar[int]
    estimated_total_price: float
    total_amount: float
    payment_status: _appointment_enums_pb2.AppointmentPaymentStatus
    evaluation_estimated_total_price: float
    def __init__(self, estimated_total_price: _Optional[float] = ..., total_amount: _Optional[float] = ..., payment_status: _Optional[_Union[_appointment_enums_pb2.AppointmentPaymentStatus, str]] = ..., evaluation_estimated_total_price: _Optional[float] = ...) -> None: ...

class ListAppointmentsParams(_message.Message):
    __slots__ = ("name", "domain", "filter", "pagination", "sorts")
    class AppointmentType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        APPOINTMENT_TYPE_UNSPECIFIED: _ClassVar[ListAppointmentsParams.AppointmentType]
        PENDING: _ClassVar[ListAppointmentsParams.AppointmentType]
        UPCOMING: _ClassVar[ListAppointmentsParams.AppointmentType]
        PAST: _ClassVar[ListAppointmentsParams.AppointmentType]
        CANCELED: _ClassVar[ListAppointmentsParams.AppointmentType]
    APPOINTMENT_TYPE_UNSPECIFIED: ListAppointmentsParams.AppointmentType
    PENDING: ListAppointmentsParams.AppointmentType
    UPCOMING: ListAppointmentsParams.AppointmentType
    PAST: ListAppointmentsParams.AppointmentType
    CANCELED: ListAppointmentsParams.AppointmentType
    class AppointmentSortField(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        APPOINTMENT_SORT_FIELD_UNSPECIFIED: _ClassVar[ListAppointmentsParams.AppointmentSortField]
        DATE_TIME: _ClassVar[ListAppointmentsParams.AppointmentSortField]
    APPOINTMENT_SORT_FIELD_UNSPECIFIED: ListAppointmentsParams.AppointmentSortField
    DATE_TIME: ListAppointmentsParams.AppointmentSortField
    class Filter(_message.Message):
        __slots__ = ("appointment_type",)
        APPOINTMENT_TYPE_FIELD_NUMBER: _ClassVar[int]
        appointment_type: ListAppointmentsParams.AppointmentType
        def __init__(self, appointment_type: _Optional[_Union[ListAppointmentsParams.AppointmentType, str]] = ...) -> None: ...
    class AppointmentSortDef(_message.Message):
        __slots__ = ("field", "asc")
        FIELD_FIELD_NUMBER: _ClassVar[int]
        ASC_FIELD_NUMBER: _ClassVar[int]
        field: ListAppointmentsParams.AppointmentSortField
        asc: bool
        def __init__(self, field: _Optional[_Union[ListAppointmentsParams.AppointmentSortField, str]] = ..., asc: bool = ...) -> None: ...
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    SORTS_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    filter: ListAppointmentsParams.Filter
    pagination: _pagination_messages_pb2.PaginationRequest
    sorts: _containers.RepeatedCompositeFieldContainer[ListAppointmentsParams.AppointmentSortDef]
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., filter: _Optional[_Union[ListAppointmentsParams.Filter, _Mapping]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., sorts: _Optional[_Iterable[_Union[ListAppointmentsParams.AppointmentSortDef, _Mapping]]] = ...) -> None: ...

class ListAppointmentsResult(_message.Message):
    __slots__ = ("appointments", "pagination")
    class AppointmentListItem(_message.Message):
        __slots__ = ("appointment", "pet_and_services", "payment")
        APPOINTMENT_FIELD_NUMBER: _ClassVar[int]
        PET_AND_SERVICES_FIELD_NUMBER: _ClassVar[int]
        PAYMENT_FIELD_NUMBER: _ClassVar[int]
        appointment: AppointmentSummaryItem
        pet_and_services: _containers.RepeatedCompositeFieldContainer[PetAndServicesSummaryItem]
        payment: AppointmentPaymentItem
        def __init__(self, appointment: _Optional[_Union[AppointmentSummaryItem, _Mapping]] = ..., pet_and_services: _Optional[_Iterable[_Union[PetAndServicesSummaryItem, _Mapping]]] = ..., payment: _Optional[_Union[AppointmentPaymentItem, _Mapping]] = ...) -> None: ...
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    appointments: _containers.RepeatedCompositeFieldContainer[ListAppointmentsResult.AppointmentListItem]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, appointments: _Optional[_Iterable[_Union[ListAppointmentsResult.AppointmentListItem, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class GetAppointmentDetailParams(_message.Message):
    __slots__ = ("name", "domain", "appointment_id", "booking_request_id")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    appointment_id: int
    booking_request_id: int
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., appointment_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ...) -> None: ...

class GetAppointmentDetailResult(_message.Message):
    __slots__ = ("appointment", "pet_and_services", "payment")
    class AppointmentItem(_message.Message):
        __slots__ = ("appointment_id", "booking_request_id", "start_date", "start_time", "end_date", "end_time", "main_care_type", "is_booking_request", "check_in_out_date_times", "appointment_status")
        APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
        BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        END_DATE_FIELD_NUMBER: _ClassVar[int]
        END_TIME_FIELD_NUMBER: _ClassVar[int]
        MAIN_CARE_TYPE_FIELD_NUMBER: _ClassVar[int]
        IS_BOOKING_REQUEST_FIELD_NUMBER: _ClassVar[int]
        CHECK_IN_OUT_DATE_TIMES_FIELD_NUMBER: _ClassVar[int]
        APPOINTMENT_STATUS_FIELD_NUMBER: _ClassVar[int]
        appointment_id: int
        booking_request_id: int
        start_date: str
        start_time: int
        end_date: str
        end_time: int
        main_care_type: _service_enum_pb2.ServiceItemType
        is_booking_request: bool
        check_in_out_date_times: _containers.RepeatedCompositeFieldContainer[GetAppointmentDetailResult.CheckInOutDateTime]
        appointment_status: _appointment_enums_pb2.AppointmentStatus
        def __init__(self, appointment_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., main_care_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., is_booking_request: bool = ..., check_in_out_date_times: _Optional[_Iterable[_Union[GetAppointmentDetailResult.CheckInOutDateTime, _Mapping]]] = ..., appointment_status: _Optional[_Union[_appointment_enums_pb2.AppointmentStatus, str]] = ...) -> None: ...
    class CheckInOutDateTime(_message.Message):
        __slots__ = ("check_in_date", "check_in_time", "check_out_date", "check_out_time")
        CHECK_IN_DATE_FIELD_NUMBER: _ClassVar[int]
        CHECK_IN_TIME_FIELD_NUMBER: _ClassVar[int]
        CHECK_OUT_DATE_FIELD_NUMBER: _ClassVar[int]
        CHECK_OUT_TIME_FIELD_NUMBER: _ClassVar[int]
        check_in_date: str
        check_in_time: int
        check_out_date: str
        check_out_time: int
        def __init__(self, check_in_date: _Optional[str] = ..., check_in_time: _Optional[int] = ..., check_out_date: _Optional[str] = ..., check_out_time: _Optional[int] = ...) -> None: ...
    class PetAndServicesDetailItem(_message.Message):
        __slots__ = ("pet", "services", "add_ons")
        PET_FIELD_NUMBER: _ClassVar[int]
        SERVICES_FIELD_NUMBER: _ClassVar[int]
        ADD_ONS_FIELD_NUMBER: _ClassVar[int]
        pet: GetAppointmentDetailResult.PetItem
        services: _containers.RepeatedCompositeFieldContainer[GetAppointmentDetailResult.ServiceDetailItem]
        add_ons: _containers.RepeatedCompositeFieldContainer[GetAppointmentDetailResult.AddOnDetailItem]
        def __init__(self, pet: _Optional[_Union[GetAppointmentDetailResult.PetItem, _Mapping]] = ..., services: _Optional[_Iterable[_Union[GetAppointmentDetailResult.ServiceDetailItem, _Mapping]]] = ..., add_ons: _Optional[_Iterable[_Union[GetAppointmentDetailResult.AddOnDetailItem, _Mapping]]] = ...) -> None: ...
    class PetItem(_message.Message):
        __slots__ = ("id", "pet_name", "pet_type", "breed", "avatar_path", "vet_name", "vet_phone_number", "vet_address", "emergency_contact_name", "emergency_contact_phone_number")
        ID_FIELD_NUMBER: _ClassVar[int]
        PET_NAME_FIELD_NUMBER: _ClassVar[int]
        PET_TYPE_FIELD_NUMBER: _ClassVar[int]
        BREED_FIELD_NUMBER: _ClassVar[int]
        AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
        VET_NAME_FIELD_NUMBER: _ClassVar[int]
        VET_PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
        VET_ADDRESS_FIELD_NUMBER: _ClassVar[int]
        EMERGENCY_CONTACT_NAME_FIELD_NUMBER: _ClassVar[int]
        EMERGENCY_CONTACT_PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
        id: int
        pet_name: str
        pet_type: _customer_pet_enums_pb2.PetType
        breed: str
        avatar_path: str
        vet_name: str
        vet_phone_number: str
        vet_address: str
        emergency_contact_name: str
        emergency_contact_phone_number: str
        def __init__(self, id: _Optional[int] = ..., pet_name: _Optional[str] = ..., pet_type: _Optional[_Union[_customer_pet_enums_pb2.PetType, str]] = ..., breed: _Optional[str] = ..., avatar_path: _Optional[str] = ..., vet_name: _Optional[str] = ..., vet_phone_number: _Optional[str] = ..., vet_address: _Optional[str] = ..., emergency_contact_name: _Optional[str] = ..., emergency_contact_phone_number: _Optional[str] = ...) -> None: ...
    class ServiceDetailItem(_message.Message):
        __slots__ = ("id", "service_name", "care_type", "pet_detail_id", "date_type", "specific_dates", "start_date", "start_time", "end_date", "end_time", "feedings", "medications", "max_duration", "service_time")
        ID_FIELD_NUMBER: _ClassVar[int]
        SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
        CARE_TYPE_FIELD_NUMBER: _ClassVar[int]
        PET_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
        DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
        SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        END_DATE_FIELD_NUMBER: _ClassVar[int]
        END_TIME_FIELD_NUMBER: _ClassVar[int]
        FEEDINGS_FIELD_NUMBER: _ClassVar[int]
        MEDICATIONS_FIELD_NUMBER: _ClassVar[int]
        MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
        SERVICE_TIME_FIELD_NUMBER: _ClassVar[int]
        id: int
        service_name: str
        care_type: _service_enum_pb2.ServiceItemType
        pet_detail_id: int
        date_type: _pet_detail_enums_pb2.PetDetailDateType
        specific_dates: _containers.RepeatedScalarFieldContainer[str]
        start_date: str
        start_time: int
        end_date: str
        end_time: int
        feedings: _containers.RepeatedCompositeFieldContainer[_appointment_pet_feeding_schedule_defs_pb2.AppointmentPetFeedingScheduleDef]
        medications: _containers.RepeatedCompositeFieldContainer[_appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef]
        max_duration: int
        service_time: int
        def __init__(self, id: _Optional[int] = ..., service_name: _Optional[str] = ..., care_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., pet_detail_id: _Optional[int] = ..., date_type: _Optional[_Union[_pet_detail_enums_pb2.PetDetailDateType, str]] = ..., specific_dates: _Optional[_Iterable[str]] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., feedings: _Optional[_Iterable[_Union[_appointment_pet_feeding_schedule_defs_pb2.AppointmentPetFeedingScheduleDef, _Mapping]]] = ..., medications: _Optional[_Iterable[_Union[_appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef, _Mapping]]] = ..., max_duration: _Optional[int] = ..., service_time: _Optional[int] = ...) -> None: ...
    class AddOnDetailItem(_message.Message):
        __slots__ = ("id", "add_on_name", "date_type", "specific_dates", "care_type", "pet_detail_id", "start_time", "end_time", "quantity_per_day", "service_time", "require_dedicated_staff", "start_date")
        ID_FIELD_NUMBER: _ClassVar[int]
        ADD_ON_NAME_FIELD_NUMBER: _ClassVar[int]
        DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
        SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
        CARE_TYPE_FIELD_NUMBER: _ClassVar[int]
        PET_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        END_TIME_FIELD_NUMBER: _ClassVar[int]
        QUANTITY_PER_DAY_FIELD_NUMBER: _ClassVar[int]
        SERVICE_TIME_FIELD_NUMBER: _ClassVar[int]
        REQUIRE_DEDICATED_STAFF_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        id: int
        add_on_name: str
        date_type: _pet_detail_enums_pb2.PetDetailDateType
        specific_dates: _containers.RepeatedScalarFieldContainer[str]
        care_type: _service_enum_pb2.ServiceItemType
        pet_detail_id: int
        start_time: int
        end_time: int
        quantity_per_day: int
        service_time: int
        require_dedicated_staff: bool
        start_date: str
        def __init__(self, id: _Optional[int] = ..., add_on_name: _Optional[str] = ..., date_type: _Optional[_Union[_pet_detail_enums_pb2.PetDetailDateType, str]] = ..., specific_dates: _Optional[_Iterable[str]] = ..., care_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., pet_detail_id: _Optional[int] = ..., start_time: _Optional[int] = ..., end_time: _Optional[int] = ..., quantity_per_day: _Optional[int] = ..., service_time: _Optional[int] = ..., require_dedicated_staff: bool = ..., start_date: _Optional[str] = ...) -> None: ...
    class PaymentItem(_message.Message):
        __slots__ = ("estimated_total_price", "total_amount", "payment_status", "paid_amount", "pre_pay_amount", "pre_auth_enable", "evaluation_estimated_total_price")
        ESTIMATED_TOTAL_PRICE_FIELD_NUMBER: _ClassVar[int]
        TOTAL_AMOUNT_FIELD_NUMBER: _ClassVar[int]
        PAYMENT_STATUS_FIELD_NUMBER: _ClassVar[int]
        PAID_AMOUNT_FIELD_NUMBER: _ClassVar[int]
        PRE_PAY_AMOUNT_FIELD_NUMBER: _ClassVar[int]
        PRE_AUTH_ENABLE_FIELD_NUMBER: _ClassVar[int]
        EVALUATION_ESTIMATED_TOTAL_PRICE_FIELD_NUMBER: _ClassVar[int]
        estimated_total_price: float
        total_amount: float
        payment_status: _appointment_enums_pb2.AppointmentPaymentStatus
        paid_amount: float
        pre_pay_amount: float
        pre_auth_enable: bool
        evaluation_estimated_total_price: float
        def __init__(self, estimated_total_price: _Optional[float] = ..., total_amount: _Optional[float] = ..., payment_status: _Optional[_Union[_appointment_enums_pb2.AppointmentPaymentStatus, str]] = ..., paid_amount: _Optional[float] = ..., pre_pay_amount: _Optional[float] = ..., pre_auth_enable: bool = ..., evaluation_estimated_total_price: _Optional[float] = ...) -> None: ...
    APPOINTMENT_FIELD_NUMBER: _ClassVar[int]
    PET_AND_SERVICES_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_FIELD_NUMBER: _ClassVar[int]
    appointment: GetAppointmentDetailResult.AppointmentItem
    pet_and_services: _containers.RepeatedCompositeFieldContainer[GetAppointmentDetailResult.PetAndServicesDetailItem]
    payment: GetAppointmentDetailResult.PaymentItem
    def __init__(self, appointment: _Optional[_Union[GetAppointmentDetailResult.AppointmentItem, _Mapping]] = ..., pet_and_services: _Optional[_Iterable[_Union[GetAppointmentDetailResult.PetAndServicesDetailItem, _Mapping]]] = ..., payment: _Optional[_Union[GetAppointmentDetailResult.PaymentItem, _Mapping]] = ...) -> None: ...

class GetGroupClassDetailParams(_message.Message):
    __slots__ = ("name", "domain", "appointment_id", "booking_request_id")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    appointment_id: int
    booking_request_id: int
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., appointment_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ...) -> None: ...

class GetGroupClassDetailResult(_message.Message):
    __slots__ = ("pet", "group_class_instance", "service_model")
    PET_FIELD_NUMBER: _ClassVar[int]
    GROUP_CLASS_INSTANCE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_MODEL_FIELD_NUMBER: _ClassVar[int]
    pet: GetAppointmentDetailResult.PetItem
    group_class_instance: _group_class_api_pb2.GroupClassInstanceView
    service_model: _service_models_pb2.ServiceModel
    def __init__(self, pet: _Optional[_Union[GetAppointmentDetailResult.PetItem, _Mapping]] = ..., group_class_instance: _Optional[_Union[_group_class_api_pb2.GroupClassInstanceView, _Mapping]] = ..., service_model: _Optional[_Union[_service_models_pb2.ServiceModel, _Mapping]] = ...) -> None: ...

class ReschedulePetFeedingMedicationParams(_message.Message):
    __slots__ = ("name", "domain", "appointment_id", "booking_request_id", "schedules")
    class PetScheduleDef(_message.Message):
        __slots__ = ("pet_detail_id", "care_type", "feedings", "medications")
        PET_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
        CARE_TYPE_FIELD_NUMBER: _ClassVar[int]
        FEEDINGS_FIELD_NUMBER: _ClassVar[int]
        MEDICATIONS_FIELD_NUMBER: _ClassVar[int]
        pet_detail_id: int
        care_type: _service_enum_pb2.ServiceItemType
        feedings: _containers.RepeatedCompositeFieldContainer[_appointment_pet_feeding_schedule_defs_pb2.AppointmentPetFeedingScheduleDef]
        medications: _containers.RepeatedCompositeFieldContainer[_appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef]
        def __init__(self, pet_detail_id: _Optional[int] = ..., care_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., feedings: _Optional[_Iterable[_Union[_appointment_pet_feeding_schedule_defs_pb2.AppointmentPetFeedingScheduleDef, _Mapping]]] = ..., medications: _Optional[_Iterable[_Union[_appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef, _Mapping]]] = ...) -> None: ...
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SCHEDULES_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    appointment_id: int
    booking_request_id: int
    schedules: _containers.RepeatedCompositeFieldContainer[ReschedulePetFeedingMedicationParams.PetScheduleDef]
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., appointment_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., schedules: _Optional[_Iterable[_Union[ReschedulePetFeedingMedicationParams.PetScheduleDef, _Mapping]]] = ...) -> None: ...

class ReschedulePetFeedingMedicationResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class CancelAppointmentParams(_message.Message):
    __slots__ = ("name", "domain", "appointment_id", "booking_request_id")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    appointment_id: int
    booking_request_id: int
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., appointment_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ...) -> None: ...

class CancelAppointmentResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class UpdateAppointmentParams(_message.Message):
    __slots__ = ("name", "domain", "appointment_id", "booking_request_id", "pet_and_services")
    class UpdatePetServiceDetailParams(_message.Message):
        __slots__ = ("pet_id", "services", "add_ons")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        SERVICES_FIELD_NUMBER: _ClassVar[int]
        ADD_ONS_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        services: _containers.RepeatedCompositeFieldContainer[UpdateAppointmentParams.UpdateServiceDetailParams]
        add_ons: _containers.RepeatedCompositeFieldContainer[UpdateAppointmentParams.UpdateAddOnDetailParams]
        def __init__(self, pet_id: _Optional[int] = ..., services: _Optional[_Iterable[_Union[UpdateAppointmentParams.UpdateServiceDetailParams, _Mapping]]] = ..., add_ons: _Optional[_Iterable[_Union[UpdateAppointmentParams.UpdateAddOnDetailParams, _Mapping]]] = ...) -> None: ...
    class UpdateServiceDetailParams(_message.Message):
        __slots__ = ("care_type", "pet_detail_id", "date_type", "specific_dates", "start_date", "start_time", "end_date", "end_time", "quantity_per_day")
        CARE_TYPE_FIELD_NUMBER: _ClassVar[int]
        PET_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
        DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
        SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        END_DATE_FIELD_NUMBER: _ClassVar[int]
        END_TIME_FIELD_NUMBER: _ClassVar[int]
        QUANTITY_PER_DAY_FIELD_NUMBER: _ClassVar[int]
        care_type: _service_enum_pb2.ServiceItemType
        pet_detail_id: int
        date_type: _pet_detail_enums_pb2.PetDetailDateType
        specific_dates: _containers.RepeatedScalarFieldContainer[str]
        start_date: str
        start_time: int
        end_date: str
        end_time: int
        quantity_per_day: int
        def __init__(self, care_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., pet_detail_id: _Optional[int] = ..., date_type: _Optional[_Union[_pet_detail_enums_pb2.PetDetailDateType, str]] = ..., specific_dates: _Optional[_Iterable[str]] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., quantity_per_day: _Optional[int] = ...) -> None: ...
    class UpdateAddOnDetailParams(_message.Message):
        __slots__ = ("care_type", "pet_detail_id", "date_type", "specific_dates", "start_time", "end_time", "quantity_per_day", "start_date")
        CARE_TYPE_FIELD_NUMBER: _ClassVar[int]
        PET_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
        DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
        SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        END_TIME_FIELD_NUMBER: _ClassVar[int]
        QUANTITY_PER_DAY_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        care_type: _service_enum_pb2.ServiceItemType
        pet_detail_id: int
        date_type: _pet_detail_enums_pb2.PetDetailDateType
        specific_dates: _containers.RepeatedScalarFieldContainer[str]
        start_time: int
        end_time: int
        quantity_per_day: int
        start_date: str
        def __init__(self, care_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., pet_detail_id: _Optional[int] = ..., date_type: _Optional[_Union[_pet_detail_enums_pb2.PetDetailDateType, str]] = ..., specific_dates: _Optional[_Iterable[str]] = ..., start_time: _Optional[int] = ..., end_time: _Optional[int] = ..., quantity_per_day: _Optional[int] = ..., start_date: _Optional[str] = ...) -> None: ...
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    PET_AND_SERVICES_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    appointment_id: int
    booking_request_id: int
    pet_and_services: _containers.RepeatedCompositeFieldContainer[UpdateAppointmentParams.UpdatePetServiceDetailParams]
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., appointment_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., pet_and_services: _Optional[_Iterable[_Union[UpdateAppointmentParams.UpdatePetServiceDetailParams, _Mapping]]] = ...) -> None: ...

class UpdateAppointmentResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class IsAvailableForRescheduleParams(_message.Message):
    __slots__ = ("name", "domain", "appointment_id", "booking_request_id", "service_item_type", "start_date", "end_date", "service_id")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    appointment_id: int
    booking_request_id: int
    service_item_type: _service_enum_pb2.ServiceItemType
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    service_id: int
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., appointment_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., service_id: _Optional[int] = ...) -> None: ...

class IsAvailableForRescheduleResult(_message.Message):
    __slots__ = ("is_available",)
    IS_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    is_available: bool
    def __init__(self, is_available: bool = ...) -> None: ...

class ListEvaluationsParams(_message.Message):
    __slots__ = ("name", "domain", "pagination")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class ListEvaluationsResult(_message.Message):
    __slots__ = ("appointments", "pagination")
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    appointments: _containers.RepeatedCompositeFieldContainer[AppointmentSummaryItem]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, appointments: _Optional[_Iterable[_Union[AppointmentSummaryItem, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...
