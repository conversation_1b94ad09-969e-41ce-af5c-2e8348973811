# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/online_booking/v1/booking_availability_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/online_booking/v1/booking_availability_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.client.online_booking.v1 import booking_request_api_pb2 as moego_dot_client_dot_online__booking_dot_v1_dot_booking__request__api__pb2
from moego.models.business_customer.v1 import business_customer_pet_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__pet__models__pb2
from moego.models.business_customer.v1 import business_pet_vaccine_record_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__vaccine__record__models__pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.grooming.v1 import service_enums_pb2 as moego_dot_models_dot_grooming_dot_v1_dot_service__enums__pb2
from moego.models.offering.v1 import evaluation_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_evaluation__models__pb2
from moego.models.offering.v1 import group_class_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_group__class__models__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.offering.v1 import service_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__models__pb2
from moego.models.online_booking.v1 import booking_availability_defs_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_booking__availability__defs__pb2
from moego.models.online_booking.v1 import booking_availability_enums_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_booking__availability__enums__pb2
from moego.models.online_booking.v1 import ob_availability_setting_defs_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_ob__availability__setting__defs__pb2
from moego.models.online_booking.v1 import ob_availability_setting_enums_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_ob__availability__setting__enums__pb2
from moego.models.online_booking.v1 import service_config_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_service__config__models__pb2
from moego.models.online_booking.v1 import week_time_range_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_week__time__range__models__pb2
from moego.service.enterprise.v1 import specific_support_service_pb2 as moego_dot_service_dot_enterprise_dot_v1_dot_specific__support__service__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=moego/client/online_booking/v1/booking_availability_api.proto\x12\x1emoego.client.online_booking.v1\x1a\x16google/type/date.proto\x1a\x38moego/client/online_booking/v1/booking_request_api.proto\x1a\x44moego/models/business_customer/v1/business_customer_pet_models.proto\x1aJmoego/models/business_customer/v1/business_pet_vaccine_record_models.proto\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a,moego/models/grooming/v1/service_enums.proto\x1a\x30moego/models/offering/v1/evaluation_models.proto\x1a\x31moego/models/offering/v1/group_class_models.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a-moego/models/offering/v1/service_models.proto\x1a>moego/models/online_booking/v1/booking_availability_defs.proto\x1a?moego/models/online_booking/v1/booking_availability_enums.proto\x1a\x41moego/models/online_booking/v1/ob_availability_setting_defs.proto\x1a\x42moego/models/online_booking/v1/ob_availability_setting_enums.proto\x1a:moego/models/online_booking/v1/service_config_models.proto\x1a;moego/models/online_booking/v1/week_time_range_models.proto\x1a:moego/service/enterprise/v1/specific_support_service.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"f\n\"GetAvailableServiceItemTypesParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omainB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"}\n\"GetAvailableServiceItemTypesResult\x12W\n\x12service_item_types\x18\x01 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypes\"\xd8\x01\n\x16GetAvailablePetsParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x66\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x0fserviceItemType\x88\x01\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x14\n\x12_service_item_type\"c\n\x16GetAvailablePetsResult\x12I\n\x04pets\x18\x01 \x03(\x0b\x32\x35.moego.client.online_booking.v1.GetAvailablePetResultR\x04pets\"\xff\x03\n\x15GetAvailablePetResult\x12Y\n\x03pet\x18\x01 \x01(\x0b\x32G.moego.models.business_customer.v1.BusinessCustomerPetOnlineBookingViewR\x03pet\x12u\n\x10question_answers\x18\x02 \x03(\x0b\x32J.moego.client.online_booking.v1.GetAvailablePetResult.QuestionAnswersEntryR\x0fquestionAnswers\x12i\n\x0fvaccine_records\x18\x03 \x03(\x0b\<EMAIL>.business_customer.v1.BusinessPetVaccineRecordModelR\x0evaccineRecords\x12\x65\n\x13unavailable_reasons\x18\x04 \x03(\x0e\x32\x34.moego.models.online_booking.v1.PetUnavailableReasonR\x12unavailableReasons\x1a\x42\n\x14QuestionAnswersEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\"\xc4\x02\n\x17GetAvailableDatesParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x61\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0fserviceItemType\x12\x35\n\nstart_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateH\x01R\tstartDate\x88\x01\x01\x12\x31\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x11.google.type.DateH\x02R\x07\x65ndDate\x88\x01\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\r\n\x0b_start_dateB\x0b\n\t_end_date\"\x94\x03\n\x17GetAvailableDatesResult\x12M\n\x14\x61vailable_start_date\x18\x01 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x12\x61vailableStartDate\x12I\n\x12\x61vailable_end_date\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x10\x61vailableEndDate\x12\x42\n\x11unavailable_dates\x18\x03 \x03(\x0b\x32\x11.google.type.DateB\x02\x18\x01R\x10unavailableDates\x12M\n\x19unavailable_arrival_dates\x18\x04 \x03(\x0b\x32\x11.google.type.DateR\x17unavailableArrivalDates\x12L\n\x19unavailable_pick_up_dates\x18\x05 \x03(\x0b\x32\x11.google.type.DateR\x16unavailablePickUpDates\"\xeb\x06\n\x1cGetAvailableTimeRangesParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x61\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0fserviceItemType\x12\x30\n\nstart_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateR\tstartDate\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x11.google.type.DateR\x07\x65ndDate\x12k\n\x0cpet_services\x18\x06 \x03(\x0b\x32H.moego.client.online_booking.v1.GetAvailableTimeRangesParams.PetServicesR\x0bpetServices\x1a\xd8\x03\n\x0bPetServices\x12\x35\n\x03pet\x18\x01 \x01(\x0b\x32#.moego.client.online_booking.v1.PetR\x03pet\x12l\n\x08services\x18\x02 \x03(\x0b\x32P.moego.client.online_booking.v1.GetAvailableTimeRangesParams.PetServices.ServiceR\x08services\x1a\xa3\x02\n\x07Service\x12}\n\nevaluation\x18\x05 \x01(\x0b\x32[.moego.client.online_booking.v1.GetAvailableTimeRangesParams.PetServices.Service.EvaluationH\x00R\nevaluation\x1a\x8d\x01\n\nEvaluation\x12,\n\revaluation_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0c\x65valuationId\x12/\n\x04\x64\x61te\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x04\x64\x61te\x12\x17\n\x04time\x18\x03 \x01(\x05H\x00R\x04time\x88\x01\x01\x42\x07\n\x05_timeB\t\n\x07serviceB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\x9a\x03\n\x1cGetAvailableTimeRangesResult\x12{\n\x0f\x64\x61y_time_ranges\x18\x01 \x03(\x0b\x32S.moego.client.online_booking.v1.GetAvailableTimeRangesResult.DayAvailableTimeRangesR\rdayTimeRanges\x1a\xfc\x01\n\x16\x44\x61yAvailableTimeRanges\x12%\n\x04\x64\x61te\x18\x01 \x01(\x0b\x32\x11.google.type.DateR\x04\x64\x61te\x12]\n\x12\x61rrival_time_range\x18\x02 \x03(\x0b\x32/.moego.models.online_booking.v1.DayTimeRangeDefR\x10\x61rrivalTimeRange\x12\\\n\x12pick_up_time_range\x18\x03 \x03(\x0b\x32/.moego.models.online_booking.v1.DayTimeRangeDefR\x0fpickUpTimeRange\"`\n\x1cGetBusinessWorkingHourParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omainB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\xa6\x04\n\x1cGetBusinessWorkingHourResult\x12\x46\n\x06monday\x18\x01 \x03(\x0b\x32..moego.models.online_booking.v1.TimeRangeModelR\x06monday\x12H\n\x07tuesday\x18\x02 \x03(\x0b\x32..moego.models.online_booking.v1.TimeRangeModelR\x07tuesday\x12L\n\twednesday\x18\x03 \x03(\x0b\x32..moego.models.online_booking.v1.TimeRangeModelR\twednesday\x12J\n\x08thursday\x18\x04 \x03(\x0b\x32..moego.models.online_booking.v1.TimeRangeModelR\x08thursday\x12\x46\n\x06\x66riday\x18\x05 \x03(\x0b\x32..moego.models.online_booking.v1.TimeRangeModelR\x06\x66riday\x12J\n\x08saturday\x18\x06 \x03(\x0b\x32..moego.models.online_booking.v1.TimeRangeModelR\x08saturday\x12\x46\n\x06sunday\x18\x07 \x03(\x0b\x32..moego.models.online_booking.v1.TimeRangeModelR\x06sunday\"\xf5\x05\n\x1dGetPetAvailableServicesParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x61\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0fserviceItemType\x12T\n\x0cservice_type\x18\x04 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0bserviceType\x12K\n\x04pets\x18\x05 \x03(\x0b\x32-.moego.models.online_booking.v1.BookingPetDefB\x08\xfa\x42\x05\x92\x01\x02\x08\x01R\x04pets\x12:\n\x14selected_service_ids\x18\x06 \x03(\x03\x42\x08\xfa\x42\x05\x92\x01\x02\x08\x00R\x12selectedServiceIds\x12?\n\nstart_date\x18\x07 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01H\x01R\tstartDate\x88\x01\x01\x12;\n\x08\x65nd_date\x18\x08 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01H\x02R\x07\x65ndDate\x88\x01\x01\x12\x42\n\x0especific_dates\x18\t \x03(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x92\x01\x02\x10<R\rspecificDates\x12L\n!book_with_other_service_item_type\x18\n \x01(\x08H\x03R\x1c\x62ookWithOtherServiceItemType\x88\x01\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\r\n\x0b_start_dateB\x0b\n\t_end_dateB$\n\"_book_with_other_service_item_type\"\x9b\x07\n\x1dGetPetAvailableServicesResult\x12W\n\ncategories\x18\x01 \x03(\x0b\x32\x37.moego.models.offering.v1.CustomizedServiceCategoryViewR\ncategories\x12\x32\n\x15\x61vailable_service_ids\x18\x02 \x03(\x03R\x13\x61vailableServiceIds\x12W\n\x0cpet_services\x18\x03 \x03(\x0b\x32\x34.moego.models.online_booking.v1.BookingPetServiceDefR\x0bpetServices\x12Z\n\x0fservice_configs\x18\x04 \x03(\x0b\x32\x31.moego.models.online_booking.v1.ServiceConfigViewR\x0eserviceConfigs\x12}\n\x10service_capacity\x18\x05 \x03(\x0b\x32R.moego.client.online_booking.v1.GetPetAvailableServicesResult.ServiceCapacityEntryR\x0fserviceCapacity\x12\x65\n\x14\x62undle_sale_services\x18\x06 \x03(\x0b\x32/.moego.models.offering.v1.ServiceBundleSaleViewB\x02\x18\x01R\x12\x62undleSaleServices\x12Z\n\x13service_brief_views\x18\x07 \x03(\x0b\x32*.moego.models.offering.v1.ServiceBriefViewR\x11serviceBriefViews\x1a\x91\x01\n\x14ServiceCapacityEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x63\n\x05value\x18\x02 \x01(\x0b\x32M.moego.client.online_booking.v1.GetPetAvailableServicesResult.ServiceCapacityR\x05value:\x02\x38\x01\x1a\x62\n\x0fServiceCapacity\x12\x1d\n\nservice_id\x18\x01 \x01(\x03R\tserviceId\x12\x30\n\x14is_lodging_available\x18\x02 \x01(\x08R\x12isLodgingAvailable\"\xad\x02\n GetAvailableEvaluationListParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x64\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x01R\x0fserviceItemType\x88\x01\x01\x12K\n\x04pets\x18\x05 \x03(\x0b\x32-.moego.models.online_booking.v1.BookingPetDefB\x08\xfa\x42\x05\x92\x01\x02\x10\x64R\x04petsB\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x14\n\x12_service_item_type\"\xad\x01\n GetAvailableEvaluationListResult\x12O\n\x0b\x65valuations\x18\x01 \x03(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewR\x0b\x65valuations\x12\x38\n\x18\x61vailable_evaluation_ids\x18\x02 \x03(\x03R\x16\x61vailableEvaluationIds\"\xc0\x01\n\x19GetAcceptedPetTypesParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x61\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0fserviceItemTypeB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"[\n\x19GetAcceptedPetTypesResult\x12>\n\tpet_types\x18\x01 \x03(\x0e\x32!.moego.models.customer.v1.PetTypeR\x08petTypes\"b\n\x1eGetAcceptedCustomerTypesParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omainB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\xfc\x02\n\x1eGetAcceptedCustomerTypesResult\x12\x85\x01\n\x15\x61\x63\x63\x65pt_customer_types\x18\x01 \x03(\x0b\x32Q.moego.client.online_booking.v1.GetAcceptedCustomerTypesResult.AcceptCustomerTypeR\x13\x61\x63\x63\x65ptCustomerTypes\x1a\xd1\x01\n\x12\x41\x63\x63\x65ptCustomerType\x12U\n\x11service_item_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12\x64\n\x14\x61\x63\x63\x65pt_customer_type\x18\x02 \x01(\x0e\x32\x32.moego.models.online_booking.v1.AcceptCustomerTypeR\x12\x61\x63\x63\x65ptCustomerType\"\xb9\x01\n\x12GetTimeRangeParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x61\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0fserviceItemTypeB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\x86\x01\n\x12GetTimeRangeResult\x12p\n\x1a\x61rrival_pick_up_time_range\x18\x01 \x01(\x0b\x32\x34.moego.models.online_booking.v1.ArrivalPickUpTimeDefR\x16\x61rrivalPickUpTimeRange\"\xbc\x01\n\x1eGetAvailableGroupClassesParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12\x41\n\npagination\x18\x04 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npaginationB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\xc6\x03\n\x1eGetAvailableGroupClassesResult\x12N\n\rgroup_classes\x18\x01 \x03(\x0b\x32).moego.models.offering.v1.GroupClassModelR\x0cgroupClasses\x12\x80\x01\n\x12ob_service_configs\x18\x02 \x03(\x0b\x32R.moego.client.online_booking.v1.GetAvailableGroupClassesResult.OBServiceConfigViewR\x10obServiceConfigs\x12\x42\n\npagination\x18\x03 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x1a\x8c\x01\n\x13OBServiceConfigView\x12$\n\x0egroup_class_id\x18\x01 \x01(\x03R\x0cgroupClassId\x12O\n\x0fshow_price_type\x18\x02 \x01(\x0e\x32\'.moego.models.grooming.v1.ShowBasePriceR\rshowPriceType\"\xd5\x01\n(GetPetAvailableGroupClassInstancesParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12$\n\x0egroup_class_id\x18\x03 \x01(\x03R\x0cgroupClassId\x12\x41\n\npagination\x18\x04 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npaginationB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\xf0\x05\n(GetPetAvailableGroupClassInstancesResult\x12\x9f\x01\n\x15group_class_instances\x18\x01 \x03(\x0b\x32k.moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsViewR\x13groupClassInstances\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x1a\xdd\x03\n\"GroupClassInstanceWithSessionsView\x12^\n\x14group_class_instance\x18\x01 \x01(\x0b\x32,.moego.models.offering.v1.GroupClassInstanceR\x12groupClassInstance\x12G\n\x08sessions\x18\x02 \x03(\x0b\x32+.moego.models.offering.v1.GroupClassSessionR\x08sessions\x12\x91\x01\n\x07trainer\x18\x03 \x01(\x0b\x32w.moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.TrainerViewR\x07trainer\x1az\n\x0bTrainerView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\nfirst_name\x18\x02 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x03 \x01(\tR\x08lastName\x12\x1f\n\x0b\x61vatar_path\x18\x04 \x01(\tR\navatarPath\"\xe3\x02\n\x1c\x43heckSpecialEvaluationParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12w\n\x0fpet_service_ids\x18\x03 \x03(\x0b\x32O.moego.client.online_booking.v1.CheckSpecialEvaluationParams.PetServiceIdsEntryR\rpetServiceIds\x1a\x87\x01\n\x12PetServiceIdsEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12[\n\x05value\x18\x02 \x01(\x0b\x32\x45.moego.service.enterprise.v1.CheckSpecialEvaluationRequest.ServiceIDsR\x05value:\x02\x38\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\"|\n\x1c\x43heckSpecialEvaluationResult\x12\\\n\x07results\x18\x01 \x03(\x0b\x32\x42.moego.service.enterprise.v1.CheckSpecialEvaluationResponse.ResultR\x07results2\xcf\x0f\n\x1a\x42ookingAvailabilityService\x12\xa6\x01\n\x1cGetAvailableServiceItemTypes\x12\x42.moego.client.online_booking.v1.GetAvailableServiceItemTypesParams\x1a\x42.moego.client.online_booking.v1.GetAvailableServiceItemTypesResult\x12\x82\x01\n\x10GetAvailablePets\x12\x36.moego.client.online_booking.v1.GetAvailablePetsParams\x1a\x36.moego.client.online_booking.v1.GetAvailablePetsResult\x12\x85\x01\n\x11GetAvailableDates\x12\x37.moego.client.online_booking.v1.GetAvailableDatesParams\x1a\x37.moego.client.online_booking.v1.GetAvailableDatesResult\x12\x94\x01\n\x16GetAvailableTimeRanges\x12<.moego.client.online_booking.v1.GetAvailableTimeRangesParams\x1a<.moego.client.online_booking.v1.GetAvailableTimeRangesResult\x12\x94\x01\n\x16GetBusinessWorkingHour\x12<.moego.client.online_booking.v1.GetBusinessWorkingHourParams\x1a<.moego.client.online_booking.v1.GetBusinessWorkingHourResult\x12\x97\x01\n\x17GetPetAvailableServices\x12=.moego.client.online_booking.v1.GetPetAvailableServicesParams\x1a=.moego.client.online_booking.v1.GetPetAvailableServicesResult\x12\xa0\x01\n\x1aGetAvailableEvaluationList\<EMAIL>.online_booking.v1.GetAvailableEvaluationListParams\<EMAIL>.online_booking.v1.GetAvailableEvaluationListResult\x12\x8b\x01\n\x13GetAcceptedPetTypes\x12\x39.moego.client.online_booking.v1.GetAcceptedPetTypesParams\x1a\x39.moego.client.online_booking.v1.GetAcceptedPetTypesResult\x12\x9a\x01\n\x18GetAcceptedCustomerTypes\x12>.moego.client.online_booking.v1.GetAcceptedCustomerTypesParams\x1a>.moego.client.online_booking.v1.GetAcceptedCustomerTypesResult\x12v\n\x0cGetTimeRange\x12\x32.moego.client.online_booking.v1.GetTimeRangeParams\x1a\x32.moego.client.online_booking.v1.GetTimeRangeResult\x12\x9a\x01\n\x18GetAvailableGroupClasses\x12>.moego.client.online_booking.v1.GetAvailableGroupClassesParams\x1a>.moego.client.online_booking.v1.GetAvailableGroupClassesResult\x12\xb8\x01\n\"GetPetAvailableGroupClassInstances\x12H.moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesParams\x1aH.moego.client.online_booking.v1.GetPetAvailableGroupClassInstancesResult\x12\x94\x01\n\x16\x43heckSpecialEvaluation\x12<.moego.client.online_booking.v1.CheckSpecialEvaluationParams\x1a<.moego.client.online_booking.v1.CheckSpecialEvaluationResultB\x92\x01\n&com.moego.idl.client.online_booking.v1P\x01Zfgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.online_booking.v1.booking_availability_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.client.online_booking.v1P\001Zfgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb'
  _globals['_GETAVAILABLESERVICEITEMTYPESPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETAVAILABLESERVICEITEMTYPESPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETAVAILABLEPETSPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETAVAILABLEPETSPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETAVAILABLEPETSPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_GETAVAILABLEPETSPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETAVAILABLEPETRESULT_QUESTIONANSWERSENTRY']._loaded_options = None
  _globals['_GETAVAILABLEPETRESULT_QUESTIONANSWERSENTRY']._serialized_options = b'8\001'
  _globals['_GETAVAILABLEDATESPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETAVAILABLEDATESPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETAVAILABLEDATESPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_GETAVAILABLEDATESPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETAVAILABLEDATESRESULT'].fields_by_name['available_start_date']._loaded_options = None
  _globals['_GETAVAILABLEDATESRESULT'].fields_by_name['available_start_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETAVAILABLEDATESRESULT'].fields_by_name['available_end_date']._loaded_options = None
  _globals['_GETAVAILABLEDATESRESULT'].fields_by_name['available_end_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETAVAILABLEDATESRESULT'].fields_by_name['unavailable_dates']._loaded_options = None
  _globals['_GETAVAILABLEDATESRESULT'].fields_by_name['unavailable_dates']._serialized_options = b'\030\001'
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES_SERVICE_EVALUATION'].fields_by_name['evaluation_id']._loaded_options = None
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES_SERVICE_EVALUATION'].fields_by_name['evaluation_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES_SERVICE_EVALUATION'].fields_by_name['date']._loaded_options = None
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES_SERVICE_EVALUATION'].fields_by_name['date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETAVAILABLETIMERANGESPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETAVAILABLETIMERANGESPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETAVAILABLETIMERANGESPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_GETAVAILABLETIMERANGESPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETBUSINESSWORKINGHOURPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETBUSINESSWORKINGHOURPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETPETAVAILABLESERVICESPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['service_type']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['service_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['pets']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['pets']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['selected_service_ids']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['selected_service_ids']._serialized_options = b'\372B\005\222\001\002\010\000'
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['start_date']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['start_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['end_date']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['end_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESPARAMS'].fields_by_name['specific_dates']._serialized_options = b'\372B\005\222\001\002\020<'
  _globals['_GETPETAVAILABLESERVICESRESULT_SERVICECAPACITYENTRY']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESRESULT_SERVICECAPACITYENTRY']._serialized_options = b'8\001'
  _globals['_GETPETAVAILABLESERVICESRESULT'].fields_by_name['bundle_sale_services']._loaded_options = None
  _globals['_GETPETAVAILABLESERVICESRESULT'].fields_by_name['bundle_sale_services']._serialized_options = b'\030\001'
  _globals['_GETAVAILABLEEVALUATIONLISTPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETAVAILABLEEVALUATIONLISTPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETAVAILABLEEVALUATIONLISTPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_GETAVAILABLEEVALUATIONLISTPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_GETAVAILABLEEVALUATIONLISTPARAMS'].fields_by_name['pets']._loaded_options = None
  _globals['_GETAVAILABLEEVALUATIONLISTPARAMS'].fields_by_name['pets']._serialized_options = b'\372B\005\222\001\002\020d'
  _globals['_GETACCEPTEDPETTYPESPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETACCEPTEDPETTYPESPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETACCEPTEDPETTYPESPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_GETACCEPTEDPETTYPESPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETACCEPTEDCUSTOMERTYPESPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETACCEPTEDCUSTOMERTYPESPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETTIMERANGEPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETTIMERANGEPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETTIMERANGEPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_GETTIMERANGEPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETAVAILABLEGROUPCLASSESPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETAVAILABLEGROUPCLASSESPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_CHECKSPECIALEVALUATIONPARAMS_PETSERVICEIDSENTRY']._loaded_options = None
  _globals['_CHECKSPECIALEVALUATIONPARAMS_PETSERVICEIDSENTRY']._serialized_options = b'8\001'
  _globals['_CHECKSPECIALEVALUATIONPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_CHECKSPECIALEVALUATIONPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETAVAILABLESERVICEITEMTYPESPARAMS']._serialized_start=1127
  _globals['_GETAVAILABLESERVICEITEMTYPESPARAMS']._serialized_end=1229
  _globals['_GETAVAILABLESERVICEITEMTYPESRESULT']._serialized_start=1231
  _globals['_GETAVAILABLESERVICEITEMTYPESRESULT']._serialized_end=1356
  _globals['_GETAVAILABLEPETSPARAMS']._serialized_start=1359
  _globals['_GETAVAILABLEPETSPARAMS']._serialized_end=1575
  _globals['_GETAVAILABLEPETSRESULT']._serialized_start=1577
  _globals['_GETAVAILABLEPETSRESULT']._serialized_end=1676
  _globals['_GETAVAILABLEPETRESULT']._serialized_start=1679
  _globals['_GETAVAILABLEPETRESULT']._serialized_end=2190
  _globals['_GETAVAILABLEPETRESULT_QUESTIONANSWERSENTRY']._serialized_start=2124
  _globals['_GETAVAILABLEPETRESULT_QUESTIONANSWERSENTRY']._serialized_end=2190
  _globals['_GETAVAILABLEDATESPARAMS']._serialized_start=2193
  _globals['_GETAVAILABLEDATESPARAMS']._serialized_end=2517
  _globals['_GETAVAILABLEDATESRESULT']._serialized_start=2520
  _globals['_GETAVAILABLEDATESRESULT']._serialized_end=2924
  _globals['_GETAVAILABLETIMERANGESPARAMS']._serialized_start=2927
  _globals['_GETAVAILABLETIMERANGESPARAMS']._serialized_end=3802
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES']._serialized_start=3312
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES']._serialized_end=3784
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES_SERVICE']._serialized_start=3493
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES_SERVICE']._serialized_end=3784
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES_SERVICE_EVALUATION']._serialized_start=3632
  _globals['_GETAVAILABLETIMERANGESPARAMS_PETSERVICES_SERVICE_EVALUATION']._serialized_end=3773
  _globals['_GETAVAILABLETIMERANGESRESULT']._serialized_start=3805
  _globals['_GETAVAILABLETIMERANGESRESULT']._serialized_end=4215
  _globals['_GETAVAILABLETIMERANGESRESULT_DAYAVAILABLETIMERANGES']._serialized_start=3963
  _globals['_GETAVAILABLETIMERANGESRESULT_DAYAVAILABLETIMERANGES']._serialized_end=4215
  _globals['_GETBUSINESSWORKINGHOURPARAMS']._serialized_start=4217
  _globals['_GETBUSINESSWORKINGHOURPARAMS']._serialized_end=4313
  _globals['_GETBUSINESSWORKINGHOURRESULT']._serialized_start=4316
  _globals['_GETBUSINESSWORKINGHOURRESULT']._serialized_end=4866
  _globals['_GETPETAVAILABLESERVICESPARAMS']._serialized_start=4869
  _globals['_GETPETAVAILABLESERVICESPARAMS']._serialized_end=5626
  _globals['_GETPETAVAILABLESERVICESRESULT']._serialized_start=5629
  _globals['_GETPETAVAILABLESERVICESRESULT']._serialized_end=6552
  _globals['_GETPETAVAILABLESERVICESRESULT_SERVICECAPACITYENTRY']._serialized_start=6307
  _globals['_GETPETAVAILABLESERVICESRESULT_SERVICECAPACITYENTRY']._serialized_end=6452
  _globals['_GETPETAVAILABLESERVICESRESULT_SERVICECAPACITY']._serialized_start=6454
  _globals['_GETPETAVAILABLESERVICESRESULT_SERVICECAPACITY']._serialized_end=6552
  _globals['_GETAVAILABLEEVALUATIONLISTPARAMS']._serialized_start=6555
  _globals['_GETAVAILABLEEVALUATIONLISTPARAMS']._serialized_end=6856
  _globals['_GETAVAILABLEEVALUATIONLISTRESULT']._serialized_start=6859
  _globals['_GETAVAILABLEEVALUATIONLISTRESULT']._serialized_end=7032
  _globals['_GETACCEPTEDPETTYPESPARAMS']._serialized_start=7035
  _globals['_GETACCEPTEDPETTYPESPARAMS']._serialized_end=7227
  _globals['_GETACCEPTEDPETTYPESRESULT']._serialized_start=7229
  _globals['_GETACCEPTEDPETTYPESRESULT']._serialized_end=7320
  _globals['_GETACCEPTEDCUSTOMERTYPESPARAMS']._serialized_start=7322
  _globals['_GETACCEPTEDCUSTOMERTYPESPARAMS']._serialized_end=7420
  _globals['_GETACCEPTEDCUSTOMERTYPESRESULT']._serialized_start=7423
  _globals['_GETACCEPTEDCUSTOMERTYPESRESULT']._serialized_end=7803
  _globals['_GETACCEPTEDCUSTOMERTYPESRESULT_ACCEPTCUSTOMERTYPE']._serialized_start=7594
  _globals['_GETACCEPTEDCUSTOMERTYPESRESULT_ACCEPTCUSTOMERTYPE']._serialized_end=7803
  _globals['_GETTIMERANGEPARAMS']._serialized_start=7806
  _globals['_GETTIMERANGEPARAMS']._serialized_end=7991
  _globals['_GETTIMERANGERESULT']._serialized_start=7994
  _globals['_GETTIMERANGERESULT']._serialized_end=8128
  _globals['_GETAVAILABLEGROUPCLASSESPARAMS']._serialized_start=8131
  _globals['_GETAVAILABLEGROUPCLASSESPARAMS']._serialized_end=8319
  _globals['_GETAVAILABLEGROUPCLASSESRESULT']._serialized_start=8322
  _globals['_GETAVAILABLEGROUPCLASSESRESULT']._serialized_end=8776
  _globals['_GETAVAILABLEGROUPCLASSESRESULT_OBSERVICECONFIGVIEW']._serialized_start=8636
  _globals['_GETAVAILABLEGROUPCLASSESRESULT_OBSERVICECONFIGVIEW']._serialized_end=8776
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESPARAMS']._serialized_start=8779
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESPARAMS']._serialized_end=8992
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESRESULT']._serialized_start=8995
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESRESULT']._serialized_end=9747
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESRESULT_GROUPCLASSINSTANCEWITHSESSIONSVIEW']._serialized_start=9270
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESRESULT_GROUPCLASSINSTANCEWITHSESSIONSVIEW']._serialized_end=9747
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESRESULT_GROUPCLASSINSTANCEWITHSESSIONSVIEW_TRAINERVIEW']._serialized_start=9625
  _globals['_GETPETAVAILABLEGROUPCLASSINSTANCESRESULT_GROUPCLASSINSTANCEWITHSESSIONSVIEW_TRAINERVIEW']._serialized_end=9747
  _globals['_CHECKSPECIALEVALUATIONPARAMS']._serialized_start=9750
  _globals['_CHECKSPECIALEVALUATIONPARAMS']._serialized_end=10105
  _globals['_CHECKSPECIALEVALUATIONPARAMS_PETSERVICEIDSENTRY']._serialized_start=9952
  _globals['_CHECKSPECIALEVALUATIONPARAMS_PETSERVICEIDSENTRY']._serialized_end=10087
  _globals['_CHECKSPECIALEVALUATIONRESULT']._serialized_start=10107
  _globals['_CHECKSPECIALEVALUATIONRESULT']._serialized_end=10231
  _globals['_BOOKINGAVAILABILITYSERVICE']._serialized_start=10234
  _globals['_BOOKINGAVAILABILITYSERVICE']._serialized_end=12233
# @@protoc_insertion_point(module_scope)
