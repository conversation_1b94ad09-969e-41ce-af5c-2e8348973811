from google.type import date_pb2 as _date_pb2
from moego.client.online_booking.v1 import booking_request_api_pb2 as _booking_request_api_pb2
from moego.models.business_customer.v1 import business_customer_pet_models_pb2 as _business_customer_pet_models_pb2
from moego.models.business_customer.v1 import business_pet_vaccine_record_models_pb2 as _business_pet_vaccine_record_models_pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as _customer_pet_enums_pb2
from moego.models.grooming.v1 import service_enums_pb2 as _service_enums_pb2
from moego.models.offering.v1 import evaluation_models_pb2 as _evaluation_models_pb2
from moego.models.offering.v1 import group_class_models_pb2 as _group_class_models_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.offering.v1 import service_models_pb2 as _service_models_pb2
from moego.models.online_booking.v1 import booking_availability_defs_pb2 as _booking_availability_defs_pb2
from moego.models.online_booking.v1 import booking_availability_enums_pb2 as _booking_availability_enums_pb2
from moego.models.online_booking.v1 import ob_availability_setting_defs_pb2 as _ob_availability_setting_defs_pb2
from moego.models.online_booking.v1 import ob_availability_setting_enums_pb2 as _ob_availability_setting_enums_pb2
from moego.models.online_booking.v1 import service_config_models_pb2 as _service_config_models_pb2
from moego.models.online_booking.v1 import week_time_range_models_pb2 as _week_time_range_models_pb2
from moego.service.enterprise.v1 import specific_support_service_pb2 as _specific_support_service_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetAvailableServiceItemTypesParams(_message.Message):
    __slots__ = ("name", "domain")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ...) -> None: ...

class GetAvailableServiceItemTypesResult(_message.Message):
    __slots__ = ("service_item_types",)
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    def __init__(self, service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ...) -> None: ...

class GetAvailablePetsParams(_message.Message):
    __slots__ = ("name", "domain", "service_item_type")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    service_item_type: _service_enum_pb2.ServiceItemType
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ...) -> None: ...

class GetAvailablePetsResult(_message.Message):
    __slots__ = ("pets",)
    PETS_FIELD_NUMBER: _ClassVar[int]
    pets: _containers.RepeatedCompositeFieldContainer[GetAvailablePetResult]
    def __init__(self, pets: _Optional[_Iterable[_Union[GetAvailablePetResult, _Mapping]]] = ...) -> None: ...

class GetAvailablePetResult(_message.Message):
    __slots__ = ("pet", "question_answers", "vaccine_records", "unavailable_reasons")
    class QuestionAnswersEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    PET_FIELD_NUMBER: _ClassVar[int]
    QUESTION_ANSWERS_FIELD_NUMBER: _ClassVar[int]
    VACCINE_RECORDS_FIELD_NUMBER: _ClassVar[int]
    UNAVAILABLE_REASONS_FIELD_NUMBER: _ClassVar[int]
    pet: _business_customer_pet_models_pb2.BusinessCustomerPetOnlineBookingView
    question_answers: _containers.ScalarMap[str, str]
    vaccine_records: _containers.RepeatedCompositeFieldContainer[_business_pet_vaccine_record_models_pb2.BusinessPetVaccineRecordModel]
    unavailable_reasons: _containers.RepeatedScalarFieldContainer[_booking_availability_enums_pb2.PetUnavailableReason]
    def __init__(self, pet: _Optional[_Union[_business_customer_pet_models_pb2.BusinessCustomerPetOnlineBookingView, _Mapping]] = ..., question_answers: _Optional[_Mapping[str, str]] = ..., vaccine_records: _Optional[_Iterable[_Union[_business_pet_vaccine_record_models_pb2.BusinessPetVaccineRecordModel, _Mapping]]] = ..., unavailable_reasons: _Optional[_Iterable[_Union[_booking_availability_enums_pb2.PetUnavailableReason, str]]] = ...) -> None: ...

class GetAvailableDatesParams(_message.Message):
    __slots__ = ("name", "domain", "service_item_type", "start_date", "end_date")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    service_item_type: _service_enum_pb2.ServiceItemType
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ...) -> None: ...

class GetAvailableDatesResult(_message.Message):
    __slots__ = ("available_start_date", "available_end_date", "unavailable_dates", "unavailable_arrival_dates", "unavailable_pick_up_dates")
    AVAILABLE_START_DATE_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_END_DATE_FIELD_NUMBER: _ClassVar[int]
    UNAVAILABLE_DATES_FIELD_NUMBER: _ClassVar[int]
    UNAVAILABLE_ARRIVAL_DATES_FIELD_NUMBER: _ClassVar[int]
    UNAVAILABLE_PICK_UP_DATES_FIELD_NUMBER: _ClassVar[int]
    available_start_date: _date_pb2.Date
    available_end_date: _date_pb2.Date
    unavailable_dates: _containers.RepeatedCompositeFieldContainer[_date_pb2.Date]
    unavailable_arrival_dates: _containers.RepeatedCompositeFieldContainer[_date_pb2.Date]
    unavailable_pick_up_dates: _containers.RepeatedCompositeFieldContainer[_date_pb2.Date]
    def __init__(self, available_start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., available_end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., unavailable_dates: _Optional[_Iterable[_Union[_date_pb2.Date, _Mapping]]] = ..., unavailable_arrival_dates: _Optional[_Iterable[_Union[_date_pb2.Date, _Mapping]]] = ..., unavailable_pick_up_dates: _Optional[_Iterable[_Union[_date_pb2.Date, _Mapping]]] = ...) -> None: ...

class GetAvailableTimeRangesParams(_message.Message):
    __slots__ = ("name", "domain", "service_item_type", "start_date", "end_date", "pet_services")
    class PetServices(_message.Message):
        __slots__ = ("pet", "services")
        class Service(_message.Message):
            __slots__ = ("evaluation",)
            class Evaluation(_message.Message):
                __slots__ = ("evaluation_id", "date", "time")
                EVALUATION_ID_FIELD_NUMBER: _ClassVar[int]
                DATE_FIELD_NUMBER: _ClassVar[int]
                TIME_FIELD_NUMBER: _ClassVar[int]
                evaluation_id: int
                date: _date_pb2.Date
                time: int
                def __init__(self, evaluation_id: _Optional[int] = ..., date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., time: _Optional[int] = ...) -> None: ...
            EVALUATION_FIELD_NUMBER: _ClassVar[int]
            evaluation: GetAvailableTimeRangesParams.PetServices.Service.Evaluation
            def __init__(self, evaluation: _Optional[_Union[GetAvailableTimeRangesParams.PetServices.Service.Evaluation, _Mapping]] = ...) -> None: ...
        PET_FIELD_NUMBER: _ClassVar[int]
        SERVICES_FIELD_NUMBER: _ClassVar[int]
        pet: _booking_request_api_pb2.Pet
        services: _containers.RepeatedCompositeFieldContainer[GetAvailableTimeRangesParams.PetServices.Service]
        def __init__(self, pet: _Optional[_Union[_booking_request_api_pb2.Pet, _Mapping]] = ..., services: _Optional[_Iterable[_Union[GetAvailableTimeRangesParams.PetServices.Service, _Mapping]]] = ...) -> None: ...
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    PET_SERVICES_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    service_item_type: _service_enum_pb2.ServiceItemType
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    pet_services: _containers.RepeatedCompositeFieldContainer[GetAvailableTimeRangesParams.PetServices]
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., pet_services: _Optional[_Iterable[_Union[GetAvailableTimeRangesParams.PetServices, _Mapping]]] = ...) -> None: ...

class GetAvailableTimeRangesResult(_message.Message):
    __slots__ = ("day_time_ranges",)
    class DayAvailableTimeRanges(_message.Message):
        __slots__ = ("date", "arrival_time_range", "pick_up_time_range")
        DATE_FIELD_NUMBER: _ClassVar[int]
        ARRIVAL_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        PICK_UP_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        date: _date_pb2.Date
        arrival_time_range: _containers.RepeatedCompositeFieldContainer[_ob_availability_setting_defs_pb2.DayTimeRangeDef]
        pick_up_time_range: _containers.RepeatedCompositeFieldContainer[_ob_availability_setting_defs_pb2.DayTimeRangeDef]
        def __init__(self, date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., arrival_time_range: _Optional[_Iterable[_Union[_ob_availability_setting_defs_pb2.DayTimeRangeDef, _Mapping]]] = ..., pick_up_time_range: _Optional[_Iterable[_Union[_ob_availability_setting_defs_pb2.DayTimeRangeDef, _Mapping]]] = ...) -> None: ...
    DAY_TIME_RANGES_FIELD_NUMBER: _ClassVar[int]
    day_time_ranges: _containers.RepeatedCompositeFieldContainer[GetAvailableTimeRangesResult.DayAvailableTimeRanges]
    def __init__(self, day_time_ranges: _Optional[_Iterable[_Union[GetAvailableTimeRangesResult.DayAvailableTimeRanges, _Mapping]]] = ...) -> None: ...

class GetBusinessWorkingHourParams(_message.Message):
    __slots__ = ("name", "domain")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ...) -> None: ...

class GetBusinessWorkingHourResult(_message.Message):
    __slots__ = ("monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday")
    MONDAY_FIELD_NUMBER: _ClassVar[int]
    TUESDAY_FIELD_NUMBER: _ClassVar[int]
    WEDNESDAY_FIELD_NUMBER: _ClassVar[int]
    THURSDAY_FIELD_NUMBER: _ClassVar[int]
    FRIDAY_FIELD_NUMBER: _ClassVar[int]
    SATURDAY_FIELD_NUMBER: _ClassVar[int]
    SUNDAY_FIELD_NUMBER: _ClassVar[int]
    monday: _containers.RepeatedCompositeFieldContainer[_week_time_range_models_pb2.TimeRangeModel]
    tuesday: _containers.RepeatedCompositeFieldContainer[_week_time_range_models_pb2.TimeRangeModel]
    wednesday: _containers.RepeatedCompositeFieldContainer[_week_time_range_models_pb2.TimeRangeModel]
    thursday: _containers.RepeatedCompositeFieldContainer[_week_time_range_models_pb2.TimeRangeModel]
    friday: _containers.RepeatedCompositeFieldContainer[_week_time_range_models_pb2.TimeRangeModel]
    saturday: _containers.RepeatedCompositeFieldContainer[_week_time_range_models_pb2.TimeRangeModel]
    sunday: _containers.RepeatedCompositeFieldContainer[_week_time_range_models_pb2.TimeRangeModel]
    def __init__(self, monday: _Optional[_Iterable[_Union[_week_time_range_models_pb2.TimeRangeModel, _Mapping]]] = ..., tuesday: _Optional[_Iterable[_Union[_week_time_range_models_pb2.TimeRangeModel, _Mapping]]] = ..., wednesday: _Optional[_Iterable[_Union[_week_time_range_models_pb2.TimeRangeModel, _Mapping]]] = ..., thursday: _Optional[_Iterable[_Union[_week_time_range_models_pb2.TimeRangeModel, _Mapping]]] = ..., friday: _Optional[_Iterable[_Union[_week_time_range_models_pb2.TimeRangeModel, _Mapping]]] = ..., saturday: _Optional[_Iterable[_Union[_week_time_range_models_pb2.TimeRangeModel, _Mapping]]] = ..., sunday: _Optional[_Iterable[_Union[_week_time_range_models_pb2.TimeRangeModel, _Mapping]]] = ...) -> None: ...

class GetPetAvailableServicesParams(_message.Message):
    __slots__ = ("name", "domain", "service_item_type", "service_type", "pets", "selected_service_ids", "start_date", "end_date", "specific_dates", "book_with_other_service_item_type")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    PETS_FIELD_NUMBER: _ClassVar[int]
    SELECTED_SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    BOOK_WITH_OTHER_SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    service_item_type: _service_enum_pb2.ServiceItemType
    service_type: _service_enum_pb2.ServiceType
    pets: _containers.RepeatedCompositeFieldContainer[_booking_availability_defs_pb2.BookingPetDef]
    selected_service_ids: _containers.RepeatedScalarFieldContainer[int]
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    specific_dates: _containers.RepeatedCompositeFieldContainer[_date_pb2.Date]
    book_with_other_service_item_type: bool
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., service_type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., pets: _Optional[_Iterable[_Union[_booking_availability_defs_pb2.BookingPetDef, _Mapping]]] = ..., selected_service_ids: _Optional[_Iterable[int]] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., specific_dates: _Optional[_Iterable[_Union[_date_pb2.Date, _Mapping]]] = ..., book_with_other_service_item_type: bool = ...) -> None: ...

class GetPetAvailableServicesResult(_message.Message):
    __slots__ = ("categories", "available_service_ids", "pet_services", "service_configs", "service_capacity", "bundle_sale_services", "service_brief_views")
    class ServiceCapacityEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: GetPetAvailableServicesResult.ServiceCapacity
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[GetPetAvailableServicesResult.ServiceCapacity, _Mapping]] = ...) -> None: ...
    class ServiceCapacity(_message.Message):
        __slots__ = ("service_id", "is_lodging_available")
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        IS_LODGING_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
        service_id: int
        is_lodging_available: bool
        def __init__(self, service_id: _Optional[int] = ..., is_lodging_available: bool = ...) -> None: ...
    CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
    PET_SERVICES_FIELD_NUMBER: _ClassVar[int]
    SERVICE_CONFIGS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_CAPACITY_FIELD_NUMBER: _ClassVar[int]
    BUNDLE_SALE_SERVICES_FIELD_NUMBER: _ClassVar[int]
    SERVICE_BRIEF_VIEWS_FIELD_NUMBER: _ClassVar[int]
    categories: _containers.RepeatedCompositeFieldContainer[_service_models_pb2.CustomizedServiceCategoryView]
    available_service_ids: _containers.RepeatedScalarFieldContainer[int]
    pet_services: _containers.RepeatedCompositeFieldContainer[_booking_availability_defs_pb2.BookingPetServiceDef]
    service_configs: _containers.RepeatedCompositeFieldContainer[_service_config_models_pb2.ServiceConfigView]
    service_capacity: _containers.MessageMap[int, GetPetAvailableServicesResult.ServiceCapacity]
    bundle_sale_services: _containers.RepeatedCompositeFieldContainer[_service_models_pb2.ServiceBundleSaleView]
    service_brief_views: _containers.RepeatedCompositeFieldContainer[_service_models_pb2.ServiceBriefView]
    def __init__(self, categories: _Optional[_Iterable[_Union[_service_models_pb2.CustomizedServiceCategoryView, _Mapping]]] = ..., available_service_ids: _Optional[_Iterable[int]] = ..., pet_services: _Optional[_Iterable[_Union[_booking_availability_defs_pb2.BookingPetServiceDef, _Mapping]]] = ..., service_configs: _Optional[_Iterable[_Union[_service_config_models_pb2.ServiceConfigView, _Mapping]]] = ..., service_capacity: _Optional[_Mapping[int, GetPetAvailableServicesResult.ServiceCapacity]] = ..., bundle_sale_services: _Optional[_Iterable[_Union[_service_models_pb2.ServiceBundleSaleView, _Mapping]]] = ..., service_brief_views: _Optional[_Iterable[_Union[_service_models_pb2.ServiceBriefView, _Mapping]]] = ...) -> None: ...

class GetAvailableEvaluationListParams(_message.Message):
    __slots__ = ("name", "domain", "service_item_type", "pets")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    PETS_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    service_item_type: _service_enum_pb2.ServiceItemType
    pets: _containers.RepeatedCompositeFieldContainer[_booking_availability_defs_pb2.BookingPetDef]
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., pets: _Optional[_Iterable[_Union[_booking_availability_defs_pb2.BookingPetDef, _Mapping]]] = ...) -> None: ...

class GetAvailableEvaluationListResult(_message.Message):
    __slots__ = ("evaluations", "available_evaluation_ids")
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_EVALUATION_IDS_FIELD_NUMBER: _ClassVar[int]
    evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationBriefView]
    available_evaluation_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]]] = ..., available_evaluation_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class GetAcceptedPetTypesParams(_message.Message):
    __slots__ = ("name", "domain", "service_item_type")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    service_item_type: _service_enum_pb2.ServiceItemType
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ...) -> None: ...

class GetAcceptedPetTypesResult(_message.Message):
    __slots__ = ("pet_types",)
    PET_TYPES_FIELD_NUMBER: _ClassVar[int]
    pet_types: _containers.RepeatedScalarFieldContainer[_customer_pet_enums_pb2.PetType]
    def __init__(self, pet_types: _Optional[_Iterable[_Union[_customer_pet_enums_pb2.PetType, str]]] = ...) -> None: ...

class GetAcceptedCustomerTypesParams(_message.Message):
    __slots__ = ("name", "domain")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ...) -> None: ...

class GetAcceptedCustomerTypesResult(_message.Message):
    __slots__ = ("accept_customer_types",)
    class AcceptCustomerType(_message.Message):
        __slots__ = ("service_item_type", "accept_customer_type")
        SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
        ACCEPT_CUSTOMER_TYPE_FIELD_NUMBER: _ClassVar[int]
        service_item_type: _service_enum_pb2.ServiceItemType
        accept_customer_type: _ob_availability_setting_enums_pb2.AcceptCustomerType
        def __init__(self, service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., accept_customer_type: _Optional[_Union[_ob_availability_setting_enums_pb2.AcceptCustomerType, str]] = ...) -> None: ...
    ACCEPT_CUSTOMER_TYPES_FIELD_NUMBER: _ClassVar[int]
    accept_customer_types: _containers.RepeatedCompositeFieldContainer[GetAcceptedCustomerTypesResult.AcceptCustomerType]
    def __init__(self, accept_customer_types: _Optional[_Iterable[_Union[GetAcceptedCustomerTypesResult.AcceptCustomerType, _Mapping]]] = ...) -> None: ...

class GetTimeRangeParams(_message.Message):
    __slots__ = ("name", "domain", "service_item_type")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    service_item_type: _service_enum_pb2.ServiceItemType
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ...) -> None: ...

class GetTimeRangeResult(_message.Message):
    __slots__ = ("arrival_pick_up_time_range",)
    ARRIVAL_PICK_UP_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
    arrival_pick_up_time_range: _ob_availability_setting_defs_pb2.ArrivalPickUpTimeDef
    def __init__(self, arrival_pick_up_time_range: _Optional[_Union[_ob_availability_setting_defs_pb2.ArrivalPickUpTimeDef, _Mapping]] = ...) -> None: ...

class GetAvailableGroupClassesParams(_message.Message):
    __slots__ = ("name", "domain", "pet_id", "pagination")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    pet_id: int
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., pet_id: _Optional[int] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class GetAvailableGroupClassesResult(_message.Message):
    __slots__ = ("group_classes", "ob_service_configs", "pagination")
    class OBServiceConfigView(_message.Message):
        __slots__ = ("group_class_id", "show_price_type")
        GROUP_CLASS_ID_FIELD_NUMBER: _ClassVar[int]
        SHOW_PRICE_TYPE_FIELD_NUMBER: _ClassVar[int]
        group_class_id: int
        show_price_type: _service_enums_pb2.ShowBasePrice
        def __init__(self, group_class_id: _Optional[int] = ..., show_price_type: _Optional[_Union[_service_enums_pb2.ShowBasePrice, str]] = ...) -> None: ...
    GROUP_CLASSES_FIELD_NUMBER: _ClassVar[int]
    OB_SERVICE_CONFIGS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    group_classes: _containers.RepeatedCompositeFieldContainer[_group_class_models_pb2.GroupClassModel]
    ob_service_configs: _containers.RepeatedCompositeFieldContainer[GetAvailableGroupClassesResult.OBServiceConfigView]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, group_classes: _Optional[_Iterable[_Union[_group_class_models_pb2.GroupClassModel, _Mapping]]] = ..., ob_service_configs: _Optional[_Iterable[_Union[GetAvailableGroupClassesResult.OBServiceConfigView, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class GetPetAvailableGroupClassInstancesParams(_message.Message):
    __slots__ = ("name", "domain", "group_class_id", "pagination")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    GROUP_CLASS_ID_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    group_class_id: int
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., group_class_id: _Optional[int] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class GetPetAvailableGroupClassInstancesResult(_message.Message):
    __slots__ = ("group_class_instances", "pagination")
    class GroupClassInstanceWithSessionsView(_message.Message):
        __slots__ = ("group_class_instance", "sessions", "trainer")
        class TrainerView(_message.Message):
            __slots__ = ("id", "first_name", "last_name", "avatar_path")
            ID_FIELD_NUMBER: _ClassVar[int]
            FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
            LAST_NAME_FIELD_NUMBER: _ClassVar[int]
            AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
            id: int
            first_name: str
            last_name: str
            avatar_path: str
            def __init__(self, id: _Optional[int] = ..., first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., avatar_path: _Optional[str] = ...) -> None: ...
        GROUP_CLASS_INSTANCE_FIELD_NUMBER: _ClassVar[int]
        SESSIONS_FIELD_NUMBER: _ClassVar[int]
        TRAINER_FIELD_NUMBER: _ClassVar[int]
        group_class_instance: _group_class_models_pb2.GroupClassInstance
        sessions: _containers.RepeatedCompositeFieldContainer[_group_class_models_pb2.GroupClassSession]
        trainer: GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.TrainerView
        def __init__(self, group_class_instance: _Optional[_Union[_group_class_models_pb2.GroupClassInstance, _Mapping]] = ..., sessions: _Optional[_Iterable[_Union[_group_class_models_pb2.GroupClassSession, _Mapping]]] = ..., trainer: _Optional[_Union[GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView.TrainerView, _Mapping]] = ...) -> None: ...
    GROUP_CLASS_INSTANCES_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    group_class_instances: _containers.RepeatedCompositeFieldContainer[GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, group_class_instances: _Optional[_Iterable[_Union[GetPetAvailableGroupClassInstancesResult.GroupClassInstanceWithSessionsView, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class CheckSpecialEvaluationParams(_message.Message):
    __slots__ = ("name", "domain", "pet_service_ids")
    class PetServiceIdsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: _specific_support_service_pb2.CheckSpecialEvaluationRequest.ServiceIDs
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[_specific_support_service_pb2.CheckSpecialEvaluationRequest.ServiceIDs, _Mapping]] = ...) -> None: ...
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    PET_SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    pet_service_ids: _containers.MessageMap[int, _specific_support_service_pb2.CheckSpecialEvaluationRequest.ServiceIDs]
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., pet_service_ids: _Optional[_Mapping[int, _specific_support_service_pb2.CheckSpecialEvaluationRequest.ServiceIDs]] = ...) -> None: ...

class CheckSpecialEvaluationResult(_message.Message):
    __slots__ = ("results",)
    RESULTS_FIELD_NUMBER: _ClassVar[int]
    results: _containers.RepeatedCompositeFieldContainer[_specific_support_service_pb2.CheckSpecialEvaluationResponse.Result]
    def __init__(self, results: _Optional[_Iterable[_Union[_specific_support_service_pb2.CheckSpecialEvaluationResponse.Result, _Mapping]]] = ...) -> None: ...
