# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/online_booking/v1/booking_request_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/online_booking/v1/booking_request_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.models.appointment.v1 import appointment_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__defs__pb2
from moego.models.appointment.v1 import appointment_pet_medication_schedule_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__pet__medication__schedule__defs__pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__enums__pb2
from moego.models.online_booking.v1 import feeding_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_feeding__models__pb2
from moego.models.online_booking.v1 import medication_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_medication__models__pb2
from moego.service.online_booking.v1 import booking_request_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8moego/client/online_booking/v1/booking_request_api.proto\x12\x1emoego.client.online_booking.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x16google/type/date.proto\x1a\x32moego/models/appointment/v1/appointment_defs.proto\x1aJmoego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto\x1a\x32moego/models/appointment/v1/pet_detail_enums.proto\x1a\x33moego/models/online_booking/v1/feeding_models.proto\x1a\x36moego/models/online_booking/v1/medication_models.proto\x1a=moego/service/online_booking/v1/booking_request_service.proto\x1a\x17validate/validate.proto\"\x9f\x04\n\x07\x41\x64\x64ress\x12\x1c\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x02id\x88\x01\x01\x12+\n\x08\x61\x64\x64ress1\x18\x02 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x01R\x08\x61\x64\x64ress1\x88\x01\x01\x12+\n\x08\x61\x64\x64ress2\x18\x03 \x01(\tB\n\xfa\x42\x07r\x05\x10\x00\x18\xff\x01H\x02R\x08\x61\x64\x64ress2\x88\x01\x01\x12#\n\x04\x63ity\x18\x04 \x01(\tB\n\xfa\x42\x07r\x05\x10\x00\x18\xff\x01H\x03R\x04\x63ity\x88\x01\x01\x12%\n\x05state\x18\x05 \x01(\tB\n\xfa\x42\x07r\x05\x10\x00\x18\xff\x01H\x04R\x05state\x88\x01\x01\x12(\n\x07zipcode\x18\x06 \x01(\tB\t\xfa\x42\x06r\x04\x10\x00\x18\x32H\x05R\x07zipcode\x88\x01\x01\x12)\n\x07\x63ountry\x18\x07 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x06R\x07\x63ountry\x88\x01\x01\x12\x1e\n\x03lat\x18\x08 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x07R\x03lat\x88\x01\x01\x12\x1e\n\x03lng\x18\t \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x08R\x03lng\x88\x01\x01\x12@\n\x1ais_profile_request_address\x18\n \x01(\x08H\tR\x17isProfileRequestAddress\x88\x01\x01\x42\x05\n\x03_idB\x0b\n\t_address1B\x0b\n\t_address2B\x07\n\x05_cityB\x08\n\x06_stateB\n\n\x08_zipcodeB\n\n\x08_countryB\x06\n\x04_latB\x06\n\x04_lngB\x1d\n\x1b_is_profile_request_address\"\xad\x0e\n\x08\x43ustomer\x12.\n\nfirst_name\x18\x01 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x00R\tfirstName\x88\x01\x01\x12*\n\tlast_name\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x01R\x08lastName\x88\x01\x01\x12\x30\n\x0cphone_number\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x02R\x0bphoneNumber\x88\x01\x01\x12%\n\x05\x65mail\x18\x04 \x01(\tB\n\xfa\x42\x07r\x05`\x01\xd0\x01\x01H\x03R\x05\x65mail\x88\x01\x01\x12Y\n\x0b\x61nswers_map\x18\x05 \x03(\x0b\x32\x38.moego.client.online_booking.v1.Customer.AnswersMapEntryR\nanswersMap\x12&\n\x0c\x63harge_token\x18\x06 \x01(\tH\x04R\x0b\x63hargeToken\x88\x01\x01\x12+\n\x0fhas_stripe_card\x18\x07 \x01(\x08H\x05R\rhasStripeCard\x88\x01\x01\x12\x31\n\x12stripe_customer_id\x18\x08 \x01(\tH\x06R\x10stripeCustomerId\x88\x01\x01\x12\x65\n\x0f\x61\x64\x64itional_info\x18\t \x01(\x0b\x32\x37.moego.client.online_booking.v1.Customer.AdditionalInfoH\x07R\x0e\x61\x64\x64itionalInfo\x88\x01\x01\x12\x46\n\x07\x61\x64\x64ress\x18\n \x01(\x0b\x32\'.moego.client.online_booking.v1.AddressH\x08R\x07\x61\x64\x64ress\x88\x01\x01\x12;\n\x08\x62irthday\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.TimestampH\tR\x08\x62irthday\x88\x01\x01\x12]\n\x11\x65mergency_contact\x18\x0c \x01(\x0b\x32\x30.moego.client.online_booking.v1.Customer.ContactR\x10\x65mergencyContact\x12W\n\x0epickup_contact\x18\r \x01(\x0b\x32\x30.moego.client.online_booking.v1.Customer.ContactR\rpickupContact\x1aU\n\x0f\x41nswersMapEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12,\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.ValueR\x05value:\x02\x38\x01\x1a\xa9\x04\n\x0e\x41\x64\x64itionalInfo\x12:\n\x12referral_source_id\x18\x01 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x00R\x10referralSourceId\x88\x01\x01\x12?\n\x14referral_source_desc\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x01R\x12referralSourceDesc\x88\x01\x01\x12>\n\x14preferred_groomer_id\x18\x03 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x02R\x12preferredGroomerId\x88\x01\x01\x12\x44\n\x17preferred_frequency_day\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x03R\x15preferredFrequencyDay\x88\x01\x01\x12\x46\n\x18preferred_frequency_type\x18\x05 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x04R\x16preferredFrequencyType\x88\x01\x01\x12#\n\rpreferred_day\x18\x06 \x03(\x05R\x0cpreferredDay\x12%\n\x0epreferred_time\x18\x07 \x03(\x05R\rpreferredTimeB\x15\n\x13_referral_source_idB\x17\n\x15_referral_source_descB\x17\n\x15_preferred_groomer_idB\x1a\n\x18_preferred_frequency_dayB\x1b\n\x19_preferred_frequency_type\x1a\xc0\x01\n\x07\x43ontact\x12+\n\nfirst_name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x00R\tfirstName\x88\x01\x01\x12)\n\tlast_name\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x01R\x08lastName\x88\x01\x01\x12/\n\x0cphone_number\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x1eH\x02R\x0bphoneNumber\x88\x01\x01\x42\r\n\x0b_first_nameB\x0c\n\n_last_nameB\x0f\n\r_phone_numberB\r\n\x0b_first_nameB\x0c\n\n_last_nameB\x0f\n\r_phone_numberB\x08\n\x06_emailB\x0f\n\r_charge_tokenB\x12\n\x10_has_stripe_cardB\x15\n\x13_stripe_customer_idB\x12\n\x10_additional_infoB\n\n\x08_addressB\x0b\n\t_birthday\"\x9b\x0f\n\x03Pet\x12#\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x05petId\x88\x01\x01\x12*\n\x08pet_name\x18\x02 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x01R\x07petName\x88\x01\x01\x12.\n\x0b\x61vatar_path\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x02R\navatarPath\x88\x01\x01\x12#\n\x05\x62reed\x18\x04 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x03R\x05\x62reed\x88\x01\x01\x12)\n\tbreed_mix\x18\x05 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x04R\x08\x62reedMix\x88\x01\x01\x12,\n\x0bpet_type_id\x18\x06 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x05R\tpetTypeId\x88\x01\x01\x12$\n\x06gender\x18\x07 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x06R\x06gender\x88\x01\x01\x12>\n\x08\x62irthday\x18\x08 \x01(\tB\x1d\xfa\x42\x1ar\x18\x32\x13^\\d{4}-\\d{2}-\\d{2}$\xd0\x01\x01H\x07R\x08\x62irthday\x88\x01\x01\x12%\n\x06weight\x18\t \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x08R\x06weight\x88\x01\x01\x12#\n\x05\x66ixed\x18\n \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\tR\x05\x66ixed\x88\x01\x01\x12)\n\x08\x62\x65havior\x18\x0b \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\nR\x08\x62\x65havior\x88\x01\x01\x12.\n\x0bhair_length\x18\x0c \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x0bR\nhairLength\x88\x01\x01\x12=\n\x13\x65xpiry_notification\x18\r \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x0cR\x12\x65xpiryNotification\x88\x01\x01\x12(\n\x08vet_name\x18\x0e \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\rR\x07vetName\x88\x01\x01\x12*\n\tvet_phone\x18\x0f \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x0eR\x08vetPhone\x88\x01\x01\x12$\n\x0bvet_address\x18\x10 \x01(\tH\x0fR\nvetAddress\x88\x01\x01\x12\x43\n\x16\x65mergency_contact_name\x18\x11 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x10R\x14\x65mergencyContactName\x88\x01\x01\x12\x45\n\x17\x65mergency_contact_phone\x18\x12 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x11R\x15\x65mergencyContactPhone\x88\x01\x01\x12\x32\n\rhealth_issues\x18\x13 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x12R\x0chealthIssues\x88\x01\x01\x12N\n\x0cvaccine_list\x18\x14 \x03(\x0b\x32+.moego.client.online_booking.v1.Pet.VaccineR\x0bvaccineList\x12*\n\tpet_image\x18\x15 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x13R\x08petImage\x88\x01\x01\x12m\n\x14pet_question_answers\x18\x19 \x03(\x0b\x32;.moego.client.online_booking.v1.Pet.PetQuestionAnswersEntryR\x12petQuestionAnswers\x1a]\n\x17PetQuestionAnswersEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12,\n\x05value\x18\x02 \x01(\x0b\x32\x16.google.protobuf.ValueR\x05value:\x02\x38\x01\x1a\x8e\x03\n\x07Vaccine\x12:\n\x12vaccine_binding_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x10vaccineBindingId\x88\x01\x01\x12 \n\x04type\x18\x02 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x01R\x04type\x88\x01\x01\x12+\n\nvaccine_id\x18\x03 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x02R\tvaccineId\x88\x01\x01\x12K\n\x0f\x65xpiration_date\x18\x04 \x01(\tB\x1d\xfa\x42\x1ar\x18\x32\x13^\\d{4}-\\d{2}-\\d{2}$\xd0\x01\x01H\x03R\x0e\x65xpirationDate\x88\x01\x01\x12.\n\x10vaccine_document\x18\x05 \x01(\tH\x04R\x0fvaccineDocument\x88\x01\x01\x12#\n\rdocument_urls\x18\x06 \x03(\tR\x0c\x64ocumentUrlsB\x15\n\x13_vaccine_binding_idB\x07\n\x05_typeB\r\n\x0b_vaccine_idB\x12\n\x10_expiration_dateB\x13\n\x11_vaccine_documentB\t\n\x07_pet_idB\x0b\n\t_pet_nameB\x0e\n\x0c_avatar_pathB\x08\n\x06_breedB\x0c\n\n_breed_mixB\x0e\n\x0c_pet_type_idB\t\n\x07_genderB\x0b\n\t_birthdayB\t\n\x07_weightB\x08\n\x06_fixedB\x0b\n\t_behaviorB\x0e\n\x0c_hair_lengthB\x16\n\x14_expiry_notificationB\x0b\n\t_vet_nameB\x0c\n\n_vet_phoneB\x0e\n\x0c_vet_addressB\x19\n\x17_emergency_contact_nameB\x1a\n\x18_emergency_contact_phoneB\x10\n\x0e_health_issuesB\x0c\n\n_pet_image\"B\n\tAgreement\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x1c\n\tsignature\x18\x03 \x01(\tR\tsignature\"\xe5\x03\n\x07\x46\x65\x65\x64ing\x12P\n\x04time\x18\x01 \x03(\x0b\x32<.moego.models.online_booking.v1.FeedingModel.FeedingScheduleR\x04time\x12-\n\x06\x61mount\x18\x02 \x01(\x01\x42\x10\x18\x01\xfa\x42\x0b\x12\t!\x00\x00\x00\x00\x00\x00\x00\x00H\x00R\x06\x61mount\x88\x01\x01\x12!\n\x04unit\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x01R\x04unit\x88\x01\x01\x12*\n\tfood_type\x18\x04 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x02R\x08\x66oodType\x88\x01\x01\x12.\n\x0b\x66ood_source\x18\x05 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x03R\nfoodSource\x88\x01\x01\x12/\n\x0binstruction\x18\x06 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x04R\x0binstruction\x88\x01\x01\x12!\n\x04note\x18\x07 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x05R\x04note\x88\x01\x01\x12,\n\namount_str\x18\x08 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x06R\tamountStr\x88\x01\x01\x42\t\n\x07_amountB\x07\n\x05_unitB\x0c\n\n_food_typeB\x0e\n\x0c_food_sourceB\x0e\n\x0c_instructionB\x07\n\x05_noteB\r\n\x0b_amount_str\"\x90\x04\n\nMedication\x12V\n\x04time\x18\x01 \x03(\x0b\x32\x42.moego.models.online_booking.v1.MedicationModel.MedicationScheduleR\x04time\x12-\n\x06\x61mount\x18\x02 \x01(\x01\x42\x10\x18\x01\xfa\x42\x0b\x12\t!\x00\x00\x00\x00\x00\x00\x00\x00H\x00R\x06\x61mount\x88\x01\x01\x12!\n\x04unit\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x01R\x04unit\x88\x01\x01\x12\x36\n\x0fmedication_name\x18\x04 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x02R\x0emedicationName\x88\x01\x01\x12#\n\x05notes\x18\x05 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x03R\x05notes\x88\x01\x01\x12,\n\namount_str\x18\x08 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x04R\tamountStr\x88\x01\x01\x12z\n\rselected_date\x18\t \x01(\x0b\x32P.moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDefH\x05R\x0cselectedDate\x88\x01\x01\x42\t\n\x07_amountB\x07\n\x05_unitB\x12\n\x10_medication_nameB\x08\n\x06_notesB\r\n\x0b_amount_strB\x10\n\x0e_selected_date\"\xe6\x03\n\rBoardingAddon\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12)\n\x0cis_every_day\x18\x03 \x01(\x08\x42\x02\x18\x01H\x00R\nisEveryDay\x88\x01\x01\x12>\n\x05\x64\x61tes\x18\x04 \x03(\tB(\xfa\x42%\x92\x01\"\" r\x1e\x32\x1c^[0-9]{4}-[0-9]{2}-[0-9]{2}$R\x05\x64\x61tes\x12#\n\rservice_price\x18\x05 \x01(\x01R\x0cservicePrice\x12\x15\n\x06tax_id\x18\x06 \x01(\x03R\x05taxId\x12\x1a\n\x08\x64uration\x18\x07 \x01(\x05R\x08\x64uration\x12-\n\x10quantity_per_day\x18\x08 \x01(\x05H\x01R\x0equantityPerDay\x88\x01\x01\x12P\n\tdate_type\x18\t \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeH\x02R\x08\x64\x61teType\x88\x01\x01\x12\x35\n\nstart_date\x18\n \x01(\x0b\x32\x11.google.type.DateH\x03R\tstartDate\x88\x01\x01\x42\x0f\n\r_is_every_dayB\x13\n\x11_quantity_per_dayB\x0c\n\n_date_typeB\r\n\x0b_start_date\"\xbb\x02\n\x0c\x44\x61ycareAddon\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12%\n\x0cis_every_day\x18\x03 \x01(\x08H\x00R\nisEveryDay\x88\x01\x01\x12>\n\x05\x64\x61tes\x18\x04 \x03(\tB(\xfa\x42%\x92\x01\"\" r\x1e\x32\x1c^[0-9]{4}-[0-9]{2}-[0-9]{2}$R\x05\x64\x61tes\x12#\n\rservice_price\x18\x05 \x01(\x01R\x0cservicePrice\x12\x15\n\x06tax_id\x18\x06 \x01(\x03R\x05taxId\x12\x1a\n\x08\x64uration\x18\x07 \x01(\x05R\x08\x64uration\x12-\n\x10quantity_per_day\x18\x08 \x01(\x05H\x01R\x0equantityPerDay\x88\x01\x01\x42\x0f\n\r_is_every_dayB\x13\n\x11_quantity_per_day\"(\n\rGroomingAddon\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\xeb\x19\n\x07Service\x12N\n\x08grooming\x18\x01 \x01(\x0b\x32\x30.moego.client.online_booking.v1.Service.GroomingH\x00R\x08grooming\x12N\n\x08\x62oarding\x18\x02 \x01(\x0b\x32\x30.moego.client.online_booking.v1.Service.BoardingH\x00R\x08\x62oarding\x12K\n\x07\x64\x61ycare\x18\x03 \x01(\x0b\x32/.moego.client.online_booking.v1.Service.DaycareH\x00R\x07\x64\x61ycare\x12T\n\nevaluation\x18\x04 \x01(\x0b\x32\x32.moego.client.online_booking.v1.Service.EvaluationH\x00R\nevaluation\x12U\n\x0b\x64og_walking\x18\x05 \x01(\x0b\x32\x32.moego.client.online_booking.v1.Service.DogWalkingH\x00R\ndogWalking\x12U\n\x0bgroup_class\x18\x06 \x01(\x0b\x32\x32.moego.client.online_booking.v1.Service.GroupClassH\x00R\ngroupClass\x1a\xbd\x01\n\x08Grooming\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12\x42\n\nstart_date\x18\x03 \x01(\tB#\xfa\x42 r\x1e\x32\x1c^[0-9]{4}-[0-9]{2}-[0-9]{2}$R\tstartDate\x12\x45\n\x06\x61\x64\x64ons\x18\x04 \x03(\x0b\x32-.moego.client.online_booking.v1.GroomingAddonR\x06\x61\x64\x64ons\x1a\xff\x06\n\x08\x42oarding\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12G\n\nstart_date\x18\x03 \x01(\tB#\xfa\x42 r\x1e\x32\x1c^[0-9]{4}-[0-9]{2}-[0-9]{2}$H\x00R\tstartDate\x88\x01\x01\x12-\n\x0c\x61rrival_time\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x0b\x61rrivalTime\x12\x43\n\x08\x65nd_date\x18\x05 \x01(\tB#\xfa\x42 r\x1e\x32\x1c^[0-9]{4}-[0-9]{2}-[0-9]{2}$H\x01R\x07\x65ndDate\x88\x01\x01\x12+\n\x0bpickup_time\x18\x06 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\npickupTime\x12#\n\rservice_price\x18\x07 \x01(\x01R\x0cservicePrice\x12\x15\n\x06tax_id\x18\x08 \x01(\x03R\x05taxId\x12J\n\x07\x66\x65\x65\x64ing\x18\t \x01(\x0b\x32\'.moego.client.online_booking.v1.FeedingB\x02\x18\x01H\x02R\x07\x66\x65\x65\x64ing\x88\x01\x01\x12S\n\nmedication\x18\n \x01(\x0b\x32*.moego.client.online_booking.v1.MedicationB\x02\x18\x01H\x03R\nmedication\x88\x01\x01\x12\x45\n\x06\x61\x64\x64ons\x18\x0b \x03(\x0b\x32-.moego.client.online_booking.v1.BoardingAddonR\x06\x61\x64\x64ons\x12\x43\n\x08\x66\x65\x65\x64ings\x18\x0c \x03(\x0b\x32\'.moego.client.online_booking.v1.FeedingR\x08\x66\x65\x65\x64ings\x12L\n\x0bmedications\x18\r \x03(\x0b\x32*.moego.client.online_booking.v1.MedicationR\x0bmedications\x12\x66\n\x08waitlist\x18\x0f \x01(\x0b\x32\x45.moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequestH\x04R\x08waitlist\x88\x01\x01\x42\r\n\x0b_start_dateB\x0b\n\t_end_dateB\n\n\x08_feedingB\r\n\x0b_medicationB\x0b\n\t_waitlist\x1a\xca\x06\n\x07\x44\x61ycare\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12>\n\x05\x64\x61tes\x18\x02 \x03(\tB(\xfa\x42%\x92\x01\"\" r\x1e\x32\x1c^[0-9]{4}-[0-9]{2}-[0-9]{2}$R\x05\x64\x61tes\x12-\n\x0c\x61rrival_time\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x0b\x61rrivalTime\x12\x30\n\x0bpickup_time\x18\x06 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00H\x00R\npickupTime\x88\x01\x01\x12#\n\rservice_price\x18\x07 \x01(\x01R\x0cservicePrice\x12\x15\n\x06tax_id\x18\x08 \x01(\x03R\x05taxId\x12!\n\x0cmax_duration\x18\t \x01(\x05R\x0bmaxDuration\x12J\n\x07\x66\x65\x65\x64ing\x18\n \x01(\x0b\x32\'.moego.client.online_booking.v1.FeedingB\x02\x18\x01H\x01R\x07\x66\x65\x65\x64ing\x88\x01\x01\x12S\n\nmedication\x18\x0b \x01(\x0b\x32*.moego.client.online_booking.v1.MedicationB\x02\x18\x01H\x02R\nmedication\x88\x01\x01\x12\x44\n\x06\x61\x64\x64ons\x18\x0c \x03(\x0b\x32,.moego.client.online_booking.v1.DaycareAddonR\x06\x61\x64\x64ons\x12\x43\n\x08\x66\x65\x65\x64ings\x18\r \x03(\x0b\x32\'.moego.client.online_booking.v1.FeedingR\x08\x66\x65\x65\x64ings\x12L\n\x0bmedications\x18\x0e \x03(\x0b\x32*.moego.client.online_booking.v1.MedicationR\x0bmedications\x12\x65\n\x08waitlist\x18\x0f \x01(\x0b\x32\x44.moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequestH\x03R\x08waitlist\x88\x01\x01\x42\x0e\n\x0c_pickup_timeB\n\n\x08_feedingB\r\n\x0b_medicationB\x0b\n\t_waitlist\x1a\x95\x02\n\nEvaluation\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12\x37\n\x04\x64\x61te\x18\x02 \x01(\tB#\xfa\x42 r\x1e\x32\x1c^[0-9]{4}-[0-9]{2}-[0-9]{2}$R\x04\x64\x61te\x12\x1e\n\x04time\x18\x03 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x04time\x12#\n\rservice_price\x18\x04 \x01(\x01R\x0cservicePrice\x12\x1a\n\x08\x64uration\x18\x05 \x01(\x05R\x08\x64uration\x12/\n\x11target_service_id\x18\x06 \x01(\x03H\x00R\x0ftargetServiceId\x88\x01\x01\x42\x14\n\x12_target_service_id\x1a\x89\x02\n\nDogWalking\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12\"\n\x08staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12\x37\n\x04\x64\x61te\x18\x03 \x01(\tB#\xfa\x42 r\x1e\x32\x1c^[0-9]{4}-[0-9]{2}-[0-9]{2}$R\x04\x64\x61te\x12\x1e\n\x04time\x18\x05 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x04time\x12#\n\rservice_price\x18\x06 \x01(\x01R\x0cservicePrice\x12\x1a\n\x08\x64uration\x18\x07 \x01(\x05R\x08\x64uration\x12\x15\n\x06tax_id\x18\x08 \x01(\x03R\x05taxId\x1a\xab\x02\n\nGroupClass\x12>\n\x17group_class_instance_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x14groupClassInstanceId\x12\"\n\x08staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12\x42\n\x05\x64\x61tes\x18\x03 \x03(\tB,\xfa\x42)\x92\x01&\x08\x01\x10\x64\" r\x1e\x32\x1c^[0-9]{4}-[0-9]{2}-[0-9]{2}$R\x05\x64\x61tes\x12)\n\nstart_time\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\tstartTime\x12%\n\x08\x65nd_time\x18\x05 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x07\x65ndTime\x12#\n\rservice_price\x18\x06 \x01(\x01R\x0cservicePriceB\x0e\n\x07service\x12\x03\xf8\x42\x01\"\xb0\x01\n\x06PrePay\x12\x33\n\rservice_total\x18\x01 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\x0cserviceTotal\x12-\n\ntax_amount\x18\x02 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\ttaxAmount\x12\x42\n\x15service_charge_amount\x18\x03 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\x13serviceChargeAmount\"\xf0\x02\n\x07PreAuth\x12/\n\x0btips_amount\x18\x01 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\ntipsAmount\x12\x33\n\rservice_total\x18\x02 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\x0cserviceTotal\x12-\n\ntax_amount\x18\x03 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\ttaxAmount\x12\x42\n\x15service_charge_amount\x18\x04 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\x13serviceChargeAmount\x12\x34\n\x11payment_method_id\x18\x05 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01R\x0fpaymentMethodId\x12)\n\x0b\x63\x61rd_number\x18\x06 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01R\ncardNumber\x12+\n\x0c\x63harge_token\x18\x07 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01R\x0b\x63hargeToken\"\xa9\x01\n\x0c\x44iscountCode\x12\x31\n\x10\x64iscount_code_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00R\x0e\x64iscountCodeId\x12\x37\n\x0f\x64iscount_amount\x18\x02 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00R\x0e\x64iscountAmount\x12-\n\rdiscount_code\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01R\x0c\x64iscountCode\"F\n\nMembership\x12\x38\n\x0emembership_ids\x18\x01 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x10\xe8\x07\x18\x01\"\x04\"\x02 \x00R\rmembershipIds\"\xa4\x07\n\x1aSubmitBookingRequestParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12I\n\x08\x63ustomer\x18\x03 \x01(\x0b\x32(.moego.client.online_booking.v1.CustomerH\x01R\x08\x63ustomer\x88\x01\x01\x12N\n\x0cpet_services\x18\x04 \x03(\x0b\x32+.moego.client.online_booking.v1.PetServicesR\x0bpetServices\x12I\n\nagreements\x18\x05 \x03(\x0b\x32).moego.client.online_booking.v1.AgreementR\nagreements\x12$\n\x0bprepay_guid\x18\x06 \x01(\tH\x02R\nprepayGuid\x88\x01\x01\x12H\n\x07pre_pay\x18\x07 \x01(\x0b\x32&.moego.client.online_booking.v1.PrePayB\x02\x18\x01H\x03R\x06prePay\x88\x01\x01\x12P\n\x0bsource_type\x18\x08 \x01(\x0e\x32*.moego.client.online_booking.v1.SourceTypeH\x04R\nsourceType\x88\x01\x01\x12G\n\x08pre_auth\x18\t \x01(\x0b\x32\'.moego.client.online_booking.v1.PreAuthH\x05R\x07preAuth\x88\x01\x01\x12V\n\rdiscount_code\x18\n \x01(\x0b\x32,.moego.client.online_booking.v1.DiscountCodeH\x06R\x0c\x64iscountCode\x88\x01\x01\x12.\n\x10\x61\x64\x64itional_notes\x18\x0b \x01(\tH\x07R\x0f\x61\x64\x64itionalNotes\x88\x01\x01\x12O\n\nmembership\x18\x0c \x01(\x0b\x32*.moego.client.online_booking.v1.MembershipH\x08R\nmembership\x88\x01\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x0b\n\t_customerB\x0e\n\x0c_prepay_guidB\n\n\x08_pre_payB\x0e\n\x0c_source_typeB\x0b\n\t_pre_authB\x10\n\x0e_discount_codeB\x13\n\x11_additional_notesB\r\n\x0b_membership\"\x93\x01\n\x0bPetServices\x12?\n\x03pet\x18\x01 \x01(\x0b\x32#.moego.client.online_booking.v1.PetB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x03pet\x12\x43\n\x08services\x18\x02 \x03(\x0b\x32\'.moego.client.online_booking.v1.ServiceR\x08services\"\x98\x01\n\x1aSubmitBookingRequestResult\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n\x08order_id\x18\x02 \x01(\x03R\x07orderId\x12\x1f\n\x0b\x63ustomer_id\x18\x03 \x01(\x03R\ncustomerId\x12.\n\x13\x61uto_accept_request\x18\x04 \x01(\x08R\x11\x61utoAcceptRequest\"\xa8\x01\n\x1eRescheduleBookingRequestParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12m\n\x13grooming_reschedule\x18\x02 \x01(\x0b\x32\x32.moego.models.appointment.v1.GroomingRescheduleDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x12groomingReschedule\"F\n\x1eRescheduleBookingRequestResult\x12$\n\x0eis_auto_accept\x18\x01 \x01(\x08R\x0cisAutoAccept\"5\n\x1a\x43\x61ncelBookingRequestParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\x1c\n\x1a\x43\x61ncelBookingRequestResult\"\xb1\x01\n\x1d\x43\x61lculateBookingRequestParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12N\n\x0cpet_services\x18\x04 \x03(\x0b\x32+.moego.client.online_booking.v1.PetServicesR\x0bpetServicesB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"S\n\x1d\x43\x61lculateBookingRequestResult\x12\x32\n\x15\x65stimated_total_price\x18\x01 \x01(\x01R\x13\x65stimatedTotalPrice*G\n\nSourceType\x12\x1b\n\x17SOURCE_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n\x18MARKETING_CAMPAIGN_EMAIL\x10\x01\x32\xf0\x04\n\x15\x42ookingRequestService\x12\x8e\x01\n\x14SubmitBookingRequest\x12:.moego.client.online_booking.v1.SubmitBookingRequestParams\x1a:.moego.client.online_booking.v1.SubmitBookingRequestResult\x12\x9a\x01\n\x18RescheduleBookingRequest\x12>.moego.client.online_booking.v1.RescheduleBookingRequestParams\x1a>.moego.client.online_booking.v1.RescheduleBookingRequestResult\x12\x8e\x01\n\x14\x43\x61ncelBookingRequest\x12:.moego.client.online_booking.v1.CancelBookingRequestParams\x1a:.moego.client.online_booking.v1.CancelBookingRequestResult\x12\x97\x01\n\x17\x43\x61lculateBookingRequest\x12=.moego.client.online_booking.v1.CalculateBookingRequestParams\x1a=.moego.client.online_booking.v1.CalculateBookingRequestResultB\x92\x01\n&com.moego.idl.client.online_booking.v1P\x01Zfgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.online_booking.v1.booking_request_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.client.online_booking.v1P\001Zfgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb'
  _globals['_ADDRESS'].fields_by_name['id']._loaded_options = None
  _globals['_ADDRESS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ADDRESS'].fields_by_name['address1']._loaded_options = None
  _globals['_ADDRESS'].fields_by_name['address1']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_ADDRESS'].fields_by_name['address2']._loaded_options = None
  _globals['_ADDRESS'].fields_by_name['address2']._serialized_options = b'\372B\007r\005\020\000\030\377\001'
  _globals['_ADDRESS'].fields_by_name['city']._loaded_options = None
  _globals['_ADDRESS'].fields_by_name['city']._serialized_options = b'\372B\007r\005\020\000\030\377\001'
  _globals['_ADDRESS'].fields_by_name['state']._loaded_options = None
  _globals['_ADDRESS'].fields_by_name['state']._serialized_options = b'\372B\007r\005\020\000\030\377\001'
  _globals['_ADDRESS'].fields_by_name['zipcode']._loaded_options = None
  _globals['_ADDRESS'].fields_by_name['zipcode']._serialized_options = b'\372B\006r\004\020\000\0302'
  _globals['_ADDRESS'].fields_by_name['country']._loaded_options = None
  _globals['_ADDRESS'].fields_by_name['country']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_ADDRESS'].fields_by_name['lat']._loaded_options = None
  _globals['_ADDRESS'].fields_by_name['lat']._serialized_options = b'\372B\004r\002\0302'
  _globals['_ADDRESS'].fields_by_name['lng']._loaded_options = None
  _globals['_ADDRESS'].fields_by_name['lng']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CUSTOMER_ANSWERSMAPENTRY']._loaded_options = None
  _globals['_CUSTOMER_ANSWERSMAPENTRY']._serialized_options = b'8\001'
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['referral_source_id']._loaded_options = None
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['referral_source_id']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['referral_source_desc']._loaded_options = None
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['referral_source_desc']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['preferred_groomer_id']._loaded_options = None
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['preferred_groomer_id']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['preferred_frequency_day']._loaded_options = None
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['preferred_frequency_day']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['preferred_frequency_type']._loaded_options = None
  _globals['_CUSTOMER_ADDITIONALINFO'].fields_by_name['preferred_frequency_type']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_CUSTOMER_CONTACT'].fields_by_name['first_name']._loaded_options = None
  _globals['_CUSTOMER_CONTACT'].fields_by_name['first_name']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CUSTOMER_CONTACT'].fields_by_name['last_name']._loaded_options = None
  _globals['_CUSTOMER_CONTACT'].fields_by_name['last_name']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CUSTOMER_CONTACT'].fields_by_name['phone_number']._loaded_options = None
  _globals['_CUSTOMER_CONTACT'].fields_by_name['phone_number']._serialized_options = b'\372B\004r\002\030\036'
  _globals['_CUSTOMER'].fields_by_name['first_name']._loaded_options = None
  _globals['_CUSTOMER'].fields_by_name['first_name']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_CUSTOMER'].fields_by_name['last_name']._loaded_options = None
  _globals['_CUSTOMER'].fields_by_name['last_name']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_CUSTOMER'].fields_by_name['phone_number']._loaded_options = None
  _globals['_CUSTOMER'].fields_by_name['phone_number']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_CUSTOMER'].fields_by_name['email']._loaded_options = None
  _globals['_CUSTOMER'].fields_by_name['email']._serialized_options = b'\372B\007r\005`\001\320\001\001'
  _globals['_PET_PETQUESTIONANSWERSENTRY']._loaded_options = None
  _globals['_PET_PETQUESTIONANSWERSENTRY']._serialized_options = b'8\001'
  _globals['_PET_VACCINE'].fields_by_name['vaccine_binding_id']._loaded_options = None
  _globals['_PET_VACCINE'].fields_by_name['vaccine_binding_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PET_VACCINE'].fields_by_name['type']._loaded_options = None
  _globals['_PET_VACCINE'].fields_by_name['type']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_PET_VACCINE'].fields_by_name['vaccine_id']._loaded_options = None
  _globals['_PET_VACCINE'].fields_by_name['vaccine_id']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_PET_VACCINE'].fields_by_name['expiration_date']._loaded_options = None
  _globals['_PET_VACCINE'].fields_by_name['expiration_date']._serialized_options = b'\372B\032r\0302\023^\\d{4}-\\d{2}-\\d{2}$\320\001\001'
  _globals['_PET'].fields_by_name['pet_id']._loaded_options = None
  _globals['_PET'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PET'].fields_by_name['pet_name']._loaded_options = None
  _globals['_PET'].fields_by_name['pet_name']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_PET'].fields_by_name['avatar_path']._loaded_options = None
  _globals['_PET'].fields_by_name['avatar_path']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['breed']._loaded_options = None
  _globals['_PET'].fields_by_name['breed']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['breed_mix']._loaded_options = None
  _globals['_PET'].fields_by_name['breed_mix']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_PET'].fields_by_name['pet_type_id']._loaded_options = None
  _globals['_PET'].fields_by_name['pet_type_id']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_PET'].fields_by_name['gender']._loaded_options = None
  _globals['_PET'].fields_by_name['gender']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_PET'].fields_by_name['birthday']._loaded_options = None
  _globals['_PET'].fields_by_name['birthday']._serialized_options = b'\372B\032r\0302\023^\\d{4}-\\d{2}-\\d{2}$\320\001\001'
  _globals['_PET'].fields_by_name['weight']._loaded_options = None
  _globals['_PET'].fields_by_name['weight']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['fixed']._loaded_options = None
  _globals['_PET'].fields_by_name['fixed']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['behavior']._loaded_options = None
  _globals['_PET'].fields_by_name['behavior']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['hair_length']._loaded_options = None
  _globals['_PET'].fields_by_name['hair_length']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['expiry_notification']._loaded_options = None
  _globals['_PET'].fields_by_name['expiry_notification']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_PET'].fields_by_name['vet_name']._loaded_options = None
  _globals['_PET'].fields_by_name['vet_name']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['vet_phone']._loaded_options = None
  _globals['_PET'].fields_by_name['vet_phone']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['emergency_contact_name']._loaded_options = None
  _globals['_PET'].fields_by_name['emergency_contact_name']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['emergency_contact_phone']._loaded_options = None
  _globals['_PET'].fields_by_name['emergency_contact_phone']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PET'].fields_by_name['health_issues']._loaded_options = None
  _globals['_PET'].fields_by_name['health_issues']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_PET'].fields_by_name['pet_image']._loaded_options = None
  _globals['_PET'].fields_by_name['pet_image']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_AGREEMENT'].fields_by_name['id']._loaded_options = None
  _globals['_AGREEMENT'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_FEEDING'].fields_by_name['amount']._loaded_options = None
  _globals['_FEEDING'].fields_by_name['amount']._serialized_options = b'\030\001\372B\013\022\t!\000\000\000\000\000\000\000\000'
  _globals['_FEEDING'].fields_by_name['unit']._loaded_options = None
  _globals['_FEEDING'].fields_by_name['unit']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_FEEDING'].fields_by_name['food_type']._loaded_options = None
  _globals['_FEEDING'].fields_by_name['food_type']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_FEEDING'].fields_by_name['food_source']._loaded_options = None
  _globals['_FEEDING'].fields_by_name['food_source']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_FEEDING'].fields_by_name['instruction']._loaded_options = None
  _globals['_FEEDING'].fields_by_name['instruction']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_FEEDING'].fields_by_name['note']._loaded_options = None
  _globals['_FEEDING'].fields_by_name['note']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_FEEDING'].fields_by_name['amount_str']._loaded_options = None
  _globals['_FEEDING'].fields_by_name['amount_str']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_MEDICATION'].fields_by_name['amount']._loaded_options = None
  _globals['_MEDICATION'].fields_by_name['amount']._serialized_options = b'\030\001\372B\013\022\t!\000\000\000\000\000\000\000\000'
  _globals['_MEDICATION'].fields_by_name['unit']._loaded_options = None
  _globals['_MEDICATION'].fields_by_name['unit']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_MEDICATION'].fields_by_name['medication_name']._loaded_options = None
  _globals['_MEDICATION'].fields_by_name['medication_name']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_MEDICATION'].fields_by_name['notes']._loaded_options = None
  _globals['_MEDICATION'].fields_by_name['notes']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_MEDICATION'].fields_by_name['amount_str']._loaded_options = None
  _globals['_MEDICATION'].fields_by_name['amount_str']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_BOARDINGADDON'].fields_by_name['id']._loaded_options = None
  _globals['_BOARDINGADDON'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BOARDINGADDON'].fields_by_name['is_every_day']._loaded_options = None
  _globals['_BOARDINGADDON'].fields_by_name['is_every_day']._serialized_options = b'\030\001'
  _globals['_BOARDINGADDON'].fields_by_name['dates']._loaded_options = None
  _globals['_BOARDINGADDON'].fields_by_name['dates']._serialized_options = b'\372B%\222\001\"\" r\0362\034^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
  _globals['_DAYCAREADDON'].fields_by_name['id']._loaded_options = None
  _globals['_DAYCAREADDON'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DAYCAREADDON'].fields_by_name['dates']._loaded_options = None
  _globals['_DAYCAREADDON'].fields_by_name['dates']._serialized_options = b'\372B%\222\001\"\" r\0362\034^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
  _globals['_GROOMINGADDON'].fields_by_name['id']._loaded_options = None
  _globals['_GROOMINGADDON'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICE_GROOMING'].fields_by_name['service_id']._loaded_options = None
  _globals['_SERVICE_GROOMING'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICE_GROOMING'].fields_by_name['start_date']._loaded_options = None
  _globals['_SERVICE_GROOMING'].fields_by_name['start_date']._serialized_options = b'\372B r\0362\034^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
  _globals['_SERVICE_BOARDING'].fields_by_name['service_id']._loaded_options = None
  _globals['_SERVICE_BOARDING'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICE_BOARDING'].fields_by_name['start_date']._loaded_options = None
  _globals['_SERVICE_BOARDING'].fields_by_name['start_date']._serialized_options = b'\372B r\0362\034^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
  _globals['_SERVICE_BOARDING'].fields_by_name['arrival_time']._loaded_options = None
  _globals['_SERVICE_BOARDING'].fields_by_name['arrival_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICE_BOARDING'].fields_by_name['end_date']._loaded_options = None
  _globals['_SERVICE_BOARDING'].fields_by_name['end_date']._serialized_options = b'\372B r\0362\034^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
  _globals['_SERVICE_BOARDING'].fields_by_name['pickup_time']._loaded_options = None
  _globals['_SERVICE_BOARDING'].fields_by_name['pickup_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICE_BOARDING'].fields_by_name['feeding']._loaded_options = None
  _globals['_SERVICE_BOARDING'].fields_by_name['feeding']._serialized_options = b'\030\001'
  _globals['_SERVICE_BOARDING'].fields_by_name['medication']._loaded_options = None
  _globals['_SERVICE_BOARDING'].fields_by_name['medication']._serialized_options = b'\030\001'
  _globals['_SERVICE_DAYCARE'].fields_by_name['service_id']._loaded_options = None
  _globals['_SERVICE_DAYCARE'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICE_DAYCARE'].fields_by_name['dates']._loaded_options = None
  _globals['_SERVICE_DAYCARE'].fields_by_name['dates']._serialized_options = b'\372B%\222\001\"\" r\0362\034^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
  _globals['_SERVICE_DAYCARE'].fields_by_name['arrival_time']._loaded_options = None
  _globals['_SERVICE_DAYCARE'].fields_by_name['arrival_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICE_DAYCARE'].fields_by_name['pickup_time']._loaded_options = None
  _globals['_SERVICE_DAYCARE'].fields_by_name['pickup_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_SERVICE_DAYCARE'].fields_by_name['feeding']._loaded_options = None
  _globals['_SERVICE_DAYCARE'].fields_by_name['feeding']._serialized_options = b'\030\001'
  _globals['_SERVICE_DAYCARE'].fields_by_name['medication']._loaded_options = None
  _globals['_SERVICE_DAYCARE'].fields_by_name['medication']._serialized_options = b'\030\001'
  _globals['_SERVICE_EVALUATION'].fields_by_name['service_id']._loaded_options = None
  _globals['_SERVICE_EVALUATION'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICE_EVALUATION'].fields_by_name['date']._loaded_options = None
  _globals['_SERVICE_EVALUATION'].fields_by_name['date']._serialized_options = b'\372B r\0362\034^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
  _globals['_SERVICE_EVALUATION'].fields_by_name['time']._loaded_options = None
  _globals['_SERVICE_EVALUATION'].fields_by_name['time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICE_DOGWALKING'].fields_by_name['service_id']._loaded_options = None
  _globals['_SERVICE_DOGWALKING'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICE_DOGWALKING'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SERVICE_DOGWALKING'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICE_DOGWALKING'].fields_by_name['date']._loaded_options = None
  _globals['_SERVICE_DOGWALKING'].fields_by_name['date']._serialized_options = b'\372B r\0362\034^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
  _globals['_SERVICE_DOGWALKING'].fields_by_name['time']._loaded_options = None
  _globals['_SERVICE_DOGWALKING'].fields_by_name['time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['group_class_instance_id']._loaded_options = None
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['group_class_instance_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['dates']._loaded_options = None
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['dates']._serialized_options = b'\372B)\222\001&\010\001\020d\" r\0362\034^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['start_time']._loaded_options = None
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['end_time']._loaded_options = None
  _globals['_SERVICE_GROUPCLASS'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICE'].oneofs_by_name['service']._loaded_options = None
  _globals['_SERVICE'].oneofs_by_name['service']._serialized_options = b'\370B\001'
  _globals['_PREPAY'].fields_by_name['service_total']._loaded_options = None
  _globals['_PREPAY'].fields_by_name['service_total']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_PREPAY'].fields_by_name['tax_amount']._loaded_options = None
  _globals['_PREPAY'].fields_by_name['tax_amount']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_PREPAY'].fields_by_name['service_charge_amount']._loaded_options = None
  _globals['_PREPAY'].fields_by_name['service_charge_amount']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_PREAUTH'].fields_by_name['tips_amount']._loaded_options = None
  _globals['_PREAUTH'].fields_by_name['tips_amount']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_PREAUTH'].fields_by_name['service_total']._loaded_options = None
  _globals['_PREAUTH'].fields_by_name['service_total']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_PREAUTH'].fields_by_name['tax_amount']._loaded_options = None
  _globals['_PREAUTH'].fields_by_name['tax_amount']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_PREAUTH'].fields_by_name['service_charge_amount']._loaded_options = None
  _globals['_PREAUTH'].fields_by_name['service_charge_amount']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_PREAUTH'].fields_by_name['payment_method_id']._loaded_options = None
  _globals['_PREAUTH'].fields_by_name['payment_method_id']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PREAUTH'].fields_by_name['card_number']._loaded_options = None
  _globals['_PREAUTH'].fields_by_name['card_number']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_PREAUTH'].fields_by_name['charge_token']._loaded_options = None
  _globals['_PREAUTH'].fields_by_name['charge_token']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_DISCOUNTCODE'].fields_by_name['discount_code_id']._loaded_options = None
  _globals['_DISCOUNTCODE'].fields_by_name['discount_code_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_DISCOUNTCODE'].fields_by_name['discount_amount']._loaded_options = None
  _globals['_DISCOUNTCODE'].fields_by_name['discount_amount']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_DISCOUNTCODE'].fields_by_name['discount_code']._loaded_options = None
  _globals['_DISCOUNTCODE'].fields_by_name['discount_code']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_MEMBERSHIP'].fields_by_name['membership_ids']._loaded_options = None
  _globals['_MEMBERSHIP'].fields_by_name['membership_ids']._serialized_options = b'\372B\016\222\001\013\020\350\007\030\001\"\004\"\002 \000'
  _globals['_SUBMITBOOKINGREQUESTPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_SUBMITBOOKINGREQUESTPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_SUBMITBOOKINGREQUESTPARAMS'].fields_by_name['pre_pay']._loaded_options = None
  _globals['_SUBMITBOOKINGREQUESTPARAMS'].fields_by_name['pre_pay']._serialized_options = b'\030\001'
  _globals['_PETSERVICES'].fields_by_name['pet']._loaded_options = None
  _globals['_PETSERVICES'].fields_by_name['pet']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_RESCHEDULEBOOKINGREQUESTPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_RESCHEDULEBOOKINGREQUESTPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_RESCHEDULEBOOKINGREQUESTPARAMS'].fields_by_name['grooming_reschedule']._loaded_options = None
  _globals['_RESCHEDULEBOOKINGREQUESTPARAMS'].fields_by_name['grooming_reschedule']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CANCELBOOKINGREQUESTPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_CANCELBOOKINGREQUESTPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CALCULATEBOOKINGREQUESTPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_CALCULATEBOOKINGREQUESTPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_SOURCETYPE']._serialized_start=12765
  _globals['_SOURCETYPE']._serialized_end=12836
  _globals['_ADDRESS']._serialized_start=557
  _globals['_ADDRESS']._serialized_end=1100
  _globals['_CUSTOMER']._serialized_start=1103
  _globals['_CUSTOMER']._serialized_end=2940
  _globals['_CUSTOMER_ANSWERSMAPENTRY']._serialized_start=1943
  _globals['_CUSTOMER_ANSWERSMAPENTRY']._serialized_end=2028
  _globals['_CUSTOMER_ADDITIONALINFO']._serialized_start=2031
  _globals['_CUSTOMER_ADDITIONALINFO']._serialized_end=2584
  _globals['_CUSTOMER_CONTACT']._serialized_start=2587
  _globals['_CUSTOMER_CONTACT']._serialized_end=2779
  _globals['_PET']._serialized_start=2943
  _globals['_PET']._serialized_end=4890
  _globals['_PET_PETQUESTIONANSWERSENTRY']._serialized_start=4088
  _globals['_PET_PETQUESTIONANSWERSENTRY']._serialized_end=4181
  _globals['_PET_VACCINE']._serialized_start=4184
  _globals['_PET_VACCINE']._serialized_end=4582
  _globals['_AGREEMENT']._serialized_start=4892
  _globals['_AGREEMENT']._serialized_end=4958
  _globals['_FEEDING']._serialized_start=4961
  _globals['_FEEDING']._serialized_end=5446
  _globals['_MEDICATION']._serialized_start=5449
  _globals['_MEDICATION']._serialized_end=5977
  _globals['_BOARDINGADDON']._serialized_start=5980
  _globals['_BOARDINGADDON']._serialized_end=6466
  _globals['_DAYCAREADDON']._serialized_start=6469
  _globals['_DAYCAREADDON']._serialized_end=6784
  _globals['_GROOMINGADDON']._serialized_start=6786
  _globals['_GROOMINGADDON']._serialized_end=6826
  _globals['_SERVICE']._serialized_start=6829
  _globals['_SERVICE']._serialized_end=10136
  _globals['_SERVICE_GROOMING']._serialized_start=7338
  _globals['_SERVICE_GROOMING']._serialized_end=7527
  _globals['_SERVICE_BOARDING']._serialized_start=7530
  _globals['_SERVICE_BOARDING']._serialized_end=8425
  _globals['_SERVICE_DAYCARE']._serialized_start=8428
  _globals['_SERVICE_DAYCARE']._serialized_end=9270
  _globals['_SERVICE_EVALUATION']._serialized_start=9273
  _globals['_SERVICE_EVALUATION']._serialized_end=9550
  _globals['_SERVICE_DOGWALKING']._serialized_start=9553
  _globals['_SERVICE_DOGWALKING']._serialized_end=9818
  _globals['_SERVICE_GROUPCLASS']._serialized_start=9821
  _globals['_SERVICE_GROUPCLASS']._serialized_end=10120
  _globals['_PREPAY']._serialized_start=10139
  _globals['_PREPAY']._serialized_end=10315
  _globals['_PREAUTH']._serialized_start=10318
  _globals['_PREAUTH']._serialized_end=10686
  _globals['_DISCOUNTCODE']._serialized_start=10689
  _globals['_DISCOUNTCODE']._serialized_end=10858
  _globals['_MEMBERSHIP']._serialized_start=10860
  _globals['_MEMBERSHIP']._serialized_end=10930
  _globals['_SUBMITBOOKINGREQUESTPARAMS']._serialized_start=10933
  _globals['_SUBMITBOOKINGREQUESTPARAMS']._serialized_end=11865
  _globals['_PETSERVICES']._serialized_start=11868
  _globals['_PETSERVICES']._serialized_end=12015
  _globals['_SUBMITBOOKINGREQUESTRESULT']._serialized_start=12018
  _globals['_SUBMITBOOKINGREQUESTRESULT']._serialized_end=12170
  _globals['_RESCHEDULEBOOKINGREQUESTPARAMS']._serialized_start=12173
  _globals['_RESCHEDULEBOOKINGREQUESTPARAMS']._serialized_end=12341
  _globals['_RESCHEDULEBOOKINGREQUESTRESULT']._serialized_start=12343
  _globals['_RESCHEDULEBOOKINGREQUESTRESULT']._serialized_end=12413
  _globals['_CANCELBOOKINGREQUESTPARAMS']._serialized_start=12415
  _globals['_CANCELBOOKINGREQUESTPARAMS']._serialized_end=12468
  _globals['_CANCELBOOKINGREQUESTRESULT']._serialized_start=12470
  _globals['_CANCELBOOKINGREQUESTRESULT']._serialized_end=12498
  _globals['_CALCULATEBOOKINGREQUESTPARAMS']._serialized_start=12501
  _globals['_CALCULATEBOOKINGREQUESTPARAMS']._serialized_end=12678
  _globals['_CALCULATEBOOKINGREQUESTRESULT']._serialized_start=12680
  _globals['_CALCULATEBOOKINGREQUESTRESULT']._serialized_end=12763
  _globals['_BOOKINGREQUESTSERVICE']._serialized_start=12839
  _globals['_BOOKINGREQUESTSERVICE']._serialized_end=13463
# @@protoc_insertion_point(module_scope)
