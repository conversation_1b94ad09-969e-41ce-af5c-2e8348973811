from google.protobuf import struct_pb2 as _struct_pb2
from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import date_pb2 as _date_pb2
from moego.models.appointment.v1 import appointment_defs_pb2 as _appointment_defs_pb2
from moego.models.appointment.v1 import appointment_pet_medication_schedule_defs_pb2 as _appointment_pet_medication_schedule_defs_pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as _pet_detail_enums_pb2
from moego.models.online_booking.v1 import feeding_models_pb2 as _feeding_models_pb2
from moego.models.online_booking.v1 import medication_models_pb2 as _medication_models_pb2
from moego.service.online_booking.v1 import booking_request_service_pb2 as _booking_request_service_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class SourceType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SOURCE_TYPE_UNSPECIFIED: _ClassVar[SourceType]
    MARKETING_CAMPAIGN_EMAIL: _ClassVar[SourceType]
SOURCE_TYPE_UNSPECIFIED: SourceType
MARKETING_CAMPAIGN_EMAIL: SourceType

class Address(_message.Message):
    __slots__ = ("id", "address1", "address2", "city", "state", "zipcode", "country", "lat", "lng", "is_profile_request_address")
    ID_FIELD_NUMBER: _ClassVar[int]
    ADDRESS1_FIELD_NUMBER: _ClassVar[int]
    ADDRESS2_FIELD_NUMBER: _ClassVar[int]
    CITY_FIELD_NUMBER: _ClassVar[int]
    STATE_FIELD_NUMBER: _ClassVar[int]
    ZIPCODE_FIELD_NUMBER: _ClassVar[int]
    COUNTRY_FIELD_NUMBER: _ClassVar[int]
    LAT_FIELD_NUMBER: _ClassVar[int]
    LNG_FIELD_NUMBER: _ClassVar[int]
    IS_PROFILE_REQUEST_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    id: int
    address1: str
    address2: str
    city: str
    state: str
    zipcode: str
    country: str
    lat: str
    lng: str
    is_profile_request_address: bool
    def __init__(self, id: _Optional[int] = ..., address1: _Optional[str] = ..., address2: _Optional[str] = ..., city: _Optional[str] = ..., state: _Optional[str] = ..., zipcode: _Optional[str] = ..., country: _Optional[str] = ..., lat: _Optional[str] = ..., lng: _Optional[str] = ..., is_profile_request_address: bool = ...) -> None: ...

class Customer(_message.Message):
    __slots__ = ("first_name", "last_name", "phone_number", "email", "answers_map", "charge_token", "has_stripe_card", "stripe_customer_id", "additional_info", "address", "birthday", "emergency_contact", "pickup_contact")
    class AnswersMapEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: _struct_pb2.Value
        def __init__(self, key: _Optional[str] = ..., value: _Optional[_Union[_struct_pb2.Value, _Mapping]] = ...) -> None: ...
    class AdditionalInfo(_message.Message):
        __slots__ = ("referral_source_id", "referral_source_desc", "preferred_groomer_id", "preferred_frequency_day", "preferred_frequency_type", "preferred_day", "preferred_time")
        REFERRAL_SOURCE_ID_FIELD_NUMBER: _ClassVar[int]
        REFERRAL_SOURCE_DESC_FIELD_NUMBER: _ClassVar[int]
        PREFERRED_GROOMER_ID_FIELD_NUMBER: _ClassVar[int]
        PREFERRED_FREQUENCY_DAY_FIELD_NUMBER: _ClassVar[int]
        PREFERRED_FREQUENCY_TYPE_FIELD_NUMBER: _ClassVar[int]
        PREFERRED_DAY_FIELD_NUMBER: _ClassVar[int]
        PREFERRED_TIME_FIELD_NUMBER: _ClassVar[int]
        referral_source_id: int
        referral_source_desc: str
        preferred_groomer_id: int
        preferred_frequency_day: int
        preferred_frequency_type: int
        preferred_day: _containers.RepeatedScalarFieldContainer[int]
        preferred_time: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, referral_source_id: _Optional[int] = ..., referral_source_desc: _Optional[str] = ..., preferred_groomer_id: _Optional[int] = ..., preferred_frequency_day: _Optional[int] = ..., preferred_frequency_type: _Optional[int] = ..., preferred_day: _Optional[_Iterable[int]] = ..., preferred_time: _Optional[_Iterable[int]] = ...) -> None: ...
    class Contact(_message.Message):
        __slots__ = ("first_name", "last_name", "phone_number")
        FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
        LAST_NAME_FIELD_NUMBER: _ClassVar[int]
        PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
        first_name: str
        last_name: str
        phone_number: str
        def __init__(self, first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., phone_number: _Optional[str] = ...) -> None: ...
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    ANSWERS_MAP_FIELD_NUMBER: _ClassVar[int]
    CHARGE_TOKEN_FIELD_NUMBER: _ClassVar[int]
    HAS_STRIPE_CARD_FIELD_NUMBER: _ClassVar[int]
    STRIPE_CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    ADDITIONAL_INFO_FIELD_NUMBER: _ClassVar[int]
    ADDRESS_FIELD_NUMBER: _ClassVar[int]
    BIRTHDAY_FIELD_NUMBER: _ClassVar[int]
    EMERGENCY_CONTACT_FIELD_NUMBER: _ClassVar[int]
    PICKUP_CONTACT_FIELD_NUMBER: _ClassVar[int]
    first_name: str
    last_name: str
    phone_number: str
    email: str
    answers_map: _containers.MessageMap[str, _struct_pb2.Value]
    charge_token: str
    has_stripe_card: bool
    stripe_customer_id: str
    additional_info: Customer.AdditionalInfo
    address: Address
    birthday: _timestamp_pb2.Timestamp
    emergency_contact: Customer.Contact
    pickup_contact: Customer.Contact
    def __init__(self, first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., phone_number: _Optional[str] = ..., email: _Optional[str] = ..., answers_map: _Optional[_Mapping[str, _struct_pb2.Value]] = ..., charge_token: _Optional[str] = ..., has_stripe_card: bool = ..., stripe_customer_id: _Optional[str] = ..., additional_info: _Optional[_Union[Customer.AdditionalInfo, _Mapping]] = ..., address: _Optional[_Union[Address, _Mapping]] = ..., birthday: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., emergency_contact: _Optional[_Union[Customer.Contact, _Mapping]] = ..., pickup_contact: _Optional[_Union[Customer.Contact, _Mapping]] = ...) -> None: ...

class Pet(_message.Message):
    __slots__ = ("pet_id", "pet_name", "avatar_path", "breed", "breed_mix", "pet_type_id", "gender", "birthday", "weight", "fixed", "behavior", "hair_length", "expiry_notification", "vet_name", "vet_phone", "vet_address", "emergency_contact_name", "emergency_contact_phone", "health_issues", "vaccine_list", "pet_image", "pet_question_answers")
    class PetQuestionAnswersEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: _struct_pb2.Value
        def __init__(self, key: _Optional[str] = ..., value: _Optional[_Union[_struct_pb2.Value, _Mapping]] = ...) -> None: ...
    class Vaccine(_message.Message):
        __slots__ = ("vaccine_binding_id", "type", "vaccine_id", "expiration_date", "vaccine_document", "document_urls")
        VACCINE_BINDING_ID_FIELD_NUMBER: _ClassVar[int]
        TYPE_FIELD_NUMBER: _ClassVar[int]
        VACCINE_ID_FIELD_NUMBER: _ClassVar[int]
        EXPIRATION_DATE_FIELD_NUMBER: _ClassVar[int]
        VACCINE_DOCUMENT_FIELD_NUMBER: _ClassVar[int]
        DOCUMENT_URLS_FIELD_NUMBER: _ClassVar[int]
        vaccine_binding_id: int
        type: int
        vaccine_id: int
        expiration_date: str
        vaccine_document: str
        document_urls: _containers.RepeatedScalarFieldContainer[str]
        def __init__(self, vaccine_binding_id: _Optional[int] = ..., type: _Optional[int] = ..., vaccine_id: _Optional[int] = ..., expiration_date: _Optional[str] = ..., vaccine_document: _Optional[str] = ..., document_urls: _Optional[_Iterable[str]] = ...) -> None: ...
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PET_NAME_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    BREED_FIELD_NUMBER: _ClassVar[int]
    BREED_MIX_FIELD_NUMBER: _ClassVar[int]
    PET_TYPE_ID_FIELD_NUMBER: _ClassVar[int]
    GENDER_FIELD_NUMBER: _ClassVar[int]
    BIRTHDAY_FIELD_NUMBER: _ClassVar[int]
    WEIGHT_FIELD_NUMBER: _ClassVar[int]
    FIXED_FIELD_NUMBER: _ClassVar[int]
    BEHAVIOR_FIELD_NUMBER: _ClassVar[int]
    HAIR_LENGTH_FIELD_NUMBER: _ClassVar[int]
    EXPIRY_NOTIFICATION_FIELD_NUMBER: _ClassVar[int]
    VET_NAME_FIELD_NUMBER: _ClassVar[int]
    VET_PHONE_FIELD_NUMBER: _ClassVar[int]
    VET_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    EMERGENCY_CONTACT_NAME_FIELD_NUMBER: _ClassVar[int]
    EMERGENCY_CONTACT_PHONE_FIELD_NUMBER: _ClassVar[int]
    HEALTH_ISSUES_FIELD_NUMBER: _ClassVar[int]
    VACCINE_LIST_FIELD_NUMBER: _ClassVar[int]
    PET_IMAGE_FIELD_NUMBER: _ClassVar[int]
    PET_QUESTION_ANSWERS_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    pet_name: str
    avatar_path: str
    breed: str
    breed_mix: int
    pet_type_id: int
    gender: int
    birthday: str
    weight: str
    fixed: str
    behavior: str
    hair_length: str
    expiry_notification: int
    vet_name: str
    vet_phone: str
    vet_address: str
    emergency_contact_name: str
    emergency_contact_phone: str
    health_issues: str
    vaccine_list: _containers.RepeatedCompositeFieldContainer[Pet.Vaccine]
    pet_image: str
    pet_question_answers: _containers.MessageMap[str, _struct_pb2.Value]
    def __init__(self, pet_id: _Optional[int] = ..., pet_name: _Optional[str] = ..., avatar_path: _Optional[str] = ..., breed: _Optional[str] = ..., breed_mix: _Optional[int] = ..., pet_type_id: _Optional[int] = ..., gender: _Optional[int] = ..., birthday: _Optional[str] = ..., weight: _Optional[str] = ..., fixed: _Optional[str] = ..., behavior: _Optional[str] = ..., hair_length: _Optional[str] = ..., expiry_notification: _Optional[int] = ..., vet_name: _Optional[str] = ..., vet_phone: _Optional[str] = ..., vet_address: _Optional[str] = ..., emergency_contact_name: _Optional[str] = ..., emergency_contact_phone: _Optional[str] = ..., health_issues: _Optional[str] = ..., vaccine_list: _Optional[_Iterable[_Union[Pet.Vaccine, _Mapping]]] = ..., pet_image: _Optional[str] = ..., pet_question_answers: _Optional[_Mapping[str, _struct_pb2.Value]] = ...) -> None: ...

class Agreement(_message.Message):
    __slots__ = ("id", "signature")
    ID_FIELD_NUMBER: _ClassVar[int]
    SIGNATURE_FIELD_NUMBER: _ClassVar[int]
    id: int
    signature: str
    def __init__(self, id: _Optional[int] = ..., signature: _Optional[str] = ...) -> None: ...

class Feeding(_message.Message):
    __slots__ = ("time", "amount", "unit", "food_type", "food_source", "instruction", "note", "amount_str")
    TIME_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_FIELD_NUMBER: _ClassVar[int]
    UNIT_FIELD_NUMBER: _ClassVar[int]
    FOOD_TYPE_FIELD_NUMBER: _ClassVar[int]
    FOOD_SOURCE_FIELD_NUMBER: _ClassVar[int]
    INSTRUCTION_FIELD_NUMBER: _ClassVar[int]
    NOTE_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_STR_FIELD_NUMBER: _ClassVar[int]
    time: _containers.RepeatedCompositeFieldContainer[_feeding_models_pb2.FeedingModel.FeedingSchedule]
    amount: float
    unit: str
    food_type: str
    food_source: str
    instruction: str
    note: str
    amount_str: str
    def __init__(self, time: _Optional[_Iterable[_Union[_feeding_models_pb2.FeedingModel.FeedingSchedule, _Mapping]]] = ..., amount: _Optional[float] = ..., unit: _Optional[str] = ..., food_type: _Optional[str] = ..., food_source: _Optional[str] = ..., instruction: _Optional[str] = ..., note: _Optional[str] = ..., amount_str: _Optional[str] = ...) -> None: ...

class Medication(_message.Message):
    __slots__ = ("time", "amount", "unit", "medication_name", "notes", "amount_str", "selected_date")
    TIME_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_FIELD_NUMBER: _ClassVar[int]
    UNIT_FIELD_NUMBER: _ClassVar[int]
    MEDICATION_NAME_FIELD_NUMBER: _ClassVar[int]
    NOTES_FIELD_NUMBER: _ClassVar[int]
    AMOUNT_STR_FIELD_NUMBER: _ClassVar[int]
    SELECTED_DATE_FIELD_NUMBER: _ClassVar[int]
    time: _containers.RepeatedCompositeFieldContainer[_medication_models_pb2.MedicationModel.MedicationSchedule]
    amount: float
    unit: str
    medication_name: str
    notes: str
    amount_str: str
    selected_date: _appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef.SelectedDateDef
    def __init__(self, time: _Optional[_Iterable[_Union[_medication_models_pb2.MedicationModel.MedicationSchedule, _Mapping]]] = ..., amount: _Optional[float] = ..., unit: _Optional[str] = ..., medication_name: _Optional[str] = ..., notes: _Optional[str] = ..., amount_str: _Optional[str] = ..., selected_date: _Optional[_Union[_appointment_pet_medication_schedule_defs_pb2.AppointmentPetMedicationScheduleDef.SelectedDateDef, _Mapping]] = ...) -> None: ...

class BoardingAddon(_message.Message):
    __slots__ = ("id", "is_every_day", "dates", "service_price", "tax_id", "duration", "quantity_per_day", "date_type", "start_date")
    ID_FIELD_NUMBER: _ClassVar[int]
    IS_EVERY_DAY_FIELD_NUMBER: _ClassVar[int]
    DATES_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_PER_DAY_FIELD_NUMBER: _ClassVar[int]
    DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    id: int
    is_every_day: bool
    dates: _containers.RepeatedScalarFieldContainer[str]
    service_price: float
    tax_id: int
    duration: int
    quantity_per_day: int
    date_type: _pet_detail_enums_pb2.PetDetailDateType
    start_date: _date_pb2.Date
    def __init__(self, id: _Optional[int] = ..., is_every_day: bool = ..., dates: _Optional[_Iterable[str]] = ..., service_price: _Optional[float] = ..., tax_id: _Optional[int] = ..., duration: _Optional[int] = ..., quantity_per_day: _Optional[int] = ..., date_type: _Optional[_Union[_pet_detail_enums_pb2.PetDetailDateType, str]] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ...) -> None: ...

class DaycareAddon(_message.Message):
    __slots__ = ("id", "is_every_day", "dates", "service_price", "tax_id", "duration", "quantity_per_day")
    ID_FIELD_NUMBER: _ClassVar[int]
    IS_EVERY_DAY_FIELD_NUMBER: _ClassVar[int]
    DATES_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_PER_DAY_FIELD_NUMBER: _ClassVar[int]
    id: int
    is_every_day: bool
    dates: _containers.RepeatedScalarFieldContainer[str]
    service_price: float
    tax_id: int
    duration: int
    quantity_per_day: int
    def __init__(self, id: _Optional[int] = ..., is_every_day: bool = ..., dates: _Optional[_Iterable[str]] = ..., service_price: _Optional[float] = ..., tax_id: _Optional[int] = ..., duration: _Optional[int] = ..., quantity_per_day: _Optional[int] = ...) -> None: ...

class GroomingAddon(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class Service(_message.Message):
    __slots__ = ("grooming", "boarding", "daycare", "evaluation", "dog_walking", "group_class")
    class Grooming(_message.Message):
        __slots__ = ("service_id", "start_date", "addons")
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        ADDONS_FIELD_NUMBER: _ClassVar[int]
        service_id: int
        start_date: str
        addons: _containers.RepeatedCompositeFieldContainer[GroomingAddon]
        def __init__(self, service_id: _Optional[int] = ..., start_date: _Optional[str] = ..., addons: _Optional[_Iterable[_Union[GroomingAddon, _Mapping]]] = ...) -> None: ...
    class Boarding(_message.Message):
        __slots__ = ("service_id", "start_date", "arrival_time", "end_date", "pickup_time", "service_price", "tax_id", "feeding", "medication", "addons", "feedings", "medications", "waitlist")
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        ARRIVAL_TIME_FIELD_NUMBER: _ClassVar[int]
        END_DATE_FIELD_NUMBER: _ClassVar[int]
        PICKUP_TIME_FIELD_NUMBER: _ClassVar[int]
        SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
        TAX_ID_FIELD_NUMBER: _ClassVar[int]
        FEEDING_FIELD_NUMBER: _ClassVar[int]
        MEDICATION_FIELD_NUMBER: _ClassVar[int]
        ADDONS_FIELD_NUMBER: _ClassVar[int]
        FEEDINGS_FIELD_NUMBER: _ClassVar[int]
        MEDICATIONS_FIELD_NUMBER: _ClassVar[int]
        WAITLIST_FIELD_NUMBER: _ClassVar[int]
        service_id: int
        start_date: str
        arrival_time: int
        end_date: str
        pickup_time: int
        service_price: float
        tax_id: int
        feeding: Feeding
        medication: Medication
        addons: _containers.RepeatedCompositeFieldContainer[BoardingAddon]
        feedings: _containers.RepeatedCompositeFieldContainer[Feeding]
        medications: _containers.RepeatedCompositeFieldContainer[Medication]
        waitlist: _booking_request_service_pb2.CreateBoardingServiceWaitlistRequest
        def __init__(self, service_id: _Optional[int] = ..., start_date: _Optional[str] = ..., arrival_time: _Optional[int] = ..., end_date: _Optional[str] = ..., pickup_time: _Optional[int] = ..., service_price: _Optional[float] = ..., tax_id: _Optional[int] = ..., feeding: _Optional[_Union[Feeding, _Mapping]] = ..., medication: _Optional[_Union[Medication, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[BoardingAddon, _Mapping]]] = ..., feedings: _Optional[_Iterable[_Union[Feeding, _Mapping]]] = ..., medications: _Optional[_Iterable[_Union[Medication, _Mapping]]] = ..., waitlist: _Optional[_Union[_booking_request_service_pb2.CreateBoardingServiceWaitlistRequest, _Mapping]] = ...) -> None: ...
    class Daycare(_message.Message):
        __slots__ = ("service_id", "dates", "arrival_time", "pickup_time", "service_price", "tax_id", "max_duration", "feeding", "medication", "addons", "feedings", "medications", "waitlist")
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        DATES_FIELD_NUMBER: _ClassVar[int]
        ARRIVAL_TIME_FIELD_NUMBER: _ClassVar[int]
        PICKUP_TIME_FIELD_NUMBER: _ClassVar[int]
        SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
        TAX_ID_FIELD_NUMBER: _ClassVar[int]
        MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
        FEEDING_FIELD_NUMBER: _ClassVar[int]
        MEDICATION_FIELD_NUMBER: _ClassVar[int]
        ADDONS_FIELD_NUMBER: _ClassVar[int]
        FEEDINGS_FIELD_NUMBER: _ClassVar[int]
        MEDICATIONS_FIELD_NUMBER: _ClassVar[int]
        WAITLIST_FIELD_NUMBER: _ClassVar[int]
        service_id: int
        dates: _containers.RepeatedScalarFieldContainer[str]
        arrival_time: int
        pickup_time: int
        service_price: float
        tax_id: int
        max_duration: int
        feeding: Feeding
        medication: Medication
        addons: _containers.RepeatedCompositeFieldContainer[DaycareAddon]
        feedings: _containers.RepeatedCompositeFieldContainer[Feeding]
        medications: _containers.RepeatedCompositeFieldContainer[Medication]
        waitlist: _booking_request_service_pb2.CreateDaycareServiceWaitlistRequest
        def __init__(self, service_id: _Optional[int] = ..., dates: _Optional[_Iterable[str]] = ..., arrival_time: _Optional[int] = ..., pickup_time: _Optional[int] = ..., service_price: _Optional[float] = ..., tax_id: _Optional[int] = ..., max_duration: _Optional[int] = ..., feeding: _Optional[_Union[Feeding, _Mapping]] = ..., medication: _Optional[_Union[Medication, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[DaycareAddon, _Mapping]]] = ..., feedings: _Optional[_Iterable[_Union[Feeding, _Mapping]]] = ..., medications: _Optional[_Iterable[_Union[Medication, _Mapping]]] = ..., waitlist: _Optional[_Union[_booking_request_service_pb2.CreateDaycareServiceWaitlistRequest, _Mapping]] = ...) -> None: ...
    class Evaluation(_message.Message):
        __slots__ = ("service_id", "date", "time", "service_price", "duration", "target_service_id")
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        DATE_FIELD_NUMBER: _ClassVar[int]
        TIME_FIELD_NUMBER: _ClassVar[int]
        SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
        DURATION_FIELD_NUMBER: _ClassVar[int]
        TARGET_SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        service_id: int
        date: str
        time: int
        service_price: float
        duration: int
        target_service_id: int
        def __init__(self, service_id: _Optional[int] = ..., date: _Optional[str] = ..., time: _Optional[int] = ..., service_price: _Optional[float] = ..., duration: _Optional[int] = ..., target_service_id: _Optional[int] = ...) -> None: ...
    class DogWalking(_message.Message):
        __slots__ = ("service_id", "staff_id", "date", "time", "service_price", "duration", "tax_id")
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        STAFF_ID_FIELD_NUMBER: _ClassVar[int]
        DATE_FIELD_NUMBER: _ClassVar[int]
        TIME_FIELD_NUMBER: _ClassVar[int]
        SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
        DURATION_FIELD_NUMBER: _ClassVar[int]
        TAX_ID_FIELD_NUMBER: _ClassVar[int]
        service_id: int
        staff_id: int
        date: str
        time: int
        service_price: float
        duration: int
        tax_id: int
        def __init__(self, service_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., date: _Optional[str] = ..., time: _Optional[int] = ..., service_price: _Optional[float] = ..., duration: _Optional[int] = ..., tax_id: _Optional[int] = ...) -> None: ...
    class GroupClass(_message.Message):
        __slots__ = ("group_class_instance_id", "staff_id", "dates", "start_time", "end_time", "service_price")
        GROUP_CLASS_INSTANCE_ID_FIELD_NUMBER: _ClassVar[int]
        STAFF_ID_FIELD_NUMBER: _ClassVar[int]
        DATES_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        END_TIME_FIELD_NUMBER: _ClassVar[int]
        SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
        group_class_instance_id: int
        staff_id: int
        dates: _containers.RepeatedScalarFieldContainer[str]
        start_time: int
        end_time: int
        service_price: float
        def __init__(self, group_class_instance_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., dates: _Optional[_Iterable[str]] = ..., start_time: _Optional[int] = ..., end_time: _Optional[int] = ..., service_price: _Optional[float] = ...) -> None: ...
    GROOMING_FIELD_NUMBER: _ClassVar[int]
    BOARDING_FIELD_NUMBER: _ClassVar[int]
    DAYCARE_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_FIELD_NUMBER: _ClassVar[int]
    DOG_WALKING_FIELD_NUMBER: _ClassVar[int]
    GROUP_CLASS_FIELD_NUMBER: _ClassVar[int]
    grooming: Service.Grooming
    boarding: Service.Boarding
    daycare: Service.Daycare
    evaluation: Service.Evaluation
    dog_walking: Service.DogWalking
    group_class: Service.GroupClass
    def __init__(self, grooming: _Optional[_Union[Service.Grooming, _Mapping]] = ..., boarding: _Optional[_Union[Service.Boarding, _Mapping]] = ..., daycare: _Optional[_Union[Service.Daycare, _Mapping]] = ..., evaluation: _Optional[_Union[Service.Evaluation, _Mapping]] = ..., dog_walking: _Optional[_Union[Service.DogWalking, _Mapping]] = ..., group_class: _Optional[_Union[Service.GroupClass, _Mapping]] = ...) -> None: ...

class PrePay(_message.Message):
    __slots__ = ("service_total", "tax_amount", "service_charge_amount")
    SERVICE_TOTAL_FIELD_NUMBER: _ClassVar[int]
    TAX_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    SERVICE_CHARGE_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    service_total: float
    tax_amount: float
    service_charge_amount: float
    def __init__(self, service_total: _Optional[float] = ..., tax_amount: _Optional[float] = ..., service_charge_amount: _Optional[float] = ...) -> None: ...

class PreAuth(_message.Message):
    __slots__ = ("tips_amount", "service_total", "tax_amount", "service_charge_amount", "payment_method_id", "card_number", "charge_token")
    TIPS_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TOTAL_FIELD_NUMBER: _ClassVar[int]
    TAX_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    SERVICE_CHARGE_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_METHOD_ID_FIELD_NUMBER: _ClassVar[int]
    CARD_NUMBER_FIELD_NUMBER: _ClassVar[int]
    CHARGE_TOKEN_FIELD_NUMBER: _ClassVar[int]
    tips_amount: float
    service_total: float
    tax_amount: float
    service_charge_amount: float
    payment_method_id: str
    card_number: str
    charge_token: str
    def __init__(self, tips_amount: _Optional[float] = ..., service_total: _Optional[float] = ..., tax_amount: _Optional[float] = ..., service_charge_amount: _Optional[float] = ..., payment_method_id: _Optional[str] = ..., card_number: _Optional[str] = ..., charge_token: _Optional[str] = ...) -> None: ...

class DiscountCode(_message.Message):
    __slots__ = ("discount_code_id", "discount_amount", "discount_code")
    DISCOUNT_CODE_ID_FIELD_NUMBER: _ClassVar[int]
    DISCOUNT_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    DISCOUNT_CODE_FIELD_NUMBER: _ClassVar[int]
    discount_code_id: int
    discount_amount: float
    discount_code: str
    def __init__(self, discount_code_id: _Optional[int] = ..., discount_amount: _Optional[float] = ..., discount_code: _Optional[str] = ...) -> None: ...

class Membership(_message.Message):
    __slots__ = ("membership_ids",)
    MEMBERSHIP_IDS_FIELD_NUMBER: _ClassVar[int]
    membership_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, membership_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class SubmitBookingRequestParams(_message.Message):
    __slots__ = ("name", "domain", "customer", "pet_services", "agreements", "prepay_guid", "pre_pay", "source_type", "pre_auth", "discount_code", "additional_notes", "membership")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_FIELD_NUMBER: _ClassVar[int]
    PET_SERVICES_FIELD_NUMBER: _ClassVar[int]
    AGREEMENTS_FIELD_NUMBER: _ClassVar[int]
    PREPAY_GUID_FIELD_NUMBER: _ClassVar[int]
    PRE_PAY_FIELD_NUMBER: _ClassVar[int]
    SOURCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    PRE_AUTH_FIELD_NUMBER: _ClassVar[int]
    DISCOUNT_CODE_FIELD_NUMBER: _ClassVar[int]
    ADDITIONAL_NOTES_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    customer: Customer
    pet_services: _containers.RepeatedCompositeFieldContainer[PetServices]
    agreements: _containers.RepeatedCompositeFieldContainer[Agreement]
    prepay_guid: str
    pre_pay: PrePay
    source_type: SourceType
    pre_auth: PreAuth
    discount_code: DiscountCode
    additional_notes: str
    membership: Membership
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., customer: _Optional[_Union[Customer, _Mapping]] = ..., pet_services: _Optional[_Iterable[_Union[PetServices, _Mapping]]] = ..., agreements: _Optional[_Iterable[_Union[Agreement, _Mapping]]] = ..., prepay_guid: _Optional[str] = ..., pre_pay: _Optional[_Union[PrePay, _Mapping]] = ..., source_type: _Optional[_Union[SourceType, str]] = ..., pre_auth: _Optional[_Union[PreAuth, _Mapping]] = ..., discount_code: _Optional[_Union[DiscountCode, _Mapping]] = ..., additional_notes: _Optional[str] = ..., membership: _Optional[_Union[Membership, _Mapping]] = ...) -> None: ...

class PetServices(_message.Message):
    __slots__ = ("pet", "services")
    PET_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    pet: Pet
    services: _containers.RepeatedCompositeFieldContainer[Service]
    def __init__(self, pet: _Optional[_Union[Pet, _Mapping]] = ..., services: _Optional[_Iterable[_Union[Service, _Mapping]]] = ...) -> None: ...

class SubmitBookingRequestResult(_message.Message):
    __slots__ = ("id", "order_id", "customer_id", "auto_accept_request")
    ID_FIELD_NUMBER: _ClassVar[int]
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    AUTO_ACCEPT_REQUEST_FIELD_NUMBER: _ClassVar[int]
    id: int
    order_id: int
    customer_id: int
    auto_accept_request: bool
    def __init__(self, id: _Optional[int] = ..., order_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., auto_accept_request: bool = ...) -> None: ...

class RescheduleBookingRequestParams(_message.Message):
    __slots__ = ("id", "grooming_reschedule")
    ID_FIELD_NUMBER: _ClassVar[int]
    GROOMING_RESCHEDULE_FIELD_NUMBER: _ClassVar[int]
    id: int
    grooming_reschedule: _appointment_defs_pb2.GroomingRescheduleDef
    def __init__(self, id: _Optional[int] = ..., grooming_reschedule: _Optional[_Union[_appointment_defs_pb2.GroomingRescheduleDef, _Mapping]] = ...) -> None: ...

class RescheduleBookingRequestResult(_message.Message):
    __slots__ = ("is_auto_accept",)
    IS_AUTO_ACCEPT_FIELD_NUMBER: _ClassVar[int]
    is_auto_accept: bool
    def __init__(self, is_auto_accept: bool = ...) -> None: ...

class CancelBookingRequestParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class CancelBookingRequestResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class CalculateBookingRequestParams(_message.Message):
    __slots__ = ("name", "domain", "pet_services")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    PET_SERVICES_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    pet_services: _containers.RepeatedCompositeFieldContainer[PetServices]
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., pet_services: _Optional[_Iterable[_Union[PetServices, _Mapping]]] = ...) -> None: ...

class CalculateBookingRequestResult(_message.Message):
    __slots__ = ("estimated_total_price",)
    ESTIMATED_TOTAL_PRICE_FIELD_NUMBER: _ClassVar[int]
    estimated_total_price: float
    def __init__(self, estimated_total_price: _Optional[float] = ...) -> None: ...
