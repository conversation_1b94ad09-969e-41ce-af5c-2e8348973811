# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/appointment/v1/appointment_extra_info_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/appointment/v1/appointment_extra_info_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n?moego/models/appointment/v1/appointment_extra_info_models.proto\x12\x1bmoego.models.appointment.v1\"t\n\x19\x41ppointmentExtraInfoModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\x12 \n\x0cis_new_order\x18\x03 \x01(\x08R\nisNewOrderB\x87\x01\n#com.moego.idl.models.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.appointment.v1.appointment_extra_info_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb'
  _globals['_APPOINTMENTEXTRAINFOMODEL']._serialized_start=96
  _globals['_APPOINTMENTEXTRAINFOMODEL']._serialized_end=212
# @@protoc_insertion_point(module_scope)
