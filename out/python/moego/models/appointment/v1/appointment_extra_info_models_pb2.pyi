from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class AppointmentExtraInfoModel(_message.Message):
    __slots__ = ("id", "appointment_id", "is_new_order")
    ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    IS_NEW_ORDER_FIELD_NUMBER: _ClassVar[int]
    id: int
    appointment_id: int
    is_new_order: bool
    def __init__(self, id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., is_new_order: bool = ...) -> None: ...
