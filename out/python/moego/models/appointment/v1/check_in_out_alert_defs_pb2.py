# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/appointment/v1/check_in_out_alert_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/appointment/v1/check_in_out_alert_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import date_pb2 as google_dot_type_dot_date__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2
from moego.models.agreement.v1 import agreement_models_pb2 as moego_dot_models_dot_agreement_dot_v1_dot_agreement__models__pb2
from moego.models.appointment.v1 import appointment_note_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__note__models__pb2
from moego.models.business_customer.v1 import business_customer_tag_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__tag__models__pb2
from moego.models.business_customer.v1 import business_pet_code_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__code__models__pb2
from moego.models.membership.v1 import subscription_models_pb2 as moego_dot_models_dot_membership_dot_v1_dot_subscription__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n9moego/models/appointment/v1/check_in_out_alert_defs.proto\x12\x1bmoego.models.appointment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x16google/type/date.proto\x1a\x17google/type/money.proto\x1a\x30moego/models/agreement/v1/agreement_models.proto\x1a\x39moego/models/appointment/v1/appointment_note_models.proto\x1a\x44moego/models/business_customer/v1/business_customer_tag_models.proto\x1a@moego/models/business_customer/v1/business_pet_code_models.proto\x1a\x34moego/models/membership/v1/subscription_models.proto\x1a\x17validate/validate.proto\"_\n\x11\x43lientPetsMapping\x12\x1f\n\x0b\x63ustomer_id\x18\x01 \x01(\x03R\ncustomerId\x12)\n\x07pet_ids\x18\x02 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02(\x00R\x06petIds\"\xb6\x01\n\x0b\x41lertDetail\x12P\n\x0c\x63lient_alert\x18\x01 \x01(\x0b\x32(.moego.models.appointment.v1.ClientAlertH\x00R\x0b\x63lientAlert\x88\x01\x01\x12\x44\n\npet_alerts\x18\x02 \x03(\x0b\x32%.moego.models.appointment.v1.PetAlertR\tpetAlertsB\x0f\n\r_client_alert\"\xdd\x06\n\x0b\x43lientAlert\x12\x1f\n\x0b\x63ustomer_id\x18\x01 \x01(\x03R\ncustomerId\x12Z\n\x10\x63lient_tag_alert\x18\x05 \x01(\x0b\x32+.moego.models.appointment.v1.ClientTagAlertH\x00R\x0e\x63lientTagAlert\x88\x01\x01\x12\\\n\x10membership_alert\x18\x06 \x01(\x0b\x32,.moego.models.appointment.v1.MembershipAlertH\x01R\x0fmembershipAlert\x88\x01\x01\x12S\n\rpackage_alert\x18\x07 \x01(\x0b\x32).moego.models.appointment.v1.PackageAlertH\x02R\x0cpackageAlert\x88\x01\x01\x12J\n\nnote_alert\x18\x08 \x01(\x0b\x32&.moego.models.appointment.v1.NoteAlertH\x03R\tnoteAlert\x88\x01\x01\x12r\n\x18unsigned_agreement_alert\x18\t \x01(\x0b\x32\x33.moego.models.appointment.v1.UnsignedAgreementAlertH\x04R\x16unsignedAgreementAlert\x88\x01\x01\x12^\n\x12\x63\x61rd_on_file_alert\x18\n \x01(\x0b\x32,.moego.models.appointment.v1.CardOnFileAlertH\x05R\x0f\x63\x61rdOnFileAlert\x88\x01\x01\x12\x66\n\x14unpaid_balance_alert\x18\x0b \x01(\x0b\x32/.moego.models.appointment.v1.UnpaidBalanceAlertH\x06R\x12unpaidBalanceAlert\x88\x01\x01\x42\x13\n\x11_client_tag_alertB\x13\n\x11_membership_alertB\x10\n\x0e_package_alertB\r\n\x0b_note_alertB\x1b\n\x19_unsigned_agreement_alertB\x15\n\x13_card_on_file_alertB\x17\n\x15_unpaid_balance_alert\"\xdc\x02\n\x08PetAlert\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12T\n\x0epet_code_alert\x18\x05 \x01(\x0b\x32).moego.models.appointment.v1.PetCodeAlertH\x00R\x0cpetCodeAlert\x88\x01\x01\x12S\n\rvaccine_alert\x18\x06 \x01(\x0b\x32).moego.models.appointment.v1.VaccineAlertH\x01R\x0cvaccineAlert\x88\x01\x01\x12V\n\x0eincident_alert\x18\x07 \x01(\x0b\x32*.moego.models.appointment.v1.IncidentAlertH\x02R\rincidentAlert\x88\x01\x01\x42\x11\n\x0f_pet_code_alertB\x10\n\x0e_vaccine_alertB\x11\n\x0f_incident_alert\"n\n\x0e\x43lientTagAlert\x12\\\n\x0b\x63lient_tags\x18\x01 \x03(\x0b\x32;.moego.models.business_customer.v1.BusinessCustomerTagModelR\nclientTags\"c\n\x0cPetCodeAlert\x12S\n\tpet_codes\x18\x01 \x03(\x0b\x32\x36.moego.models.business_customer.v1.BusinessPetCodeViewR\x08petCodes\"\xfa\x02\n\x0cVaccineAlert\x12Q\n\x08vaccines\x18\x01 \x03(\x0b\x32\x35.moego.models.appointment.v1.VaccineAlert.VaccineViewR\x08vaccines\x1a\x96\x02\n\x0bVaccineView\x12\x1d\n\nvaccine_id\x18\x01 \x01(\x03R\tvaccineId\x12!\n\x0cvaccine_name\x18\x02 \x01(\tR\x0bvaccineName\x12,\n\x12vaccine_binding_id\x18\x03 \x01(\x03R\x10vaccineBindingId\x12#\n\rdocument_urls\x18\x04 \x03(\tR\x0c\x64ocumentUrls\x12?\n\x0f\x65xpiration_date\x18\x05 \x01(\x0b\x32\x11.google.type.DateH\x00R\x0e\x65xpirationDate\x88\x01\x01\x12\x1d\n\nis_missing\x18\x06 \x01(\x08R\tisMissingB\x12\n\x10_expiration_date\"\x9a\x03\n\rIncidentAlert\x12U\n\tincidents\x18\x01 \x03(\x0b\x32\x37.moego.models.appointment.v1.IncidentAlert.IncidentViewR\tincidents\x1a\xb1\x02\n\x0cIncidentView\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x17\n\x07pet_ids\x18\x02 \x03(\x03R\x06petIds\x12,\n\x12incident_report_id\x18\x03 \x01(\x03R\x10incidentReportId\x12(\n\x10incident_type_id\x18\x04 \x01(\x03R\x0eincidentTypeId\x12,\n\x12incident_type_name\x18\x05 \x01(\tR\x10incidentTypeName\x12 \n\x0b\x64\x65scription\x18\x06 \x01(\tR\x0b\x64\x65scription\x12?\n\rincident_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0cincidentTime\"p\n\x0fMembershipAlert\x12]\n\rsubscriptions\x18\x01 \x03(\x0b\x32\x37.moego.models.membership.v1.MembershipSubscriptionModelR\rsubscriptions\"\xe3\x01\n\x0f\x43\x61rdOnFileAlert\x12Z\n\ncof_status\x18\x01 \x01(\x0e\x32\x36.moego.models.appointment.v1.CardOnFileAlert.COFStatusH\x00R\tcofStatus\x88\x01\x01\"e\n\tCOFStatus\x12\x1a\n\x16\x43OF_STATUS_UNSPECIFIED\x10\x00\x12\x0e\n\nAUTHORIZED\x10\x01\x12\x0b\n\x07PENDING\x10\x02\x12\n\n\x06\x46\x41ILED\x10\x03\x12\x13\n\x0fNO_CARD_ON_FILE\x10\x04\x42\r\n\x0b_cof_status\"\xdb\x02\n\x0cPackageAlert\x12Q\n\x08packages\x18\x01 \x03(\x0b\x32\x35.moego.models.appointment.v1.PackageAlert.PackageViewR\x08packages\x1a\xf7\x01\n\x0bPackageView\x12\x1f\n\x0b\x63ustomer_id\x18\x01 \x01(\x03R\ncustomerId\x12\x1d\n\npackage_id\x18\x02 \x01(\x03R\tpackageId\x12!\n\x0cpackage_name\x18\x03 \x01(\tR\x0bpackageName\x12\x30\n\nstart_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateR\tstartDate\x12?\n\x0f\x65xpiration_date\x18\x05 \x01(\x0b\x32\x11.google.type.DateH\x00R\x0e\x65xpirationDate\x88\x01\x01\x42\x12\n\x10_expiration_date\"q\n\tNoteAlert\x12U\n\nalert_note\x18\x01 \x01(\x0b\x32\x31.moego.models.appointment.v1.AppointmentNoteModelH\x00R\talertNote\x88\x01\x01\x42\r\n\x0b_alert_note\"m\n\x16UnsignedAgreementAlert\x12S\n\nagreements\x18\x01 \x03(\x0b\x32\x33.moego.models.agreement.v1.AgreementModelSimpleViewR\nagreements\"d\n\x12UnpaidBalanceAlert\x12<\n\runpaid_amount\x18\x01 \x01(\x0b\x32\x12.google.type.MoneyH\x00R\x0cunpaidAmount\x88\x01\x01\x42\x10\n\x0e_unpaid_amountB\x87\x01\n#com.moego.idl.models.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.appointment.v1.check_in_out_alert_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb'
  _globals['_CLIENTPETSMAPPING'].fields_by_name['pet_ids']._loaded_options = None
  _globals['_CLIENTPETSMAPPING'].fields_by_name['pet_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002(\000'
  _globals['_CLIENTPETSMAPPING']._serialized_start=496
  _globals['_CLIENTPETSMAPPING']._serialized_end=591
  _globals['_ALERTDETAIL']._serialized_start=594
  _globals['_ALERTDETAIL']._serialized_end=776
  _globals['_CLIENTALERT']._serialized_start=779
  _globals['_CLIENTALERT']._serialized_end=1640
  _globals['_PETALERT']._serialized_start=1643
  _globals['_PETALERT']._serialized_end=1991
  _globals['_CLIENTTAGALERT']._serialized_start=1993
  _globals['_CLIENTTAGALERT']._serialized_end=2103
  _globals['_PETCODEALERT']._serialized_start=2105
  _globals['_PETCODEALERT']._serialized_end=2204
  _globals['_VACCINEALERT']._serialized_start=2207
  _globals['_VACCINEALERT']._serialized_end=2585
  _globals['_VACCINEALERT_VACCINEVIEW']._serialized_start=2307
  _globals['_VACCINEALERT_VACCINEVIEW']._serialized_end=2585
  _globals['_INCIDENTALERT']._serialized_start=2588
  _globals['_INCIDENTALERT']._serialized_end=2998
  _globals['_INCIDENTALERT_INCIDENTVIEW']._serialized_start=2693
  _globals['_INCIDENTALERT_INCIDENTVIEW']._serialized_end=2998
  _globals['_MEMBERSHIPALERT']._serialized_start=3000
  _globals['_MEMBERSHIPALERT']._serialized_end=3112
  _globals['_CARDONFILEALERT']._serialized_start=3115
  _globals['_CARDONFILEALERT']._serialized_end=3342
  _globals['_CARDONFILEALERT_COFSTATUS']._serialized_start=3226
  _globals['_CARDONFILEALERT_COFSTATUS']._serialized_end=3327
  _globals['_PACKAGEALERT']._serialized_start=3345
  _globals['_PACKAGEALERT']._serialized_end=3692
  _globals['_PACKAGEALERT_PACKAGEVIEW']._serialized_start=3445
  _globals['_PACKAGEALERT_PACKAGEVIEW']._serialized_end=3692
  _globals['_NOTEALERT']._serialized_start=3694
  _globals['_NOTEALERT']._serialized_end=3807
  _globals['_UNSIGNEDAGREEMENTALERT']._serialized_start=3809
  _globals['_UNSIGNEDAGREEMENTALERT']._serialized_end=3918
  _globals['_UNPAIDBALANCEALERT']._serialized_start=3920
  _globals['_UNPAIDBALANCEALERT']._serialized_end=4020
# @@protoc_insertion_point(module_scope)
