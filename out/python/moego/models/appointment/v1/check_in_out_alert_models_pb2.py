# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/appointment/v1/check_in_out_alert_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/appointment/v1/check_in_out_alert_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n;moego/models/appointment/v1/check_in_out_alert_models.proto\x12\x1bmoego.models.appointment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xef\x02\n\x17\x43heckInOutAlertSettings\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12]\n\x11\x63heck_in_settings\x18\x02 \x01(\x0b\x32\x31.moego.models.appointment.v1.CheckInAlertSettingsR\x0f\x63heckInSettings\x12`\n\x12\x63heck_out_settings\x18\x03 \x01(\x0b\x32\x32.moego.models.appointment.v1.CheckOutAlertSettingsR\x10\x63heckOutSettings\x12\x39\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x39\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tupdatedAt\"\xb6\x01\n\x14\x43heckInAlertSettings\x12\x18\n\x07\x65nabled\x18\x01 \x01(\x08R\x07\x65nabled\x12*\n\x11trigger_for_quick\x18\x02 \x01(\x08R\x0ftriggerForQuick\x12K\n\x08settings\x18\x03 \x01(\x0b\x32*.moego.models.appointment.v1.AlertSettingsH\x00R\x08settings\x88\x01\x01\x42\x0b\n\t_settings\"\x8b\x01\n\x15\x43heckOutAlertSettings\x12\x18\n\x07\x65nabled\x18\x01 \x01(\x08R\x07\x65nabled\x12K\n\x08settings\x18\x02 \x01(\x0b\x32*.moego.models.appointment.v1.AlertSettingsH\x00R\x08settings\x88\x01\x01\x42\x0b\n\t_settings\"\xd9\x01\n\rAlertSettings\x12X\n\x0c\x63lient_alert\x18\x01 \x01(\x0b\x32\x30.moego.models.appointment.v1.ClientAlertSettingsH\x00R\x0b\x63lientAlert\x88\x01\x01\x12O\n\tpet_alert\x18\x02 \x01(\x0b\x32-.moego.models.appointment.v1.PetAlertSettingsH\x01R\x08petAlert\x88\x01\x01\x42\x0f\n\r_client_alertB\x0c\n\n_pet_alert\"\x88\x01\n\x13\x43lientAlertSettings\x12P\n\x0c\x63lient_icons\x18\x01 \x03(\x0e\x32-.moego.models.appointment.v1.ClientAlertIconsR\x0b\x63lientIcons\x12\x1f\n\x0b\x63lient_tags\x18\x02 \x03(\x03R\nclientTags\"x\n\x10PetAlertSettings\x12G\n\tpet_icons\x18\x01 \x03(\x0e\x32*.moego.models.appointment.v1.PetAlertIconsR\x08petIcons\x12\x1b\n\tpet_codes\x18\x02 \x03(\x03R\x08petCodes*\xbf\x01\n\x10\x43lientAlertIcons\x12\"\n\x1e\x43LIENT_ALERT_ICONS_UNSPECIFIED\x10\x00\x12\x13\n\x0fICON_MEMBERSHIP\x10\x01\x12\x15\n\x11ICON_CARD_ON_FILE\x10\x02\x12\x10\n\x0cICON_PACKAGE\x10\x03\x12\x13\n\x0fICON_ALERT_NOTE\x10\x04\x12\x1b\n\x17ICON_UNSIGNED_AGREEMENT\x10\x05\x12\x17\n\x13ICON_UNPAID_BALANCE\x10\x06*U\n\rPetAlertIcons\x12\x1f\n\x1bPET_ALERT_ICONS_UNSPECIFIED\x10\x00\x12\x10\n\x0cICON_VACCINE\x10\x01\x12\x11\n\rICON_INCIDENT\x10\x02\x42\x87\x01\n#com.moego.idl.models.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.appointment.v1.check_in_out_alert_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb'
  _globals['_CLIENTALERTICONS']._serialized_start=1304
  _globals['_CLIENTALERTICONS']._serialized_end=1495
  _globals['_PETALERTICONS']._serialized_start=1497
  _globals['_PETALERTICONS']._serialized_end=1582
  _globals['_CHECKINOUTALERTSETTINGS']._serialized_start=126
  _globals['_CHECKINOUTALERTSETTINGS']._serialized_end=493
  _globals['_CHECKINALERTSETTINGS']._serialized_start=496
  _globals['_CHECKINALERTSETTINGS']._serialized_end=678
  _globals['_CHECKOUTALERTSETTINGS']._serialized_start=681
  _globals['_CHECKOUTALERTSETTINGS']._serialized_end=820
  _globals['_ALERTSETTINGS']._serialized_start=823
  _globals['_ALERTSETTINGS']._serialized_end=1040
  _globals['_CLIENTALERTSETTINGS']._serialized_start=1043
  _globals['_CLIENTALERTSETTINGS']._serialized_end=1179
  _globals['_PETALERTSETTINGS']._serialized_start=1181
  _globals['_PETALERTSETTINGS']._serialized_end=1301
# @@protoc_insertion_point(module_scope)
