from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ClientAlertIcons(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    CLIENT_ALERT_ICONS_UNSPECIFIED: _ClassVar[ClientAlertIcons]
    ICON_MEMBERSHIP: _ClassVar[ClientAlertIcons]
    ICON_CARD_ON_FILE: _ClassVar[ClientAlertIcons]
    ICON_PACKAGE: _ClassVar[ClientAlertIcons]
    ICON_ALERT_NOTE: _ClassVar[ClientAlertIcons]
    ICON_UNSIGNED_AGREEMENT: _ClassVar[ClientAlertIcons]
    ICON_UNPAID_BALANCE: _ClassVar[ClientAlertIcons]

class PetAlertIcons(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PET_ALERT_ICONS_UNSPECIFIED: _ClassVar[PetAlertIcons]
    ICON_VACCINE: _ClassVar[PetAlertIcons]
    ICON_INCIDENT: _ClassVar[PetAlertIcons]
CLIENT_ALERT_ICONS_UNSPECIFIED: ClientAlertIcons
ICON_MEMBERSHIP: ClientAlertIcons
ICON_CARD_ON_FILE: ClientAlertIcons
ICON_PACKAGE: ClientAlertIcons
ICON_ALERT_NOTE: ClientAlertIcons
ICON_UNSIGNED_AGREEMENT: ClientAlertIcons
ICON_UNPAID_BALANCE: ClientAlertIcons
PET_ALERT_ICONS_UNSPECIFIED: PetAlertIcons
ICON_VACCINE: PetAlertIcons
ICON_INCIDENT: PetAlertIcons

class CheckInOutAlertSettings(_message.Message):
    __slots__ = ("company_id", "check_in_settings", "check_out_settings", "created_at", "updated_at")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CHECK_IN_SETTINGS_FIELD_NUMBER: _ClassVar[int]
    CHECK_OUT_SETTINGS_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    check_in_settings: CheckInAlertSettings
    check_out_settings: CheckOutAlertSettings
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    def __init__(self, company_id: _Optional[int] = ..., check_in_settings: _Optional[_Union[CheckInAlertSettings, _Mapping]] = ..., check_out_settings: _Optional[_Union[CheckOutAlertSettings, _Mapping]] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class CheckInAlertSettings(_message.Message):
    __slots__ = ("enabled", "trigger_for_quick", "settings")
    ENABLED_FIELD_NUMBER: _ClassVar[int]
    TRIGGER_FOR_QUICK_FIELD_NUMBER: _ClassVar[int]
    SETTINGS_FIELD_NUMBER: _ClassVar[int]
    enabled: bool
    trigger_for_quick: bool
    settings: AlertSettings
    def __init__(self, enabled: bool = ..., trigger_for_quick: bool = ..., settings: _Optional[_Union[AlertSettings, _Mapping]] = ...) -> None: ...

class CheckOutAlertSettings(_message.Message):
    __slots__ = ("enabled", "settings")
    ENABLED_FIELD_NUMBER: _ClassVar[int]
    SETTINGS_FIELD_NUMBER: _ClassVar[int]
    enabled: bool
    settings: AlertSettings
    def __init__(self, enabled: bool = ..., settings: _Optional[_Union[AlertSettings, _Mapping]] = ...) -> None: ...

class AlertSettings(_message.Message):
    __slots__ = ("client_alert", "pet_alert")
    CLIENT_ALERT_FIELD_NUMBER: _ClassVar[int]
    PET_ALERT_FIELD_NUMBER: _ClassVar[int]
    client_alert: ClientAlertSettings
    pet_alert: PetAlertSettings
    def __init__(self, client_alert: _Optional[_Union[ClientAlertSettings, _Mapping]] = ..., pet_alert: _Optional[_Union[PetAlertSettings, _Mapping]] = ...) -> None: ...

class ClientAlertSettings(_message.Message):
    __slots__ = ("client_icons", "client_tags")
    CLIENT_ICONS_FIELD_NUMBER: _ClassVar[int]
    CLIENT_TAGS_FIELD_NUMBER: _ClassVar[int]
    client_icons: _containers.RepeatedScalarFieldContainer[ClientAlertIcons]
    client_tags: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, client_icons: _Optional[_Iterable[_Union[ClientAlertIcons, str]]] = ..., client_tags: _Optional[_Iterable[int]] = ...) -> None: ...

class PetAlertSettings(_message.Message):
    __slots__ = ("pet_icons", "pet_codes")
    PET_ICONS_FIELD_NUMBER: _ClassVar[int]
    PET_CODES_FIELD_NUMBER: _ClassVar[int]
    pet_icons: _containers.RepeatedScalarFieldContainer[PetAlertIcons]
    pet_codes: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, pet_icons: _Optional[_Iterable[_Union[PetAlertIcons, str]]] = ..., pet_codes: _Optional[_Iterable[int]] = ...) -> None: ...
