# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/appointment/v1/daily_report_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/appointment/v1/daily_report_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.models.appointment.v1 import daily_report_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_daily__report__enums__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n3moego/models/appointment/v1/daily_report_defs.proto\x12\x1bmoego.models.appointment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x16google/type/date.proto\x1a\x34moego/models/appointment/v1/daily_report_enums.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x17validate/validate.proto\"X\n\tReportDef\x12K\n\x07\x63ontent\x18\x01 \x01(\x0b\x32\'.moego.models.appointment.v1.ContentDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07\x63ontent\"\x82\x02\n\nContentDef\x12*\n\x06photos\x18\x01 \x03(\tB\x12\xfa\x42\x0f\x92\x01\x0c\x10\x05\"\x08r\x06\x18\xe8\x07\x88\x01\x01R\x06photos\x12*\n\x06videos\x18\x02 \x03(\tB\x12\xfa\x42\x0f\x92\x01\x0c\x10\x01\"\x08r\x06\x18\xe8\x07\x88\x01\x01R\x06videos\x12P\n\tfeedbacks\x18\x03 \x03(\x0b\x32(.moego.models.appointment.v1.QuestionDefB\x08\xfa\x42\x05\x92\x01\x02\x08\x01R\tfeedbacks\x12J\n\x0btheme_color\x18\x04 \x01(\tB)\xfa\x42&r$2\"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$R\nthemeColor\"\x8a\x04\n\x0bQuestionDef\x12Y\n\x08\x63\x61tegory\x18\x02 \x01(\x0e\x32\x31.moego.models.appointment.v1.QuestionCategoryTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x08\x63\x61tegory\x12I\n\x04type\x18\x03 \x01(\x0e\x32).moego.models.appointment.v1.QuestionTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x04type\x12,\n\x03key\x18\x04 \x01(\tB\x1a\xfa\x42\x17r\x15\x10\x01\x18\x64\x32\x0f^[a-zA-Z0-9_]+$R\x03key\x12\x1d\n\x05title\x18\x05 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x64R\x05title\x12\x1a\n\x08required\x18\x06 \x01(\x08R\x08required\x12\x12\n\x04show\x18\x07 \x01(\x08R\x04show\x12&\n\x07options\x18\x08 \x03(\tB\x0c\xfa\x42\t\x92\x01\x06\"\x04r\x02\x18\x64R\x07options\x12&\n\x07\x63hoices\x18\t \x03(\tB\x0c\xfa\x42\t\x92\x01\x06\"\x04r\x02\x18\x64R\x07\x63hoices\x12\x33\n\x0e\x63ustom_options\x18\n \x03(\tB\x0c\xfa\x42\t\x92\x01\x06\"\x04r\x02\x18\x64R\rcustomOptions\x12\'\n\ninput_text\x18\x0b \x01(\tB\x08\xfa\x42\x05r\x03\x18\xf4\x03R\tinputText\x12*\n\x0bplaceholder\x18\x0c \x01(\tB\x08\xfa\x42\x05r\x03\x18\xac\x02R\x0bplaceholder\"\x8a\x02\n\x14SentHistoryRecordDef\x12>\n\x06report\x18\x01 \x01(\x0b\x32&.moego.models.appointment.v1.ReportDefR\x06report\x12 \n\x0b\x64\x65scription\x18\x02 \x01(\tR\x0b\x64\x65scription\x12\x34\n\x0cservice_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateR\x0bserviceDate\x12\x37\n\tsend_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x08sendTime\x12!\n\x0csent_success\x18\x05 \x01(\x08R\x0bsentSuccess\"\x95\x01\n\rSentResultDef\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12!\n\x0csent_success\x18\x03 \x01(\x08R\x0bsentSuccess\x12#\n\rerror_message\x18\x04 \x01(\tR\x0c\x65rrorMessage\"\xe0\x03\n\x14\x44\x61ilyReportConfigDef\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12%\n\x0e\x61ppointment_id\x18\x03 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x04 \x01(\x03R\x05petId\x12\x12\n\x04uuid\x18\x05 \x01(\tR\x04uuid\x12>\n\x06report\x18\x06 \x01(\x0b\x32&.moego.models.appointment.v1.ReportDefR\x06report\x12\x45\n\x06status\x18\x07 \x01(\x0e\x32-.moego.models.appointment.v1.ReportCardStatusR\x06status\x12;\n\x0bupdate_time\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nupdateTime\x12\x37\n\tsend_time\x18\t \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x08sendTime\x12H\n\x0bsend_method\x18\n \x01(\x0e\x32\'.moego.models.appointment.v1.SendMethodR\nsendMethod\"\xa2\x03\n\x1bListDailyReportConfigFilter\x12V\n\x06status\x18\x01 \x01(\x0e\x32-.moego.models.appointment.v1.ReportCardStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x06status\x88\x01\x01\x12j\n\x12service_item_types\x18\x02 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x10serviceItemTypes\x12\x35\n\nstart_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateH\x01R\tstartDate\x88\x01\x01\x12\x31\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateH\x02R\x07\x65ndDate\x88\x01\x01\x12#\n\x06pet_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x03R\x05petId\x88\x01\x01\x42\t\n\x07_statusB\r\n\x0b_start_dateB\x0b\n\t_end_dateB\t\n\x07_pet_idB\x87\x01\n#com.moego.idl.models.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.appointment.v1.daily_report_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb'
  _globals['_REPORTDEF'].fields_by_name['content']._loaded_options = None
  _globals['_REPORTDEF'].fields_by_name['content']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CONTENTDEF'].fields_by_name['photos']._loaded_options = None
  _globals['_CONTENTDEF'].fields_by_name['photos']._serialized_options = b'\372B\017\222\001\014\020\005\"\010r\006\030\350\007\210\001\001'
  _globals['_CONTENTDEF'].fields_by_name['videos']._loaded_options = None
  _globals['_CONTENTDEF'].fields_by_name['videos']._serialized_options = b'\372B\017\222\001\014\020\001\"\010r\006\030\350\007\210\001\001'
  _globals['_CONTENTDEF'].fields_by_name['feedbacks']._loaded_options = None
  _globals['_CONTENTDEF'].fields_by_name['feedbacks']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_CONTENTDEF'].fields_by_name['theme_color']._loaded_options = None
  _globals['_CONTENTDEF'].fields_by_name['theme_color']._serialized_options = b'\372B&r$2\"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$'
  _globals['_QUESTIONDEF'].fields_by_name['category']._loaded_options = None
  _globals['_QUESTIONDEF'].fields_by_name['category']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_QUESTIONDEF'].fields_by_name['type']._loaded_options = None
  _globals['_QUESTIONDEF'].fields_by_name['type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_QUESTIONDEF'].fields_by_name['key']._loaded_options = None
  _globals['_QUESTIONDEF'].fields_by_name['key']._serialized_options = b'\372B\027r\025\020\001\030d2\017^[a-zA-Z0-9_]+$'
  _globals['_QUESTIONDEF'].fields_by_name['title']._loaded_options = None
  _globals['_QUESTIONDEF'].fields_by_name['title']._serialized_options = b'\372B\004r\002\030d'
  _globals['_QUESTIONDEF'].fields_by_name['options']._loaded_options = None
  _globals['_QUESTIONDEF'].fields_by_name['options']._serialized_options = b'\372B\t\222\001\006\"\004r\002\030d'
  _globals['_QUESTIONDEF'].fields_by_name['choices']._loaded_options = None
  _globals['_QUESTIONDEF'].fields_by_name['choices']._serialized_options = b'\372B\t\222\001\006\"\004r\002\030d'
  _globals['_QUESTIONDEF'].fields_by_name['custom_options']._loaded_options = None
  _globals['_QUESTIONDEF'].fields_by_name['custom_options']._serialized_options = b'\372B\t\222\001\006\"\004r\002\030d'
  _globals['_QUESTIONDEF'].fields_by_name['input_text']._loaded_options = None
  _globals['_QUESTIONDEF'].fields_by_name['input_text']._serialized_options = b'\372B\005r\003\030\364\003'
  _globals['_QUESTIONDEF'].fields_by_name['placeholder']._loaded_options = None
  _globals['_QUESTIONDEF'].fields_by_name['placeholder']._serialized_options = b'\372B\005r\003\030\254\002'
  _globals['_LISTDAILYREPORTCONFIGFILTER'].fields_by_name['status']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGFILTER'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTDAILYREPORTCONFIGFILTER'].fields_by_name['service_item_types']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGFILTER'].fields_by_name['service_item_types']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_LISTDAILYREPORTCONFIGFILTER'].fields_by_name['pet_id']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGFILTER'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_REPORTDEF']._serialized_start=265
  _globals['_REPORTDEF']._serialized_end=353
  _globals['_CONTENTDEF']._serialized_start=356
  _globals['_CONTENTDEF']._serialized_end=614
  _globals['_QUESTIONDEF']._serialized_start=617
  _globals['_QUESTIONDEF']._serialized_end=1139
  _globals['_SENTHISTORYRECORDDEF']._serialized_start=1142
  _globals['_SENTHISTORYRECORDDEF']._serialized_end=1408
  _globals['_SENTRESULTDEF']._serialized_start=1411
  _globals['_SENTRESULTDEF']._serialized_end=1560
  _globals['_DAILYREPORTCONFIGDEF']._serialized_start=1563
  _globals['_DAILYREPORTCONFIGDEF']._serialized_end=2043
  _globals['_LISTDAILYREPORTCONFIGFILTER']._serialized_start=2046
  _globals['_LISTDAILYREPORTCONFIGFILTER']._serialized_end=2464
# @@protoc_insertion_point(module_scope)
