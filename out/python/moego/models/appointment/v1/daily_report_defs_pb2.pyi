from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import date_pb2 as _date_pb2
from moego.models.appointment.v1 import daily_report_enums_pb2 as _daily_report_enums_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ReportDef(_message.Message):
    __slots__ = ("content",)
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    content: ContentDef
    def __init__(self, content: _Optional[_Union[ContentDef, _Mapping]] = ...) -> None: ...

class ContentDef(_message.Message):
    __slots__ = ("photos", "videos", "feedbacks", "theme_color")
    PHOTOS_FIELD_NUMBER: _ClassVar[int]
    VIDEOS_FIELD_NUMBER: _ClassVar[int]
    FEEDBACKS_FIELD_NUMBER: _ClassVar[int]
    THEME_COLOR_FIELD_NUMBER: _ClassVar[int]
    photos: _containers.RepeatedScalarFieldContainer[str]
    videos: _containers.RepeatedScalarFieldContainer[str]
    feedbacks: _containers.RepeatedCompositeFieldContainer[QuestionDef]
    theme_color: str
    def __init__(self, photos: _Optional[_Iterable[str]] = ..., videos: _Optional[_Iterable[str]] = ..., feedbacks: _Optional[_Iterable[_Union[QuestionDef, _Mapping]]] = ..., theme_color: _Optional[str] = ...) -> None: ...

class QuestionDef(_message.Message):
    __slots__ = ("category", "type", "key", "title", "required", "show", "options", "choices", "custom_options", "input_text", "placeholder")
    CATEGORY_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    TITLE_FIELD_NUMBER: _ClassVar[int]
    REQUIRED_FIELD_NUMBER: _ClassVar[int]
    SHOW_FIELD_NUMBER: _ClassVar[int]
    OPTIONS_FIELD_NUMBER: _ClassVar[int]
    CHOICES_FIELD_NUMBER: _ClassVar[int]
    CUSTOM_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    INPUT_TEXT_FIELD_NUMBER: _ClassVar[int]
    PLACEHOLDER_FIELD_NUMBER: _ClassVar[int]
    category: _daily_report_enums_pb2.QuestionCategoryType
    type: _daily_report_enums_pb2.QuestionType
    key: str
    title: str
    required: bool
    show: bool
    options: _containers.RepeatedScalarFieldContainer[str]
    choices: _containers.RepeatedScalarFieldContainer[str]
    custom_options: _containers.RepeatedScalarFieldContainer[str]
    input_text: str
    placeholder: str
    def __init__(self, category: _Optional[_Union[_daily_report_enums_pb2.QuestionCategoryType, str]] = ..., type: _Optional[_Union[_daily_report_enums_pb2.QuestionType, str]] = ..., key: _Optional[str] = ..., title: _Optional[str] = ..., required: bool = ..., show: bool = ..., options: _Optional[_Iterable[str]] = ..., choices: _Optional[_Iterable[str]] = ..., custom_options: _Optional[_Iterable[str]] = ..., input_text: _Optional[str] = ..., placeholder: _Optional[str] = ...) -> None: ...

class SentHistoryRecordDef(_message.Message):
    __slots__ = ("report", "description", "service_date", "send_time", "sent_success")
    REPORT_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    SEND_TIME_FIELD_NUMBER: _ClassVar[int]
    SENT_SUCCESS_FIELD_NUMBER: _ClassVar[int]
    report: ReportDef
    description: str
    service_date: _date_pb2.Date
    send_time: _timestamp_pb2.Timestamp
    sent_success: bool
    def __init__(self, report: _Optional[_Union[ReportDef, _Mapping]] = ..., description: _Optional[str] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., send_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., sent_success: bool = ...) -> None: ...

class SentResultDef(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "sent_success", "error_message")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SENT_SUCCESS_FIELD_NUMBER: _ClassVar[int]
    ERROR_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    sent_success: bool
    error_message: str
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., sent_success: bool = ..., error_message: _Optional[str] = ...) -> None: ...

class DailyReportConfigDef(_message.Message):
    __slots__ = ("id", "customer_id", "appointment_id", "pet_id", "uuid", "report", "status", "update_time", "send_time")
    ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    UUID_FIELD_NUMBER: _ClassVar[int]
    REPORT_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    SEND_TIME_FIELD_NUMBER: _ClassVar[int]
    id: int
    customer_id: int
    appointment_id: int
    pet_id: int
    uuid: str
    report: ReportDef
    status: _daily_report_enums_pb2.ReportCardStatus
    update_time: _timestamp_pb2.Timestamp
    send_time: _timestamp_pb2.Timestamp
    def __init__(self, id: _Optional[int] = ..., customer_id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., uuid: _Optional[str] = ..., report: _Optional[_Union[ReportDef, _Mapping]] = ..., status: _Optional[_Union[_daily_report_enums_pb2.ReportCardStatus, str]] = ..., update_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., send_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class ListDailyReportConfigFilter(_message.Message):
    __slots__ = ("status", "service_item_types", "start_date", "end_date", "pet_id")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    status: _daily_report_enums_pb2.ReportCardStatus
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    pet_id: int
    def __init__(self, status: _Optional[_Union[_daily_report_enums_pb2.ReportCardStatus, str]] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., pet_id: _Optional[int] = ...) -> None: ...
