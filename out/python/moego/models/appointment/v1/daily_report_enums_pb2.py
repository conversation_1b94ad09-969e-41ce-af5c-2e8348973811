# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/appointment/v1/daily_report_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/appointment/v1/daily_report_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4moego/models/appointment/v1/daily_report_enums.proto\x12\x1bmoego.models.appointment.v1*\x97\x01\n\x0cQuestionType\x12\x1d\n\x19QUESTION_TYPE_UNSPECIFIED\x10\x00\x12\x11\n\rSINGLE_CHOICE\x10\x01\x12\x10\n\x0cMULTI_CHOICE\x10\x02\x12\x0e\n\nTEXT_INPUT\x10\x03\x12\r\n\tBODY_VIEW\x10\x04\x12\x14\n\x10SHORT_TEXT_INPUT\x10\x05\x12\x0e\n\nTAG_CHOICE\x10\x06*w\n\x14QuestionCategoryType\x12&\n\"QUESTION_CATEGORY_TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08\x46\x45\x45\x44\x42\x41\x43K\x10\x01\x12\x11\n\rPET_CONDITION\x10\x02\x12\x16\n\x12\x43USTOMIZE_FEEDBACK\x10\x03*\x93\x01\n\x10ReportCardStatus\x12\"\n\x1eREPORT_CARD_STATUS_UNSPECIFIED\x10\x00\x12\x17\n\x13REPORT_CARD_CREATED\x10\x01\x12\x15\n\x11REPORT_CARD_DRAFT\x10\x02\x12\x15\n\x11REPORT_CARD_READY\x10\x03\x12\x14\n\x10REPORT_CARD_SENT\x10\x04*U\n\nSendMethod\x12\x1b\n\x17SEND_METHOD_UNSPECIFIED\x10\x00\x12\x15\n\x11SEND_METHOD_EMAIL\x10\x01\x12\x13\n\x0fSEND_METHOD_SMS\x10\x02\x42\x87\x01\n#com.moego.idl.models.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.appointment.v1.daily_report_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb'
  _globals['_QUESTIONTYPE']._serialized_start=86
  _globals['_QUESTIONTYPE']._serialized_end=237
  _globals['_QUESTIONCATEGORYTYPE']._serialized_start=239
  _globals['_QUESTIONCATEGORYTYPE']._serialized_end=358
  _globals['_REPORTCARDSTATUS']._serialized_start=361
  _globals['_REPORTCARDSTATUS']._serialized_end=508
  _globals['_SENDMETHOD']._serialized_start=510
  _globals['_SENDMETHOD']._serialized_end=595
# @@protoc_insertion_point(module_scope)
