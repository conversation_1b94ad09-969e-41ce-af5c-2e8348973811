from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class QuestionType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    QUESTION_TYPE_UNSPECIFIED: _ClassVar[QuestionType]
    SINGLE_CHOICE: _ClassVar[QuestionType]
    MULTI_CHOICE: _ClassVar[QuestionType]
    TEXT_INPUT: _ClassVar[QuestionType]
    BODY_VIEW: _ClassVar[QuestionType]
    SHORT_TEXT_INPUT: _ClassVar[QuestionType]
    TAG_CHOICE: _ClassVar[QuestionType]

class QuestionCategoryType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    QUESTION_CATEGORY_TYPE_UNSPECIFIED: _ClassVar[QuestionCategoryType]
    FEEDBACK: _ClassVar[QuestionCategoryType]
    PET_CONDITION: _ClassVar[QuestionCategoryType]
    CUSTOMIZE_FEEDBACK: _ClassVar[QuestionCategoryType]

class ReportCardStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    REPORT_CARD_STATUS_UNSPECIFIED: _ClassVar[ReportCardStatus]
    REPORT_CARD_CREATED: _ClassVar[ReportCardStatus]
    REPORT_CARD_DRAFT: _ClassVar[ReportCardStatus]
    REPORT_CARD_READY: _ClassVar[ReportCardStatus]
    REPORT_CARD_SENT: _ClassVar[ReportCardStatus]

class SendMethod(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SEND_METHOD_UNSPECIFIED: _ClassVar[SendMethod]
    SEND_METHOD_EMAIL: _ClassVar[SendMethod]
    SEND_METHOD_SMS: _ClassVar[SendMethod]
QUESTION_TYPE_UNSPECIFIED: QuestionType
SINGLE_CHOICE: QuestionType
MULTI_CHOICE: QuestionType
TEXT_INPUT: QuestionType
BODY_VIEW: QuestionType
SHORT_TEXT_INPUT: QuestionType
TAG_CHOICE: QuestionType
QUESTION_CATEGORY_TYPE_UNSPECIFIED: QuestionCategoryType
FEEDBACK: QuestionCategoryType
PET_CONDITION: QuestionCategoryType
CUSTOMIZE_FEEDBACK: QuestionCategoryType
REPORT_CARD_STATUS_UNSPECIFIED: ReportCardStatus
REPORT_CARD_CREATED: ReportCardStatus
REPORT_CARD_DRAFT: ReportCardStatus
REPORT_CARD_READY: ReportCardStatus
REPORT_CARD_SENT: ReportCardStatus
SEND_METHOD_UNSPECIFIED: SendMethod
SEND_METHOD_EMAIL: SendMethod
SEND_METHOD_SMS: SendMethod
