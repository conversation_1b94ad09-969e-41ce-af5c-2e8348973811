# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/customer/v1/customer_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/customer/v1/customer_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,moego/models/customer/v1/customer_defs.proto\x12\x18moego.models.customer.v1\x1a\x17validate/validate.proto\"\xc9\x08\n\x0b\x43ustomerDef\x12+\n\nfirst_name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x00R\tfirstName\x88\x01\x01\x12)\n\tlast_name\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x01R\x08lastName\x88\x01\x01\x12.\n\x0b\x61vatar_path\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x02R\navatarPath\x88\x01\x01\x12/\n\x0cphone_number\x18\x04 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x1eH\x03R\x0bphoneNumber\x88\x01\x01\x12\"\n\x05\x65mail\x18\x05 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x04R\x05\x65mail\x88\x01\x01\x12:\n\x12referral_source_id\x18\x06 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x05R\x10referralSourceId\x88\x01\x01\x12>\n\x14preferred_groomer_id\x18\x07 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x06R\x12preferredGroomerId\x88\x01\x01\x12J\n\x18preferred_frequency_type\x18\x08 \x01(\x05\x42\x0b\xfa\x42\x08\x1a\x06\x30\x00\x30\x01\x30\x02H\x07R\x16preferredFrequencyType\x88\x01\x01\x12;\n\x17preferred_frequency_day\x18\t \x01(\x05H\x08R\x15preferredFrequencyDay\x88\x01\x01\x12\x35\n\rpreferred_day\x18\n \x03(\x05\x42\x10\xfa\x42\r\x92\x01\n\x10\x07\"\x06\x1a\x04\x18\x06(\x00R\x0cpreferredDay\x12\x38\n\x0epreferred_time\x18\x0b \x03(\x05\x42\x11\xfa\x42\x0e\x92\x01\x0b\x10\x02\"\x07\x1a\x05\x18\xa0\x0b(\x00R\rpreferredTime\x12Z\n\x11\x65mergency_contact\x18\x0c \x01(\x0b\x32-.moego.models.customer.v1.CustomerDef.ContactR\x10\x65mergencyContact\x12T\n\x0epickup_contact\x18\r \x01(\x0b\x32-.moego.models.customer.v1.CustomerDef.ContactR\rpickupContact\x1a\x83\x01\n\x07\x43ontact\x12&\n\nfirst_name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\tfirstName\x12$\n\tlast_name\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\x08lastName\x12*\n\x0cphone_number\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x1eR\x0bphoneNumberB\r\n\x0b_first_nameB\x0c\n\n_last_nameB\x0e\n\x0c_avatar_pathB\x0f\n\r_phone_numberB\x08\n\x06_emailB\x15\n\x13_referral_source_idB\x17\n\x15_preferred_groomer_idB\x1b\n\x19_preferred_frequency_typeB\x1a\n\x18_preferred_frequency_dayB~\n com.moego.idl.models.customer.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.customer.v1.customer_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.models.customer.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb'
  _globals['_CUSTOMERDEF_CONTACT'].fields_by_name['first_name']._loaded_options = None
  _globals['_CUSTOMERDEF_CONTACT'].fields_by_name['first_name']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CUSTOMERDEF_CONTACT'].fields_by_name['last_name']._loaded_options = None
  _globals['_CUSTOMERDEF_CONTACT'].fields_by_name['last_name']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CUSTOMERDEF_CONTACT'].fields_by_name['phone_number']._loaded_options = None
  _globals['_CUSTOMERDEF_CONTACT'].fields_by_name['phone_number']._serialized_options = b'\372B\004r\002\030\036'
  _globals['_CUSTOMERDEF'].fields_by_name['first_name']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['first_name']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CUSTOMERDEF'].fields_by_name['last_name']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['last_name']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CUSTOMERDEF'].fields_by_name['avatar_path']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['avatar_path']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_CUSTOMERDEF'].fields_by_name['phone_number']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['phone_number']._serialized_options = b'\372B\004r\002\030\036'
  _globals['_CUSTOMERDEF'].fields_by_name['email']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['email']._serialized_options = b'\372B\004r\002\0302'
  _globals['_CUSTOMERDEF'].fields_by_name['referral_source_id']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['referral_source_id']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_CUSTOMERDEF'].fields_by_name['preferred_groomer_id']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['preferred_groomer_id']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_CUSTOMERDEF'].fields_by_name['preferred_frequency_type']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['preferred_frequency_type']._serialized_options = b'\372B\010\032\0060\0000\0010\002'
  _globals['_CUSTOMERDEF'].fields_by_name['preferred_day']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['preferred_day']._serialized_options = b'\372B\r\222\001\n\020\007\"\006\032\004\030\006(\000'
  _globals['_CUSTOMERDEF'].fields_by_name['preferred_time']._loaded_options = None
  _globals['_CUSTOMERDEF'].fields_by_name['preferred_time']._serialized_options = b'\372B\016\222\001\013\020\002\"\007\032\005\030\240\013(\000'
  _globals['_CUSTOMERDEF']._serialized_start=100
  _globals['_CUSTOMERDEF']._serialized_end=1197
  _globals['_CUSTOMERDEF_CONTACT']._serialized_start=889
  _globals['_CUSTOMERDEF_CONTACT']._serialized_end=1020
# @@protoc_insertion_point(module_scope)
