from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CustomerDef(_message.Message):
    __slots__ = ("first_name", "last_name", "avatar_path", "phone_number", "email", "referral_source_id", "preferred_groomer_id", "preferred_frequency_type", "preferred_frequency_day", "preferred_day", "preferred_time", "emergency_contact", "pickup_contact")
    class Contact(_message.Message):
        __slots__ = ("first_name", "last_name", "phone_number")
        FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
        LAST_NAME_FIELD_NUMBER: _ClassVar[int]
        PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
        first_name: str
        last_name: str
        phone_number: str
        def __init__(self, first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., phone_number: _Optional[str] = ...) -> None: ...
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    REFERRAL_SOURCE_ID_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_GROOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_FREQUENCY_TYPE_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_FREQUENCY_DAY_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_DAY_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_TIME_FIELD_NUMBER: _ClassVar[int]
    EMERGENCY_CONTACT_FIELD_NUMBER: _ClassVar[int]
    PICKUP_CONTACT_FIELD_NUMBER: _ClassVar[int]
    first_name: str
    last_name: str
    avatar_path: str
    phone_number: str
    email: str
    referral_source_id: int
    preferred_groomer_id: int
    preferred_frequency_type: int
    preferred_frequency_day: int
    preferred_day: _containers.RepeatedScalarFieldContainer[int]
    preferred_time: _containers.RepeatedScalarFieldContainer[int]
    emergency_contact: CustomerDef.Contact
    pickup_contact: CustomerDef.Contact
    def __init__(self, first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., avatar_path: _Optional[str] = ..., phone_number: _Optional[str] = ..., email: _Optional[str] = ..., referral_source_id: _Optional[int] = ..., preferred_groomer_id: _Optional[int] = ..., preferred_frequency_type: _Optional[int] = ..., preferred_frequency_day: _Optional[int] = ..., preferred_day: _Optional[_Iterable[int]] = ..., preferred_time: _Optional[_Iterable[int]] = ..., emergency_contact: _Optional[_Union[CustomerDef.Contact, _Mapping]] = ..., pickup_contact: _Optional[_Union[CustomerDef.Contact, _Mapping]] = ...) -> None: ...
