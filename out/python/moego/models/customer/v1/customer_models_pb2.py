# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/customer/v1/customer_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/customer/v1/customer_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.moego/models/customer/v1/customer_models.proto\x12\x18moego.models.customer.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd0\x05\n\rCustomerModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\x1d\n\nfirst_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x04 \x01(\tR\x08lastName\x12!\n\x0cphone_number\x18\x05 \x01(\tR\x0bphoneNumber\x12\x14\n\x05\x65mail\x18\x06 \x01(\tR\x05\x65mail\x12\x1f\n\x0b\x61vatar_path\x18\x07 \x01(\tR\navatarPath\x12,\n\x12referral_source_id\x18\x08 \x01(\x05R\x10referralSourceId\x12\x30\n\x14preferred_groomer_id\x18\t \x01(\x05R\x12preferredGroomerId\x12\x38\n\x18preferred_frequency_type\x18\n \x01(\x05R\x16preferredFrequencyType\x12\x36\n\x17preferred_frequency_day\x18\x0b \x01(\x05R\x15preferredFrequencyDay\x12#\n\rpreferred_day\x18\x0c \x03(\x05R\x0cpreferredDay\x12%\n\x0epreferred_time\x18\r \x03(\x05R\rpreferredTime\x12\x39\n\x17is_block_online_booking\x18\x0e \x01(\x08\x42\x02\x18\x01R\x14isBlockOnlineBooking\x12(\n\x10is_block_message\x18\x0f \x01(\x08R\x0eisBlockMessage\x12&\n\x0fsend_auto_email\x18\x10 \x01(\x08R\rsendAutoEmail\x12*\n\x11send_auto_message\x18\x11 \x01(\x08R\x0fsendAutoMessage\x12!\n\x0c\x63lient_color\x18\x12 \x01(\tR\x0b\x63lientColor\"\x84\x01\n\x15\x43ustomerModelNameView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\x1d\n\nfirst_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x04 \x01(\tR\x08lastName\"\xfa\x07\n\x1e\x43ustomerModelOnlineBookingView\x12\x0e\n\x02id\x18\x01 \x01(\x05R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x05R\nbusinessId\x12\x1d\n\nfirst_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x04 \x01(\tR\x08lastName\x12!\n\x0cphone_number\x18\x05 \x01(\tR\x0bphoneNumber\x12\x14\n\x05\x65mail\x18\x06 \x01(\tR\x05\x65mail\x12\x1f\n\x0b\x61vatar_path\x18\x07 \x01(\tR\navatarPath\x12,\n\x12referral_source_id\x18\x08 \x01(\x05R\x10referralSourceId\x12\x30\n\x14preferred_groomer_id\x18\t \x01(\x05R\x12preferredGroomerId\x12\x38\n\x18preferred_frequency_type\x18\n \x01(\x05R\x16preferredFrequencyType\x12\x36\n\x17preferred_frequency_day\x18\x0b \x01(\x05R\x15preferredFrequencyDay\x12#\n\rpreferred_day\x18\x0c \x03(\x05R\x0cpreferredDay\x12%\n\x0epreferred_time\x18\r \x03(\x05R\rpreferredTime\x12\x39\n\x17is_block_online_booking\x18\x0e \x01(\x05\x42\x02\x18\x01R\x14isBlockOnlineBooking\x12\x36\n\x08\x62irthday\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x08\x62irthday\x12#\n\rcustomer_type\x18\x10 \x01(\tR\x0c\x63ustomerType\x12v\n\x11\x65mergency_contact\x18\x11 \x01(\x0b\x32I.moego.models.customer.v1.CustomerModelOnlineBookingView.EmergencyContactR\x10\x65mergencyContact\x12p\n\x0epickup_contact\x18\x12 \x01(\x0b\x32I.moego.models.customer.v1.CustomerModelOnlineBookingView.EmergencyContactR\rpickupContact\x1aq\n\x10\x45mergencyContact\x12\x1d\n\nfirst_name\x18\x01 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x02 \x01(\tR\x08lastName\x12!\n\x0cphone_number\x18\x03 \x01(\tR\x0bphoneNumberB~\n com.moego.idl.models.customer.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.customer.v1.customer_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.models.customer.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb'
  _globals['_CUSTOMERMODEL'].fields_by_name['is_block_online_booking']._loaded_options = None
  _globals['_CUSTOMERMODEL'].fields_by_name['is_block_online_booking']._serialized_options = b'\030\001'
  _globals['_CUSTOMERMODELONLINEBOOKINGVIEW'].fields_by_name['is_block_online_booking']._loaded_options = None
  _globals['_CUSTOMERMODELONLINEBOOKINGVIEW'].fields_by_name['is_block_online_booking']._serialized_options = b'\030\001'
  _globals['_CUSTOMERMODEL']._serialized_start=110
  _globals['_CUSTOMERMODEL']._serialized_end=830
  _globals['_CUSTOMERMODELNAMEVIEW']._serialized_start=833
  _globals['_CUSTOMERMODELNAMEVIEW']._serialized_end=965
  _globals['_CUSTOMERMODELONLINEBOOKINGVIEW']._serialized_start=968
  _globals['_CUSTOMERMODELONLINEBOOKINGVIEW']._serialized_end=1986
  _globals['_CUSTOMERMODELONLINEBOOKINGVIEW_EMERGENCYCONTACT']._serialized_start=1873
  _globals['_CUSTOMERMODELONLINEBOOKINGVIEW_EMERGENCYCONTACT']._serialized_end=1986
# @@protoc_insertion_point(module_scope)
