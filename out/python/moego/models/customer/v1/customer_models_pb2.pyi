from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CustomerModel(_message.Message):
    __slots__ = ("id", "business_id", "first_name", "last_name", "phone_number", "email", "avatar_path", "referral_source_id", "preferred_groomer_id", "preferred_frequency_type", "preferred_frequency_day", "preferred_day", "preferred_time", "is_block_online_booking", "is_block_message", "send_auto_email", "send_auto_message", "client_color")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    REFERRAL_SOURCE_ID_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_GROOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_FREQUENCY_TYPE_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_FREQUENCY_DAY_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_DAY_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_TIME_FIELD_NUMBER: _ClassVar[int]
    IS_BLOCK_ONLINE_BOOKING_FIELD_NUMBER: _ClassVar[int]
    IS_BLOCK_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    SEND_AUTO_EMAIL_FIELD_NUMBER: _ClassVar[int]
    SEND_AUTO_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    CLIENT_COLOR_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    first_name: str
    last_name: str
    phone_number: str
    email: str
    avatar_path: str
    referral_source_id: int
    preferred_groomer_id: int
    preferred_frequency_type: int
    preferred_frequency_day: int
    preferred_day: _containers.RepeatedScalarFieldContainer[int]
    preferred_time: _containers.RepeatedScalarFieldContainer[int]
    is_block_online_booking: bool
    is_block_message: bool
    send_auto_email: bool
    send_auto_message: bool
    client_color: str
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., phone_number: _Optional[str] = ..., email: _Optional[str] = ..., avatar_path: _Optional[str] = ..., referral_source_id: _Optional[int] = ..., preferred_groomer_id: _Optional[int] = ..., preferred_frequency_type: _Optional[int] = ..., preferred_frequency_day: _Optional[int] = ..., preferred_day: _Optional[_Iterable[int]] = ..., preferred_time: _Optional[_Iterable[int]] = ..., is_block_online_booking: bool = ..., is_block_message: bool = ..., send_auto_email: bool = ..., send_auto_message: bool = ..., client_color: _Optional[str] = ...) -> None: ...

class CustomerModelNameView(_message.Message):
    __slots__ = ("id", "business_id", "first_name", "last_name")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    first_name: str
    last_name: str
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., first_name: _Optional[str] = ..., last_name: _Optional[str] = ...) -> None: ...

class CustomerModelOnlineBookingView(_message.Message):
    __slots__ = ("id", "business_id", "first_name", "last_name", "phone_number", "email", "avatar_path", "referral_source_id", "preferred_groomer_id", "preferred_frequency_type", "preferred_frequency_day", "preferred_day", "preferred_time", "is_block_online_booking", "birthday", "customer_type", "emergency_contact", "pickup_contact")
    class EmergencyContact(_message.Message):
        __slots__ = ("first_name", "last_name", "phone_number")
        FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
        LAST_NAME_FIELD_NUMBER: _ClassVar[int]
        PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
        first_name: str
        last_name: str
        phone_number: str
        def __init__(self, first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., phone_number: _Optional[str] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    REFERRAL_SOURCE_ID_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_GROOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_FREQUENCY_TYPE_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_FREQUENCY_DAY_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_DAY_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_TIME_FIELD_NUMBER: _ClassVar[int]
    IS_BLOCK_ONLINE_BOOKING_FIELD_NUMBER: _ClassVar[int]
    BIRTHDAY_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_TYPE_FIELD_NUMBER: _ClassVar[int]
    EMERGENCY_CONTACT_FIELD_NUMBER: _ClassVar[int]
    PICKUP_CONTACT_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    first_name: str
    last_name: str
    phone_number: str
    email: str
    avatar_path: str
    referral_source_id: int
    preferred_groomer_id: int
    preferred_frequency_type: int
    preferred_frequency_day: int
    preferred_day: _containers.RepeatedScalarFieldContainer[int]
    preferred_time: _containers.RepeatedScalarFieldContainer[int]
    is_block_online_booking: int
    birthday: _timestamp_pb2.Timestamp
    customer_type: str
    emergency_contact: CustomerModelOnlineBookingView.EmergencyContact
    pickup_contact: CustomerModelOnlineBookingView.EmergencyContact
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., phone_number: _Optional[str] = ..., email: _Optional[str] = ..., avatar_path: _Optional[str] = ..., referral_source_id: _Optional[int] = ..., preferred_groomer_id: _Optional[int] = ..., preferred_frequency_type: _Optional[int] = ..., preferred_frequency_day: _Optional[int] = ..., preferred_day: _Optional[_Iterable[int]] = ..., preferred_time: _Optional[_Iterable[int]] = ..., is_block_online_booking: _Optional[int] = ..., birthday: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., customer_type: _Optional[str] = ..., emergency_contact: _Optional[_Union[CustomerModelOnlineBookingView.EmergencyContact, _Mapping]] = ..., pickup_contact: _Optional[_Union[CustomerModelOnlineBookingView.EmergencyContact, _Mapping]] = ...) -> None: ...
