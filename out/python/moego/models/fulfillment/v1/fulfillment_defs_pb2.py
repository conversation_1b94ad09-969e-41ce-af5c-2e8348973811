# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/fulfillment/v1/fulfillment_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/fulfillment/v1/fulfillment_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2
from moego.models.business_customer.v1 import business_customer_pet_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__pet__models__pb2
from moego.models.fulfillment.v1 import fulfillment_enums_pb2 as moego_dot_models_dot_fulfillment_dot_v1_dot_fulfillment__enums__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.order.v1 import service_charge_enums_pb2 as moego_dot_models_dot_order_dot_v1_dot_service__charge__enums__pb2
from moego.models.organization.v1 import tax_models_pb2 as moego_dot_models_dot_organization_dot_v1_dot_tax__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n2moego/models/fulfillment/v1/fulfillment_defs.proto\x12\x1bmoego.models.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17google/type/money.proto\x1a\x44moego/models/business_customer/v1/business_customer_pet_models.proto\x1a\x33moego/models/fulfillment/v1/fulfillment_enums.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x30moego/models/order/v1/service_charge_enums.proto\x1a-moego/models/organization/v1/tax_models.proto\x1a\x17validate/validate.proto\"\xca\x04\n\x14\x46ulfillmentCreateDef\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12(\n\x0b\x63ustomer_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\ncustomerId\x12:\n\x12\x62ooking_request_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x10\x62ookingRequestId\x88\x01\x01\x12\x42\n\x0fstart_date_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\rstartDateTime\x12>\n\rend_date_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x0b\x65ndDateTime\x12G\n\x06status\x18\x08 \x01(\x0e\x32#.moego.models.fulfillment.v1.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x06status\x12>\n\ncolor_code\x18\t \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^#([A-Fa-f0-9]{6})$H\x01R\tcolorCode\x88\x01\x01\x12G\n\x06source\x18\x0b \x01(\x0e\x32#.moego.models.fulfillment.v1.SourceB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x06sourceB\x15\n\x13_booking_request_idB\r\n\x0b_color_code\"\xde\x03\n\x14\x46ulfillmentUpdateDef\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12:\n\x12\x62ooking_request_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x10\x62ookingRequestId\x88\x01\x01\x12G\n\x0fstart_date_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x01R\rstartDateTime\x88\x01\x01\x12\x43\n\rend_date_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x02R\x0b\x65ndDateTime\x88\x01\x01\x12L\n\x06status\x18\x08 \x01(\x0e\x32#.moego.models.fulfillment.v1.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x03R\x06status\x88\x01\x01\x12>\n\ncolor_code\x18\t \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^#([A-Fa-f0-9]{6})$H\x04R\tcolorCode\x88\x01\x01\x42\x15\n\x13_booking_request_idB\x12\n\x10_start_date_timeB\x10\n\x0e_end_date_timeB\t\n\x07_statusB\r\n\x0b_color_code\"\x89\x03\n\rSurchargeItem\x12*\n\x11service_charge_id\x18\x01 \x01(\x03R\x0fserviceChargeId\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12\x38\n\x04type\x18\x03 \x01(\x0e\x32$.moego.models.order.v1.SurchargeTypeR\x04type\x12 \n\x0b\x64\x65scription\x18\x04 \x01(\tR\x0b\x64\x65scription\x12\x37\n\x18service_charge_detail_id\x18\x05 \x01(\x03R\x15serviceChargeDetailId\x12\x31\n\nunit_price\x18\x06 \x01(\x0b\x32\x12.google.type.MoneyR\tunitPrice\x12\x1a\n\x08quantity\x18\x07 \x01(\x05R\x08quantity\x12\x33\n\x0btotal_price\x18\x08 \x01(\x0b\x32\x12.google.type.MoneyR\ntotalPrice\x12\x1f\n\x0b\x65xternal_id\x18\t \x01(\tR\nexternalId\"\xa5\x01\n\nPetService\x12Q\n\x03pet\x18\x01 \x01(\x0b\x32?.moego.models.business_customer.v1.BusinessCustomerPetInfoModelR\x03pet\x12\x44\n\x08services\x18\x02 \x03(\x0b\x32(.moego.models.fulfillment.v1.ServiceItemR\x08services\"\x91\x05\n\x0bServiceItem\x12\x1d\n\nservice_id\x18\x01 \x01(\x03R\tserviceId\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12U\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12H\n\x0cservice_type\x18\x04 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeR\x0bserviceType\x12\x1b\n\tdetail_id\x18\x05 \x01(\x03R\x08\x64\x65tailId\x12\x31\n\nunit_price\x18\x06 \x01(\x0b\x32\x12.google.type.MoneyR\tunitPrice\x12\x1a\n\x08quantity\x18\x07 \x01(\x05R\x08quantity\x12\x33\n\x0btotal_price\x18\x08 \x01(\x0b\x32\x12.google.type.MoneyR\ntotalPrice\x12\x19\n\x08staff_id\x18\t \x01(\x03R\x07staffId\x12 \n\x0b\x64\x65scription\x18\n \x01(\tR\x0b\x64\x65scription\x12\x44\n\nline_items\x18\x0b \x03(\x0b\x32%.moego.models.fulfillment.v1.LineItemR\tlineItems\x12+\n\x12order_line_item_id\x18\x0c \x01(\x03R\x0forderLineItemId\x12<\n\x03tax\x18\r \x01(\x0b\x32*.moego.models.organization.v1.TaxRuleModelR\x03tax\x12\x1f\n\x0b\x65xternal_id\x18\x0e \x01(\tR\nexternalId\"\xda\x02\n\x08LineItem\x12\x1b\n\titem_name\x18\x01 \x01(\tR\x08itemName\x12\x31\n\nunit_price\x18\x02 \x01(\x0b\x32\x12.google.type.MoneyR\tunitPrice\x12\x1a\n\x08quantity\x18\x03 \x01(\x05R\x08quantity\x12\x31\n\nline_total\x18\x04 \x01(\x0b\x32\x12.google.type.MoneyR\tlineTotal\x12O\n\titem_type\x18\x05 \x01(\x0e\x32\x32.moego.models.fulfillment.v1.LineItem.LineItemTypeR\x08itemType\"^\n\x0cLineItemType\x12\x1e\n\x1aLINE_ITEM_TYPE_UNSPECIFIED\x10\x00\x12\x11\n\rSERVICE_PRICE\x10\x01\x12\x1b\n\x17PRICING_RULE_ADJUSTMENT\x10\x02\x42\x87\x01\n#com.moego.idl.models.fulfillment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.fulfillment.v1.fulfillment_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.fulfillment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1;fulfillmentpb'
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['company_id']._loaded_options = None
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['business_id']._loaded_options = None
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['customer_id']._loaded_options = None
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['booking_request_id']._loaded_options = None
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['booking_request_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['status']._loaded_options = None
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['color_code']._loaded_options = None
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['color_code']._serialized_options = b'\372B\027r\0252\023^#([A-Fa-f0-9]{6})$'
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['source']._loaded_options = None
  _globals['_FULFILLMENTCREATEDEF'].fields_by_name['source']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_FULFILLMENTUPDATEDEF'].fields_by_name['id']._loaded_options = None
  _globals['_FULFILLMENTUPDATEDEF'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_FULFILLMENTUPDATEDEF'].fields_by_name['booking_request_id']._loaded_options = None
  _globals['_FULFILLMENTUPDATEDEF'].fields_by_name['booking_request_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_FULFILLMENTUPDATEDEF'].fields_by_name['status']._loaded_options = None
  _globals['_FULFILLMENTUPDATEDEF'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_FULFILLMENTUPDATEDEF'].fields_by_name['color_code']._loaded_options = None
  _globals['_FULFILLMENTUPDATEDEF'].fields_by_name['color_code']._serialized_options = b'\372B\027r\0252\023^#([A-Fa-f0-9]{6})$'
  _globals['_FULFILLMENTCREATEDEF']._serialized_start=432
  _globals['_FULFILLMENTCREATEDEF']._serialized_end=1018
  _globals['_FULFILLMENTUPDATEDEF']._serialized_start=1021
  _globals['_FULFILLMENTUPDATEDEF']._serialized_end=1499
  _globals['_SURCHARGEITEM']._serialized_start=1502
  _globals['_SURCHARGEITEM']._serialized_end=1895
  _globals['_PETSERVICE']._serialized_start=1898
  _globals['_PETSERVICE']._serialized_end=2063
  _globals['_SERVICEITEM']._serialized_start=2066
  _globals['_SERVICEITEM']._serialized_end=2723
  _globals['_LINEITEM']._serialized_start=2726
  _globals['_LINEITEM']._serialized_end=3072
  _globals['_LINEITEM_LINEITEMTYPE']._serialized_start=2978
  _globals['_LINEITEM_LINEITEMTYPE']._serialized_end=3072
# @@protoc_insertion_point(module_scope)
