from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import money_pb2 as _money_pb2
from moego.models.business_customer.v1 import business_customer_pet_models_pb2 as _business_customer_pet_models_pb2
from moego.models.fulfillment.v1 import fulfillment_enums_pb2 as _fulfillment_enums_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.order.v1 import service_charge_enums_pb2 as _service_charge_enums_pb2
from moego.models.organization.v1 import tax_models_pb2 as _tax_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class FulfillmentCreateDef(_message.Message):
    __slots__ = ("company_id", "business_id", "customer_id", "booking_request_id", "start_date_time", "end_date_time", "status", "color_code", "source")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    customer_id: int
    booking_request_id: int
    start_date_time: _timestamp_pb2.Timestamp
    end_date_time: _timestamp_pb2.Timestamp
    status: _fulfillment_enums_pb2.Status
    color_code: str
    source: _fulfillment_enums_pb2.Source
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., start_date_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., end_date_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., status: _Optional[_Union[_fulfillment_enums_pb2.Status, str]] = ..., color_code: _Optional[str] = ..., source: _Optional[_Union[_fulfillment_enums_pb2.Source, str]] = ...) -> None: ...

class FulfillmentUpdateDef(_message.Message):
    __slots__ = ("id", "booking_request_id", "start_date_time", "end_date_time", "status", "color_code")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    start_date_time: _timestamp_pb2.Timestamp
    end_date_time: _timestamp_pb2.Timestamp
    status: _fulfillment_enums_pb2.Status
    color_code: str
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., start_date_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., end_date_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., status: _Optional[_Union[_fulfillment_enums_pb2.Status, str]] = ..., color_code: _Optional[str] = ...) -> None: ...

class SurchargeItem(_message.Message):
    __slots__ = ("service_charge_id", "name", "type", "description", "service_charge_detail_id", "unit_price", "quantity", "total_price", "external_id")
    SERVICE_CHARGE_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    SERVICE_CHARGE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    UNIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_FIELD_NUMBER: _ClassVar[int]
    TOTAL_PRICE_FIELD_NUMBER: _ClassVar[int]
    EXTERNAL_ID_FIELD_NUMBER: _ClassVar[int]
    service_charge_id: int
    name: str
    type: _service_charge_enums_pb2.SurchargeType
    description: str
    service_charge_detail_id: int
    unit_price: _money_pb2.Money
    quantity: int
    total_price: _money_pb2.Money
    external_id: str
    def __init__(self, service_charge_id: _Optional[int] = ..., name: _Optional[str] = ..., type: _Optional[_Union[_service_charge_enums_pb2.SurchargeType, str]] = ..., description: _Optional[str] = ..., service_charge_detail_id: _Optional[int] = ..., unit_price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., quantity: _Optional[int] = ..., total_price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., external_id: _Optional[str] = ...) -> None: ...

class PetService(_message.Message):
    __slots__ = ("pet", "services")
    PET_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    pet: _business_customer_pet_models_pb2.BusinessCustomerPetInfoModel
    services: _containers.RepeatedCompositeFieldContainer[ServiceItem]
    def __init__(self, pet: _Optional[_Union[_business_customer_pet_models_pb2.BusinessCustomerPetInfoModel, _Mapping]] = ..., services: _Optional[_Iterable[_Union[ServiceItem, _Mapping]]] = ...) -> None: ...

class ServiceItem(_message.Message):
    __slots__ = ("service_id", "name", "service_item_type", "service_type", "detail_id", "unit_price", "quantity", "total_price", "staff_id", "description", "line_items", "order_line_item_id", "tax", "external_id")
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    UNIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_FIELD_NUMBER: _ClassVar[int]
    TOTAL_PRICE_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    LINE_ITEMS_FIELD_NUMBER: _ClassVar[int]
    ORDER_LINE_ITEM_ID_FIELD_NUMBER: _ClassVar[int]
    TAX_FIELD_NUMBER: _ClassVar[int]
    EXTERNAL_ID_FIELD_NUMBER: _ClassVar[int]
    service_id: int
    name: str
    service_item_type: _service_enum_pb2.ServiceItemType
    service_type: _service_enum_pb2.ServiceType
    detail_id: int
    unit_price: _money_pb2.Money
    quantity: int
    total_price: _money_pb2.Money
    staff_id: int
    description: str
    line_items: _containers.RepeatedCompositeFieldContainer[LineItem]
    order_line_item_id: int
    tax: _tax_models_pb2.TaxRuleModel
    external_id: str
    def __init__(self, service_id: _Optional[int] = ..., name: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., service_type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., detail_id: _Optional[int] = ..., unit_price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., quantity: _Optional[int] = ..., total_price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., staff_id: _Optional[int] = ..., description: _Optional[str] = ..., line_items: _Optional[_Iterable[_Union[LineItem, _Mapping]]] = ..., order_line_item_id: _Optional[int] = ..., tax: _Optional[_Union[_tax_models_pb2.TaxRuleModel, _Mapping]] = ..., external_id: _Optional[str] = ...) -> None: ...

class LineItem(_message.Message):
    __slots__ = ("item_name", "unit_price", "quantity", "line_total", "item_type")
    class LineItemType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        LINE_ITEM_TYPE_UNSPECIFIED: _ClassVar[LineItem.LineItemType]
        SERVICE_PRICE: _ClassVar[LineItem.LineItemType]
        PRICING_RULE_ADJUSTMENT: _ClassVar[LineItem.LineItemType]
    LINE_ITEM_TYPE_UNSPECIFIED: LineItem.LineItemType
    SERVICE_PRICE: LineItem.LineItemType
    PRICING_RULE_ADJUSTMENT: LineItem.LineItemType
    ITEM_NAME_FIELD_NUMBER: _ClassVar[int]
    UNIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_FIELD_NUMBER: _ClassVar[int]
    LINE_TOTAL_FIELD_NUMBER: _ClassVar[int]
    ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    item_name: str
    unit_price: _money_pb2.Money
    quantity: int
    line_total: _money_pb2.Money
    item_type: LineItem.LineItemType
    def __init__(self, item_name: _Optional[str] = ..., unit_price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., quantity: _Optional[int] = ..., line_total: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., item_type: _Optional[_Union[LineItem.LineItemType, str]] = ...) -> None: ...
