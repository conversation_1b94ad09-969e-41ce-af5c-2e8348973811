# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/offering/v1/evaluation_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/offering/v1/evaluation_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n0moego/models/offering/v1/evaluation_models.proto\x12\x18moego.models.offering.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a+moego/models/offering/v1/service_enum.proto\"\xdc\x08\n\x0f\x45valuationModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12;\n\x1a\x61vailable_for_all_business\x18\x02 \x01(\x08R\x17\x61vailableForAllBusiness\x12\x34\n\x16\x61vailable_business_ids\x18\x03 \x03(\x03R\x14\x61vailableBusinessIds\x12W\n\x12service_item_types\x18\x04 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypes\x12\x14\n\x05price\x18\x05 \x01(\x01R\x05price\x12\x1a\n\x08\x64uration\x18\x06 \x01(\x05R\x08\x64uration\x12\x1d\n\ncolor_code\x18\x07 \x01(\tR\tcolorCode\x12\x12\n\x04name\x18\x08 \x01(\tR\x04name\x12\x1b\n\tis_active\x18\t \x01(\x08R\x08isActive\x12%\n\x0elodging_filter\x18\n \x01(\x08R\rlodgingFilter\x12\x34\n\x16\x63ustomized_lodging_ids\x18\x0b \x03(\x03R\x14\x63ustomizedLodgingIds\x12 \n\x0b\x64\x65scription\x18\x0c \x01(\tR\x0b\x64\x65scription\x12\x37\n\x18\x61lias_for_online_booking\x18\r \x01(\tR\x15\x61liasForOnlineBooking\x12 \n\x0cis_all_staff\x18\x0e \x01(\x08R\nisAllStaff\x12,\n\x12\x61llowed_staff_list\x18\x0f \x03(\x03R\x10\x61llowedStaffList\x12\x35\n\x17\x61llow_staff_auto_assign\x18\x10 \x01(\x08R\x14\x61llowStaffAutoAssign\x12#\n\ris_resettable\x18\x11 \x01(\x08R\x0cisResettable\x12.\n\x13reset_interval_days\x18\x12 \x01(\x05R\x11resetIntervalDays\x12\x1d\n\ncompany_id\x18\x13 \x01(\x03R\tcompanyId\x12!\n\x0c\x62reed_filter\x18\x14 \x01(\x08R\x0b\x62reedFilter\x12M\n\rbreed_filters\x18\x15 \x03(\x0b\x32(.moego.models.offering.v1.PetBreedFilterR\x0c\x62reedFilters\x12\x15\n\x06tax_id\x18\x16 \x01(\x03R\x05taxId\x12\x39\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x39\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x39\n\ndeleted_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tdeletedAt\"\xf4\x02\n\x0e\x45valuationView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12W\n\x12service_item_types\x18\x04 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypes\x12\x14\n\x05price\x18\x05 \x01(\x01R\x05price\x12\x1a\n\x08\x64uration\x18\x06 \x01(\x05R\x08\x64uration\x12\x1d\n\ncolor_code\x18\x07 \x01(\tR\tcolorCode\x12\x12\n\x04name\x18\x08 \x01(\tR\x04name\x12\x1b\n\tis_active\x18\t \x01(\x08R\x08isActive\x12 \n\x0b\x64\x65scription\x18\x0c \x01(\tR\x0b\x64\x65scription\x12.\n\x13reset_interval_days\x18\x12 \x01(\x05R\x11resetIntervalDays\x12\x1a\n\x06tax_id\x18\x16 \x01(\x03H\x00R\x05taxId\x88\x01\x01\x42\t\n\x07_tax_id\"\x91\x01\n\x0ePetBreedFilter\x12<\n\x08pet_type\x18\x01 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\x12 \n\x0cis_all_breed\x18\x02 \x01(\x08R\nisAllBreed\x12\x1f\n\x0b\x62reed_names\x18\x03 \x03(\tR\nbreedNames\"\xc7\x05\n\x13\x45valuationBriefView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n\x05price\x18\x02 \x01(\x01R\x05price\x12\x1a\n\x08\x64uration\x18\x03 \x01(\x05R\x08\x64uration\x12\x1d\n\ncolor_code\x18\x04 \x01(\tR\tcolorCode\x12\x12\n\x04name\x18\x05 \x01(\tR\x04name\x12 \n\x0b\x64\x65scription\x18\x06 \x01(\tR\x0b\x64\x65scription\x12\x37\n\x18\x61lias_for_online_booking\x18\x07 \x01(\tR\x15\x61liasForOnlineBooking\x12=\n\x1bis_online_booking_available\x18\x08 \x01(\x08R\x18isOnlineBookingAvailable\x12 \n\x0cis_all_staff\x18\t \x01(\x08R\nisAllStaff\x12,\n\x12\x61llowed_staff_list\x18\n \x03(\x03R\x10\x61llowedStaffList\x12\x35\n\x17\x61llow_staff_auto_assign\x18\x0b \x01(\x08R\x14\x61llowStaffAutoAssign\x12\x1b\n\tis_active\x18\x0c \x01(\x08R\x08isActive\x12!\n\x0c\x62reed_filter\x18\x14 \x01(\x08R\x0b\x62reedFilter\x12M\n\rbreed_filters\x18\x15 \x03(\x0b\x32(.moego.models.offering.v1.PetBreedFilterR\x0c\x62reedFilters\x12\x15\n\x06tax_id\x18\x16 \x01(\x03R\x05taxId\x12\x39\n\ncreated_at\x18# \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x39\n\nupdated_at\x18$ \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tupdatedAt\"\xae\x06\n\x1b\x45valuationServiceClientView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12;\n\x1a\x61vailable_for_all_business\x18\x02 \x01(\x08R\x17\x61vailableForAllBusiness\x12\x34\n\x16\x61vailable_business_ids\x18\x03 \x03(\x03R\x14\x61vailableBusinessIds\x12W\n\x12service_item_types\x18\x04 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypes\x12\x14\n\x05price\x18\x05 \x01(\x01R\x05price\x12\x1a\n\x08\x64uration\x18\x06 \x01(\x05R\x08\x64uration\x12\x1d\n\ncolor_code\x18\x07 \x01(\tR\tcolorCode\x12\x12\n\x04name\x18\x08 \x01(\tR\x04name\x12\x1b\n\tis_active\x18\t \x01(\x08R\x08isActive\x12%\n\x0elodging_filter\x18\n \x01(\x08R\rlodgingFilter\x12\x34\n\x16\x63ustomized_lodging_ids\x18\x0b \x03(\x03R\x14\x63ustomizedLodgingIds\x12 \n\x0b\x64\x65scription\x18\x0c \x01(\tR\x0b\x64\x65scription\x12\x37\n\x18\x61lias_for_online_booking\x18\r \x01(\tR\x15\x61liasForOnlineBooking\x12 \n\x0cis_all_staff\x18\x0e \x01(\x08R\nisAllStaff\x12,\n\x12\x61llowed_staff_list\x18\x0f \x03(\x03R\x10\x61llowedStaffList\x12\x35\n\x17\x61llow_staff_auto_assign\x18\x10 \x01(\x08R\x14\x61llowStaffAutoAssign\x12#\n\ris_resettable\x18\x11 \x01(\x08R\x0cisResettable\x12.\n\x13reset_interval_days\x18\x12 \x01(\x05R\x11resetIntervalDays\x12\x1d\n\ncompany_id\x18\x13 \x01(\x03R\tcompanyIdB~\n com.moego.idl.models.offering.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.offering.v1.evaluation_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.models.offering.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb'
  _globals['_EVALUATIONMODEL']._serialized_start=208
  _globals['_EVALUATIONMODEL']._serialized_end=1324
  _globals['_EVALUATIONVIEW']._serialized_start=1327
  _globals['_EVALUATIONVIEW']._serialized_end=1699
  _globals['_PETBREEDFILTER']._serialized_start=1702
  _globals['_PETBREEDFILTER']._serialized_end=1847
  _globals['_EVALUATIONBRIEFVIEW']._serialized_start=1850
  _globals['_EVALUATIONBRIEFVIEW']._serialized_end=2561
  _globals['_EVALUATIONSERVICECLIENTVIEW']._serialized_start=2564
  _globals['_EVALUATIONSERVICECLIENTVIEW']._serialized_end=3378
# @@protoc_insertion_point(module_scope)
