from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as _customer_pet_enums_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class EvaluationModel(_message.Message):
    __slots__ = ("id", "available_for_all_business", "available_business_ids", "service_item_types", "price", "duration", "color_code", "name", "is_active", "lodging_filter", "customized_lodging_ids", "description", "alias_for_online_booking", "is_all_staff", "allowed_staff_list", "allow_staff_auto_assign", "is_resettable", "reset_interval_days", "company_id", "breed_filter", "breed_filters", "tax_id", "created_at", "updated_at", "deleted_at")
    ID_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_FOR_ALL_BUSINESS_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    IS_ACTIVE_FIELD_NUMBER: _ClassVar[int]
    LODGING_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_LODGING_IDS_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    ALIAS_FOR_ONLINE_BOOKING_FIELD_NUMBER: _ClassVar[int]
    IS_ALL_STAFF_FIELD_NUMBER: _ClassVar[int]
    ALLOWED_STAFF_LIST_FIELD_NUMBER: _ClassVar[int]
    ALLOW_STAFF_AUTO_ASSIGN_FIELD_NUMBER: _ClassVar[int]
    IS_RESETTABLE_FIELD_NUMBER: _ClassVar[int]
    RESET_INTERVAL_DAYS_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BREED_FILTER_FIELD_NUMBER: _ClassVar[int]
    BREED_FILTERS_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    DELETED_AT_FIELD_NUMBER: _ClassVar[int]
    id: int
    available_for_all_business: bool
    available_business_ids: _containers.RepeatedScalarFieldContainer[int]
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    price: float
    duration: int
    color_code: str
    name: str
    is_active: bool
    lodging_filter: bool
    customized_lodging_ids: _containers.RepeatedScalarFieldContainer[int]
    description: str
    alias_for_online_booking: str
    is_all_staff: bool
    allowed_staff_list: _containers.RepeatedScalarFieldContainer[int]
    allow_staff_auto_assign: bool
    is_resettable: bool
    reset_interval_days: int
    company_id: int
    breed_filter: bool
    breed_filters: _containers.RepeatedCompositeFieldContainer[PetBreedFilter]
    tax_id: int
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    deleted_at: _timestamp_pb2.Timestamp
    def __init__(self, id: _Optional[int] = ..., available_for_all_business: bool = ..., available_business_ids: _Optional[_Iterable[int]] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., price: _Optional[float] = ..., duration: _Optional[int] = ..., color_code: _Optional[str] = ..., name: _Optional[str] = ..., is_active: bool = ..., lodging_filter: bool = ..., customized_lodging_ids: _Optional[_Iterable[int]] = ..., description: _Optional[str] = ..., alias_for_online_booking: _Optional[str] = ..., is_all_staff: bool = ..., allowed_staff_list: _Optional[_Iterable[int]] = ..., allow_staff_auto_assign: bool = ..., is_resettable: bool = ..., reset_interval_days: _Optional[int] = ..., company_id: _Optional[int] = ..., breed_filter: bool = ..., breed_filters: _Optional[_Iterable[_Union[PetBreedFilter, _Mapping]]] = ..., tax_id: _Optional[int] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., deleted_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class EvaluationView(_message.Message):
    __slots__ = ("id", "service_item_types", "price", "duration", "color_code", "name", "is_active", "description", "reset_interval_days", "tax_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    IS_ACTIVE_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    RESET_INTERVAL_DAYS_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    price: float
    duration: int
    color_code: str
    name: str
    is_active: bool
    description: str
    reset_interval_days: int
    tax_id: int
    def __init__(self, id: _Optional[int] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., price: _Optional[float] = ..., duration: _Optional[int] = ..., color_code: _Optional[str] = ..., name: _Optional[str] = ..., is_active: bool = ..., description: _Optional[str] = ..., reset_interval_days: _Optional[int] = ..., tax_id: _Optional[int] = ...) -> None: ...

class PetBreedFilter(_message.Message):
    __slots__ = ("pet_type", "is_all_breed", "breed_names")
    PET_TYPE_FIELD_NUMBER: _ClassVar[int]
    IS_ALL_BREED_FIELD_NUMBER: _ClassVar[int]
    BREED_NAMES_FIELD_NUMBER: _ClassVar[int]
    pet_type: _customer_pet_enums_pb2.PetType
    is_all_breed: bool
    breed_names: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, pet_type: _Optional[_Union[_customer_pet_enums_pb2.PetType, str]] = ..., is_all_breed: bool = ..., breed_names: _Optional[_Iterable[str]] = ...) -> None: ...

class EvaluationBriefView(_message.Message):
    __slots__ = ("id", "price", "duration", "color_code", "name", "description", "alias_for_online_booking", "is_online_booking_available", "is_all_staff", "allowed_staff_list", "allow_staff_auto_assign", "is_active", "breed_filter", "breed_filters", "tax_id", "created_at", "updated_at")
    ID_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    ALIAS_FOR_ONLINE_BOOKING_FIELD_NUMBER: _ClassVar[int]
    IS_ONLINE_BOOKING_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    IS_ALL_STAFF_FIELD_NUMBER: _ClassVar[int]
    ALLOWED_STAFF_LIST_FIELD_NUMBER: _ClassVar[int]
    ALLOW_STAFF_AUTO_ASSIGN_FIELD_NUMBER: _ClassVar[int]
    IS_ACTIVE_FIELD_NUMBER: _ClassVar[int]
    BREED_FILTER_FIELD_NUMBER: _ClassVar[int]
    BREED_FILTERS_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    id: int
    price: float
    duration: int
    color_code: str
    name: str
    description: str
    alias_for_online_booking: str
    is_online_booking_available: bool
    is_all_staff: bool
    allowed_staff_list: _containers.RepeatedScalarFieldContainer[int]
    allow_staff_auto_assign: bool
    is_active: bool
    breed_filter: bool
    breed_filters: _containers.RepeatedCompositeFieldContainer[PetBreedFilter]
    tax_id: int
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    def __init__(self, id: _Optional[int] = ..., price: _Optional[float] = ..., duration: _Optional[int] = ..., color_code: _Optional[str] = ..., name: _Optional[str] = ..., description: _Optional[str] = ..., alias_for_online_booking: _Optional[str] = ..., is_online_booking_available: bool = ..., is_all_staff: bool = ..., allowed_staff_list: _Optional[_Iterable[int]] = ..., allow_staff_auto_assign: bool = ..., is_active: bool = ..., breed_filter: bool = ..., breed_filters: _Optional[_Iterable[_Union[PetBreedFilter, _Mapping]]] = ..., tax_id: _Optional[int] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class EvaluationServiceClientView(_message.Message):
    __slots__ = ("id", "available_for_all_business", "available_business_ids", "service_item_types", "price", "duration", "color_code", "name", "is_active", "lodging_filter", "customized_lodging_ids", "description", "alias_for_online_booking", "is_all_staff", "allowed_staff_list", "allow_staff_auto_assign", "is_resettable", "reset_interval_days", "company_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_FOR_ALL_BUSINESS_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    IS_ACTIVE_FIELD_NUMBER: _ClassVar[int]
    LODGING_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_LODGING_IDS_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    ALIAS_FOR_ONLINE_BOOKING_FIELD_NUMBER: _ClassVar[int]
    IS_ALL_STAFF_FIELD_NUMBER: _ClassVar[int]
    ALLOWED_STAFF_LIST_FIELD_NUMBER: _ClassVar[int]
    ALLOW_STAFF_AUTO_ASSIGN_FIELD_NUMBER: _ClassVar[int]
    IS_RESETTABLE_FIELD_NUMBER: _ClassVar[int]
    RESET_INTERVAL_DAYS_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    available_for_all_business: bool
    available_business_ids: _containers.RepeatedScalarFieldContainer[int]
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    price: float
    duration: int
    color_code: str
    name: str
    is_active: bool
    lodging_filter: bool
    customized_lodging_ids: _containers.RepeatedScalarFieldContainer[int]
    description: str
    alias_for_online_booking: str
    is_all_staff: bool
    allowed_staff_list: _containers.RepeatedScalarFieldContainer[int]
    allow_staff_auto_assign: bool
    is_resettable: bool
    reset_interval_days: int
    company_id: int
    def __init__(self, id: _Optional[int] = ..., available_for_all_business: bool = ..., available_business_ids: _Optional[_Iterable[int]] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., price: _Optional[float] = ..., duration: _Optional[int] = ..., color_code: _Optional[str] = ..., name: _Optional[str] = ..., is_active: bool = ..., lodging_filter: bool = ..., customized_lodging_ids: _Optional[_Iterable[int]] = ..., description: _Optional[str] = ..., alias_for_online_booking: _Optional[str] = ..., is_all_staff: bool = ..., allowed_staff_list: _Optional[_Iterable[int]] = ..., allow_staff_auto_assign: bool = ..., is_resettable: bool = ..., reset_interval_days: _Optional[int] = ..., company_id: _Optional[int] = ...) -> None: ...
