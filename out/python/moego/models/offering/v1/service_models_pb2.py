# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/offering/v1/service_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/offering/v1/service_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-moego/models/offering/v1/service_models.proto\x12\x18moego.models.offering.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a+moego/models/offering/v1/service_enum.proto\"\x8f\x01\n\x14ServiceCategoryModel\x12\x1f\n\x0b\x63\x61tegory_id\x18\x01 \x01(\x03R\ncategoryId\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12\x42\n\x08services\x18\x03 \x03(\x0b\x32&.moego.models.offering.v1.ServiceModelR\x08services\"\xac\x17\n\x0cServiceModel\x12\x1d\n\nservice_id\x18\x01 \x01(\x03R\tserviceId\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12 \n\x0b\x64\x65scription\x18\x03 \x01(\tR\x0b\x64\x65scription\x12\x1a\n\x08inactive\x18\x04 \x01(\x08R\x08inactive\x12\x16\n\x06images\x18\x05 \x03(\tR\x06images\x12\x1f\n\x0b\x63\x61tegory_id\x18\x06 \x01(\x03R\ncategoryId\x12\x1d\n\ncolor_code\x18\x07 \x01(\tR\tcolorCode\x12h\n\x16location_override_list\x18\x08 \x03(\x0b\x32..moego.models.offering.v1.LocationOverrideRuleB\x02\x18\x01R\x14locationOverrideList\x12\x36\n\x17require_dedicated_staff\x18\t \x01(\x08R\x15requireDedicatedStaff\x12U\n\x11service_item_type\x18\n \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12\x14\n\x05price\x18\x0b \x01(\x01R\x05price\x12I\n\nprice_unit\x18\x0c \x01(\x0e\x32*.moego.models.offering.v1.ServicePriceUnitR\tpriceUnit\x12\x15\n\x06tax_id\x18\r \x01(\x03R\x05taxId\x12\x1a\n\x08\x64uration\x18\x0e \x01(\x05R\x08\x64uration\x12\x33\n\x16\x61\x64\x64_to_commission_base\x18\x0f \x01(\x08R\x13\x61\x64\x64ToCommissionBase\x12\x17\n\x07\x63\x61n_tip\x18\x10 \x01(\x08R\x06\x63\x61nTip\x12&\n\x0fis_all_location\x18\x11 \x01(\x08R\risAllLocation\x12;\n\x1a\x61vailable_business_id_list\x18\x12 \x03(\x03R\x17\x61vailableBusinessIdList\x12!\n\x0c\x62reed_filter\x18\x13 \x01(\x08R\x0b\x62reedFilter\x12T\n\x10\x63ustomized_breed\x18\x14 \x03(\x0b\x32).moego.models.offering.v1.CustomizedBreedR\x0f\x63ustomizedBreed\x12&\n\x0fpet_size_filter\x18\x15 \x01(\x08R\rpetSizeFilter\x12\x30\n\x14\x63ustomized_pet_sizes\x18\x16 \x03(\x03R\x12\x63ustomizedPetSizes\x12\'\n\rweight_filter\x18\x17 \x01(\x08\x42\x02\x18\x01R\x0cweightFilter\x12%\n\x0cweight_range\x18\x18 \x03(\x01\x42\x02\x18\x01R\x0bweightRange\x12\x1f\n\x0b\x63oat_filter\x18\x19 \x01(\x08R\ncoatFilter\x12\'\n\x0f\x63ustomized_coat\x18\x1a \x03(\x03R\x0e\x63ustomizedCoat\x12:\n\x19require_dedicated_lodging\x18\x1b \x01(\x08R\x17requireDedicatedLodging\x12%\n\x0elodging_filter\x18\x1c \x01(\x08R\rlodgingFilter\x12/\n\x13\x63ustomized_lodgings\x18\x1d \x03(\x03R\x12\x63ustomizedLodgings\x12%\n\x0eservice_filter\x18\x1e \x01(\x08R\rserviceFilter\x12W\n\x13service_filter_list\x18\x1f \x03(\x0b\x32\'.moego.models.offering.v1.ServiceFilterR\x11serviceFilterList\x12\x39\n\x04type\x18  \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeR\x04type\x12!\n\x0cmax_duration\x18! \x01(\x05R\x0bmaxDuration\x12X\n\x12\x61uto_rollover_rule\x18\" \x01(\x0b\x32*.moego.models.offering.v1.AutoRolloverRuleR\x10\x61utoRolloverRule\x12;\n\x0b\x63reate_time\x18# \x01(\x0b\x32\x1a.google.protobuf.TimestampR\ncreateTime\x12;\n\x0bupdate_time\x18$ \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nupdateTime\x12\x1d\n\nis_deleted\x18% \x01(\x08R\tisDeleted\x12\x1d\n\ncompany_id\x18& \x01(\x03R\tcompanyId\x12\x35\n\x17\x61vailable_staff_id_list\x18\' \x03(\x03R\x14\x61vailableStaffIdList\x12t\n\x1clocation_staff_override_list\x18( \x03(\x0b\x32\x33.moego.models.offering.v1.LocationStaffOverrideRuleR\x19locationStaffOverrideList\x12\x35\n\x17\x61vailable_for_all_staff\x18* \x01(\x08R\x14\x61vailableForAllStaff\x12\\\n\x0fpet_code_filter\x18+ \x01(\x0b\x32\x34.moego.models.offering.v1.ServiceModel.PetCodeFilterR\rpetCodeFilter\x12,\n\x12\x62undle_service_ids\x18, \x03(\x03R\x10\x62undleServiceIds\x12\x45\n\x06source\x18- \x01(\x0e\x32-.moego.models.offering.v1.ServiceModel.SourceR\x06source\x12!\n\x0cnum_sessions\x18. \x01(\x05R\x0bnumSessions\x12\x30\n\x14\x64uration_session_min\x18/ \x01(\x05R\x12\x64urationSessionMin\x12\x1a\n\x08\x63\x61pacity\x18\x30 \x01(\x05R\x08\x63\x61pacity\x12\x41\n\x1dis_require_prerequisite_class\x18\x32 \x01(\x08R\x1aisRequirePrerequisiteClass\x12\x34\n\x16prerequisite_class_ids\x18\x33 \x03(\x03R\x14prerequisiteClassIds\x12\x34\n\x16is_evaluation_required\x18\x34 \x01(\x08R\x14isEvaluationRequired\x12@\n\x1dis_evaluation_required_for_ob\x18\x35 \x01(\x08R\x19isEvaluationRequiredForOb\x12#\n\revaluation_id\x18\x36 \x01(\x03R\x0c\x65valuationId\x12l\n\x17\x61\x64\x64itional_service_rule\x18\x37 \x01(\x0b\x32/.moego.models.offering.v1.AdditionalServiceRuleH\x00R\x15\x61\x64\x64itionalServiceRule\x88\x01\x01\x1a|\n\rPetCodeFilter\x12\"\n\ris_white_list\x18\x01 \x01(\x08R\x0bisWhiteList\x12%\n\x0fis_all_pet_code\x18\x02 \x01(\x08R\x0cisAllPetCode\x12 \n\x0cpet_code_ids\x18\x03 \x03(\x03R\npetCodeIds\"H\n\x06Source\x12\x16\n\x12SOURCE_UNSPECIFIED\x10\x00\x12\x12\n\x0eMOEGO_PLATFORM\x10\x01\x12\x12\n\x0e\x45NTERPRISE_HUB\x10\x02\x42\x1a\n\x18_additional_service_ruleJ\x04\x08)\x10*R\x0cis_all_staff\"\xea\x01\n\x14LocationOverrideRule\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x19\n\x05price\x18\x02 \x01(\x01H\x00R\x05price\x88\x01\x01\x12\x1a\n\x06tax_id\x18\x03 \x01(\x03H\x01R\x05taxId\x88\x01\x01\x12\x1f\n\x08\x64uration\x18\x04 \x01(\x05H\x02R\x08\x64uration\x88\x01\x01\x12&\n\x0cmax_duration\x18\x05 \x01(\x05H\x03R\x0bmaxDuration\x88\x01\x01\x42\x08\n\x06_priceB\t\n\x07_tax_idB\x0b\n\t_durationB\x0f\n\r_max_duration\"l\n\x13ServiceOverrideRule\x12U\n\x11pet_override_list\x18\x01 \x03(\x0b\x32).moego.models.offering.v1.PetOverrideRuleR\x0fpetOverrideList\"{\n\x0fPetOverrideRule\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x19\n\x05price\x18\x02 \x01(\x01H\x00R\x05price\x88\x01\x01\x12\x1f\n\x08\x64uration\x18\x03 \x01(\x05H\x01R\x08\x64uration\x88\x01\x01\x42\x08\n\x06_priceB\x0b\n\t_duration\"{\n\x10\x41utoRolloverRule\x12\x18\n\x07\x65nabled\x18\x01 \x01(\x08R\x07\x65nabled\x12!\n\x0c\x61\x66ter_minute\x18\x02 \x01(\x05R\x0b\x61\x66terMinute\x12*\n\x11target_service_id\x18\x03 \x01(\x03R\x0ftargetServiceId\"\xfb\x04\n\x13ServiceAvailability\x12&\n\x0fis_all_location\x18\x02 \x01(\x08R\risAllLocation\x12;\n\x1a\x61vailable_business_id_list\x18\x03 \x03(\x03R\x17\x61vailableBusinessIdList\x12!\n\x0c\x62reed_filter\x18\x04 \x01(\x08R\x0b\x62reedFilter\x12T\n\x10\x63ustomized_breed\x18\x05 \x03(\x0b\x32).moego.models.offering.v1.CustomizedBreedR\x0f\x63ustomizedBreed\x12&\n\x0fpet_size_filter\x18\x06 \x01(\x08R\rpetSizeFilter\x12\x30\n\x14\x63ustomized_pet_sizes\x18\x07 \x03(\x03R\x12\x63ustomizedPetSizes\x12\'\n\rweight_filter\x18\x08 \x01(\x08\x42\x02\x18\x01R\x0cweightFilter\x12%\n\x0cweight_range\x18\t \x03(\x01\x42\x02\x18\x01R\x0bweightRange\x12\x1f\n\x0b\x63oat_filter\x18\n \x01(\x08R\ncoatFilter\x12\'\n\x0f\x63ustomized_coat\x18\x0b \x03(\x03R\x0e\x63ustomizedCoat\x12:\n\x19require_dedicated_lodging\x18\x0c \x01(\x08R\x17requireDedicatedLodging\x12%\n\x0elodging_filter\x18\r \x01(\x08R\rlodgingFilter\x12/\n\x13\x63ustomized_lodgings\x18\x0e \x03(\x03R\x12\x63ustomizedLodgings\"p\n\x0f\x43ustomizedBreed\x12\x1e\n\x0bpet_type_id\x18\x01 \x01(\x03R\tpetTypeId\x12\x16\n\x06\x62reeds\x18\x02 \x03(\tR\x06\x62reeds\x12\x1a\n\x06is_all\x18\x03 \x01(\x08H\x00R\x05isAll\x88\x01\x01\x42\t\n\x07_is_all\"\x82\x02\n\rServiceFilter\x12U\n\x11service_item_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12@\n\x1a\x61vailable_for_all_services\x18\x02 \x01(\x08H\x00R\x17\x61vailableForAllServices\x88\x01\x01\x12\x39\n\x19\x61vailable_service_id_list\x18\x03 \x03(\x03R\x16\x61vailableServiceIdListB\x1d\n\x1b_available_for_all_services\"\xa1\x01\n\x1d\x43ustomizedServiceCategoryView\x12\x1f\n\x0b\x63\x61tegory_id\x18\x01 \x01(\x03R\ncategoryId\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12K\n\x08services\x18\x03 \x03(\x0b\x32/.moego.models.offering.v1.CustomizedServiceViewR\x08services\"\xcc\n\n\x15\x43ustomizedServiceView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n\x05price\x18\x03 \x01(\x01R\x05price\x12I\n\nprice_unit\x18\x04 \x01(\x0e\x32*.moego.models.offering.v1.ServicePriceUnitR\tpriceUnit\x12\x1f\n\x08\x64uration\x18\x05 \x01(\x05H\x00R\x08\x64uration\x88\x01\x01\x12\x39\n\x04type\x18\x06 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeR\x04type\x12\x1f\n\x0b\x63\x61tegory_id\x18\x07 \x01(\x03R\ncategoryId\x12]\n\x13price_override_type\x18\x08 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeR\x11priceOverrideType\x12\x63\n\x16\x64uration_override_type\x18\t \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeR\x14\x64urationOverrideType\x12\x15\n\x06tax_id\x18\n \x01(\x03R\x05taxId\x12U\n\x11service_item_type\x18\x0b \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12 \n\x0b\x64\x65scription\x18\x0c \x01(\tR\x0b\x64\x65scription\x12\x36\n\x17require_dedicated_staff\x18\r \x01(\x08R\x15requireDedicatedStaff\x12:\n\x19require_dedicated_lodging\x18\x0e \x01(\x08R\x17requireDedicatedLodging\x12\x1a\n\x08inactive\x18\x0f \x01(\x08R\x08inactive\x12!\n\x0cmax_duration\x18\x10 \x01(\x05R\x0bmaxDuration\x12\x16\n\x06images\x18\x11 \x03(\tR\x06images\x12[\n\x13staff_override_list\x18\x12 \x03(\x0b\x32+.moego.models.offering.v1.StaffOverrideRuleR\x11staffOverrideList\x12j\n\x10\x61vailable_staffs\x18\x13 \x01(\x0b\x32?.moego.models.offering.v1.CustomizedServiceView.AvailableStaffsR\x0f\x61vailableStaffs\x12%\n\x0elodging_filter\x18\x14 \x01(\x08R\rlodgingFilter\x12/\n\x13\x63ustomized_lodgings\x18\x15 \x03(\x03R\x12\x63ustomizedLodgings\x12,\n\x12\x62undle_service_ids\x18\x16 \x03(\x03R\x10\x62undleServiceIds\x12g\n\x17\x61\x64\x64itional_service_rule\x18\x17 \x01(\x0b\x32/.moego.models.offering.v1.AdditionalServiceRuleR\x15\x61\x64\x64itionalServiceRule\x1aM\n\x0f\x41vailableStaffs\x12(\n\x10is_all_available\x18\x01 \x01(\x08R\x0eisAllAvailable\x12\x10\n\x03ids\x18\x02 \x03(\x03R\x03idsB\x0b\n\t_duration\"h\n\x19\x43ustomizedServiceViewList\x12K\n\x08services\x18\x01 \x03(\x0b\x32/.moego.models.offering.v1.CustomizedServiceViewR\x08services\"\xad\t\n\x10ServiceBriefView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x63\x61tegory_id\x18\x02 \x01(\x03R\ncategoryId\x12\x39\n\x04type\x18\x03 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeR\x04type\x12U\n\x11service_item_type\x18\x04 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12\x12\n\x04name\x18\x05 \x01(\tR\x04name\x12 \n\x0b\x64\x65scription\x18\x06 \x01(\tR\x0b\x64\x65scription\x12\x14\n\x05price\x18\x07 \x01(\x01R\x05price\x12I\n\nprice_unit\x18\x08 \x01(\x0e\x32*.moego.models.offering.v1.ServicePriceUnitR\tpriceUnit\x12\x1a\n\x08\x64uration\x18\t \x01(\x05R\x08\x64uration\x12;\n\x0b\x63reate_time\x18\n \x01(\x0b\x32\x1a.google.protobuf.TimestampR\ncreateTime\x12;\n\x0bupdate_time\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nupdateTime\x12\x1a\n\x08inactive\x18\x0c \x01(\x08R\x08inactive\x12\x1d\n\nis_deleted\x18\r \x01(\x08R\tisDeleted\x12\x1d\n\ncolor_code\x18\x0e \x01(\tR\tcolorCode\x12!\n\x0cmax_duration\x18\x0f \x01(\x05R\x0bmaxDuration\x12\x36\n\x17require_dedicated_staff\x18\x10 \x01(\x08R\x15requireDedicatedStaff\x12%\n\x0elodging_filter\x18\x11 \x01(\x08R\rlodgingFilter\x12/\n\x13\x63ustomized_lodgings\x18\x12 \x03(\x03R\x12\x63ustomizedLodgings\x12!\n\x0cnum_sessions\x18\x13 \x01(\x05R\x0bnumSessions\x12\x30\n\x14\x64uration_session_min\x18\x14 \x01(\x05R\x12\x64urationSessionMin\x12\x1a\n\x08\x63\x61pacity\x18\x15 \x01(\x05R\x08\x63\x61pacity\x12\x41\n\x1dis_require_prerequisite_class\x18\x16 \x01(\x08R\x1aisRequirePrerequisiteClass\x12\x34\n\x16prerequisite_class_ids\x18\x17 \x03(\x03R\x14prerequisiteClassIds\x12\x34\n\x16is_evaluation_required\x18\x18 \x01(\x08R\x14isEvaluationRequired\x12@\n\x1dis_evaluation_required_for_ob\x18\x19 \x01(\x08R\x19isEvaluationRequiredForOb\x12#\n\revaluation_id\x18\x1a \x01(\x03R\x0c\x65valuationId\x12\x15\n\x06tax_id\x18\x1b \x01(\x03R\x05taxId\"\x84\x02\n\x11ServiceClientView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x04 \x01(\tR\x04name\x12\x39\n\x04type\x18\x06 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeR\x04type\x12\x1a\n\x08inactive\x18\n \x01(\x08R\x08inactive\x12U\n\x11service_item_type\x18\x0b \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12\x1d\n\nis_deleted\x18\r \x01(\x08R\tisDeleted\"\x81\x01\n\x11StaffOverrideRule\x12\x19\n\x08staff_id\x18\x01 \x01(\x03R\x07staffId\x12\x19\n\x05price\x18\x02 \x01(\x01H\x00R\x05price\x88\x01\x01\x12\x1f\n\x08\x64uration\x18\x03 \x01(\x05H\x01R\x08\x64uration\x88\x01\x01\x42\x08\n\x06_priceB\x0b\n\t_duration\"\xd5\x01\n\x19LocationStaffOverrideRule\x12[\n\x11location_override\x18\x01 \x01(\x0b\x32..moego.models.offering.v1.LocationOverrideRuleR\x10locationOverride\x12[\n\x13staff_override_list\x18\x02 \x03(\x0b\x32+.moego.models.offering.v1.StaffOverrideRuleR\x11staffOverrideList\"\x9c\x01\n\x15ServiceBundleSaleView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n\x05price\x18\x03 \x01(\x01R\x05price\x12I\n\nprice_unit\x18\x04 \x01(\x0e\x32*.moego.models.offering.v1.ServicePriceUnitR\tpriceUnit\"\xcb\x02\n\x15\x41\x64\x64itionalServiceRule\x12\x16\n\x06\x65nable\x18\x01 \x01(\x08R\x06\x65nable\x12&\n\x0fmin_stay_length\x18\x02 \x01(\x05R\rminStayLength\x12Z\n\x0b\x61pply_rules\x18\x03 \x03(\x0b\x32\x39.moego.models.offering.v1.AdditionalServiceRule.ApplyRuleR\napplyRules\x1a\x95\x01\n\tApplyRule\x12\x1d\n\nservice_id\x18\x01 \x01(\x03R\tserviceId\x12?\n\tdate_type\x18\x02 \x01(\x0e\x32\".moego.models.offering.v1.DateTypeR\x08\x64\x61teType\x12(\n\x10quantity_per_day\x18\x03 \x01(\x05R\x0equantityPerDayB~\n com.moego.idl.models.offering.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.offering.v1.service_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.models.offering.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb'
  _globals['_SERVICEMODEL'].fields_by_name['location_override_list']._loaded_options = None
  _globals['_SERVICEMODEL'].fields_by_name['location_override_list']._serialized_options = b'\030\001'
  _globals['_SERVICEMODEL'].fields_by_name['weight_filter']._loaded_options = None
  _globals['_SERVICEMODEL'].fields_by_name['weight_filter']._serialized_options = b'\030\001'
  _globals['_SERVICEMODEL'].fields_by_name['weight_range']._loaded_options = None
  _globals['_SERVICEMODEL'].fields_by_name['weight_range']._serialized_options = b'\030\001'
  _globals['_SERVICEAVAILABILITY'].fields_by_name['weight_filter']._loaded_options = None
  _globals['_SERVICEAVAILABILITY'].fields_by_name['weight_filter']._serialized_options = b'\030\001'
  _globals['_SERVICEAVAILABILITY'].fields_by_name['weight_range']._loaded_options = None
  _globals['_SERVICEAVAILABILITY'].fields_by_name['weight_range']._serialized_options = b'\030\001'
  _globals['_SERVICECATEGORYMODEL']._serialized_start=154
  _globals['_SERVICECATEGORYMODEL']._serialized_end=297
  _globals['_SERVICEMODEL']._serialized_start=300
  _globals['_SERVICEMODEL']._serialized_end=3288
  _globals['_SERVICEMODEL_PETCODEFILTER']._serialized_start=3042
  _globals['_SERVICEMODEL_PETCODEFILTER']._serialized_end=3166
  _globals['_SERVICEMODEL_SOURCE']._serialized_start=3168
  _globals['_SERVICEMODEL_SOURCE']._serialized_end=3240
  _globals['_LOCATIONOVERRIDERULE']._serialized_start=3291
  _globals['_LOCATIONOVERRIDERULE']._serialized_end=3525
  _globals['_SERVICEOVERRIDERULE']._serialized_start=3527
  _globals['_SERVICEOVERRIDERULE']._serialized_end=3635
  _globals['_PETOVERRIDERULE']._serialized_start=3637
  _globals['_PETOVERRIDERULE']._serialized_end=3760
  _globals['_AUTOROLLOVERRULE']._serialized_start=3762
  _globals['_AUTOROLLOVERRULE']._serialized_end=3885
  _globals['_SERVICEAVAILABILITY']._serialized_start=3888
  _globals['_SERVICEAVAILABILITY']._serialized_end=4523
  _globals['_CUSTOMIZEDBREED']._serialized_start=4525
  _globals['_CUSTOMIZEDBREED']._serialized_end=4637
  _globals['_SERVICEFILTER']._serialized_start=4640
  _globals['_SERVICEFILTER']._serialized_end=4898
  _globals['_CUSTOMIZEDSERVICECATEGORYVIEW']._serialized_start=4901
  _globals['_CUSTOMIZEDSERVICECATEGORYVIEW']._serialized_end=5062
  _globals['_CUSTOMIZEDSERVICEVIEW']._serialized_start=5065
  _globals['_CUSTOMIZEDSERVICEVIEW']._serialized_end=6421
  _globals['_CUSTOMIZEDSERVICEVIEW_AVAILABLESTAFFS']._serialized_start=6331
  _globals['_CUSTOMIZEDSERVICEVIEW_AVAILABLESTAFFS']._serialized_end=6408
  _globals['_CUSTOMIZEDSERVICEVIEWLIST']._serialized_start=6423
  _globals['_CUSTOMIZEDSERVICEVIEWLIST']._serialized_end=6527
  _globals['_SERVICEBRIEFVIEW']._serialized_start=6530
  _globals['_SERVICEBRIEFVIEW']._serialized_end=7727
  _globals['_SERVICECLIENTVIEW']._serialized_start=7730
  _globals['_SERVICECLIENTVIEW']._serialized_end=7990
  _globals['_STAFFOVERRIDERULE']._serialized_start=7993
  _globals['_STAFFOVERRIDERULE']._serialized_end=8122
  _globals['_LOCATIONSTAFFOVERRIDERULE']._serialized_start=8125
  _globals['_LOCATIONSTAFFOVERRIDERULE']._serialized_end=8338
  _globals['_SERVICEBUNDLESALEVIEW']._serialized_start=8341
  _globals['_SERVICEBUNDLESALEVIEW']._serialized_end=8497
  _globals['_ADDITIONALSERVICERULE']._serialized_start=8500
  _globals['_ADDITIONALSERVICERULE']._serialized_end=8831
  _globals['_ADDITIONALSERVICERULE_APPLYRULE']._serialized_start=8682
  _globals['_ADDITIONALSERVICERULE_APPLYRULE']._serialized_end=8831
# @@protoc_insertion_point(module_scope)
