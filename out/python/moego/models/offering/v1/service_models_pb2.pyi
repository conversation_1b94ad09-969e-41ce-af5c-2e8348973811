from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ServiceCategoryModel(_message.Message):
    __slots__ = ("category_id", "name", "services")
    CATEGORY_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    category_id: int
    name: str
    services: _containers.RepeatedCompositeFieldContainer[ServiceModel]
    def __init__(self, category_id: _Optional[int] = ..., name: _Optional[str] = ..., services: _Optional[_Iterable[_Union[ServiceModel, _Mapping]]] = ...) -> None: ...

class ServiceModel(_message.Message):
    __slots__ = ("service_id", "name", "description", "inactive", "images", "category_id", "color_code", "location_override_list", "require_dedicated_staff", "service_item_type", "price", "price_unit", "tax_id", "duration", "add_to_commission_base", "can_tip", "is_all_location", "available_business_id_list", "breed_filter", "customized_breed", "pet_size_filter", "customized_pet_sizes", "weight_filter", "weight_range", "coat_filter", "customized_coat", "require_dedicated_lodging", "lodging_filter", "customized_lodgings", "service_filter", "service_filter_list", "type", "max_duration", "auto_rollover_rule", "create_time", "update_time", "is_deleted", "company_id", "available_staff_id_list", "location_staff_override_list", "available_for_all_staff", "pet_code_filter", "bundle_service_ids", "source", "num_sessions", "duration_session_min", "capacity", "is_require_prerequisite_class", "prerequisite_class_ids", "is_evaluation_required", "is_evaluation_required_for_ob", "evaluation_id", "additional_service_rule")
    class Source(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        SOURCE_UNSPECIFIED: _ClassVar[ServiceModel.Source]
        MOEGO_PLATFORM: _ClassVar[ServiceModel.Source]
        ENTERPRISE_HUB: _ClassVar[ServiceModel.Source]
    SOURCE_UNSPECIFIED: ServiceModel.Source
    MOEGO_PLATFORM: ServiceModel.Source
    ENTERPRISE_HUB: ServiceModel.Source
    class PetCodeFilter(_message.Message):
        __slots__ = ("is_white_list", "is_all_pet_code", "pet_code_ids")
        IS_WHITE_LIST_FIELD_NUMBER: _ClassVar[int]
        IS_ALL_PET_CODE_FIELD_NUMBER: _ClassVar[int]
        PET_CODE_IDS_FIELD_NUMBER: _ClassVar[int]
        is_white_list: bool
        is_all_pet_code: bool
        pet_code_ids: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, is_white_list: bool = ..., is_all_pet_code: bool = ..., pet_code_ids: _Optional[_Iterable[int]] = ...) -> None: ...
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    IMAGES_FIELD_NUMBER: _ClassVar[int]
    CATEGORY_ID_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    LOCATION_OVERRIDE_LIST_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_STAFF_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    ADD_TO_COMMISSION_BASE_FIELD_NUMBER: _ClassVar[int]
    CAN_TIP_FIELD_NUMBER: _ClassVar[int]
    IS_ALL_LOCATION_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_BUSINESS_ID_LIST_FIELD_NUMBER: _ClassVar[int]
    BREED_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_BREED_FIELD_NUMBER: _ClassVar[int]
    PET_SIZE_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_PET_SIZES_FIELD_NUMBER: _ClassVar[int]
    WEIGHT_FILTER_FIELD_NUMBER: _ClassVar[int]
    WEIGHT_RANGE_FIELD_NUMBER: _ClassVar[int]
    COAT_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_COAT_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_LODGING_FIELD_NUMBER: _ClassVar[int]
    LODGING_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_FILTER_FIELD_NUMBER: _ClassVar[int]
    SERVICE_FILTER_LIST_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
    AUTO_ROLLOVER_RULE_FIELD_NUMBER: _ClassVar[int]
    CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    IS_DELETED_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_STAFF_ID_LIST_FIELD_NUMBER: _ClassVar[int]
    LOCATION_STAFF_OVERRIDE_LIST_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_FOR_ALL_STAFF_FIELD_NUMBER: _ClassVar[int]
    PET_CODE_FILTER_FIELD_NUMBER: _ClassVar[int]
    BUNDLE_SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    NUM_SESSIONS_FIELD_NUMBER: _ClassVar[int]
    DURATION_SESSION_MIN_FIELD_NUMBER: _ClassVar[int]
    CAPACITY_FIELD_NUMBER: _ClassVar[int]
    IS_REQUIRE_PREREQUISITE_CLASS_FIELD_NUMBER: _ClassVar[int]
    PREREQUISITE_CLASS_IDS_FIELD_NUMBER: _ClassVar[int]
    IS_EVALUATION_REQUIRED_FIELD_NUMBER: _ClassVar[int]
    IS_EVALUATION_REQUIRED_FOR_OB_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_ID_FIELD_NUMBER: _ClassVar[int]
    ADDITIONAL_SERVICE_RULE_FIELD_NUMBER: _ClassVar[int]
    service_id: int
    name: str
    description: str
    inactive: bool
    images: _containers.RepeatedScalarFieldContainer[str]
    category_id: int
    color_code: str
    location_override_list: _containers.RepeatedCompositeFieldContainer[LocationOverrideRule]
    require_dedicated_staff: bool
    service_item_type: _service_enum_pb2.ServiceItemType
    price: float
    price_unit: _service_enum_pb2.ServicePriceUnit
    tax_id: int
    duration: int
    add_to_commission_base: bool
    can_tip: bool
    is_all_location: bool
    available_business_id_list: _containers.RepeatedScalarFieldContainer[int]
    breed_filter: bool
    customized_breed: _containers.RepeatedCompositeFieldContainer[CustomizedBreed]
    pet_size_filter: bool
    customized_pet_sizes: _containers.RepeatedScalarFieldContainer[int]
    weight_filter: bool
    weight_range: _containers.RepeatedScalarFieldContainer[float]
    coat_filter: bool
    customized_coat: _containers.RepeatedScalarFieldContainer[int]
    require_dedicated_lodging: bool
    lodging_filter: bool
    customized_lodgings: _containers.RepeatedScalarFieldContainer[int]
    service_filter: bool
    service_filter_list: _containers.RepeatedCompositeFieldContainer[ServiceFilter]
    type: _service_enum_pb2.ServiceType
    max_duration: int
    auto_rollover_rule: AutoRolloverRule
    create_time: _timestamp_pb2.Timestamp
    update_time: _timestamp_pb2.Timestamp
    is_deleted: bool
    company_id: int
    available_staff_id_list: _containers.RepeatedScalarFieldContainer[int]
    location_staff_override_list: _containers.RepeatedCompositeFieldContainer[LocationStaffOverrideRule]
    available_for_all_staff: bool
    pet_code_filter: ServiceModel.PetCodeFilter
    bundle_service_ids: _containers.RepeatedScalarFieldContainer[int]
    source: ServiceModel.Source
    num_sessions: int
    duration_session_min: int
    capacity: int
    is_require_prerequisite_class: bool
    prerequisite_class_ids: _containers.RepeatedScalarFieldContainer[int]
    is_evaluation_required: bool
    is_evaluation_required_for_ob: bool
    evaluation_id: int
    additional_service_rule: AdditionalServiceRule
    def __init__(self, service_id: _Optional[int] = ..., name: _Optional[str] = ..., description: _Optional[str] = ..., inactive: bool = ..., images: _Optional[_Iterable[str]] = ..., category_id: _Optional[int] = ..., color_code: _Optional[str] = ..., location_override_list: _Optional[_Iterable[_Union[LocationOverrideRule, _Mapping]]] = ..., require_dedicated_staff: bool = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., price: _Optional[float] = ..., price_unit: _Optional[_Union[_service_enum_pb2.ServicePriceUnit, str]] = ..., tax_id: _Optional[int] = ..., duration: _Optional[int] = ..., add_to_commission_base: bool = ..., can_tip: bool = ..., is_all_location: bool = ..., available_business_id_list: _Optional[_Iterable[int]] = ..., breed_filter: bool = ..., customized_breed: _Optional[_Iterable[_Union[CustomizedBreed, _Mapping]]] = ..., pet_size_filter: bool = ..., customized_pet_sizes: _Optional[_Iterable[int]] = ..., weight_filter: bool = ..., weight_range: _Optional[_Iterable[float]] = ..., coat_filter: bool = ..., customized_coat: _Optional[_Iterable[int]] = ..., require_dedicated_lodging: bool = ..., lodging_filter: bool = ..., customized_lodgings: _Optional[_Iterable[int]] = ..., service_filter: bool = ..., service_filter_list: _Optional[_Iterable[_Union[ServiceFilter, _Mapping]]] = ..., type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., max_duration: _Optional[int] = ..., auto_rollover_rule: _Optional[_Union[AutoRolloverRule, _Mapping]] = ..., create_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., update_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., is_deleted: bool = ..., company_id: _Optional[int] = ..., available_staff_id_list: _Optional[_Iterable[int]] = ..., location_staff_override_list: _Optional[_Iterable[_Union[LocationStaffOverrideRule, _Mapping]]] = ..., available_for_all_staff: bool = ..., pet_code_filter: _Optional[_Union[ServiceModel.PetCodeFilter, _Mapping]] = ..., bundle_service_ids: _Optional[_Iterable[int]] = ..., source: _Optional[_Union[ServiceModel.Source, str]] = ..., num_sessions: _Optional[int] = ..., duration_session_min: _Optional[int] = ..., capacity: _Optional[int] = ..., is_require_prerequisite_class: bool = ..., prerequisite_class_ids: _Optional[_Iterable[int]] = ..., is_evaluation_required: bool = ..., is_evaluation_required_for_ob: bool = ..., evaluation_id: _Optional[int] = ..., additional_service_rule: _Optional[_Union[AdditionalServiceRule, _Mapping]] = ...) -> None: ...

class LocationOverrideRule(_message.Message):
    __slots__ = ("business_id", "price", "tax_id", "duration", "max_duration")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    price: float
    tax_id: int
    duration: int
    max_duration: int
    def __init__(self, business_id: _Optional[int] = ..., price: _Optional[float] = ..., tax_id: _Optional[int] = ..., duration: _Optional[int] = ..., max_duration: _Optional[int] = ...) -> None: ...

class ServiceOverrideRule(_message.Message):
    __slots__ = ("pet_override_list",)
    PET_OVERRIDE_LIST_FIELD_NUMBER: _ClassVar[int]
    pet_override_list: _containers.RepeatedCompositeFieldContainer[PetOverrideRule]
    def __init__(self, pet_override_list: _Optional[_Iterable[_Union[PetOverrideRule, _Mapping]]] = ...) -> None: ...

class PetOverrideRule(_message.Message):
    __slots__ = ("pet_id", "price", "duration")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    price: float
    duration: int
    def __init__(self, pet_id: _Optional[int] = ..., price: _Optional[float] = ..., duration: _Optional[int] = ...) -> None: ...

class AutoRolloverRule(_message.Message):
    __slots__ = ("enabled", "after_minute", "target_service_id")
    ENABLED_FIELD_NUMBER: _ClassVar[int]
    AFTER_MINUTE_FIELD_NUMBER: _ClassVar[int]
    TARGET_SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    enabled: bool
    after_minute: int
    target_service_id: int
    def __init__(self, enabled: bool = ..., after_minute: _Optional[int] = ..., target_service_id: _Optional[int] = ...) -> None: ...

class ServiceAvailability(_message.Message):
    __slots__ = ("is_all_location", "available_business_id_list", "breed_filter", "customized_breed", "pet_size_filter", "customized_pet_sizes", "weight_filter", "weight_range", "coat_filter", "customized_coat", "require_dedicated_lodging", "lodging_filter", "customized_lodgings")
    IS_ALL_LOCATION_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_BUSINESS_ID_LIST_FIELD_NUMBER: _ClassVar[int]
    BREED_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_BREED_FIELD_NUMBER: _ClassVar[int]
    PET_SIZE_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_PET_SIZES_FIELD_NUMBER: _ClassVar[int]
    WEIGHT_FILTER_FIELD_NUMBER: _ClassVar[int]
    WEIGHT_RANGE_FIELD_NUMBER: _ClassVar[int]
    COAT_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_COAT_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_LODGING_FIELD_NUMBER: _ClassVar[int]
    LODGING_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    is_all_location: bool
    available_business_id_list: _containers.RepeatedScalarFieldContainer[int]
    breed_filter: bool
    customized_breed: _containers.RepeatedCompositeFieldContainer[CustomizedBreed]
    pet_size_filter: bool
    customized_pet_sizes: _containers.RepeatedScalarFieldContainer[int]
    weight_filter: bool
    weight_range: _containers.RepeatedScalarFieldContainer[float]
    coat_filter: bool
    customized_coat: _containers.RepeatedScalarFieldContainer[int]
    require_dedicated_lodging: bool
    lodging_filter: bool
    customized_lodgings: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, is_all_location: bool = ..., available_business_id_list: _Optional[_Iterable[int]] = ..., breed_filter: bool = ..., customized_breed: _Optional[_Iterable[_Union[CustomizedBreed, _Mapping]]] = ..., pet_size_filter: bool = ..., customized_pet_sizes: _Optional[_Iterable[int]] = ..., weight_filter: bool = ..., weight_range: _Optional[_Iterable[float]] = ..., coat_filter: bool = ..., customized_coat: _Optional[_Iterable[int]] = ..., require_dedicated_lodging: bool = ..., lodging_filter: bool = ..., customized_lodgings: _Optional[_Iterable[int]] = ...) -> None: ...

class CustomizedBreed(_message.Message):
    __slots__ = ("pet_type_id", "breeds", "is_all")
    PET_TYPE_ID_FIELD_NUMBER: _ClassVar[int]
    BREEDS_FIELD_NUMBER: _ClassVar[int]
    IS_ALL_FIELD_NUMBER: _ClassVar[int]
    pet_type_id: int
    breeds: _containers.RepeatedScalarFieldContainer[str]
    is_all: bool
    def __init__(self, pet_type_id: _Optional[int] = ..., breeds: _Optional[_Iterable[str]] = ..., is_all: bool = ...) -> None: ...

class ServiceFilter(_message.Message):
    __slots__ = ("service_item_type", "available_for_all_services", "available_service_id_list")
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_FOR_ALL_SERVICES_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_SERVICE_ID_LIST_FIELD_NUMBER: _ClassVar[int]
    service_item_type: _service_enum_pb2.ServiceItemType
    available_for_all_services: bool
    available_service_id_list: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., available_for_all_services: bool = ..., available_service_id_list: _Optional[_Iterable[int]] = ...) -> None: ...

class CustomizedServiceCategoryView(_message.Message):
    __slots__ = ("category_id", "name", "services")
    CATEGORY_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    category_id: int
    name: str
    services: _containers.RepeatedCompositeFieldContainer[CustomizedServiceView]
    def __init__(self, category_id: _Optional[int] = ..., name: _Optional[str] = ..., services: _Optional[_Iterable[_Union[CustomizedServiceView, _Mapping]]] = ...) -> None: ...

class CustomizedServiceView(_message.Message):
    __slots__ = ("id", "name", "price", "price_unit", "duration", "type", "category_id", "price_override_type", "duration_override_type", "tax_id", "service_item_type", "description", "require_dedicated_staff", "require_dedicated_lodging", "inactive", "max_duration", "images", "staff_override_list", "available_staffs", "lodging_filter", "customized_lodgings", "bundle_service_ids", "additional_service_rule")
    class AvailableStaffs(_message.Message):
        __slots__ = ("is_all_available", "ids")
        IS_ALL_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
        IDS_FIELD_NUMBER: _ClassVar[int]
        is_all_available: bool
        ids: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, is_all_available: bool = ..., ids: _Optional[_Iterable[int]] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    CATEGORY_ID_FIELD_NUMBER: _ClassVar[int]
    PRICE_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    DURATION_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_STAFF_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_LODGING_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
    IMAGES_FIELD_NUMBER: _ClassVar[int]
    STAFF_OVERRIDE_LIST_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_STAFFS_FIELD_NUMBER: _ClassVar[int]
    LODGING_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    BUNDLE_SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
    ADDITIONAL_SERVICE_RULE_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    price: float
    price_unit: _service_enum_pb2.ServicePriceUnit
    duration: int
    type: _service_enum_pb2.ServiceType
    category_id: int
    price_override_type: _service_enum_pb2.ServiceOverrideType
    duration_override_type: _service_enum_pb2.ServiceOverrideType
    tax_id: int
    service_item_type: _service_enum_pb2.ServiceItemType
    description: str
    require_dedicated_staff: bool
    require_dedicated_lodging: bool
    inactive: bool
    max_duration: int
    images: _containers.RepeatedScalarFieldContainer[str]
    staff_override_list: _containers.RepeatedCompositeFieldContainer[StaffOverrideRule]
    available_staffs: CustomizedServiceView.AvailableStaffs
    lodging_filter: bool
    customized_lodgings: _containers.RepeatedScalarFieldContainer[int]
    bundle_service_ids: _containers.RepeatedScalarFieldContainer[int]
    additional_service_rule: AdditionalServiceRule
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., price: _Optional[float] = ..., price_unit: _Optional[_Union[_service_enum_pb2.ServicePriceUnit, str]] = ..., duration: _Optional[int] = ..., type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., category_id: _Optional[int] = ..., price_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ..., duration_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ..., tax_id: _Optional[int] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., description: _Optional[str] = ..., require_dedicated_staff: bool = ..., require_dedicated_lodging: bool = ..., inactive: bool = ..., max_duration: _Optional[int] = ..., images: _Optional[_Iterable[str]] = ..., staff_override_list: _Optional[_Iterable[_Union[StaffOverrideRule, _Mapping]]] = ..., available_staffs: _Optional[_Union[CustomizedServiceView.AvailableStaffs, _Mapping]] = ..., lodging_filter: bool = ..., customized_lodgings: _Optional[_Iterable[int]] = ..., bundle_service_ids: _Optional[_Iterable[int]] = ..., additional_service_rule: _Optional[_Union[AdditionalServiceRule, _Mapping]] = ...) -> None: ...

class CustomizedServiceViewList(_message.Message):
    __slots__ = ("services",)
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    services: _containers.RepeatedCompositeFieldContainer[CustomizedServiceView]
    def __init__(self, services: _Optional[_Iterable[_Union[CustomizedServiceView, _Mapping]]] = ...) -> None: ...

class ServiceBriefView(_message.Message):
    __slots__ = ("id", "category_id", "type", "service_item_type", "name", "description", "price", "price_unit", "duration", "create_time", "update_time", "inactive", "is_deleted", "color_code", "max_duration", "require_dedicated_staff", "lodging_filter", "customized_lodgings", "num_sessions", "duration_session_min", "capacity", "is_require_prerequisite_class", "prerequisite_class_ids", "is_evaluation_required", "is_evaluation_required_for_ob", "evaluation_id", "tax_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    CATEGORY_ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    IS_DELETED_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_STAFF_FIELD_NUMBER: _ClassVar[int]
    LODGING_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    NUM_SESSIONS_FIELD_NUMBER: _ClassVar[int]
    DURATION_SESSION_MIN_FIELD_NUMBER: _ClassVar[int]
    CAPACITY_FIELD_NUMBER: _ClassVar[int]
    IS_REQUIRE_PREREQUISITE_CLASS_FIELD_NUMBER: _ClassVar[int]
    PREREQUISITE_CLASS_IDS_FIELD_NUMBER: _ClassVar[int]
    IS_EVALUATION_REQUIRED_FIELD_NUMBER: _ClassVar[int]
    IS_EVALUATION_REQUIRED_FOR_OB_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_ID_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    category_id: int
    type: _service_enum_pb2.ServiceType
    service_item_type: _service_enum_pb2.ServiceItemType
    name: str
    description: str
    price: float
    price_unit: _service_enum_pb2.ServicePriceUnit
    duration: int
    create_time: _timestamp_pb2.Timestamp
    update_time: _timestamp_pb2.Timestamp
    inactive: bool
    is_deleted: bool
    color_code: str
    max_duration: int
    require_dedicated_staff: bool
    lodging_filter: bool
    customized_lodgings: _containers.RepeatedScalarFieldContainer[int]
    num_sessions: int
    duration_session_min: int
    capacity: int
    is_require_prerequisite_class: bool
    prerequisite_class_ids: _containers.RepeatedScalarFieldContainer[int]
    is_evaluation_required: bool
    is_evaluation_required_for_ob: bool
    evaluation_id: int
    tax_id: int
    def __init__(self, id: _Optional[int] = ..., category_id: _Optional[int] = ..., type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., name: _Optional[str] = ..., description: _Optional[str] = ..., price: _Optional[float] = ..., price_unit: _Optional[_Union[_service_enum_pb2.ServicePriceUnit, str]] = ..., duration: _Optional[int] = ..., create_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., update_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., inactive: bool = ..., is_deleted: bool = ..., color_code: _Optional[str] = ..., max_duration: _Optional[int] = ..., require_dedicated_staff: bool = ..., lodging_filter: bool = ..., customized_lodgings: _Optional[_Iterable[int]] = ..., num_sessions: _Optional[int] = ..., duration_session_min: _Optional[int] = ..., capacity: _Optional[int] = ..., is_require_prerequisite_class: bool = ..., prerequisite_class_ids: _Optional[_Iterable[int]] = ..., is_evaluation_required: bool = ..., is_evaluation_required_for_ob: bool = ..., evaluation_id: _Optional[int] = ..., tax_id: _Optional[int] = ...) -> None: ...

class ServiceClientView(_message.Message):
    __slots__ = ("id", "name", "type", "inactive", "service_item_type", "is_deleted")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    IS_DELETED_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    type: _service_enum_pb2.ServiceType
    inactive: bool
    service_item_type: _service_enum_pb2.ServiceItemType
    is_deleted: bool
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., inactive: bool = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., is_deleted: bool = ...) -> None: ...

class StaffOverrideRule(_message.Message):
    __slots__ = ("staff_id", "price", "duration")
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    staff_id: int
    price: float
    duration: int
    def __init__(self, staff_id: _Optional[int] = ..., price: _Optional[float] = ..., duration: _Optional[int] = ...) -> None: ...

class LocationStaffOverrideRule(_message.Message):
    __slots__ = ("location_override", "staff_override_list")
    LOCATION_OVERRIDE_FIELD_NUMBER: _ClassVar[int]
    STAFF_OVERRIDE_LIST_FIELD_NUMBER: _ClassVar[int]
    location_override: LocationOverrideRule
    staff_override_list: _containers.RepeatedCompositeFieldContainer[StaffOverrideRule]
    def __init__(self, location_override: _Optional[_Union[LocationOverrideRule, _Mapping]] = ..., staff_override_list: _Optional[_Iterable[_Union[StaffOverrideRule, _Mapping]]] = ...) -> None: ...

class ServiceBundleSaleView(_message.Message):
    __slots__ = ("id", "name", "price", "price_unit")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    price: float
    price_unit: _service_enum_pb2.ServicePriceUnit
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., price: _Optional[float] = ..., price_unit: _Optional[_Union[_service_enum_pb2.ServicePriceUnit, str]] = ...) -> None: ...

class AdditionalServiceRule(_message.Message):
    __slots__ = ("enable", "min_stay_length", "apply_rules")
    class ApplyRule(_message.Message):
        __slots__ = ("service_id", "date_type", "quantity_per_day")
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
        QUANTITY_PER_DAY_FIELD_NUMBER: _ClassVar[int]
        service_id: int
        date_type: _service_enum_pb2.DateType
        quantity_per_day: int
        def __init__(self, service_id: _Optional[int] = ..., date_type: _Optional[_Union[_service_enum_pb2.DateType, str]] = ..., quantity_per_day: _Optional[int] = ...) -> None: ...
    ENABLE_FIELD_NUMBER: _ClassVar[int]
    MIN_STAY_LENGTH_FIELD_NUMBER: _ClassVar[int]
    APPLY_RULES_FIELD_NUMBER: _ClassVar[int]
    enable: bool
    min_stay_length: int
    apply_rules: _containers.RepeatedCompositeFieldContainer[AdditionalServiceRule.ApplyRule]
    def __init__(self, enable: bool = ..., min_stay_length: _Optional[int] = ..., apply_rules: _Optional[_Iterable[_Union[AdditionalServiceRule.ApplyRule, _Mapping]]] = ...) -> None: ...
