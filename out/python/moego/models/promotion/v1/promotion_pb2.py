# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/promotion/v1/promotion.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/promotion/v1/promotion.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import decimal_pb2 as google_dot_type_dot_decimal__pb2
from google.type import interval_pb2 as google_dot_type_dot_interval__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2
from moego.models.promotion.v1 import subject_pb2 as moego_dot_models_dot_promotion_dot_v1_dot_subject__pb2
from moego.models.reporting.v2 import common_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_common__model__pb2
from moego.utils.v1 import time_period_pb2 as moego_dot_utils_dot_v1_dot_time__period__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)moego/models/promotion/v1/promotion.proto\x12\x19moego.models.promotion.v1\x1a\x19google/type/decimal.proto\x1a\x1agoogle/type/interval.proto\x1a\x17google/type/money.proto\x1a\'moego/models/promotion/v1/subject.proto\x1a,moego/models/reporting/v2/common_model.proto\x1a moego/utils/v1/time_period.proto\"\xab\x03\n\x06Source\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12:\n\x04type\x18\x02 \x01(\x0e\x32&.moego.models.promotion.v1.Source.TypeR\x04type\x12H\n\x08\x64iscount\x18\x03 \x01(\x0b\x32*.moego.models.promotion.v1.DiscountSubjectH\x00R\x08\x64iscount\x12\x45\n\x07package\x18\x04 \x01(\x0b\x32).moego.models.promotion.v1.PackageSubjectH\x00R\x07package\x12N\n\nmembership\x18\x05 \x01(\x0b\x32,.moego.models.promotion.v1.MembershipSubjectH\x00R\nmembership\"i\n\x04Type\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08\x44ISCOUNT\x10\x01\x12\x0b\n\x07PACKAGE\x10\x02\x12\x17\n\x13MEMBERSHIP_DISCOUNT\x10\x03\x12\x17\n\x13MEMBERSHIP_QUANTITY\x10\x04\x42\t\n\x07subject\"\x82\x04\n\tPromotion\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12 \n\x0b\x64\x65scription\x18\x03 \x01(\tR\x0b\x64\x65scription\x12\x39\n\x06source\x18\x04 \x01(\x0b\x32!.moego.models.promotion.v1.SourceR\x06source\x12?\n\x08\x64iscount\x18\x05 \x01(\x0b\x32#.moego.models.promotion.v1.DiscountR\x08\x64iscount\x12K\n\x0crestrictions\x18\x06 \x01(\x0b\x32\'.moego.models.promotion.v1.RestrictionsR\x0crestrictions\x12L\n\x0fvalidity_period\x18\x07 \x01(\x0b\x32#.moego.models.promotion.v1.DurationR\x0evalidityPeriod\x12N\n\x10\x61vailable_period\x18\x08 \x01(\x0b\x32#.moego.models.promotion.v1.DurationR\x0f\x61vailablePeriod\x12H\n\x0bredemptions\x18\t \x01(\x0b\x32&.moego.models.promotion.v1.RedemptionsR\x0bredemptions\"\x92\x01\n\x0cRestrictions\x12<\n\x07targets\x18\x01 \x03(\x0b\x32\".moego.models.promotion.v1.TargetsR\x07targets\x12\x44\n\x0cuser_filters\x18\x02 \x03(\x0b\x32!.moego.models.promotion.v1.FilterR\x0buserFilters\"f\n\x07Targets\x12\x39\n\x04type\x18\x01 \x01(\x0e\x32%.moego.models.promotion.v1.TargetTypeR\x04type\x12\x0e\n\x02id\x18\x02 \x03(\x03R\x02id\x12\x10\n\x03\x61ll\x18\x03 \x01(\x08R\x03\x61ll\"\x95\x02\n\x17\x43ouponApplicationTarget\x12\x46\n\x0btarget_type\x18\x01 \x01(\x0e\x32%.moego.models.promotion.v1.TargetTypeR\ntargetType\x12\x1b\n\ttarget_id\x18\x02 \x01(\x03R\x08targetId\x12\x31\n\nunit_price\x18\x03 \x01(\x0b\x32\x12.google.type.MoneyR\tunitPrice\x12\'\n\x0ftarget_quantity\x18\x04 \x01(\x05R\x0etargetQuantity\x12\'\n\rorder_item_id\x18\x05 \x01(\x03H\x00R\x0borderItemId\x88\x01\x01\x42\x10\n\x0e_order_item_id\"\xc4\x01\n\x08\x44uration\x12\x45\n\x0frelative_period\x18\x01 \x01(\x0b\x32\x1a.moego.utils.v1.TimePeriodH\x00R\x0erelativePeriod\x12@\n\x0f\x61\x62solute_period\x18\x02 \x01(\x0b\x32\x15.google.type.IntervalH\x00R\x0e\x61\x62solutePeriod\x12#\n\x0cnever_expire\x18\x03 \x01(\x08H\x00R\x0bneverExpireB\n\n\x08\x64uration\"q\n\x06\x46ilter\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value\x12?\n\x08operator\x18\x03 \x01(\x0e\x32#.moego.models.reporting.v2.OperatorR\x08operator\"\xa3\x01\n\x08\x44iscount\x12\x37\n\x0c\x66ixed_amount\x18\x01 \x01(\x0b\x32\x12.google.type.MoneyH\x00R\x0b\x66ixedAmount\x12\x36\n\npercentage\x18\x02 \x01(\x0b\x32\x14.google.type.DecimalH\x00R\npercentage\x12\x1e\n\tdeduction\x18\x03 \x01(\x03H\x00R\tdeductionB\x06\n\x04mode\"\x9c\x01\n\x0bRedemptions\x12%\n\x0eredeemed_times\x18\x01 \x01(\x03R\rredeemedTimes\x12(\n\x10max_redeem_times\x18\x02 \x01(\x03R\x0emaxRedeemTimes\x12\x1c\n\tunlimited\x18\x05 \x01(\x08R\tunlimited\x12\x1e\n\nrefundable\x18\x06 \x01(\x08R\nrefundable*W\n\nTargetType\x12\x1b\n\x17TARGET_TYPE_UNSPECIFIED\x10\x00\x12\x0b\n\x07SERVICE\x10\x01\x12\x0b\n\x07PRODUCT\x10\x02\x12\x12\n\x0eSERVICE_CHARGE\x10\x03\x42\x81\x01\n!com.moego.idl.models.promotion.v1P\x01ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1;promotionpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.promotion.v1.promotion_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.models.promotion.v1P\001ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/promotion/v1;promotionpb'
  _globals['_TARGETTYPE']._serialized_start=2392
  _globals['_TARGETTYPE']._serialized_end=2479
  _globals['_SOURCE']._serialized_start=274
  _globals['_SOURCE']._serialized_end=701
  _globals['_SOURCE_TYPE']._serialized_start=585
  _globals['_SOURCE_TYPE']._serialized_end=690
  _globals['_PROMOTION']._serialized_start=704
  _globals['_PROMOTION']._serialized_end=1218
  _globals['_RESTRICTIONS']._serialized_start=1221
  _globals['_RESTRICTIONS']._serialized_end=1367
  _globals['_TARGETS']._serialized_start=1369
  _globals['_TARGETS']._serialized_end=1471
  _globals['_COUPONAPPLICATIONTARGET']._serialized_start=1474
  _globals['_COUPONAPPLICATIONTARGET']._serialized_end=1751
  _globals['_DURATION']._serialized_start=1754
  _globals['_DURATION']._serialized_end=1950
  _globals['_FILTER']._serialized_start=1952
  _globals['_FILTER']._serialized_end=2065
  _globals['_DISCOUNT']._serialized_start=2068
  _globals['_DISCOUNT']._serialized_end=2231
  _globals['_REDEMPTIONS']._serialized_start=2234
  _globals['_REDEMPTIONS']._serialized_end=2390
# @@protoc_insertion_point(module_scope)
