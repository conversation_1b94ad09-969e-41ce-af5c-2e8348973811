from google.type import decimal_pb2 as _decimal_pb2
from google.type import interval_pb2 as _interval_pb2
from google.type import money_pb2 as _money_pb2
from moego.models.promotion.v1 import subject_pb2 as _subject_pb2
from moego.models.reporting.v2 import common_model_pb2 as _common_model_pb2
from moego.utils.v1 import time_period_pb2 as _time_period_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class TargetType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    TARGET_TYPE_UNSPECIFIED: _ClassVar[TargetType]
    SERVICE: _ClassVar[TargetType]
    PRODUCT: _ClassVar[TargetType]
    SERVICE_CHARGE: _ClassVar[TargetType]
TARGET_TYPE_UNSPECIFIED: TargetType
SERVICE: TargetType
PRODUCT: TargetType
SERVICE_CHARGE: TargetType

class Source(_message.Message):
    __slots__ = ("id", "type", "discount", "package", "membership")
    class Type(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TYPE_UNSPECIFIED: _ClassVar[Source.Type]
        DISCOUNT: _ClassVar[Source.Type]
        PACKAGE: _ClassVar[Source.Type]
        MEMBERSHIP_DISCOUNT: _ClassVar[Source.Type]
        MEMBERSHIP_QUANTITY: _ClassVar[Source.Type]
    TYPE_UNSPECIFIED: Source.Type
    DISCOUNT: Source.Type
    PACKAGE: Source.Type
    MEMBERSHIP_DISCOUNT: Source.Type
    MEMBERSHIP_QUANTITY: Source.Type
    ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    DISCOUNT_FIELD_NUMBER: _ClassVar[int]
    PACKAGE_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_FIELD_NUMBER: _ClassVar[int]
    id: int
    type: Source.Type
    discount: _subject_pb2.DiscountSubject
    package: _subject_pb2.PackageSubject
    membership: _subject_pb2.MembershipSubject
    def __init__(self, id: _Optional[int] = ..., type: _Optional[_Union[Source.Type, str]] = ..., discount: _Optional[_Union[_subject_pb2.DiscountSubject, _Mapping]] = ..., package: _Optional[_Union[_subject_pb2.PackageSubject, _Mapping]] = ..., membership: _Optional[_Union[_subject_pb2.MembershipSubject, _Mapping]] = ...) -> None: ...

class Promotion(_message.Message):
    __slots__ = ("id", "name", "description", "source", "discount", "restrictions", "validity_period", "available_period", "redemptions")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    DISCOUNT_FIELD_NUMBER: _ClassVar[int]
    RESTRICTIONS_FIELD_NUMBER: _ClassVar[int]
    VALIDITY_PERIOD_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_PERIOD_FIELD_NUMBER: _ClassVar[int]
    REDEMPTIONS_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    description: str
    source: Source
    discount: Discount
    restrictions: Restrictions
    validity_period: Duration
    available_period: Duration
    redemptions: Redemptions
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., description: _Optional[str] = ..., source: _Optional[_Union[Source, _Mapping]] = ..., discount: _Optional[_Union[Discount, _Mapping]] = ..., restrictions: _Optional[_Union[Restrictions, _Mapping]] = ..., validity_period: _Optional[_Union[Duration, _Mapping]] = ..., available_period: _Optional[_Union[Duration, _Mapping]] = ..., redemptions: _Optional[_Union[Redemptions, _Mapping]] = ...) -> None: ...

class Restrictions(_message.Message):
    __slots__ = ("targets", "user_filters")
    TARGETS_FIELD_NUMBER: _ClassVar[int]
    USER_FILTERS_FIELD_NUMBER: _ClassVar[int]
    targets: _containers.RepeatedCompositeFieldContainer[Targets]
    user_filters: _containers.RepeatedCompositeFieldContainer[Filter]
    def __init__(self, targets: _Optional[_Iterable[_Union[Targets, _Mapping]]] = ..., user_filters: _Optional[_Iterable[_Union[Filter, _Mapping]]] = ...) -> None: ...

class Targets(_message.Message):
    __slots__ = ("type", "id", "all")
    TYPE_FIELD_NUMBER: _ClassVar[int]
    ID_FIELD_NUMBER: _ClassVar[int]
    ALL_FIELD_NUMBER: _ClassVar[int]
    type: TargetType
    id: _containers.RepeatedScalarFieldContainer[int]
    all: bool
    def __init__(self, type: _Optional[_Union[TargetType, str]] = ..., id: _Optional[_Iterable[int]] = ..., all: bool = ...) -> None: ...

class CouponApplicationTarget(_message.Message):
    __slots__ = ("target_type", "target_id", "unit_price", "target_quantity", "order_item_id")
    TARGET_TYPE_FIELD_NUMBER: _ClassVar[int]
    TARGET_ID_FIELD_NUMBER: _ClassVar[int]
    UNIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    TARGET_QUANTITY_FIELD_NUMBER: _ClassVar[int]
    ORDER_ITEM_ID_FIELD_NUMBER: _ClassVar[int]
    target_type: TargetType
    target_id: int
    unit_price: _money_pb2.Money
    target_quantity: int
    order_item_id: int
    def __init__(self, target_type: _Optional[_Union[TargetType, str]] = ..., target_id: _Optional[int] = ..., unit_price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., target_quantity: _Optional[int] = ..., order_item_id: _Optional[int] = ...) -> None: ...

class Duration(_message.Message):
    __slots__ = ("relative_period", "absolute_period", "never_expire")
    RELATIVE_PERIOD_FIELD_NUMBER: _ClassVar[int]
    ABSOLUTE_PERIOD_FIELD_NUMBER: _ClassVar[int]
    NEVER_EXPIRE_FIELD_NUMBER: _ClassVar[int]
    relative_period: _time_period_pb2.TimePeriod
    absolute_period: _interval_pb2.Interval
    never_expire: bool
    def __init__(self, relative_period: _Optional[_Union[_time_period_pb2.TimePeriod, _Mapping]] = ..., absolute_period: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., never_expire: bool = ...) -> None: ...

class Filter(_message.Message):
    __slots__ = ("key", "value", "operator")
    KEY_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    OPERATOR_FIELD_NUMBER: _ClassVar[int]
    key: str
    value: str
    operator: _common_model_pb2.Operator
    def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ..., operator: _Optional[_Union[_common_model_pb2.Operator, str]] = ...) -> None: ...

class Discount(_message.Message):
    __slots__ = ("fixed_amount", "percentage", "deduction")
    FIXED_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    PERCENTAGE_FIELD_NUMBER: _ClassVar[int]
    DEDUCTION_FIELD_NUMBER: _ClassVar[int]
    fixed_amount: _money_pb2.Money
    percentage: _decimal_pb2.Decimal
    deduction: int
    def __init__(self, fixed_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., percentage: _Optional[_Union[_decimal_pb2.Decimal, _Mapping]] = ..., deduction: _Optional[int] = ...) -> None: ...

class Redemptions(_message.Message):
    __slots__ = ("redeemed_times", "max_redeem_times", "unlimited", "refundable")
    REDEEMED_TIMES_FIELD_NUMBER: _ClassVar[int]
    MAX_REDEEM_TIMES_FIELD_NUMBER: _ClassVar[int]
    UNLIMITED_FIELD_NUMBER: _ClassVar[int]
    REFUNDABLE_FIELD_NUMBER: _ClassVar[int]
    redeemed_times: int
    max_redeem_times: int
    unlimited: bool
    refundable: bool
    def __init__(self, redeemed_times: _Optional[int] = ..., max_redeem_times: _Optional[int] = ..., unlimited: bool = ..., refundable: bool = ...) -> None: ...
