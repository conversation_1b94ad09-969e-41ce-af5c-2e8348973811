# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/agreement/v1/agreement_record_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/agreement/v1/agreement_record_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.agreement.v1 import agreement_enums_pb2 as moego_dot_models_dot_agreement_dot_v1_dot_agreement__enums__pb2
from moego.models.agreement.v1 import agreement_record_models_pb2 as moego_dot_models_dot_agreement_dot_v1_dot_agreement__record__models__pb2
from moego.models.message.v1 import message_enums_pb2 as moego_dot_models_dot_message_dot_v1_dot_message__enums__pb2
from moego.utils.v1 import pagination_messages_pb2 as moego_dot_utils_dot_v1_dot_pagination__messages__pb2
from moego.utils.v1 import status_messages_pb2 as moego_dot_utils_dot_v1_dot_status__messages__pb2
from moego.utils.v2 import condition_messages_pb2 as moego_dot_utils_dot_v2_dot_condition__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n9moego/service/agreement/v1/agreement_record_service.proto\x12\x1amoego.service.agreement.v1\x1a/moego/models/agreement/v1/agreement_enums.proto\x1a\x37moego/models/agreement/v1/agreement_record_models.proto\x1a+moego/models/message/v1/message_enums.proto\x1a(moego/utils/v1/pagination_messages.proto\x1a$moego/utils/v1/status_messages.proto\x1a\'moego/utils/v2/condition_messages.proto\x1a\x17validate/validate.proto\"\xbe\x02\n\x12\x43heckRecordRequest\x12\x13\n\x02id\x18\x01 \x01(\x03H\x00R\x02id\x88\x01\x01\x12.\n\x04uuid\x18\x02 \x01(\tB\x15\xfa\x42\x12r\x10\x32\x0e^[0-9a-f]{32}$H\x01R\x04uuid\x88\x01\x01\x12$\n\x0b\x62usiness_id\x18\x03 \x01(\x03H\x02R\nbusinessId\x88\x01\x01\x12$\n\x0b\x63ustomer_id\x18\x04 \x01(\x03H\x03R\ncustomerId\x88\x01\x01\x12&\n\x0c\x61greement_id\x18\x05 \x01(\x03H\x04R\x0b\x61greementId\x88\x01\x01\x12 \n\ttarget_id\x18\x06 \x01(\x03H\x05R\x08targetId\x88\x01\x01\x42\x05\n\x03_idB\x07\n\x05_uuidB\x0e\n\x0c_business_idB\x0e\n\x0c_customer_idB\x0f\n\r_agreement_idB\x0c\n\n_target_id\"?\n\x13\x43heckRecordResponse\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\x12\x10\n\x03msg\x18\x02 \x01(\tR\x03msg\"\xa3\x06\n\x14GetRecordListRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12$\n\x0b\x63ustomer_id\x18\x02 \x01(\x03H\x00R\ncustomerId\x88\x01\x01\x12&\n\x0c\x61greement_id\x18\x03 \x01(\x03H\x01R\x0b\x61greementId\x88\x01\x01\x12 \n\ttarget_id\x18\x04 \x01(\x03H\x02R\x08targetId\x88\x01\x01\x12\x31\n\rservice_types\x18\x05 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x03R\x0cserviceTypes\x88\x01\x01\x12?\n\x06status\x18\x06 \x01(\x0e\x32\x16.moego.utils.v1.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x04R\x06status\x88\x01\x01\x12]\n\rsigned_status\x18\x07 \x01(\x0e\x32\'.moego.models.agreement.v1.SignedStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x05R\x0csignedStatus\x88\x01\x01\x12W\n\x0bsigned_type\x18\x08 \x01(\x0e\x32%.moego.models.agreement.v1.SignedTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x06R\nsignedType\x88\x01\x01\x12W\n\x0bsource_type\x18\t \x01(\x0e\x32%.moego.models.agreement.v1.SourceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x07R\nsourceType\x88\x01\x01\x12\x41\n\npagination\x18\n \x01(\x0b\x32!.moego.utils.v1.PaginationRequestR\npagination\x12\x34\n\torder_bys\x18\x0b \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBysB\x0e\n\x0c_customer_idB\x0f\n\r_agreement_idB\x0c\n\n_target_idB\x10\n\x0e_service_typesB\t\n\x07_statusB\x10\n\x0e_signed_statusB\x0e\n\x0c_signed_typeB\x0e\n\x0c_source_type\"\xd2\x01\n\x15GetRecordListResponse\x12u\n\x1c\x61greement_record_simple_view\x18\x01 \x03(\x0b\x32\x34.moego.models.agreement.v1.AgreementRecordSimpleViewR\x19\x61greementRecordSimpleView\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v1.PaginationResponseR\npagination\"\xdb\x06\n\x1dGetRecordListByCompanyRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12$\n\x0b\x63ustomer_id\x18\x02 \x01(\x03H\x00R\ncustomerId\x88\x01\x01\x12&\n\x0c\x61greement_id\x18\x03 \x01(\x03H\x01R\x0b\x61greementId\x88\x01\x01\x12 \n\ttarget_id\x18\x04 \x01(\x03H\x02R\x08targetId\x88\x01\x01\x12\x31\n\rservice_types\x18\x05 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x03R\x0cserviceTypes\x88\x01\x01\x12?\n\x06status\x18\x06 \x01(\x0e\x32\x16.moego.utils.v1.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x04R\x06status\x88\x01\x01\x12]\n\rsigned_status\x18\x07 \x01(\x0e\x32\'.moego.models.agreement.v1.SignedStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x05R\x0csignedStatus\x88\x01\x01\x12W\n\x0bsigned_type\x18\x08 \x01(\x0e\x32%.moego.models.agreement.v1.SignedTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x06R\nsignedType\x88\x01\x01\x12W\n\x0bsource_type\x18\t \x01(\x0e\x32%.moego.models.agreement.v1.SourceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x07R\nsourceType\x88\x01\x01\x12\x41\n\npagination\x18\n \x01(\x0b\x32!.moego.utils.v1.PaginationRequestR\npagination\x12/\n\ntarget_ids\x18\x0b \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02 \x00R\ttargetIds\x12\x34\n\torder_bys\x18\x0c \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBysB\x0e\n\x0c_customer_idB\x0f\n\r_agreement_idB\x0c\n\n_target_idB\x10\n\x0e_service_typesB\t\n\x07_statusB\x10\n\x0e_signed_statusB\x0e\n\x0c_signed_typeB\x0e\n\x0c_source_type\"\xdb\x01\n\x1eGetRecordListByCompanyResponse\x12u\n\x1c\x61greement_record_simple_view\x18\x01 \x03(\x0b\x32\x34.moego.models.agreement.v1.AgreementRecordSimpleViewR\x19\x61greementRecordSimpleView\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v1.PaginationResponseR\npagination\"\xd0\x01\n\x10GetRecordRequest\x12$\n\x0b\x62usiness_id\x18\x01 \x01(\x03H\x00R\nbusinessId\x88\x01\x01\x12\x13\n\x02id\x18\x02 \x01(\x03H\x01R\x02id\x88\x01\x01\x12.\n\x04uuid\x18\x03 \x01(\tB\x15\xfa\x42\x12r\x10\x32\x0e^[0-9a-f]{32}$H\x02R\x04uuid\x88\x01\x01\x12\"\n\ncompany_id\x18\x04 \x01(\x03H\x03R\tcompanyId\x88\x01\x01\x42\x0e\n\x0c_business_idB\x05\n\x03_idB\x07\n\x05_uuidB\r\n\x0b_company_id\"\xc2\x02\n#GetRecentSignedAgreementListRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12W\n\x0bsigned_type\x18\x03 \x01(\x0e\x32%.moego.models.agreement.v1.SignedTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\nsignedType\x88\x01\x01\x12\x31\n\rservice_types\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x01R\x0cserviceTypes\x88\x01\x01\x12\x1e\n\x08is_valid\x18\x05 \x01(\x08H\x02R\x07isValid\x88\x01\x01\x42\x0e\n\x0c_signed_typeB\x10\n\x0e_service_typesB\x0b\n\t_is_valid\"\x95\x01\n$GetRecentSignedAgreementListResponse\x12m\n\x15\x61greement_recent_view\x18\x01 \x03(\x0b\x32\x39.moego.models.agreement.v1.AgreementWithRecentRecordsViewR\x13\x61greementRecentView\"\xa0\x02\n(BatchGetRecentSignedAgreementListRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x33\n\x0c\x63ustomer_ids\x18\x02 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02 \x00R\x0b\x63ustomerIds\x12Y\n\x0bsigned_type\x18\x03 \x03(\x0e\x32%.moego.models.agreement.v1.SignedTypeB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\nsignedType\x12\x31\n\rservice_types\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x00R\x0cserviceTypes\x88\x01\x01\x42\x10\n\x0e_service_types\"\xd8\x02\n)BatchGetRecentSignedAgreementListResponse\x12\x9e\x01\n\x19\x63ustomer_recent_agreement\x18\x01 \x03(\x0b\x32\x62.moego.service.agreement.v1.BatchGetRecentSignedAgreementListResponse.CustomerRecentAgreementEntryR\x17\x63ustomerRecentAgreement\x1a\x89\x01\n\x1c\x43ustomerRecentAgreementEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12S\n\x05value\x18\x02 \x01(\x0b\x32=.moego.models.agreement.v1.AgreementWithRecentRecordsViewListR\x05value:\x02\x38\x01\"\xc9\x02\n,GetRecentSignedAgreementListByCompanyRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12W\n\x0bsigned_type\x18\x03 \x01(\x0e\x32%.moego.models.agreement.v1.SignedTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\nsignedType\x88\x01\x01\x12\x31\n\rservice_types\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x01R\x0cserviceTypes\x88\x01\x01\x12\x1e\n\x08is_valid\x18\x05 \x01(\x08H\x02R\x07isValid\x88\x01\x01\x42\x0e\n\x0c_signed_typeB\x10\n\x0e_service_typesB\x0b\n\t_is_valid\"\x9e\x01\n-GetRecentSignedAgreementListByCompanyResponse\x12m\n\x15\x61greement_recent_view\x18\x01 \x03(\x0b\x32\x39.moego.models.agreement.v1.AgreementWithRecentRecordsViewR\x13\x61greementRecentView\"F\n\x13\x44\x65leteRecordRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\".\n\x14\x44\x65leteRecordResponse\x12\x16\n\x06number\x18\x01 \x01(\x05R\x06number\"\x91\x03\n\x10\x41\x64\x64RecordRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12!\n\x0c\x61greement_id\x18\x03 \x01(\x03R\x0b\x61greementId\x12 \n\ttarget_id\x18\x04 \x01(\x03H\x00R\x08targetId\x88\x01\x01\x12\x31\n\rservice_types\x18\x05 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x01R\x0cserviceTypes\x88\x01\x01\x12W\n\x0bsource_type\x18\x06 \x01(\x0e\x32%.moego.models.agreement.v1.SourceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x02R\nsourceType\x88\x01\x01\x12+\n\ncompany_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x03R\tcompanyId\x88\x01\x01\x42\x0c\n\n_target_idB\x10\n\x0e_service_typesB\x0e\n\x0c_source_typeB\r\n\x0b_company_id\"\x9c\x03\n\x14SignAgreementRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12!\n\x0c\x61greement_id\x18\x03 \x01(\x03R\x0b\x61greementId\x12)\n\tsignature\x18\x04 \x01(\tB\x0b\xfa\x42\x08r\x06\x18\x80\x08\x88\x01\x01R\tsignature\x12 \n\ttarget_id\x18\x05 \x01(\x03H\x00R\x08targetId\x88\x01\x01\x12\x31\n\rservice_types\x18\x06 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x01R\x0cserviceTypes\x88\x01\x01\x12W\n\x0bsource_type\x18\x07 \x01(\x0e\x32%.moego.models.agreement.v1.SourceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x02R\nsourceType\x88\x01\x01\x12\x16\n\x06inputs\x18\x08 \x03(\tR\x06inputsB\x0c\n\n_target_idB\x10\n\x0e_service_typesB\x0e\n\x0c_source_type\"\xe1\x01\n\x11SignRecordRequest\x12\x13\n\x02id\x18\x01 \x01(\x03H\x00R\x02id\x88\x01\x01\x12.\n\x04uuid\x18\x02 \x01(\tB\x15\xfa\x42\x12r\x10\x32\x0e^[0-9a-f]{32}$H\x01R\x04uuid\x88\x01\x01\x12$\n\x0b\x62usiness_id\x18\x03 \x01(\x03H\x02R\nbusinessId\x88\x01\x01\x12)\n\tsignature\x18\x04 \x01(\tB\x0b\xfa\x42\x08r\x06\x18\x80\x08\x88\x01\x01R\tsignature\x12\x16\n\x06inputs\x18\x05 \x03(\tR\x06inputsB\x05\n\x03_idB\x07\n\x05_uuidB\x0e\n\x0c_business_id\"\x98\x03\n\x17UploadSignedFileRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12\x33\n\x0f\x61greement_title\x18\x03 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\x80\x02R\x0e\x61greementTitle\x12\x31\n\rservice_types\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x00R\x0cserviceTypes\x88\x01\x01\x12\x39\n\x0cupload_files\x18\x05 \x03(\tB\x16\xfa\x42\x13\x92\x01\x10\x08\x01\x10@\x18\x01\"\x08r\x06\x18\x80\x08\x88\x01\x01R\x0buploadFiles\x12W\n\x0bsource_type\x18\x06 \x01(\x0e\x32%.moego.models.agreement.v1.SourceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\nsourceType\x88\x01\x01\x12\x1d\n\ncompany_id\x18\x07 \x01(\x03R\tcompanyIdB\x10\n\x0e_service_typesB\x0e\n\x0c_source_type\"\xe3\x01\n\x16SendSignRequestRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x03 \x01(\x03R\ncustomerId\x12\x19\n\x08staff_id\x18\x04 \x01(\x03R\x07staffId\x12\\\n\x11send_message_type\x18\x05 \x01(\x0e\x32$.moego.models.message.v1.MessageTypeB\n\xfa\x42\x07\x82\x01\x04\x18\x01\x18\x02R\x0fsendMessageType\"0\n\x17SendSignRequestResponse\x12\x15\n\x06msg_id\x18\x01 \x01(\x03R\x05msgId2\xce\x0e\n\x16\x41greementRecordService\x12n\n\x0b\x43heckRecord\x12..moego.service.agreement.v1.CheckRecordRequest\x1a/.moego.service.agreement.v1.CheckRecordResponse\x12j\n\tAddRecord\x12,.moego.service.agreement.v1.AddRecordRequest\x1a/.moego.models.agreement.v1.AgreementRecordModel\x12x\n\x10UploadSignedFile\x12\x33.moego.service.agreement.v1.UploadSignedFileRequest\x1a/.moego.models.agreement.v1.AgreementRecordModel\x12\xa1\x01\n\x1cGetRecentSignedAgreementList\x12?.moego.service.agreement.v1.GetRecentSignedAgreementListRequest\<EMAIL>\x12\xb0\x01\n!BatchGetRecentSignedAgreementList\x12\x44.moego.service.agreement.v1.BatchGetRecentSignedAgreementListRequest\x1a\x45.moego.service.agreement.v1.BatchGetRecentSignedAgreementListResponse\x12t\n\rGetRecordList\x12\x30.moego.service.agreement.v1.GetRecordListRequest\x1a\x31.moego.service.agreement.v1.GetRecordListResponse\x12j\n\tGetRecord\x12,.moego.service.agreement.v1.GetRecordRequest\x1a/.moego.models.agreement.v1.AgreementRecordModel\x12y\n\x13GetRecordSimpleView\x12,.moego.service.agreement.v1.GetRecordRequest\x1a\x34.moego.models.agreement.v1.AgreementRecordSimpleView\x12w\n\rSignAgreement\x12\x30.moego.service.agreement.v1.SignAgreementRequest\x1a\x34.moego.models.agreement.v1.AgreementRecordSimpleView\x12q\n\nSignRecord\x12-.moego.service.agreement.v1.SignRecordRequest\x1a\x34.moego.models.agreement.v1.AgreementRecordSimpleView\x12z\n\x0fSendSignRequest\x12\x32.moego.service.agreement.v1.SendSignRequestRequest\x1a\x33.moego.service.agreement.v1.SendSignRequestResponse\x12q\n\x0c\x44\x65leteRecord\x12/.moego.service.agreement.v1.DeleteRecordRequest\x1a\x30.moego.service.agreement.v1.DeleteRecordResponse\x12\xbc\x01\n%GetRecentSignedAgreementListByCompany\x12H.moego.service.agreement.v1.GetRecentSignedAgreementListByCompanyRequest\x1aI.moego.service.agreement.v1.GetRecentSignedAgreementListByCompanyResponse\x12\x8f\x01\n\x16GetRecordListByCompany\x12\x39.moego.service.agreement.v1.GetRecordListByCompanyRequest\x1a:.moego.service.agreement.v1.GetRecordListByCompanyResponseB\x86\x01\n\"com.moego.idl.service.agreement.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/agreement/v1;agreementsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.agreement.v1.agreement_record_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.service.agreement.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/agreement/v1;agreementsvcpb'
  _globals['_CHECKRECORDREQUEST'].fields_by_name['uuid']._loaded_options = None
  _globals['_CHECKRECORDREQUEST'].fields_by_name['uuid']._serialized_options = b'\372B\022r\0202\016^[0-9a-f]{32}$'
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['signed_status']._loaded_options = None
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['signed_status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['signed_type']._loaded_options = None
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['signed_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['source_type']._loaded_options = None
  _globals['_GETRECORDLISTREQUEST'].fields_by_name['source_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['signed_status']._loaded_options = None
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['signed_status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['signed_type']._loaded_options = None
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['signed_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['source_type']._loaded_options = None
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['source_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['target_ids']._loaded_options = None
  _globals['_GETRECORDLISTBYCOMPANYREQUEST'].fields_by_name['target_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002 \000'
  _globals['_GETRECORDREQUEST'].fields_by_name['uuid']._loaded_options = None
  _globals['_GETRECORDREQUEST'].fields_by_name['uuid']._serialized_options = b'\372B\022r\0202\016^[0-9a-f]{32}$'
  _globals['_GETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['signed_type']._loaded_options = None
  _globals['_GETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['signed_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_GETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['customer_ids']._loaded_options = None
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['customer_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002 \000'
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['signed_type']._loaded_options = None
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['signed_type']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTRESPONSE_CUSTOMERRECENTAGREEMENTENTRY']._loaded_options = None
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTRESPONSE_CUSTOMERRECENTAGREEMENTENTRY']._serialized_options = b'8\001'
  _globals['_GETRECENTSIGNEDAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['signed_type']._loaded_options = None
  _globals['_GETRECENTSIGNEDAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['signed_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETRECENTSIGNEDAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_GETRECENTSIGNEDAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_ADDRECORDREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_ADDRECORDREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_ADDRECORDREQUEST'].fields_by_name['source_type']._loaded_options = None
  _globals['_ADDRECORDREQUEST'].fields_by_name['source_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_ADDRECORDREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_ADDRECORDREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SIGNAGREEMENTREQUEST'].fields_by_name['signature']._loaded_options = None
  _globals['_SIGNAGREEMENTREQUEST'].fields_by_name['signature']._serialized_options = b'\372B\010r\006\030\200\010\210\001\001'
  _globals['_SIGNAGREEMENTREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_SIGNAGREEMENTREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_SIGNAGREEMENTREQUEST'].fields_by_name['source_type']._loaded_options = None
  _globals['_SIGNAGREEMENTREQUEST'].fields_by_name['source_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SIGNRECORDREQUEST'].fields_by_name['uuid']._loaded_options = None
  _globals['_SIGNRECORDREQUEST'].fields_by_name['uuid']._serialized_options = b'\372B\022r\0202\016^[0-9a-f]{32}$'
  _globals['_SIGNRECORDREQUEST'].fields_by_name['signature']._loaded_options = None
  _globals['_SIGNRECORDREQUEST'].fields_by_name['signature']._serialized_options = b'\372B\010r\006\030\200\010\210\001\001'
  _globals['_UPLOADSIGNEDFILEREQUEST'].fields_by_name['agreement_title']._loaded_options = None
  _globals['_UPLOADSIGNEDFILEREQUEST'].fields_by_name['agreement_title']._serialized_options = b'\372B\007r\005\020\001\030\200\002'
  _globals['_UPLOADSIGNEDFILEREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_UPLOADSIGNEDFILEREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_UPLOADSIGNEDFILEREQUEST'].fields_by_name['upload_files']._loaded_options = None
  _globals['_UPLOADSIGNEDFILEREQUEST'].fields_by_name['upload_files']._serialized_options = b'\372B\023\222\001\020\010\001\020@\030\001\"\010r\006\030\200\010\210\001\001'
  _globals['_UPLOADSIGNEDFILEREQUEST'].fields_by_name['source_type']._loaded_options = None
  _globals['_UPLOADSIGNEDFILEREQUEST'].fields_by_name['source_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SENDSIGNREQUESTREQUEST'].fields_by_name['send_message_type']._loaded_options = None
  _globals['_SENDSIGNREQUESTREQUEST'].fields_by_name['send_message_type']._serialized_options = b'\372B\007\202\001\004\030\001\030\002'
  _globals['_CHECKRECORDREQUEST']._serialized_start=387
  _globals['_CHECKRECORDREQUEST']._serialized_end=705
  _globals['_CHECKRECORDRESPONSE']._serialized_start=707
  _globals['_CHECKRECORDRESPONSE']._serialized_end=770
  _globals['_GETRECORDLISTREQUEST']._serialized_start=773
  _globals['_GETRECORDLISTREQUEST']._serialized_end=1576
  _globals['_GETRECORDLISTRESPONSE']._serialized_start=1579
  _globals['_GETRECORDLISTRESPONSE']._serialized_end=1789
  _globals['_GETRECORDLISTBYCOMPANYREQUEST']._serialized_start=1792
  _globals['_GETRECORDLISTBYCOMPANYREQUEST']._serialized_end=2651
  _globals['_GETRECORDLISTBYCOMPANYRESPONSE']._serialized_start=2654
  _globals['_GETRECORDLISTBYCOMPANYRESPONSE']._serialized_end=2873
  _globals['_GETRECORDREQUEST']._serialized_start=2876
  _globals['_GETRECORDREQUEST']._serialized_end=3084
  _globals['_GETRECENTSIGNEDAGREEMENTLISTREQUEST']._serialized_start=3087
  _globals['_GETRECENTSIGNEDAGREEMENTLISTREQUEST']._serialized_end=3409
  _globals['_GETRECENTSIGNEDAGREEMENTLISTRESPONSE']._serialized_start=3412
  _globals['_GETRECENTSIGNEDAGREEMENTLISTRESPONSE']._serialized_end=3561
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTREQUEST']._serialized_start=3564
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTREQUEST']._serialized_end=3852
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTRESPONSE']._serialized_start=3855
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTRESPONSE']._serialized_end=4199
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTRESPONSE_CUSTOMERRECENTAGREEMENTENTRY']._serialized_start=4062
  _globals['_BATCHGETRECENTSIGNEDAGREEMENTLISTRESPONSE_CUSTOMERRECENTAGREEMENTENTRY']._serialized_end=4199
  _globals['_GETRECENTSIGNEDAGREEMENTLISTBYCOMPANYREQUEST']._serialized_start=4202
  _globals['_GETRECENTSIGNEDAGREEMENTLISTBYCOMPANYREQUEST']._serialized_end=4531
  _globals['_GETRECENTSIGNEDAGREEMENTLISTBYCOMPANYRESPONSE']._serialized_start=4534
  _globals['_GETRECENTSIGNEDAGREEMENTLISTBYCOMPANYRESPONSE']._serialized_end=4692
  _globals['_DELETERECORDREQUEST']._serialized_start=4694
  _globals['_DELETERECORDREQUEST']._serialized_end=4764
  _globals['_DELETERECORDRESPONSE']._serialized_start=4766
  _globals['_DELETERECORDRESPONSE']._serialized_end=4812
  _globals['_ADDRECORDREQUEST']._serialized_start=4815
  _globals['_ADDRECORDREQUEST']._serialized_end=5216
  _globals['_SIGNAGREEMENTREQUEST']._serialized_start=5219
  _globals['_SIGNAGREEMENTREQUEST']._serialized_end=5631
  _globals['_SIGNRECORDREQUEST']._serialized_start=5634
  _globals['_SIGNRECORDREQUEST']._serialized_end=5859
  _globals['_UPLOADSIGNEDFILEREQUEST']._serialized_start=5862
  _globals['_UPLOADSIGNEDFILEREQUEST']._serialized_end=6270
  _globals['_SENDSIGNREQUESTREQUEST']._serialized_start=6273
  _globals['_SENDSIGNREQUESTREQUEST']._serialized_end=6500
  _globals['_SENDSIGNREQUESTRESPONSE']._serialized_start=6502
  _globals['_SENDSIGNREQUESTRESPONSE']._serialized_end=6550
  _globals['_AGREEMENTRECORDSERVICE']._serialized_start=6553
  _globals['_AGREEMENTRECORDSERVICE']._serialized_end=8423
# @@protoc_insertion_point(module_scope)
