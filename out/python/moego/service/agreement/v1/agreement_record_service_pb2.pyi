from moego.models.agreement.v1 import agreement_enums_pb2 as _agreement_enums_pb2
from moego.models.agreement.v1 import agreement_record_models_pb2 as _agreement_record_models_pb2
from moego.models.message.v1 import message_enums_pb2 as _message_enums_pb2
from moego.utils.v1 import pagination_messages_pb2 as _pagination_messages_pb2
from moego.utils.v1 import status_messages_pb2 as _status_messages_pb2
from moego.utils.v2 import condition_messages_pb2 as _condition_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CheckRecordRequest(_message.Message):
    __slots__ = ("id", "uuid", "business_id", "customer_id", "agreement_id", "target_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    UUID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_ID_FIELD_NUMBER: _ClassVar[int]
    TARGET_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    uuid: str
    business_id: int
    customer_id: int
    agreement_id: int
    target_id: int
    def __init__(self, id: _Optional[int] = ..., uuid: _Optional[str] = ..., business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., agreement_id: _Optional[int] = ..., target_id: _Optional[int] = ...) -> None: ...

class CheckRecordResponse(_message.Message):
    __slots__ = ("result", "msg")
    RESULT_FIELD_NUMBER: _ClassVar[int]
    MSG_FIELD_NUMBER: _ClassVar[int]
    result: bool
    msg: str
    def __init__(self, result: bool = ..., msg: _Optional[str] = ...) -> None: ...

class GetRecordListRequest(_message.Message):
    __slots__ = ("business_id", "customer_id", "agreement_id", "target_id", "service_types", "status", "signed_status", "signed_type", "source_type", "pagination", "order_bys")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_ID_FIELD_NUMBER: _ClassVar[int]
    TARGET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SIGNED_STATUS_FIELD_NUMBER: _ClassVar[int]
    SIGNED_TYPE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_id: int
    agreement_id: int
    target_id: int
    service_types: int
    status: _status_messages_pb2.Status
    signed_status: _agreement_enums_pb2.SignedStatus
    signed_type: _agreement_enums_pb2.SignedType
    source_type: _agreement_enums_pb2.SourceType
    pagination: _pagination_messages_pb2.PaginationRequest
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2.OrderBy]
    def __init__(self, business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., agreement_id: _Optional[int] = ..., target_id: _Optional[int] = ..., service_types: _Optional[int] = ..., status: _Optional[_Union[_status_messages_pb2.Status, str]] = ..., signed_status: _Optional[_Union[_agreement_enums_pb2.SignedStatus, str]] = ..., signed_type: _Optional[_Union[_agreement_enums_pb2.SignedType, str]] = ..., source_type: _Optional[_Union[_agreement_enums_pb2.SourceType, str]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2.OrderBy, _Mapping]]] = ...) -> None: ...

class GetRecordListResponse(_message.Message):
    __slots__ = ("agreement_record_simple_view", "pagination")
    AGREEMENT_RECORD_SIMPLE_VIEW_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    agreement_record_simple_view: _containers.RepeatedCompositeFieldContainer[_agreement_record_models_pb2.AgreementRecordSimpleView]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, agreement_record_simple_view: _Optional[_Iterable[_Union[_agreement_record_models_pb2.AgreementRecordSimpleView, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class GetRecordListByCompanyRequest(_message.Message):
    __slots__ = ("company_id", "customer_id", "agreement_id", "target_id", "service_types", "status", "signed_status", "signed_type", "source_type", "pagination", "target_ids", "order_bys")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_ID_FIELD_NUMBER: _ClassVar[int]
    TARGET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SIGNED_STATUS_FIELD_NUMBER: _ClassVar[int]
    SIGNED_TYPE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    TARGET_IDS_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    customer_id: int
    agreement_id: int
    target_id: int
    service_types: int
    status: _status_messages_pb2.Status
    signed_status: _agreement_enums_pb2.SignedStatus
    signed_type: _agreement_enums_pb2.SignedType
    source_type: _agreement_enums_pb2.SourceType
    pagination: _pagination_messages_pb2.PaginationRequest
    target_ids: _containers.RepeatedScalarFieldContainer[int]
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2.OrderBy]
    def __init__(self, company_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., agreement_id: _Optional[int] = ..., target_id: _Optional[int] = ..., service_types: _Optional[int] = ..., status: _Optional[_Union[_status_messages_pb2.Status, str]] = ..., signed_status: _Optional[_Union[_agreement_enums_pb2.SignedStatus, str]] = ..., signed_type: _Optional[_Union[_agreement_enums_pb2.SignedType, str]] = ..., source_type: _Optional[_Union[_agreement_enums_pb2.SourceType, str]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., target_ids: _Optional[_Iterable[int]] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2.OrderBy, _Mapping]]] = ...) -> None: ...

class GetRecordListByCompanyResponse(_message.Message):
    __slots__ = ("agreement_record_simple_view", "pagination")
    AGREEMENT_RECORD_SIMPLE_VIEW_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    agreement_record_simple_view: _containers.RepeatedCompositeFieldContainer[_agreement_record_models_pb2.AgreementRecordSimpleView]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, agreement_record_simple_view: _Optional[_Iterable[_Union[_agreement_record_models_pb2.AgreementRecordSimpleView, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class GetRecordRequest(_message.Message):
    __slots__ = ("business_id", "id", "uuid", "company_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    ID_FIELD_NUMBER: _ClassVar[int]
    UUID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    id: int
    uuid: str
    company_id: int
    def __init__(self, business_id: _Optional[int] = ..., id: _Optional[int] = ..., uuid: _Optional[str] = ..., company_id: _Optional[int] = ...) -> None: ...

class GetRecentSignedAgreementListRequest(_message.Message):
    __slots__ = ("business_id", "customer_id", "signed_type", "service_types", "is_valid")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    SIGNED_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    IS_VALID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_id: int
    signed_type: _agreement_enums_pb2.SignedType
    service_types: int
    is_valid: bool
    def __init__(self, business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., signed_type: _Optional[_Union[_agreement_enums_pb2.SignedType, str]] = ..., service_types: _Optional[int] = ..., is_valid: bool = ...) -> None: ...

class GetRecentSignedAgreementListResponse(_message.Message):
    __slots__ = ("agreement_recent_view",)
    AGREEMENT_RECENT_VIEW_FIELD_NUMBER: _ClassVar[int]
    agreement_recent_view: _containers.RepeatedCompositeFieldContainer[_agreement_record_models_pb2.AgreementWithRecentRecordsView]
    def __init__(self, agreement_recent_view: _Optional[_Iterable[_Union[_agreement_record_models_pb2.AgreementWithRecentRecordsView, _Mapping]]] = ...) -> None: ...

class BatchGetRecentSignedAgreementListRequest(_message.Message):
    __slots__ = ("business_id", "customer_ids", "signed_type", "service_types")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
    SIGNED_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_ids: _containers.RepeatedScalarFieldContainer[int]
    signed_type: _containers.RepeatedScalarFieldContainer[_agreement_enums_pb2.SignedType]
    service_types: int
    def __init__(self, business_id: _Optional[int] = ..., customer_ids: _Optional[_Iterable[int]] = ..., signed_type: _Optional[_Iterable[_Union[_agreement_enums_pb2.SignedType, str]]] = ..., service_types: _Optional[int] = ...) -> None: ...

class BatchGetRecentSignedAgreementListResponse(_message.Message):
    __slots__ = ("customer_recent_agreement",)
    class CustomerRecentAgreementEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: _agreement_record_models_pb2.AgreementWithRecentRecordsViewList
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[_agreement_record_models_pb2.AgreementWithRecentRecordsViewList, _Mapping]] = ...) -> None: ...
    CUSTOMER_RECENT_AGREEMENT_FIELD_NUMBER: _ClassVar[int]
    customer_recent_agreement: _containers.MessageMap[int, _agreement_record_models_pb2.AgreementWithRecentRecordsViewList]
    def __init__(self, customer_recent_agreement: _Optional[_Mapping[int, _agreement_record_models_pb2.AgreementWithRecentRecordsViewList]] = ...) -> None: ...

class GetRecentSignedAgreementListByCompanyRequest(_message.Message):
    __slots__ = ("company_id", "customer_id", "signed_type", "service_types", "is_valid")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    SIGNED_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    IS_VALID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    customer_id: int
    signed_type: _agreement_enums_pb2.SignedType
    service_types: int
    is_valid: bool
    def __init__(self, company_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., signed_type: _Optional[_Union[_agreement_enums_pb2.SignedType, str]] = ..., service_types: _Optional[int] = ..., is_valid: bool = ...) -> None: ...

class GetRecentSignedAgreementListByCompanyResponse(_message.Message):
    __slots__ = ("agreement_recent_view",)
    AGREEMENT_RECENT_VIEW_FIELD_NUMBER: _ClassVar[int]
    agreement_recent_view: _containers.RepeatedCompositeFieldContainer[_agreement_record_models_pb2.AgreementWithRecentRecordsView]
    def __init__(self, agreement_recent_view: _Optional[_Iterable[_Union[_agreement_record_models_pb2.AgreementWithRecentRecordsView, _Mapping]]] = ...) -> None: ...

class DeleteRecordRequest(_message.Message):
    __slots__ = ("id", "business_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class DeleteRecordResponse(_message.Message):
    __slots__ = ("number",)
    NUMBER_FIELD_NUMBER: _ClassVar[int]
    number: int
    def __init__(self, number: _Optional[int] = ...) -> None: ...

class AddRecordRequest(_message.Message):
    __slots__ = ("business_id", "customer_id", "agreement_id", "target_id", "service_types", "source_type", "company_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_ID_FIELD_NUMBER: _ClassVar[int]
    TARGET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    SOURCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_id: int
    agreement_id: int
    target_id: int
    service_types: int
    source_type: _agreement_enums_pb2.SourceType
    company_id: int
    def __init__(self, business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., agreement_id: _Optional[int] = ..., target_id: _Optional[int] = ..., service_types: _Optional[int] = ..., source_type: _Optional[_Union[_agreement_enums_pb2.SourceType, str]] = ..., company_id: _Optional[int] = ...) -> None: ...

class SignAgreementRequest(_message.Message):
    __slots__ = ("business_id", "customer_id", "agreement_id", "signature", "target_id", "service_types", "source_type", "inputs")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_ID_FIELD_NUMBER: _ClassVar[int]
    SIGNATURE_FIELD_NUMBER: _ClassVar[int]
    TARGET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    SOURCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    INPUTS_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_id: int
    agreement_id: int
    signature: str
    target_id: int
    service_types: int
    source_type: _agreement_enums_pb2.SourceType
    inputs: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., agreement_id: _Optional[int] = ..., signature: _Optional[str] = ..., target_id: _Optional[int] = ..., service_types: _Optional[int] = ..., source_type: _Optional[_Union[_agreement_enums_pb2.SourceType, str]] = ..., inputs: _Optional[_Iterable[str]] = ...) -> None: ...

class SignRecordRequest(_message.Message):
    __slots__ = ("id", "uuid", "business_id", "signature", "inputs")
    ID_FIELD_NUMBER: _ClassVar[int]
    UUID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    SIGNATURE_FIELD_NUMBER: _ClassVar[int]
    INPUTS_FIELD_NUMBER: _ClassVar[int]
    id: int
    uuid: str
    business_id: int
    signature: str
    inputs: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, id: _Optional[int] = ..., uuid: _Optional[str] = ..., business_id: _Optional[int] = ..., signature: _Optional[str] = ..., inputs: _Optional[_Iterable[str]] = ...) -> None: ...

class UploadSignedFileRequest(_message.Message):
    __slots__ = ("business_id", "customer_id", "agreement_title", "service_types", "upload_files", "source_type", "company_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_TITLE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    UPLOAD_FILES_FIELD_NUMBER: _ClassVar[int]
    SOURCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_id: int
    agreement_title: str
    service_types: int
    upload_files: _containers.RepeatedScalarFieldContainer[str]
    source_type: _agreement_enums_pb2.SourceType
    company_id: int
    def __init__(self, business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., agreement_title: _Optional[str] = ..., service_types: _Optional[int] = ..., upload_files: _Optional[_Iterable[str]] = ..., source_type: _Optional[_Union[_agreement_enums_pb2.SourceType, str]] = ..., company_id: _Optional[int] = ...) -> None: ...

class SendSignRequestRequest(_message.Message):
    __slots__ = ("id", "business_id", "customer_id", "staff_id", "send_message_type")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SEND_MESSAGE_TYPE_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    customer_id: int
    staff_id: int
    send_message_type: _message_enums_pb2.MessageType
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., send_message_type: _Optional[_Union[_message_enums_pb2.MessageType, str]] = ...) -> None: ...

class SendSignRequestResponse(_message.Message):
    __slots__ = ("msg_id",)
    MSG_ID_FIELD_NUMBER: _ClassVar[int]
    msg_id: int
    def __init__(self, msg_id: _Optional[int] = ...) -> None: ...
