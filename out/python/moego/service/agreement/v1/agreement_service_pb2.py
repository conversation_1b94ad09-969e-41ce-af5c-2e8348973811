# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/agreement/v1/agreement_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/agreement/v1/agreement_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.agreement.v1 import agreement_enums_pb2 as moego_dot_models_dot_agreement_dot_v1_dot_agreement__enums__pb2
from moego.models.agreement.v1 import agreement_models_pb2 as moego_dot_models_dot_agreement_dot_v1_dot_agreement__models__pb2
from moego.utils.v1 import status_messages_pb2 as moego_dot_utils_dot_v1_dot_status__messages__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n2moego/service/agreement/v1/agreement_service.proto\x12\x1amoego.service.agreement.v1\x1a/moego/models/agreement/v1/agreement_enums.proto\x1a\x30moego/models/agreement/v1/agreement_models.proto\x1a$moego/utils/v1/status_messages.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"H\n\x15\x43heckAgreementRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\"y\n\x13GetAgreementRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\"\n\ncompany_id\x18\x03 \x01(\x03H\x00R\tcompanyId\x88\x01\x01\x42\r\n\x0b_company_id\"I\n\x16\x44\x65leteAgreementRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\"1\n\x17\x44\x65leteAgreementResponse\x12\x16\n\x06number\x18\x01 \x01(\x05R\x06number\"B\n\x16\x43heckAgreementResponse\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\x12\x10\n\x03msg\x18\x02 \x01(\tR\x03msg\"\xdd\x01\n\x17GetAgreementListRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x10\n\x03ids\x18\x02 \x03(\x03R\x03ids\x12?\n\x06status\x18\x03 \x01(\x0e\x32\x16.moego.utils.v1.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x06status\x88\x01\x01\x12\x31\n\rservice_types\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x01R\x0cserviceTypes\x88\x01\x01\x42\t\n\x07_statusB\x10\n\x0e_service_types\"\xcf\x01\n\x14InitAgreementRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x32\n\rbusiness_name\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x02H\x00R\x0c\x62usinessName\x88\x01\x01\x12\"\n\ncreator_id\x18\x03 \x01(\x03H\x01R\tcreatorId\x88\x01\x01\x12\x1d\n\ncompany_id\x18\x04 \x01(\x03R\tcompanyIdB\x10\n\x0e_business_nameB\r\n\x0b_creator_id\"\xe1\x05\n\x13\x41\x64\x64\x41greementRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1d\n\ncreator_id\x18\x02 \x01(\x03R\tcreatorId\x12X\n\rsigned_policy\x18\x03 \x01(\x0e\x32\'.moego.models.agreement.v1.SignedPolicyB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0csignedPolicy\x12,\n\rservice_types\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00R\x0cserviceTypes\x12\x38\n\x0f\x61greement_title\x18\x05 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\x80\x02H\x00R\x0e\x61greementTitle\x88\x01\x01\x12=\n\x11\x61greement_content\x18\x06 \x01(\tB\x0b\xfa\x42\x08r\x06\x10\x01\x18\x80\x80@H\x01R\x10\x61greementContent\x88\x01\x01\x12\x33\n\x0csms_template\x18\x07 \x01(\tB\x0b\xfa\x42\x08r\x06\x10\x01\x18\x80\x80\x04H\x02R\x0bsmsTemplate\x88\x01\x01\x12\x41\n\x14\x65mail_template_title\x18\x08 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\x80\x02H\x03R\x12\x65mailTemplateTitle\x88\x01\x01\x12@\n\x13\x65mail_template_body\x18\t \x01(\tB\x0b\xfa\x42\x08r\x06\x10\x01\x18\x80\x80\x08H\x04R\x11\x65mailTemplateBody\x88\x01\x01\x12\x32\n\rbusiness_name\x18\n \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x02H\x05R\x0c\x62usinessName\x88\x01\x01\x12\x1d\n\ncompany_id\x18\x0b \x01(\x03R\tcompanyIdB\x12\n\x10_agreement_titleB\x14\n\x12_agreement_contentB\x0f\n\r_sms_templateB\x17\n\x15_email_template_titleB\x16\n\x14_email_template_bodyB\x10\n\x0e_business_name\"\xb7\x05\n\x16UpdateAgreementRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12]\n\rsigned_policy\x18\x03 \x01(\x0e\x32\'.moego.models.agreement.v1.SignedPolicyB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x0csignedPolicy\x88\x01\x01\x12\x38\n\x0f\x61greement_title\x18\x04 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\x80\x02H\x01R\x0e\x61greementTitle\x88\x01\x01\x12=\n\x11\x61greement_content\x18\x05 \x01(\tB\x0b\xfa\x42\x08r\x06\x10\x01\x18\x80\x80@H\x02R\x10\x61greementContent\x88\x01\x01\x12\x33\n\x0csms_template\x18\x06 \x01(\tB\x0b\xfa\x42\x08r\x06\x10\x01\x18\x80\x80\x04H\x03R\x0bsmsTemplate\x88\x01\x01\x12\x41\n\x14\x65mail_template_title\x18\x07 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\x80\x02H\x04R\x12\x65mailTemplateTitle\x88\x01\x01\x12@\n\x13\x65mail_template_body\x18\x08 \x01(\tB\x0b\xfa\x42\x08r\x06\x10\x01\x18\x80\x80\x08H\x05R\x11\x65mailTemplateBody\x88\x01\x01\x12>\n\x19update_last_required_time\x18\t \x01(\x08H\x06R\x16updateLastRequiredTime\x88\x01\x01\x42\x10\n\x0e_signed_policyB\x12\n\x10_agreement_titleB\x14\n\x12_agreement_contentB\x0f\n\r_sms_templateB\x17\n\x15_email_template_titleB\x16\n\x14_email_template_bodyB\x1c\n\x1a_update_last_required_time\"\xc6\x01\n\x18UpdateServiceTypeRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12U\n\x0cservice_type\x18\x03 \x01(\x0e\x32&.moego.models.agreement.v1.ServiceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x0bserviceType\x12\"\n\rset_or_cancel\x18\x04 \x01(\x08R\x0bsetOrCancel\"\x83\x01\n\x18GetAgreementListResponse\x12g\n\x15\x61greement_simple_view\x18\x01 \x03(\x0b\x32\x33.moego.models.agreement.v1.AgreementModelSimpleViewR\x13\x61greementSimpleView\"\x8d\x01\n\x1fGetAgreementContentListResponse\x12j\n\x16\x61greement_content_view\x18\x01 \x03(\x0b\x32\x34.moego.models.agreement.v1.AgreementModelContentViewR\x14\x61greementContentView\"\x82\x02\n!GetAgreementSignStatusListRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12 \n\ttarget_id\x18\x03 \x01(\x03H\x00R\x08targetId\x88\x01\x01\x12Z\n\x0cservice_type\x18\x04 \x01(\x0e\x32&.moego.models.agreement.v1.ServiceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x0bserviceType\x88\x01\x01\x42\x0c\n\n_target_idB\x0f\n\r_service_type\"\x8c\x01\n\"GetAgreementSignStatusListResponse\x12\x66\n\x15\x61greement_status_view\x18\x01 \x03(\x0b\x32\x32.moego.models.agreement.v1.AgreementSignStatusViewR\x13\x61greementStatusView\"\xd8\x02\n+BatchGetAgreementUnsignedAppointmentRequest\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\xa2\x01\n\x1c\x63ustomer_with_appointment_id\x18\x02 \x03(\x0b\x32\x61.moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentIdR\x19\x63ustomerWithAppointmentId\x1a\x63\n\x19\x43ustomerWithAppointmentId\x12\x1f\n\x0b\x63ustomer_id\x18\x01 \x01(\x03R\ncustomerId\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\"U\n,BatchGetAgreementUnsignedAppointmentResponse\x12%\n\x0e\x61ppointment_id\x18\x01 \x03(\x03R\rappointmentId\"\x96\x01\n(GetAgreementContentListByCompanyResponse\x12j\n\x16\x61greement_content_view\x18\x01 \x03(\x0b\x32\x34.moego.models.agreement.v1.AgreementModelContentViewR\x14\x61greementContentView\"\xa5\x02\n GetAgreementListByCompanyRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\x10\n\x03ids\x18\x02 \x03(\x03R\x03ids\x12?\n\x06status\x18\x03 \x01(\x0e\x32\x16.moego.utils.v1.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x06status\x88\x01\x01\x12\x31\n\rservice_types\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x01R\x0cserviceTypes\x88\x01\x01\x12\x36\n\x0c\x62usiness_ids\x18\x05 \x03(\x03\x42\x13\xfa\x42\x10\x92\x01\r\x10\xf4\x03\x18\x01\"\x04\"\x02 \x00(\x01R\x0b\x62usinessIdsB\t\n\x07_statusB\x10\n\x0e_service_types\"\x8c\x01\n!GetAgreementListByCompanyResponse\x12g\n\x15\x61greement_simple_view\x18\x01 \x03(\x0b\x32\x33.moego.models.agreement.v1.AgreementModelSimpleViewR\x13\x61greementSimpleView\"\x86\x03\n\x15ListAgreementsRequest\x12\x46\n\npagination\x18\x01 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x00R\npagination\x88\x01\x01\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12%\n\x03ids\x18\x03 \x03(\x03\x42\x13\xfa\x42\x10\x92\x01\r\x10\xf4\x03\x18\x01\"\x04\"\x02 \x00(\x01R\x03ids\x12?\n\x06status\x18\x04 \x01(\x0e\x32\x16.moego.utils.v1.StatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x06status\x88\x01\x01\x12\x31\n\rservice_types\x18\x05 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x02R\x0cserviceTypes\x88\x01\x01\x12\x36\n\x0c\x62usiness_ids\x18\x06 \x03(\x03\x42\x13\xfa\x42\x10\x92\x01\r\x10\xf4\x03\x18\x01\"\x04\"\x02 \x00(\x01R\x0b\x62usinessIdsB\r\n\x0b_paginationB\t\n\x07_statusB\x10\n\x0e_service_types\"\xbb\x01\n\x16ListAgreementsResponse\x12G\n\npagination\x18\x01 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseH\x00R\npagination\x88\x01\x01\x12I\n\nagreements\x18\x02 \x03(\x0b\x32).moego.models.agreement.v1.AgreementModelR\nagreementsB\r\n\x0b_pagination\"\xfe\x01\n\x1cListUnsignedAgreementRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\x32\n\x0c\x62usiness_ids\x18\x02 \x03(\x03\x42\x0f\xfa\x42\x0c\x92\x01\t\x10\xe8\x07\"\x04\"\x02 \x00R\x0b\x62usinessIds\x12-\n\x0b\x63ustomer_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\ncustomerId\x88\x01\x01\x12\x31\n\rservice_types\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x01R\x0cserviceTypes\x88\x01\x01\x42\x0e\n\x0c_customer_idB\x10\n\x0e_service_types\"j\n\x1dListUnsignedAgreementResponse\x12I\n\nagreements\x18\x01 \x03(\x0b\x32).moego.models.agreement.v1.AgreementModelR\nagreements\"\xfc\x01\n\'ListUnsignedAgreementByCustomersRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\x31\n\x0c\x62usiness_ids\x18\x02 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x10\x64\"\x04\"\x02 \x00R\x0b\x62usinessIds\x12\x31\n\x0c\x63ustomer_ids\x18\x03 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x10\x64\"\x04\"\x02 \x00R\x0b\x63ustomerIds\x12\x31\n\rservice_types\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x00R\x0cserviceTypes\x88\x01\x01\x42\x10\n\x0e_service_types\"\xc8\x02\n(ListUnsignedAgreementByCustomersResponse\x12\x8b\x01\n\x13\x63ustomer_agreements\x18\x01 \x03(\x0b\x32Z.moego.service.agreement.v1.ListUnsignedAgreementByCustomersResponse.CustomerAgreementViewR\x12\x63ustomerAgreements\x1a\x8d\x01\n\x15\x43ustomerAgreementView\x12\x1f\n\x0b\x63ustomer_id\x18\x01 \x01(\x03R\ncustomerId\x12S\n\nagreements\x18\x02 \x03(\x0b\x32\x33.moego.models.agreement.v1.AgreementModelSimpleViewR\nagreements2\xac\x11\n\x10\x41greementService\x12w\n\x0e\x43heckAgreement\x12\x31.moego.service.agreement.v1.CheckAgreementRequest\x1a\x32.moego.service.agreement.v1.CheckAgreementResponse\x12j\n\x0cGetAgreement\x12/.moego.service.agreement.v1.GetAgreementRequest\x1a).moego.models.agreement.v1.AgreementModel\x12}\n\x10GetAgreementList\x12\x33.moego.service.agreement.v1.GetAgreementListRequest\x1a\x34.moego.service.agreement.v1.GetAgreementListResponse\x12\x8b\x01\n\x17GetAgreementContentList\x12\x33.moego.service.agreement.v1.GetAgreementListRequest\x1a;.moego.service.agreement.v1.GetAgreementContentListResponse\x12\x9b\x01\n\x1aGetAgreementSignStatusList\x12=.moego.service.agreement.v1.GetAgreementSignStatusListRequest\x1a>.moego.service.agreement.v1.GetAgreementSignStatusListResponse\x12l\n\rInitAgreement\x12\x30.moego.service.agreement.v1.InitAgreementRequest\x1a).moego.models.agreement.v1.AgreementModel\x12j\n\x0c\x41\x64\x64\x41greement\x12/.moego.service.agreement.v1.AddAgreementRequest\x1a).moego.models.agreement.v1.AgreementModel\x12p\n\x0fUpdateAgreement\x12\x32.moego.service.agreement.v1.UpdateAgreementRequest\x1a).moego.models.agreement.v1.AgreementModel\x12\x87\x01\n\x1aUpdateAgreementServiceType\x12\x34.moego.service.agreement.v1.UpdateServiceTypeRequest\x1a\x33.moego.models.agreement.v1.AgreementModelSimpleView\x12z\n\x0f\x44\x65leteAgreement\x12\x32.moego.service.agreement.v1.DeleteAgreementRequest\x1a\x33.moego.service.agreement.v1.DeleteAgreementResponse\x12\xb9\x01\n$BatchGetAgreementUnsignedAppointment\x12G.moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest\x1aH.moego.service.agreement.v1.BatchGetAgreementUnsignedAppointmentResponse\x12\xa6\x01\n GetAgreementContentListByCompany\x12<.moego.service.agreement.v1.GetAgreementListByCompanyRequest\x1a\x44.moego.service.agreement.v1.GetAgreementContentListByCompanyResponse\x12\x98\x01\n\x19GetAgreementListByCompany\x12<.moego.service.agreement.v1.GetAgreementListByCompanyRequest\x1a=.moego.service.agreement.v1.GetAgreementListByCompanyResponse\x12w\n\x0eListAgreements\x12\x31.moego.service.agreement.v1.ListAgreementsRequest\x1a\x32.moego.service.agreement.v1.ListAgreementsResponse\x12\x8c\x01\n\x15ListUnsignedAgreement\x12\x38.moego.service.agreement.v1.ListUnsignedAgreementRequest\x1a\x39.moego.service.agreement.v1.ListUnsignedAgreementResponse\x12\xad\x01\n ListUnsignedAgreementByCustomers\x12\x43.moego.service.agreement.v1.ListUnsignedAgreementByCustomersRequest\x1a\x44.moego.service.agreement.v1.ListUnsignedAgreementByCustomersResponseB\x86\x01\n\"com.moego.idl.service.agreement.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/agreement/v1;agreementsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.agreement.v1.agreement_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.service.agreement.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/agreement/v1;agreementsvcpb'
  _globals['_GETAGREEMENTLISTREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_GETAGREEMENTLISTREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETAGREEMENTLISTREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_GETAGREEMENTLISTREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_INITAGREEMENTREQUEST'].fields_by_name['business_name']._loaded_options = None
  _globals['_INITAGREEMENTREQUEST'].fields_by_name['business_name']._serialized_options = b'\372B\005r\003\030\200\002'
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['signed_policy']._loaded_options = None
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['signed_policy']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['agreement_title']._loaded_options = None
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['agreement_title']._serialized_options = b'\372B\007r\005\020\001\030\200\002'
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['agreement_content']._loaded_options = None
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['agreement_content']._serialized_options = b'\372B\010r\006\020\001\030\200\200@'
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['sms_template']._loaded_options = None
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['sms_template']._serialized_options = b'\372B\010r\006\020\001\030\200\200\004'
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['email_template_title']._loaded_options = None
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['email_template_title']._serialized_options = b'\372B\007r\005\020\001\030\200\002'
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['email_template_body']._loaded_options = None
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['email_template_body']._serialized_options = b'\372B\010r\006\020\001\030\200\200\010'
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['business_name']._loaded_options = None
  _globals['_ADDAGREEMENTREQUEST'].fields_by_name['business_name']._serialized_options = b'\372B\005r\003\030\200\002'
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['signed_policy']._loaded_options = None
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['signed_policy']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['agreement_title']._loaded_options = None
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['agreement_title']._serialized_options = b'\372B\007r\005\020\001\030\200\002'
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['agreement_content']._loaded_options = None
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['agreement_content']._serialized_options = b'\372B\010r\006\020\001\030\200\200@'
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['sms_template']._loaded_options = None
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['sms_template']._serialized_options = b'\372B\010r\006\020\001\030\200\200\004'
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['email_template_title']._loaded_options = None
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['email_template_title']._serialized_options = b'\372B\007r\005\020\001\030\200\002'
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['email_template_body']._loaded_options = None
  _globals['_UPDATEAGREEMENTREQUEST'].fields_by_name['email_template_body']._serialized_options = b'\372B\010r\006\020\001\030\200\200\010'
  _globals['_UPDATESERVICETYPEREQUEST'].fields_by_name['service_type']._loaded_options = None
  _globals['_UPDATESERVICETYPEREQUEST'].fields_by_name['service_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETAGREEMENTSIGNSTATUSLISTREQUEST'].fields_by_name['service_type']._loaded_options = None
  _globals['_GETAGREEMENTSIGNSTATUSLISTREQUEST'].fields_by_name['service_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\020\222\001\r\020\364\003\030\001\"\004\"\002 \000(\001'
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['ids']._loaded_options = None
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['ids']._serialized_options = b'\372B\020\222\001\r\020\364\003\030\001\"\004\"\002 \000(\001'
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_LISTAGREEMENTSREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\020\222\001\r\020\364\003\030\001\"\004\"\002 \000(\001'
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\014\222\001\t\020\350\007\"\004\"\002 \000'
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\013\222\001\010\020d\"\004\"\002 \000'
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST'].fields_by_name['customer_ids']._loaded_options = None
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST'].fields_by_name['customer_ids']._serialized_options = b'\372B\013\222\001\010\020d\"\004\"\002 \000'
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST'].fields_by_name['service_types']._loaded_options = None
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST'].fields_by_name['service_types']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_CHECKAGREEMENTREQUEST']._serialized_start=286
  _globals['_CHECKAGREEMENTREQUEST']._serialized_end=358
  _globals['_GETAGREEMENTREQUEST']._serialized_start=360
  _globals['_GETAGREEMENTREQUEST']._serialized_end=481
  _globals['_DELETEAGREEMENTREQUEST']._serialized_start=483
  _globals['_DELETEAGREEMENTREQUEST']._serialized_end=556
  _globals['_DELETEAGREEMENTRESPONSE']._serialized_start=558
  _globals['_DELETEAGREEMENTRESPONSE']._serialized_end=607
  _globals['_CHECKAGREEMENTRESPONSE']._serialized_start=609
  _globals['_CHECKAGREEMENTRESPONSE']._serialized_end=675
  _globals['_GETAGREEMENTLISTREQUEST']._serialized_start=678
  _globals['_GETAGREEMENTLISTREQUEST']._serialized_end=899
  _globals['_INITAGREEMENTREQUEST']._serialized_start=902
  _globals['_INITAGREEMENTREQUEST']._serialized_end=1109
  _globals['_ADDAGREEMENTREQUEST']._serialized_start=1112
  _globals['_ADDAGREEMENTREQUEST']._serialized_end=1849
  _globals['_UPDATEAGREEMENTREQUEST']._serialized_start=1852
  _globals['_UPDATEAGREEMENTREQUEST']._serialized_end=2547
  _globals['_UPDATESERVICETYPEREQUEST']._serialized_start=2550
  _globals['_UPDATESERVICETYPEREQUEST']._serialized_end=2748
  _globals['_GETAGREEMENTLISTRESPONSE']._serialized_start=2751
  _globals['_GETAGREEMENTLISTRESPONSE']._serialized_end=2882
  _globals['_GETAGREEMENTCONTENTLISTRESPONSE']._serialized_start=2885
  _globals['_GETAGREEMENTCONTENTLISTRESPONSE']._serialized_end=3026
  _globals['_GETAGREEMENTSIGNSTATUSLISTREQUEST']._serialized_start=3029
  _globals['_GETAGREEMENTSIGNSTATUSLISTREQUEST']._serialized_end=3287
  _globals['_GETAGREEMENTSIGNSTATUSLISTRESPONSE']._serialized_start=3290
  _globals['_GETAGREEMENTSIGNSTATUSLISTRESPONSE']._serialized_end=3430
  _globals['_BATCHGETAGREEMENTUNSIGNEDAPPOINTMENTREQUEST']._serialized_start=3433
  _globals['_BATCHGETAGREEMENTUNSIGNEDAPPOINTMENTREQUEST']._serialized_end=3777
  _globals['_BATCHGETAGREEMENTUNSIGNEDAPPOINTMENTREQUEST_CUSTOMERWITHAPPOINTMENTID']._serialized_start=3678
  _globals['_BATCHGETAGREEMENTUNSIGNEDAPPOINTMENTREQUEST_CUSTOMERWITHAPPOINTMENTID']._serialized_end=3777
  _globals['_BATCHGETAGREEMENTUNSIGNEDAPPOINTMENTRESPONSE']._serialized_start=3779
  _globals['_BATCHGETAGREEMENTUNSIGNEDAPPOINTMENTRESPONSE']._serialized_end=3864
  _globals['_GETAGREEMENTCONTENTLISTBYCOMPANYRESPONSE']._serialized_start=3867
  _globals['_GETAGREEMENTCONTENTLISTBYCOMPANYRESPONSE']._serialized_end=4017
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST']._serialized_start=4020
  _globals['_GETAGREEMENTLISTBYCOMPANYREQUEST']._serialized_end=4313
  _globals['_GETAGREEMENTLISTBYCOMPANYRESPONSE']._serialized_start=4316
  _globals['_GETAGREEMENTLISTBYCOMPANYRESPONSE']._serialized_end=4456
  _globals['_LISTAGREEMENTSREQUEST']._serialized_start=4459
  _globals['_LISTAGREEMENTSREQUEST']._serialized_end=4849
  _globals['_LISTAGREEMENTSRESPONSE']._serialized_start=4852
  _globals['_LISTAGREEMENTSRESPONSE']._serialized_end=5039
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST']._serialized_start=5042
  _globals['_LISTUNSIGNEDAGREEMENTREQUEST']._serialized_end=5296
  _globals['_LISTUNSIGNEDAGREEMENTRESPONSE']._serialized_start=5298
  _globals['_LISTUNSIGNEDAGREEMENTRESPONSE']._serialized_end=5404
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST']._serialized_start=5407
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSREQUEST']._serialized_end=5659
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSRESPONSE']._serialized_start=5662
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSRESPONSE']._serialized_end=5990
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSRESPONSE_CUSTOMERAGREEMENTVIEW']._serialized_start=5849
  _globals['_LISTUNSIGNEDAGREEMENTBYCUSTOMERSRESPONSE_CUSTOMERAGREEMENTVIEW']._serialized_end=5990
  _globals['_AGREEMENTSERVICE']._serialized_start=5993
  _globals['_AGREEMENTSERVICE']._serialized_end=8213
# @@protoc_insertion_point(module_scope)
