from moego.models.agreement.v1 import agreement_enums_pb2 as _agreement_enums_pb2
from moego.models.agreement.v1 import agreement_models_pb2 as _agreement_models_pb2
from moego.utils.v1 import status_messages_pb2 as _status_messages_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CheckAgreementRequest(_message.Message):
    __slots__ = ("id", "business_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class GetAgreementRequest(_message.Message):
    __slots__ = ("id", "business_id", "company_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    company_id: int
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., company_id: _Optional[int] = ...) -> None: ...

class DeleteAgreementRequest(_message.Message):
    __slots__ = ("id", "business_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class DeleteAgreementResponse(_message.Message):
    __slots__ = ("number",)
    NUMBER_FIELD_NUMBER: _ClassVar[int]
    number: int
    def __init__(self, number: _Optional[int] = ...) -> None: ...

class CheckAgreementResponse(_message.Message):
    __slots__ = ("result", "msg")
    RESULT_FIELD_NUMBER: _ClassVar[int]
    MSG_FIELD_NUMBER: _ClassVar[int]
    result: bool
    msg: str
    def __init__(self, result: bool = ..., msg: _Optional[str] = ...) -> None: ...

class GetAgreementListRequest(_message.Message):
    __slots__ = ("business_id", "ids", "status", "service_types")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    IDS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    ids: _containers.RepeatedScalarFieldContainer[int]
    status: _status_messages_pb2.Status
    service_types: int
    def __init__(self, business_id: _Optional[int] = ..., ids: _Optional[_Iterable[int]] = ..., status: _Optional[_Union[_status_messages_pb2.Status, str]] = ..., service_types: _Optional[int] = ...) -> None: ...

class InitAgreementRequest(_message.Message):
    __slots__ = ("business_id", "business_name", "creator_id", "company_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_NAME_FIELD_NUMBER: _ClassVar[int]
    CREATOR_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    business_name: str
    creator_id: int
    company_id: int
    def __init__(self, business_id: _Optional[int] = ..., business_name: _Optional[str] = ..., creator_id: _Optional[int] = ..., company_id: _Optional[int] = ...) -> None: ...

class AddAgreementRequest(_message.Message):
    __slots__ = ("business_id", "creator_id", "signed_policy", "service_types", "agreement_title", "agreement_content", "sms_template", "email_template_title", "email_template_body", "business_name", "company_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CREATOR_ID_FIELD_NUMBER: _ClassVar[int]
    SIGNED_POLICY_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_TITLE_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_CONTENT_FIELD_NUMBER: _ClassVar[int]
    SMS_TEMPLATE_FIELD_NUMBER: _ClassVar[int]
    EMAIL_TEMPLATE_TITLE_FIELD_NUMBER: _ClassVar[int]
    EMAIL_TEMPLATE_BODY_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_NAME_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    creator_id: int
    signed_policy: _agreement_enums_pb2.SignedPolicy
    service_types: int
    agreement_title: str
    agreement_content: str
    sms_template: str
    email_template_title: str
    email_template_body: str
    business_name: str
    company_id: int
    def __init__(self, business_id: _Optional[int] = ..., creator_id: _Optional[int] = ..., signed_policy: _Optional[_Union[_agreement_enums_pb2.SignedPolicy, str]] = ..., service_types: _Optional[int] = ..., agreement_title: _Optional[str] = ..., agreement_content: _Optional[str] = ..., sms_template: _Optional[str] = ..., email_template_title: _Optional[str] = ..., email_template_body: _Optional[str] = ..., business_name: _Optional[str] = ..., company_id: _Optional[int] = ...) -> None: ...

class UpdateAgreementRequest(_message.Message):
    __slots__ = ("id", "business_id", "signed_policy", "agreement_title", "agreement_content", "sms_template", "email_template_title", "email_template_body", "update_last_required_time")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    SIGNED_POLICY_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_TITLE_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_CONTENT_FIELD_NUMBER: _ClassVar[int]
    SMS_TEMPLATE_FIELD_NUMBER: _ClassVar[int]
    EMAIL_TEMPLATE_TITLE_FIELD_NUMBER: _ClassVar[int]
    EMAIL_TEMPLATE_BODY_FIELD_NUMBER: _ClassVar[int]
    UPDATE_LAST_REQUIRED_TIME_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    signed_policy: _agreement_enums_pb2.SignedPolicy
    agreement_title: str
    agreement_content: str
    sms_template: str
    email_template_title: str
    email_template_body: str
    update_last_required_time: bool
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., signed_policy: _Optional[_Union[_agreement_enums_pb2.SignedPolicy, str]] = ..., agreement_title: _Optional[str] = ..., agreement_content: _Optional[str] = ..., sms_template: _Optional[str] = ..., email_template_title: _Optional[str] = ..., email_template_body: _Optional[str] = ..., update_last_required_time: bool = ...) -> None: ...

class UpdateServiceTypeRequest(_message.Message):
    __slots__ = ("id", "business_id", "service_type", "set_or_cancel")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    SET_OR_CANCEL_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    service_type: _agreement_enums_pb2.ServiceType
    set_or_cancel: bool
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., service_type: _Optional[_Union[_agreement_enums_pb2.ServiceType, str]] = ..., set_or_cancel: bool = ...) -> None: ...

class GetAgreementListResponse(_message.Message):
    __slots__ = ("agreement_simple_view",)
    AGREEMENT_SIMPLE_VIEW_FIELD_NUMBER: _ClassVar[int]
    agreement_simple_view: _containers.RepeatedCompositeFieldContainer[_agreement_models_pb2.AgreementModelSimpleView]
    def __init__(self, agreement_simple_view: _Optional[_Iterable[_Union[_agreement_models_pb2.AgreementModelSimpleView, _Mapping]]] = ...) -> None: ...

class GetAgreementContentListResponse(_message.Message):
    __slots__ = ("agreement_content_view",)
    AGREEMENT_CONTENT_VIEW_FIELD_NUMBER: _ClassVar[int]
    agreement_content_view: _containers.RepeatedCompositeFieldContainer[_agreement_models_pb2.AgreementModelContentView]
    def __init__(self, agreement_content_view: _Optional[_Iterable[_Union[_agreement_models_pb2.AgreementModelContentView, _Mapping]]] = ...) -> None: ...

class GetAgreementSignStatusListRequest(_message.Message):
    __slots__ = ("business_id", "customer_id", "target_id", "service_type")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    TARGET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_id: int
    target_id: int
    service_type: _agreement_enums_pb2.ServiceType
    def __init__(self, business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., target_id: _Optional[int] = ..., service_type: _Optional[_Union[_agreement_enums_pb2.ServiceType, str]] = ...) -> None: ...

class GetAgreementSignStatusListResponse(_message.Message):
    __slots__ = ("agreement_status_view",)
    AGREEMENT_STATUS_VIEW_FIELD_NUMBER: _ClassVar[int]
    agreement_status_view: _containers.RepeatedCompositeFieldContainer[_agreement_models_pb2.AgreementSignStatusView]
    def __init__(self, agreement_status_view: _Optional[_Iterable[_Union[_agreement_models_pb2.AgreementSignStatusView, _Mapping]]] = ...) -> None: ...

class BatchGetAgreementUnsignedAppointmentRequest(_message.Message):
    __slots__ = ("business_id", "customer_with_appointment_id")
    class CustomerWithAppointmentId(_message.Message):
        __slots__ = ("customer_id", "appointment_id")
        CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
        APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
        customer_id: int
        appointment_id: int
        def __init__(self, customer_id: _Optional[int] = ..., appointment_id: _Optional[int] = ...) -> None: ...
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_WITH_APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_with_appointment_id: _containers.RepeatedCompositeFieldContainer[BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentId]
    def __init__(self, business_id: _Optional[int] = ..., customer_with_appointment_id: _Optional[_Iterable[_Union[BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentId, _Mapping]]] = ...) -> None: ...

class BatchGetAgreementUnsignedAppointmentResponse(_message.Message):
    __slots__ = ("appointment_id",)
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, appointment_id: _Optional[_Iterable[int]] = ...) -> None: ...

class GetAgreementContentListByCompanyResponse(_message.Message):
    __slots__ = ("agreement_content_view",)
    AGREEMENT_CONTENT_VIEW_FIELD_NUMBER: _ClassVar[int]
    agreement_content_view: _containers.RepeatedCompositeFieldContainer[_agreement_models_pb2.AgreementModelContentView]
    def __init__(self, agreement_content_view: _Optional[_Iterable[_Union[_agreement_models_pb2.AgreementModelContentView, _Mapping]]] = ...) -> None: ...

class GetAgreementListByCompanyRequest(_message.Message):
    __slots__ = ("company_id", "ids", "status", "service_types", "business_ids")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    IDS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    ids: _containers.RepeatedScalarFieldContainer[int]
    status: _status_messages_pb2.Status
    service_types: int
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, company_id: _Optional[int] = ..., ids: _Optional[_Iterable[int]] = ..., status: _Optional[_Union[_status_messages_pb2.Status, str]] = ..., service_types: _Optional[int] = ..., business_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class GetAgreementListByCompanyResponse(_message.Message):
    __slots__ = ("agreement_simple_view",)
    AGREEMENT_SIMPLE_VIEW_FIELD_NUMBER: _ClassVar[int]
    agreement_simple_view: _containers.RepeatedCompositeFieldContainer[_agreement_models_pb2.AgreementModelSimpleView]
    def __init__(self, agreement_simple_view: _Optional[_Iterable[_Union[_agreement_models_pb2.AgreementModelSimpleView, _Mapping]]] = ...) -> None: ...

class ListAgreementsRequest(_message.Message):
    __slots__ = ("pagination", "company_id", "ids", "status", "service_types", "business_ids")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    IDS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    company_id: int
    ids: _containers.RepeatedScalarFieldContainer[int]
    status: _status_messages_pb2.Status
    service_types: int
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., company_id: _Optional[int] = ..., ids: _Optional[_Iterable[int]] = ..., status: _Optional[_Union[_status_messages_pb2.Status, str]] = ..., service_types: _Optional[int] = ..., business_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class ListAgreementsResponse(_message.Message):
    __slots__ = ("pagination", "agreements")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    AGREEMENTS_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationResponse
    agreements: _containers.RepeatedCompositeFieldContainer[_agreement_models_pb2.AgreementModel]
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., agreements: _Optional[_Iterable[_Union[_agreement_models_pb2.AgreementModel, _Mapping]]] = ...) -> None: ...

class ListUnsignedAgreementRequest(_message.Message):
    __slots__ = ("company_id", "business_ids", "customer_id", "service_types")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    customer_id: int
    service_types: int
    def __init__(self, company_id: _Optional[int] = ..., business_ids: _Optional[_Iterable[int]] = ..., customer_id: _Optional[int] = ..., service_types: _Optional[int] = ...) -> None: ...

class ListUnsignedAgreementResponse(_message.Message):
    __slots__ = ("agreements",)
    AGREEMENTS_FIELD_NUMBER: _ClassVar[int]
    agreements: _containers.RepeatedCompositeFieldContainer[_agreement_models_pb2.AgreementModel]
    def __init__(self, agreements: _Optional[_Iterable[_Union[_agreement_models_pb2.AgreementModel, _Mapping]]] = ...) -> None: ...

class ListUnsignedAgreementByCustomersRequest(_message.Message):
    __slots__ = ("company_id", "business_ids", "customer_ids", "service_types")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPES_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    customer_ids: _containers.RepeatedScalarFieldContainer[int]
    service_types: int
    def __init__(self, company_id: _Optional[int] = ..., business_ids: _Optional[_Iterable[int]] = ..., customer_ids: _Optional[_Iterable[int]] = ..., service_types: _Optional[int] = ...) -> None: ...

class ListUnsignedAgreementByCustomersResponse(_message.Message):
    __slots__ = ("customer_agreements",)
    class CustomerAgreementView(_message.Message):
        __slots__ = ("customer_id", "agreements")
        CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
        AGREEMENTS_FIELD_NUMBER: _ClassVar[int]
        customer_id: int
        agreements: _containers.RepeatedCompositeFieldContainer[_agreement_models_pb2.AgreementModelSimpleView]
        def __init__(self, customer_id: _Optional[int] = ..., agreements: _Optional[_Iterable[_Union[_agreement_models_pb2.AgreementModelSimpleView, _Mapping]]] = ...) -> None: ...
    CUSTOMER_AGREEMENTS_FIELD_NUMBER: _ClassVar[int]
    customer_agreements: _containers.RepeatedCompositeFieldContainer[ListUnsignedAgreementByCustomersResponse.CustomerAgreementView]
    def __init__(self, customer_agreements: _Optional[_Iterable[_Union[ListUnsignedAgreementByCustomersResponse.CustomerAgreementView, _Mapping]]] = ...) -> None: ...
