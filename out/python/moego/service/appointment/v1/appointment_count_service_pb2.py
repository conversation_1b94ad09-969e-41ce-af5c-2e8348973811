# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/appointment/v1/appointment_count_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/appointment/v1/appointment_count_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.appointment.v1 import appointment_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__enums__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n<moego/service/appointment/v1/appointment_count_service.proto\x12\x1cmoego.service.appointment.v1\x1a\x33moego/models/appointment/v1/appointment_enums.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x17validate/validate.proto\"\xa7\x02\n#GetAppointmentCountByServiceRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12&\n\nservice_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12\\\n\x10\x61ppointment_type\x18\x04 \x01(\x0e\x32,.moego.models.appointment.v1.AppointmentTypeH\x01R\x0f\x61ppointmentType\x88\x01\x01\x42\x0e\n\x0c_business_idB\x13\n\x11_appointment_type\"<\n$GetAppointmentCountByServiceResponse\x12\x14\n\x05\x63ount\x18\x01 \x01(\x03R\x05\x63ount\"\x8b\x03\n GetAppointmentCountByDateRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12\x45\n\x0estart_date_gte\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\x0cstartDateGte\x88\x01\x01\x12?\n\x0b\x65nd_date_lt\x18\x04 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x02R\tendDateLt\x88\x01\x01\x12U\n\x11service_item_type\x18\x05 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemTypeB\x0e\n\x0c_business_idB\x11\n\x0f_start_date_gteB\x0e\n\x0c_end_date_lt\"k\n!GetAppointmentCountByDateResponse\x12\x14\n\x05\x63ount\x18\x01 \x01(\x05R\x05\x63ount\x12\x30\n\x14\x65valuation_pet_count\x18\x02 \x01(\x05R\x12\x65valuationPetCount\"{\n$BatchGetTotalAppointmentCountRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12+\n\x0c\x63ustomer_ids\x18\x02 \x03(\x03\x42\x08\xfa\x42\x05\x92\x01\x02\x10\x64R\x0b\x63ustomerIds\"\xc7\x01\n%BatchGetTotalAppointmentCountResponse\x12\x64\n\x05\x63ount\x18\x01 \x03(\x0b\x32N.moego.service.appointment.v1.BatchGetTotalAppointmentCountResponse.CountEntryR\x05\x63ount\x1a\x38\n\nCountEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x05R\x05value:\x02\x38\x01\"\xaf\x01\n\'BatchGetUpcomingAppointmentCountRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12+\n\x0c\x63ustomer_ids\x18\x02 \x03(\x03\x42\x08\xfa\x42\x05\x92\x01\x02\x10\x64R\x0b\x63ustomerIds\x12/\n\x0e\x65valuation_ids\x18\x03 \x03(\x03\x42\x08\xfa\x42\x05\x92\x01\x02\x10\x64R\revaluationIds\"\x9a\x03\n(BatchGetUpcomingAppointmentCountResponse\x12g\n\x05\x63ount\x18\x01 \x03(\x0b\x32Q.moego.service.appointment.v1.BatchGetUpcomingAppointmentCountResponse.CountEntryR\x05\x63ount\x12\x86\x01\n\x10\x65valuation_count\x18\x02 \x03(\x0b\x32[.moego.service.appointment.v1.BatchGetUpcomingAppointmentCountResponse.EvaluationCountEntryR\x0f\x65valuationCount\x1a\x38\n\nCountEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x05R\x05value:\x02\x38\x01\x1a\x42\n\x14\x45valuationCountEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x05R\x05value:\x02\x38\x01\x32\xbf\x05\n\x17\x41ppointmentCountService\x12\xa5\x01\n\x1cGetAppointmentCountByService\x12\x41.moego.service.appointment.v1.GetAppointmentCountByServiceRequest\x1a\x42.moego.service.appointment.v1.GetAppointmentCountByServiceResponse\x12\x9c\x01\n\x19GetAppointmentCountByDate\x12>.moego.service.appointment.v1.GetAppointmentCountByDateRequest\x1a?.moego.service.appointment.v1.GetAppointmentCountByDateResponse\x12\xa8\x01\n\x1d\x42\x61tchGetTotalAppointmentCount\x12\x42.moego.service.appointment.v1.BatchGetTotalAppointmentCountRequest\x1a\x43.moego.service.appointment.v1.BatchGetTotalAppointmentCountResponse\x12\xb1\x01\n BatchGetUpcomingAppointmentCount\x12\x45.moego.service.appointment.v1.BatchGetUpcomingAppointmentCountRequest\x1a\x46.moego.service.appointment.v1.BatchGetUpcomingAppointmentCountResponseB\x8c\x01\n$com.moego.idl.service.appointment.v1P\x01Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.appointment.v1.appointment_count_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.service.appointment.v1P\001Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb'
  _globals['_GETAPPOINTMENTCOUNTBYSERVICEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETAPPOINTMENTCOUNTBYSERVICEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPOINTMENTCOUNTBYSERVICEREQUEST'].fields_by_name['service_id']._loaded_options = None
  _globals['_GETAPPOINTMENTCOUNTBYSERVICEREQUEST'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPOINTMENTCOUNTBYSERVICEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETAPPOINTMENTCOUNTBYSERVICEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST'].fields_by_name['start_date_gte']._loaded_options = None
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST'].fields_by_name['start_date_gte']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST'].fields_by_name['end_date_lt']._loaded_options = None
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST'].fields_by_name['end_date_lt']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTREQUEST'].fields_by_name['customer_ids']._loaded_options = None
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTREQUEST'].fields_by_name['customer_ids']._serialized_options = b'\372B\005\222\001\002\020d'
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTRESPONSE_COUNTENTRY']._loaded_options = None
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTRESPONSE_COUNTENTRY']._serialized_options = b'8\001'
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTREQUEST'].fields_by_name['customer_ids']._loaded_options = None
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTREQUEST'].fields_by_name['customer_ids']._serialized_options = b'\372B\005\222\001\002\020d'
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTREQUEST'].fields_by_name['evaluation_ids']._loaded_options = None
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTREQUEST'].fields_by_name['evaluation_ids']._serialized_options = b'\372B\005\222\001\002\020d'
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE_COUNTENTRY']._loaded_options = None
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE_COUNTENTRY']._serialized_options = b'8\001'
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE_EVALUATIONCOUNTENTRY']._loaded_options = None
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE_EVALUATIONCOUNTENTRY']._serialized_options = b'8\001'
  _globals['_GETAPPOINTMENTCOUNTBYSERVICEREQUEST']._serialized_start=218
  _globals['_GETAPPOINTMENTCOUNTBYSERVICEREQUEST']._serialized_end=513
  _globals['_GETAPPOINTMENTCOUNTBYSERVICERESPONSE']._serialized_start=515
  _globals['_GETAPPOINTMENTCOUNTBYSERVICERESPONSE']._serialized_end=575
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST']._serialized_start=578
  _globals['_GETAPPOINTMENTCOUNTBYDATEREQUEST']._serialized_end=973
  _globals['_GETAPPOINTMENTCOUNTBYDATERESPONSE']._serialized_start=975
  _globals['_GETAPPOINTMENTCOUNTBYDATERESPONSE']._serialized_end=1082
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTREQUEST']._serialized_start=1084
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTREQUEST']._serialized_end=1207
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTRESPONSE']._serialized_start=1210
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTRESPONSE']._serialized_end=1409
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTRESPONSE_COUNTENTRY']._serialized_start=1353
  _globals['_BATCHGETTOTALAPPOINTMENTCOUNTRESPONSE_COUNTENTRY']._serialized_end=1409
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTREQUEST']._serialized_start=1412
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTREQUEST']._serialized_end=1587
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE']._serialized_start=1590
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE']._serialized_end=2000
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE_COUNTENTRY']._serialized_start=1353
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE_COUNTENTRY']._serialized_end=1409
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE_EVALUATIONCOUNTENTRY']._serialized_start=1934
  _globals['_BATCHGETUPCOMINGAPPOINTMENTCOUNTRESPONSE_EVALUATIONCOUNTENTRY']._serialized_end=2000
  _globals['_APPOINTMENTCOUNTSERVICE']._serialized_start=2003
  _globals['_APPOINTMENTCOUNTSERVICE']._serialized_end=2706
# @@protoc_insertion_point(module_scope)
