from moego.models.appointment.v1 import appointment_enums_pb2 as _appointment_enums_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetAppointmentCountByServiceRequest(_message.Message):
    __slots__ = ("company_id", "service_id", "business_id", "appointment_type")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_TYPE_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    service_id: int
    business_id: int
    appointment_type: _appointment_enums_pb2.AppointmentType
    def __init__(self, company_id: _Optional[int] = ..., service_id: _Optional[int] = ..., business_id: _Optional[int] = ..., appointment_type: _Optional[_Union[_appointment_enums_pb2.AppointmentType, str]] = ...) -> None: ...

class GetAppointmentCountByServiceResponse(_message.Message):
    __slots__ = ("count",)
    COUNT_FIELD_NUMBER: _ClassVar[int]
    count: int
    def __init__(self, count: _Optional[int] = ...) -> None: ...

class GetAppointmentCountByDateRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "start_date_gte", "end_date_lt", "service_item_type")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_GTE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_LT_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    start_date_gte: str
    end_date_lt: str
    service_item_type: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., start_date_gte: _Optional[str] = ..., end_date_lt: _Optional[str] = ..., service_item_type: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ...) -> None: ...

class GetAppointmentCountByDateResponse(_message.Message):
    __slots__ = ("count", "evaluation_pet_count")
    COUNT_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_PET_COUNT_FIELD_NUMBER: _ClassVar[int]
    count: int
    evaluation_pet_count: int
    def __init__(self, count: _Optional[int] = ..., evaluation_pet_count: _Optional[int] = ...) -> None: ...

class BatchGetTotalAppointmentCountRequest(_message.Message):
    __slots__ = ("company_id", "customer_ids")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    customer_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, company_id: _Optional[int] = ..., customer_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class BatchGetTotalAppointmentCountResponse(_message.Message):
    __slots__ = ("count",)
    class CountEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: int
        def __init__(self, key: _Optional[int] = ..., value: _Optional[int] = ...) -> None: ...
    COUNT_FIELD_NUMBER: _ClassVar[int]
    count: _containers.ScalarMap[int, int]
    def __init__(self, count: _Optional[_Mapping[int, int]] = ...) -> None: ...

class BatchGetUpcomingAppointmentCountRequest(_message.Message):
    __slots__ = ("company_id", "customer_ids", "evaluation_ids")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_IDS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    customer_ids: _containers.RepeatedScalarFieldContainer[int]
    evaluation_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, company_id: _Optional[int] = ..., customer_ids: _Optional[_Iterable[int]] = ..., evaluation_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class BatchGetUpcomingAppointmentCountResponse(_message.Message):
    __slots__ = ("count", "evaluation_count")
    class CountEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: int
        def __init__(self, key: _Optional[int] = ..., value: _Optional[int] = ...) -> None: ...
    class EvaluationCountEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: int
        def __init__(self, key: _Optional[int] = ..., value: _Optional[int] = ...) -> None: ...
    COUNT_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_COUNT_FIELD_NUMBER: _ClassVar[int]
    count: _containers.ScalarMap[int, int]
    evaluation_count: _containers.ScalarMap[int, int]
    def __init__(self, count: _Optional[_Mapping[int, int]] = ..., evaluation_count: _Optional[_Mapping[int, int]] = ...) -> None: ...
