# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/appointment/v1/check_in_out_alert_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/appointment/v1/check_in_out_alert_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.appointment.v1 import check_in_out_alert_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_check__in__out__alert__defs__pb2
from moego.models.appointment.v1 import check_in_out_alert_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_check__in__out__alert__models__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=moego/service/appointment/v1/check_in_out_alert_service.proto\x12\x1cmoego.service.appointment.v1\x1a\x39moego/models/appointment/v1/check_in_out_alert_defs.proto\x1a;moego/models/appointment/v1/check_in_out_alert_models.proto\"8\n\x17GetAlertSettingsRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\"l\n\x18GetAlertSettingsResponse\x12P\n\x08settings\x18\x01 \x01(\x0b\x32\x34.moego.models.appointment.v1.CheckInOutAlertSettingsR\x08settings\"\xfa\x01\n\x18SaveAlertSettingsRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12]\n\x11\x63heck_in_settings\x18\x02 \x01(\x0b\x32\x31.moego.models.appointment.v1.CheckInAlertSettingsR\x0f\x63heckInSettings\x12`\n\x12\x63heck_out_settings\x18\x03 \x01(\x0b\x32\x32.moego.models.appointment.v1.CheckOutAlertSettingsR\x10\x63heckOutSettings\"m\n\x19SaveAlertSettingsResponse\x12P\n\x08settings\x18\x01 \x01(\x0b\x32\x34.moego.models.appointment.v1.CheckInOutAlertSettingsR\x08settings\"\x83\x01\n\x1aGetAlertsForCheckInRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12%\n\x0e\x61ppointment_id\x18\x03 \x01(\x03R\rappointmentId\"l\n\x1bGetAlertsForCheckInResponse\x12\x43\n\x05\x61lert\x18\x01 \x01(\x0b\x32(.moego.models.appointment.v1.AlertDetailH\x00R\x05\x61lert\x88\x01\x01\x42\x08\n\x06_alert\"\x84\x01\n\x1bGetAlertsForCheckOutRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12%\n\x0e\x61ppointment_id\x18\x03 \x01(\x03R\rappointmentId\"m\n\x1cGetAlertsForCheckOutResponse\x12\x43\n\x05\x61lert\x18\x01 \x01(\x0b\x32(.moego.models.appointment.v1.AlertDetailH\x00R\x05\x61lert\x88\x01\x01\x42\x08\n\x06_alert\"\xb2\x01\n\x1f\x42\x61tchGetAlertsForCheckInRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12O\n\x0b\x63lient_pets\x18\x03 \x03(\x0b\x32..moego.models.appointment.v1.ClientPetsMappingR\nclientPets\"d\n BatchGetAlertsForCheckInResponse\x12@\n\x06\x61lerts\x18\x01 \x03(\x0b\x32(.moego.models.appointment.v1.AlertDetailR\x06\x61lerts2\xdc\x05\n\x16\x43heckInOutAlertService\x12\x81\x01\n\x10GetAlertSettings\x12\x35.moego.service.appointment.v1.GetAlertSettingsRequest\x1a\x36.moego.service.appointment.v1.GetAlertSettingsResponse\x12\x84\x01\n\x11SaveAlertSettings\x12\x36.moego.service.appointment.v1.SaveAlertSettingsRequest\x1a\x37.moego.service.appointment.v1.SaveAlertSettingsResponse\x12\x99\x01\n\x18\x42\x61tchGetAlertsForCheckIn\x12=.moego.service.appointment.v1.BatchGetAlertsForCheckInRequest\x1a>.moego.service.appointment.v1.BatchGetAlertsForCheckInResponse\x12\x8a\x01\n\x13GetAlertsForCheckIn\x12\x38.moego.service.appointment.v1.GetAlertsForCheckInRequest\x1a\x39.moego.service.appointment.v1.GetAlertsForCheckInResponse\x12\x8d\x01\n\x14GetAlertsForCheckOut\x12\x39.moego.service.appointment.v1.GetAlertsForCheckOutRequest\x1a:.moego.service.appointment.v1.GetAlertsForCheckOutResponseB\x8c\x01\n$com.moego.idl.service.appointment.v1P\x01Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.appointment.v1.check_in_out_alert_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.service.appointment.v1P\001Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb'
  _globals['_GETALERTSETTINGSREQUEST']._serialized_start=215
  _globals['_GETALERTSETTINGSREQUEST']._serialized_end=271
  _globals['_GETALERTSETTINGSRESPONSE']._serialized_start=273
  _globals['_GETALERTSETTINGSRESPONSE']._serialized_end=381
  _globals['_SAVEALERTSETTINGSREQUEST']._serialized_start=384
  _globals['_SAVEALERTSETTINGSREQUEST']._serialized_end=634
  _globals['_SAVEALERTSETTINGSRESPONSE']._serialized_start=636
  _globals['_SAVEALERTSETTINGSRESPONSE']._serialized_end=745
  _globals['_GETALERTSFORCHECKINREQUEST']._serialized_start=748
  _globals['_GETALERTSFORCHECKINREQUEST']._serialized_end=879
  _globals['_GETALERTSFORCHECKINRESPONSE']._serialized_start=881
  _globals['_GETALERTSFORCHECKINRESPONSE']._serialized_end=989
  _globals['_GETALERTSFORCHECKOUTREQUEST']._serialized_start=992
  _globals['_GETALERTSFORCHECKOUTREQUEST']._serialized_end=1124
  _globals['_GETALERTSFORCHECKOUTRESPONSE']._serialized_start=1126
  _globals['_GETALERTSFORCHECKOUTRESPONSE']._serialized_end=1235
  _globals['_BATCHGETALERTSFORCHECKINREQUEST']._serialized_start=1238
  _globals['_BATCHGETALERTSFORCHECKINREQUEST']._serialized_end=1416
  _globals['_BATCHGETALERTSFORCHECKINRESPONSE']._serialized_start=1418
  _globals['_BATCHGETALERTSFORCHECKINRESPONSE']._serialized_end=1518
  _globals['_CHECKINOUTALERTSERVICE']._serialized_start=1521
  _globals['_CHECKINOUTALERTSERVICE']._serialized_end=2253
# @@protoc_insertion_point(module_scope)
