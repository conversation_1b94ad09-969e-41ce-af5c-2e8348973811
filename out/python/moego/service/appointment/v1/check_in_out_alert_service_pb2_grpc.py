# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.appointment.v1 import check_in_out_alert_service_pb2 as moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2


class CheckInOutAlertServiceStub(object):
    """CheckInOutAlertService
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetAlertSettings = channel.unary_unary(
                '/moego.service.appointment.v1.CheckInOutAlertService/GetAlertSettings',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertSettingsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertSettingsResponse.FromString,
                _registered_method=True)
        self.SaveAlertSettings = channel.unary_unary(
                '/moego.service.appointment.v1.CheckInOutAlertService/SaveAlertSettings',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.SaveAlertSettingsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.SaveAlertSettingsResponse.FromString,
                _registered_method=True)
        self.BatchGetAlertsForCheckIn = channel.unary_unary(
                '/moego.service.appointment.v1.CheckInOutAlertService/BatchGetAlertsForCheckIn',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.BatchGetAlertsForCheckInRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.BatchGetAlertsForCheckInResponse.FromString,
                _registered_method=True)
        self.GetAlertsForCheckIn = channel.unary_unary(
                '/moego.service.appointment.v1.CheckInOutAlertService/GetAlertsForCheckIn',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckInRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckInResponse.FromString,
                _registered_method=True)
        self.GetAlertsForCheckOut = channel.unary_unary(
                '/moego.service.appointment.v1.CheckInOutAlertService/GetAlertsForCheckOut',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckOutRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckOutResponse.FromString,
                _registered_method=True)


class CheckInOutAlertServiceServicer(object):
    """CheckInOutAlertService
    """

    def GetAlertSettings(self, request, context):
        """get check-in-out alter settings
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveAlertSettings(self, request, context):
        """save check-in-out alter settings
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchGetAlertsForCheckIn(self, request, context):
        """batch get alter detail for check in
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAlertsForCheckIn(self, request, context):
        """get alter detail for check in
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAlertsForCheckOut(self, request, context):
        """get alter detail for check out
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CheckInOutAlertServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetAlertSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAlertSettings,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertSettingsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertSettingsResponse.SerializeToString,
            ),
            'SaveAlertSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveAlertSettings,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.SaveAlertSettingsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.SaveAlertSettingsResponse.SerializeToString,
            ),
            'BatchGetAlertsForCheckIn': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchGetAlertsForCheckIn,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.BatchGetAlertsForCheckInRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.BatchGetAlertsForCheckInResponse.SerializeToString,
            ),
            'GetAlertsForCheckIn': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAlertsForCheckIn,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckInRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckInResponse.SerializeToString,
            ),
            'GetAlertsForCheckOut': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAlertsForCheckOut,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckOutRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckOutResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.appointment.v1.CheckInOutAlertService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.appointment.v1.CheckInOutAlertService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class CheckInOutAlertService(object):
    """CheckInOutAlertService
    """

    @staticmethod
    def GetAlertSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.CheckInOutAlertService/GetAlertSettings',
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertSettingsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertSettingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SaveAlertSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.CheckInOutAlertService/SaveAlertSettings',
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.SaveAlertSettingsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.SaveAlertSettingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchGetAlertsForCheckIn(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.CheckInOutAlertService/BatchGetAlertsForCheckIn',
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.BatchGetAlertsForCheckInRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.BatchGetAlertsForCheckInResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAlertsForCheckIn(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.CheckInOutAlertService/GetAlertsForCheckIn',
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckInRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckInResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAlertsForCheckOut(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.CheckInOutAlertService/GetAlertsForCheckOut',
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckOutRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_check__in__out__alert__service__pb2.GetAlertsForCheckOutResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
