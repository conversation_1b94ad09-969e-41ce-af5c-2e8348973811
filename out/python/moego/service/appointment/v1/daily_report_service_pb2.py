# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/appointment/v1/daily_report_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/appointment/v1/daily_report_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.models.appointment.v1 import daily_report_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_daily__report__defs__pb2
from moego.models.appointment.v1 import daily_report_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_daily__report__enums__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7moego/service/appointment/v1/daily_report_service.proto\x12\x1cmoego.service.appointment.v1\x1a\x16google/type/date.proto\x1a\x33moego/models/appointment/v1/daily_report_defs.proto\x1a\x34moego/models/appointment/v1/daily_report_enums.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xff\x01\n\x1bGetDailyReportConfigRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12>\n\x0cservice_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0bserviceDate\x12&\n\ncompany_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\"\xb5\x01\n\x1cGetDailyReportConfigResponse\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12>\n\x06report\x18\x02 \x01(\x0b\x32&.moego.models.appointment.v1.ReportDefR\x06report\x12\x45\n\x06status\x18\x03 \x01(\x0e\x32-.moego.models.appointment.v1.ReportCardStatusR\x06status\"\xef\x01\n\x1cListDailyReportConfigRequest\x12:\n\x0f\x61ppointment_ids\x18\x01 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\xe8\x07\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\x12\x41\n\x0cservice_date\x18\x02 \x03(\x0b\x32\x11.google.type.DateB\x0b\xfa\x42\x08\x92\x01\x05\x08\x01\x10\xe8\x07R\x0bserviceDate\x12&\n\ncompany_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\"y\n\x1dListDailyReportConfigResponse\x12X\n\x0ereport_configs\x18\x01 \x03(\x0b\x32\x31.moego.models.appointment.v1.DailyReportConfigDefR\rreportConfigs\"\xef\x01\n\x1fGetDailyReportSentResultRequest\x12:\n\x0f\x61ppointment_ids\x18\x01 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x08\x01\x10\xe8\x07\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\x12>\n\x0cservice_date\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0bserviceDate\x12&\n\ncompany_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\"q\n GetDailyReportSentResultResponse\x12M\n\x0csent_results\x18\x01 \x03(\x0b\x32*.moego.models.appointment.v1.SentResultDefR\x0bsentResults\"\x9a\x03\n\x1eUpsertDailyReportConfigRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12(\n\x0b\x63ustomer_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\ncustomerId\x12>\n\x0cservice_date\x18\x04 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0bserviceDate\x12H\n\x06report\x18\x05 \x01(\x0b\x32&.moego.models.appointment.v1.ReportDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x06report\x12&\n\ncompany_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\"\n\x08staff_id\x18\x08 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\"E\n\x1fUpsertDailyReportConfigResponse\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04uuid\x18\x02 \x01(\tR\x04uuid\"\xc4\x01\n GetDailyReportSentHistoryRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12&\n\ncompany_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\"\x88\x01\n!GetDailyReportSentHistoryResponse\x12\x63\n\x14sent_history_records\x18\x01 \x03(\x0b\x32\x31.moego.models.appointment.v1.SentHistoryRecordDefR\x12sentHistoryRecords\"?\n GetDailyReportForCustomerRequest\x12\x1b\n\x04uuid\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32R\x04uuid\"\xf0\x01\n!GetDailyReportForCustomerResponse\x12>\n\x06report\x18\x01 \x01(\x0b\x32&.moego.models.appointment.v1.ReportDefR\x06report\x12\x34\n\x0cservice_date\x18\x02 \x01(\x0b\x32\x11.google.type.DateR\x0bserviceDate\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12\x1f\n\x0b\x62usiness_id\x18\x04 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x05 \x01(\x03R\tcompanyId\"\x81\x02\n\x1dGenerateMessageContentRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12>\n\x0cservice_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0bserviceDate\x12&\n\ncompany_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\":\n\x1eGenerateMessageContentResponse\x12\x18\n\x07message\x18\x01 \x01(\tR\x07message\"\x8e\x02\n\x12SendMessageRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12&\n\ncompany_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\"\n\x08staff_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12Y\n\x0bsend_method\x18\x02 \x01(\x0e\x32\'.moego.models.appointment.v1.SendMethodB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\nsendMethod\x88\x01\x01\x42\x0e\n\x0c_send_method\"-\n\x13SendMessageResponse\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\"\xa1\x02\n$ListDailyReportConfigByFilterRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12Z\n\x06\x66ilter\x18\x03 \x01(\x0b\x32\x38.moego.models.appointment.v1.ListDailyReportConfigFilterB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x06\x66ilter\x12K\n\npagination\x18\x04 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npagination\"\xc5\x01\n%ListDailyReportConfigByFilterResponse\x12X\n\x0ereport_configs\x18\x01 \x03(\x0b\x32\x31.moego.models.appointment.v1.DailyReportConfigDefR\rreportConfigs\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"\x84\x02\n BatchSendDailyDraftReportRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x38\n\x10\x64\x61ily_report_ids\x18\x03 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\x0e\x64\x61ilyReportIds\x12T\n\x0bsend_method\x18\x04 \x01(\x0e\x32\'.moego.models.appointment.v1.SendMethodB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\nsendMethod\"#\n!BatchSendDailyDraftReportResponse\"\xb1\x01\n#BatchDeleteDailyReportConfigRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x38\n\x10\x64\x61ily_report_ids\x18\x03 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\x0e\x64\x61ilyReportIds\"&\n$BatchDeleteDailyReportConfigResponse2\xa6\r\n\x12\x44\x61ilyReportService\x12\x8d\x01\n\x14GetDailyReportConfig\x12\x39.moego.service.appointment.v1.GetDailyReportConfigRequest\x1a:.moego.service.appointment.v1.GetDailyReportConfigResponse\x12\x90\x01\n\x15ListDailyReportConfig\x12:.moego.service.appointment.v1.ListDailyReportConfigRequest\x1a;.moego.service.appointment.v1.ListDailyReportConfigResponse\x12\x99\x01\n\x18GetDailyReportSentResult\x12=.moego.service.appointment.v1.GetDailyReportSentResultRequest\x1a>.moego.service.appointment.v1.GetDailyReportSentResultResponse\x12\x96\x01\n\x17UpsertDailyReportConfig\x12<.moego.service.appointment.v1.UpsertDailyReportConfigRequest\x1a=.moego.service.appointment.v1.UpsertDailyReportConfigResponse\x12\x9c\x01\n\x19GetDailyReportSentHistory\x12>.moego.service.appointment.v1.GetDailyReportSentHistoryRequest\x1a?.moego.service.appointment.v1.GetDailyReportSentHistoryResponse\x12\x9c\x01\n\x19GetDailyReportForCustomer\x12>.moego.service.appointment.v1.GetDailyReportForCustomerRequest\x1a?.moego.service.appointment.v1.GetDailyReportForCustomerResponse\x12\x93\x01\n\x16GenerateMessageContent\x12;.moego.service.appointment.v1.GenerateMessageContentRequest\x1a<.moego.service.appointment.v1.GenerateMessageContentResponse\x12r\n\x0bSendMessage\x12\x30.moego.service.appointment.v1.SendMessageRequest\x1a\x31.moego.service.appointment.v1.SendMessageResponse\x12\xa8\x01\n\x1dListDailyReportConfigByFilter\x12\x42.moego.service.appointment.v1.ListDailyReportConfigByFilterRequest\x1a\x43.moego.service.appointment.v1.ListDailyReportConfigByFilterResponse\x12\x9c\x01\n\x19\x42\x61tchSendDailyDraftReport\x12>.moego.service.appointment.v1.BatchSendDailyDraftReportRequest\x1a?.moego.service.appointment.v1.BatchSendDailyDraftReportResponse\x12\xa5\x01\n\x1c\x42\x61tchDeleteDailyReportConfig\x12\x41.moego.service.appointment.v1.BatchDeleteDailyReportConfigRequest\x1a\x42.moego.service.appointment.v1.BatchDeleteDailyReportConfigResponseB\x8c\x01\n$com.moego.idl.service.appointment.v1P\x01Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.appointment.v1.daily_report_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.service.appointment.v1P\001Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb'
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['service_date']._loaded_options = None
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['service_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETDAILYREPORTCONFIGREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTDAILYREPORTCONFIGREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\016\222\001\013\010\001\020\350\007\"\004\"\002 \000'
  _globals['_LISTDAILYREPORTCONFIGREQUEST'].fields_by_name['service_date']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGREQUEST'].fields_by_name['service_date']._serialized_options = b'\372B\010\222\001\005\010\001\020\350\007'
  _globals['_LISTDAILYREPORTCONFIGREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTDAILYREPORTCONFIGREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTSENTRESULTREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_GETDAILYREPORTSENTRESULTREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\016\222\001\013\010\001\020\350\007\"\004\"\002 \000'
  _globals['_GETDAILYREPORTSENTRESULTREQUEST'].fields_by_name['service_date']._loaded_options = None
  _globals['_GETDAILYREPORTSENTRESULTREQUEST'].fields_by_name['service_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETDAILYREPORTSENTRESULTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETDAILYREPORTSENTRESULTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTSENTRESULTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETDAILYREPORTSENTRESULTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['service_date']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['service_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['report']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['report']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDAILYREPORTFORCUSTOMERREQUEST'].fields_by_name['uuid']._loaded_options = None
  _globals['_GETDAILYREPORTFORCUSTOMERREQUEST'].fields_by_name['uuid']._serialized_options = b'\372B\004r\002\0302'
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['service_date']._loaded_options = None
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['service_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GENERATEMESSAGECONTENTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['send_method']._loaded_options = None
  _globals['_SENDMESSAGEREQUEST'].fields_by_name['send_method']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST'].fields_by_name['filter']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST'].fields_by_name['filter']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST'].fields_by_name['pagination']._loaded_options = None
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST'].fields_by_name['daily_report_ids']._loaded_options = None
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST'].fields_by_name['daily_report_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST'].fields_by_name['send_method']._loaded_options = None
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST'].fields_by_name['send_method']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_BATCHDELETEDAILYREPORTCONFIGREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHDELETEDAILYREPORTCONFIGREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHDELETEDAILYREPORTCONFIGREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_BATCHDELETEDAILYREPORTCONFIGREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHDELETEDAILYREPORTCONFIGREQUEST'].fields_by_name['daily_report_ids']._loaded_options = None
  _globals['_BATCHDELETEDAILYREPORTCONFIGREQUEST'].fields_by_name['daily_report_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_GETDAILYREPORTCONFIGREQUEST']._serialized_start=288
  _globals['_GETDAILYREPORTCONFIGREQUEST']._serialized_end=543
  _globals['_GETDAILYREPORTCONFIGRESPONSE']._serialized_start=546
  _globals['_GETDAILYREPORTCONFIGRESPONSE']._serialized_end=727
  _globals['_LISTDAILYREPORTCONFIGREQUEST']._serialized_start=730
  _globals['_LISTDAILYREPORTCONFIGREQUEST']._serialized_end=969
  _globals['_LISTDAILYREPORTCONFIGRESPONSE']._serialized_start=971
  _globals['_LISTDAILYREPORTCONFIGRESPONSE']._serialized_end=1092
  _globals['_GETDAILYREPORTSENTRESULTREQUEST']._serialized_start=1095
  _globals['_GETDAILYREPORTSENTRESULTREQUEST']._serialized_end=1334
  _globals['_GETDAILYREPORTSENTRESULTRESPONSE']._serialized_start=1336
  _globals['_GETDAILYREPORTSENTRESULTRESPONSE']._serialized_end=1449
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST']._serialized_start=1452
  _globals['_UPSERTDAILYREPORTCONFIGREQUEST']._serialized_end=1862
  _globals['_UPSERTDAILYREPORTCONFIGRESPONSE']._serialized_start=1864
  _globals['_UPSERTDAILYREPORTCONFIGRESPONSE']._serialized_end=1933
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST']._serialized_start=1936
  _globals['_GETDAILYREPORTSENTHISTORYREQUEST']._serialized_end=2132
  _globals['_GETDAILYREPORTSENTHISTORYRESPONSE']._serialized_start=2135
  _globals['_GETDAILYREPORTSENTHISTORYRESPONSE']._serialized_end=2271
  _globals['_GETDAILYREPORTFORCUSTOMERREQUEST']._serialized_start=2273
  _globals['_GETDAILYREPORTFORCUSTOMERREQUEST']._serialized_end=2336
  _globals['_GETDAILYREPORTFORCUSTOMERRESPONSE']._serialized_start=2339
  _globals['_GETDAILYREPORTFORCUSTOMERRESPONSE']._serialized_end=2579
  _globals['_GENERATEMESSAGECONTENTREQUEST']._serialized_start=2582
  _globals['_GENERATEMESSAGECONTENTREQUEST']._serialized_end=2839
  _globals['_GENERATEMESSAGECONTENTRESPONSE']._serialized_start=2841
  _globals['_GENERATEMESSAGECONTENTRESPONSE']._serialized_end=2899
  _globals['_SENDMESSAGEREQUEST']._serialized_start=2902
  _globals['_SENDMESSAGEREQUEST']._serialized_end=3172
  _globals['_SENDMESSAGERESPONSE']._serialized_start=3174
  _globals['_SENDMESSAGERESPONSE']._serialized_end=3219
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST']._serialized_start=3222
  _globals['_LISTDAILYREPORTCONFIGBYFILTERREQUEST']._serialized_end=3511
  _globals['_LISTDAILYREPORTCONFIGBYFILTERRESPONSE']._serialized_start=3514
  _globals['_LISTDAILYREPORTCONFIGBYFILTERRESPONSE']._serialized_end=3711
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST']._serialized_start=3714
  _globals['_BATCHSENDDAILYDRAFTREPORTREQUEST']._serialized_end=3974
  _globals['_BATCHSENDDAILYDRAFTREPORTRESPONSE']._serialized_start=3976
  _globals['_BATCHSENDDAILYDRAFTREPORTRESPONSE']._serialized_end=4011
  _globals['_BATCHDELETEDAILYREPORTCONFIGREQUEST']._serialized_start=4014
  _globals['_BATCHDELETEDAILYREPORTCONFIGREQUEST']._serialized_end=4191
  _globals['_BATCHDELETEDAILYREPORTCONFIGRESPONSE']._serialized_start=4193
  _globals['_BATCHDELETEDAILYREPORTCONFIGRESPONSE']._serialized_end=4231
  _globals['_DAILYREPORTSERVICE']._serialized_start=4234
  _globals['_DAILYREPORTSERVICE']._serialized_end=5936
# @@protoc_insertion_point(module_scope)
