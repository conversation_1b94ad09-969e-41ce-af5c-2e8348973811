from google.type import date_pb2 as _date_pb2
from moego.models.appointment.v1 import daily_report_defs_pb2 as _daily_report_defs_pb2
from moego.models.appointment.v1 import daily_report_enums_pb2 as _daily_report_enums_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetDailyReportConfigRequest(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "service_date", "company_id", "business_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    service_date: _date_pb2.Date
    company_id: int
    business_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class GetDailyReportConfigResponse(_message.Message):
    __slots__ = ("id", "report", "status")
    ID_FIELD_NUMBER: _ClassVar[int]
    REPORT_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    id: int
    report: _daily_report_defs_pb2.ReportDef
    status: _daily_report_enums_pb2.ReportCardStatus
    def __init__(self, id: _Optional[int] = ..., report: _Optional[_Union[_daily_report_defs_pb2.ReportDef, _Mapping]] = ..., status: _Optional[_Union[_daily_report_enums_pb2.ReportCardStatus, str]] = ...) -> None: ...

class ListDailyReportConfigRequest(_message.Message):
    __slots__ = ("appointment_ids", "service_date", "company_id", "business_id")
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    service_date: _containers.RepeatedCompositeFieldContainer[_date_pb2.Date]
    company_id: int
    business_id: int
    def __init__(self, appointment_ids: _Optional[_Iterable[int]] = ..., service_date: _Optional[_Iterable[_Union[_date_pb2.Date, _Mapping]]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class ListDailyReportConfigResponse(_message.Message):
    __slots__ = ("report_configs",)
    REPORT_CONFIGS_FIELD_NUMBER: _ClassVar[int]
    report_configs: _containers.RepeatedCompositeFieldContainer[_daily_report_defs_pb2.DailyReportConfigDef]
    def __init__(self, report_configs: _Optional[_Iterable[_Union[_daily_report_defs_pb2.DailyReportConfigDef, _Mapping]]] = ...) -> None: ...

class GetDailyReportSentResultRequest(_message.Message):
    __slots__ = ("appointment_ids", "service_date", "company_id", "business_id")
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    service_date: _date_pb2.Date
    company_id: int
    business_id: int
    def __init__(self, appointment_ids: _Optional[_Iterable[int]] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class GetDailyReportSentResultResponse(_message.Message):
    __slots__ = ("sent_results",)
    SENT_RESULTS_FIELD_NUMBER: _ClassVar[int]
    sent_results: _containers.RepeatedCompositeFieldContainer[_daily_report_defs_pb2.SentResultDef]
    def __init__(self, sent_results: _Optional[_Iterable[_Union[_daily_report_defs_pb2.SentResultDef, _Mapping]]] = ...) -> None: ...

class UpsertDailyReportConfigRequest(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "customer_id", "service_date", "report", "company_id", "business_id", "staff_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    REPORT_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    customer_id: int
    service_date: _date_pb2.Date
    report: _daily_report_defs_pb2.ReportDef
    company_id: int
    business_id: int
    staff_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., report: _Optional[_Union[_daily_report_defs_pb2.ReportDef, _Mapping]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., staff_id: _Optional[int] = ...) -> None: ...

class UpsertDailyReportConfigResponse(_message.Message):
    __slots__ = ("id", "uuid")
    ID_FIELD_NUMBER: _ClassVar[int]
    UUID_FIELD_NUMBER: _ClassVar[int]
    id: int
    uuid: str
    def __init__(self, id: _Optional[int] = ..., uuid: _Optional[str] = ...) -> None: ...

class GetDailyReportSentHistoryRequest(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "company_id", "business_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    company_id: int
    business_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class GetDailyReportSentHistoryResponse(_message.Message):
    __slots__ = ("sent_history_records",)
    SENT_HISTORY_RECORDS_FIELD_NUMBER: _ClassVar[int]
    sent_history_records: _containers.RepeatedCompositeFieldContainer[_daily_report_defs_pb2.SentHistoryRecordDef]
    def __init__(self, sent_history_records: _Optional[_Iterable[_Union[_daily_report_defs_pb2.SentHistoryRecordDef, _Mapping]]] = ...) -> None: ...

class GetDailyReportForCustomerRequest(_message.Message):
    __slots__ = ("uuid",)
    UUID_FIELD_NUMBER: _ClassVar[int]
    uuid: str
    def __init__(self, uuid: _Optional[str] = ...) -> None: ...

class GetDailyReportForCustomerResponse(_message.Message):
    __slots__ = ("report", "service_date", "pet_id", "business_id", "company_id")
    REPORT_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    report: _daily_report_defs_pb2.ReportDef
    service_date: _date_pb2.Date
    pet_id: int
    business_id: int
    company_id: int
    def __init__(self, report: _Optional[_Union[_daily_report_defs_pb2.ReportDef, _Mapping]] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., pet_id: _Optional[int] = ..., business_id: _Optional[int] = ..., company_id: _Optional[int] = ...) -> None: ...

class GenerateMessageContentRequest(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "service_date", "company_id", "business_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_DATE_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    service_date: _date_pb2.Date
    company_id: int
    business_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., service_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class GenerateMessageContentResponse(_message.Message):
    __slots__ = ("message",)
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    message: str
    def __init__(self, message: _Optional[str] = ...) -> None: ...

class SendMessageRequest(_message.Message):
    __slots__ = ("id", "company_id", "business_id", "staff_id", "send_method")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SEND_METHOD_FIELD_NUMBER: _ClassVar[int]
    id: int
    company_id: int
    business_id: int
    staff_id: int
    send_method: _daily_report_enums_pb2.SendMethod
    def __init__(self, id: _Optional[int] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., send_method: _Optional[_Union[_daily_report_enums_pb2.SendMethod, str]] = ...) -> None: ...

class SendMessageResponse(_message.Message):
    __slots__ = ("result",)
    RESULT_FIELD_NUMBER: _ClassVar[int]
    result: bool
    def __init__(self, result: bool = ...) -> None: ...

class ListDailyReportConfigByFilterRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "filter", "pagination")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    filter: _daily_report_defs_pb2.ListDailyReportConfigFilter
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., filter: _Optional[_Union[_daily_report_defs_pb2.ListDailyReportConfigFilter, _Mapping]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class ListDailyReportConfigByFilterResponse(_message.Message):
    __slots__ = ("report_configs", "pagination")
    REPORT_CONFIGS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    report_configs: _containers.RepeatedCompositeFieldContainer[_daily_report_defs_pb2.DailyReportConfigDef]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, report_configs: _Optional[_Iterable[_Union[_daily_report_defs_pb2.DailyReportConfigDef, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class BatchSendDailyDraftReportRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "daily_report_ids", "send_method")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    DAILY_REPORT_IDS_FIELD_NUMBER: _ClassVar[int]
    SEND_METHOD_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    daily_report_ids: _containers.RepeatedScalarFieldContainer[int]
    send_method: _daily_report_enums_pb2.SendMethod
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., daily_report_ids: _Optional[_Iterable[int]] = ..., send_method: _Optional[_Union[_daily_report_enums_pb2.SendMethod, str]] = ...) -> None: ...

class BatchSendDailyDraftReportResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class BatchDeleteDailyReportConfigRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "daily_report_ids")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    DAILY_REPORT_IDS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    daily_report_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., daily_report_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class BatchDeleteDailyReportConfigResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
