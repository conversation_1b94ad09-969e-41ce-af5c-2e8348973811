# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.appointment.v1 import daily_report_service_pb2 as moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2


class DailyReportServiceStub(object):
    """the daily report service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetDailyReportConfig = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/GetDailyReportConfig',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportConfigRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportConfigResponse.FromString,
                _registered_method=True)
        self.ListDailyReportConfig = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/ListDailyReportConfig',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigResponse.FromString,
                _registered_method=True)
        self.GetDailyReportSentResult = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/GetDailyReportSentResult',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentResultRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentResultResponse.FromString,
                _registered_method=True)
        self.UpsertDailyReportConfig = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/UpsertDailyReportConfig',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.UpsertDailyReportConfigRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.UpsertDailyReportConfigResponse.FromString,
                _registered_method=True)
        self.GetDailyReportSentHistory = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/GetDailyReportSentHistory',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentHistoryRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentHistoryResponse.FromString,
                _registered_method=True)
        self.GetDailyReportForCustomer = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/GetDailyReportForCustomer',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportForCustomerRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportForCustomerResponse.FromString,
                _registered_method=True)
        self.GenerateMessageContent = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/GenerateMessageContent',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GenerateMessageContentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GenerateMessageContentResponse.FromString,
                _registered_method=True)
        self.SendMessage = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/SendMessage',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.SendMessageRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.SendMessageResponse.FromString,
                _registered_method=True)
        self.ListDailyReportConfigByFilter = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/ListDailyReportConfigByFilter',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigByFilterRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigByFilterResponse.FromString,
                _registered_method=True)
        self.BatchSendDailyDraftReport = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/BatchSendDailyDraftReport',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchSendDailyDraftReportRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchSendDailyDraftReportResponse.FromString,
                _registered_method=True)
        self.BatchDeleteDailyReportConfig = channel.unary_unary(
                '/moego.service.appointment.v1.DailyReportService/BatchDeleteDailyReportConfig',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchDeleteDailyReportConfigRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchDeleteDailyReportConfigResponse.FromString,
                _registered_method=True)


class DailyReportServiceServicer(object):
    """the daily report service
    """

    def GetDailyReportConfig(self, request, context):
        """get daily report config
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListDailyReportConfig(self, request, context):
        """list daily report config
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDailyReportSentResult(self, request, context):
        """get daily report config sent result
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpsertDailyReportConfig(self, request, context):
        """upsert daily report config
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDailyReportSentHistory(self, request, context):
        """get daily report sent history
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDailyReportForCustomer(self, request, context):
        """get daily report for customer
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateMessageContent(self, request, context):
        """generate message content
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendMessage(self, request, context):
        """send message
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListDailyReportConfigByFilter(self, request, context):
        """list daily report config by filter
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchSendDailyDraftReport(self, request, context):
        """batch send daily draft report
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchDeleteDailyReportConfig(self, request, context):
        """batch delete daily report config
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DailyReportServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetDailyReportConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDailyReportConfig,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportConfigRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportConfigResponse.SerializeToString,
            ),
            'ListDailyReportConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.ListDailyReportConfig,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigResponse.SerializeToString,
            ),
            'GetDailyReportSentResult': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDailyReportSentResult,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentResultRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentResultResponse.SerializeToString,
            ),
            'UpsertDailyReportConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.UpsertDailyReportConfig,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.UpsertDailyReportConfigRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.UpsertDailyReportConfigResponse.SerializeToString,
            ),
            'GetDailyReportSentHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDailyReportSentHistory,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentHistoryRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentHistoryResponse.SerializeToString,
            ),
            'GetDailyReportForCustomer': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDailyReportForCustomer,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportForCustomerRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportForCustomerResponse.SerializeToString,
            ),
            'GenerateMessageContent': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateMessageContent,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GenerateMessageContentRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GenerateMessageContentResponse.SerializeToString,
            ),
            'SendMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.SendMessage,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.SendMessageRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.SendMessageResponse.SerializeToString,
            ),
            'ListDailyReportConfigByFilter': grpc.unary_unary_rpc_method_handler(
                    servicer.ListDailyReportConfigByFilter,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigByFilterRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigByFilterResponse.SerializeToString,
            ),
            'BatchSendDailyDraftReport': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchSendDailyDraftReport,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchSendDailyDraftReportRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchSendDailyDraftReportResponse.SerializeToString,
            ),
            'BatchDeleteDailyReportConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchDeleteDailyReportConfig,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchDeleteDailyReportConfigRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchDeleteDailyReportConfigResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.appointment.v1.DailyReportService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.appointment.v1.DailyReportService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class DailyReportService(object):
    """the daily report service
    """

    @staticmethod
    def GetDailyReportConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/GetDailyReportConfig',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportConfigRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListDailyReportConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/ListDailyReportConfig',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDailyReportSentResult(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/GetDailyReportSentResult',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentResultRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentResultResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpsertDailyReportConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/UpsertDailyReportConfig',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.UpsertDailyReportConfigRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.UpsertDailyReportConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDailyReportSentHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/GetDailyReportSentHistory',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentHistoryRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportSentHistoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDailyReportForCustomer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/GetDailyReportForCustomer',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportForCustomerRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GetDailyReportForCustomerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GenerateMessageContent(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/GenerateMessageContent',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GenerateMessageContentRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.GenerateMessageContentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SendMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/SendMessage',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.SendMessageRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.SendMessageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListDailyReportConfigByFilter(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/ListDailyReportConfigByFilter',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigByFilterRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.ListDailyReportConfigByFilterResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchSendDailyDraftReport(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/BatchSendDailyDraftReport',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchSendDailyDraftReportRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchSendDailyDraftReportResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchDeleteDailyReportConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.DailyReportService/BatchDeleteDailyReportConfig',
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchDeleteDailyReportConfigRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_daily__report__service__pb2.BatchDeleteDailyReportConfigResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
