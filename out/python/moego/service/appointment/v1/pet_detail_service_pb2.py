# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/appointment/v1/pet_detail_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/appointment/v1/pet_detail_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import interval_pb2 as google_dot_type_dot_interval__pb2
from moego.models.appointment.v1 import appointment_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__enums__pb2
from moego.models.appointment.v1 import evaluation_service_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_evaluation__service__models__pb2
from moego.models.appointment.v1 import pet_detail_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__defs__pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__enums__pb2
from moego.models.appointment.v1 import pet_detail_models_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__models__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.offering.v1 import service_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5moego/service/appointment/v1/pet_detail_service.proto\x12\x1cmoego.service.appointment.v1\x1a\x1agoogle/type/interval.proto\x1a\x33moego/models/appointment/v1/appointment_enums.proto\x1a;moego/models/appointment/v1/evaluation_service_models.proto\x1a\x31moego/models/appointment/v1/pet_detail_defs.proto\x1a\x32moego/models/appointment/v1/pet_detail_enums.proto\x1a\x33moego/models/appointment/v1/pet_detail_models.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a-moego/models/offering/v1/service_models.proto\x1a\x17validate/validate.proto\"\xb5\x04\n\x1dSaveOrUpdatePetDetailsRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12Q\n\npet_detail\x18\x02 \x01(\x0b\x32).moego.models.appointment.v1.PetDetailDefB\x02\x18\x01H\x00R\tpetDetail\x88\x01\x01\x12T\n\x0bpet_details\x18\x07 \x03(\x0b\x32).moego.models.appointment.v1.PetDetailDefB\x08\xfa\x42\x05\x92\x01\x02\x10\x64R\npetDetails\x12\x91\x01\n\x1frepeat_appointment_modify_scope\x18\x03 \x01(\x0e\x32\x39.moego.models.appointment.v1.RepeatAppointmentModifyScopeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x1crepeatAppointmentModifyScope\x88\x01\x01\x12(\n\x0b\x62usiness_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12&\n\ncompany_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\"\n\x08staff_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffIdB\r\n\x0b_pet_detailB\"\n _repeat_appointment_modify_scope\" \n\x1eSaveOrUpdatePetDetailsResponse\"\x81\x02\n$CreatePetDetailsForExtraOrderRequest\x12-\n\x0e\x65xtra_order_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0c\x65xtraOrderId\x12.\n\x0e\x61ppointment_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12R\n\npet_detail\x18\x03 \x01(\x0b\x32).moego.models.appointment.v1.PetDetailDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\tpetDetail\x12&\n\ncompany_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\"[\n%CreatePetDetailsForExtraOrderResponse\x12\x32\n\x0epet_detail_ids\x18\x01 \x03(\x03\x42\x0c\xfa\x42\t\x92\x01\x06\"\x04\"\x02 \x00R\x0cpetDetailIds\"\x90\x03\n\x10\x44\x65letePetRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12\x91\x01\n\x1frepeat_appointment_modify_scope\x18\x03 \x01(\x0e\x32\x39.moego.models.appointment.v1.RepeatAppointmentModifyScopeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x1crepeatAppointmentModifyScope\x88\x01\x01\x12(\n\x0b\x62usiness_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12&\n\ncompany_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\"\n\x08staff_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffIdB\"\n _repeat_appointment_modify_scope\"\x13\n\x11\x44\x65letePetResponse\"\xed\x01\n\x1a\x44\x65letePetEvaluationRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12.\n\x0e\x61ppointment_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12H\n\x1c\x65valuation_service_detail_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x19\x65valuationServiceDetailId\x12-\n\x0etoken_staff_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0ctokenStaffId\"\x1d\n\x1b\x44\x65letePetEvaluationResponse\"\x8a\x02\n\x1fUpdateUpcomingPetDetailsRequest\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\"\n\x08staff_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12s\n\x16location_override_rule\x18\x04 \x03(\x0b\x32..moego.models.offering.v1.LocationOverrideRuleB\r\xfa\x42\n\x92\x01\x07\"\x05\x8a\x01\x02\x10\x01R\x14locationOverrideRule\"[\n UpdateUpcomingPetDetailsResponse\x12\x37\n\x13\x61\x66\x66\x65\x63ted_appt_count\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x11\x61\x66\x66\x65\x63tedApptCount\"\xc1\x01\n\x17GetPetDetailListRequest\x12(\n\ncompany_id\x18\x01 \x01(\x03\x42\t\xfa\x42\x06\"\x04 \x00@\x01R\tcompanyId\x12\x35\n\x0f\x61ppointment_ids\x18\x02 \x03(\x03\x42\x0c\xfa\x42\t\x92\x01\x06\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\x12/\n\x11with_actual_dates\x18\x03 \x01(\x08H\x00R\x0fwithActualDates\x88\x01\x01\x42\x14\n\x12_with_actual_dates\"\xc6\x01\n\x18GetPetDetailListResponse\x12L\n\x0bpet_details\x18\x01 \x03(\x0b\x32+.moego.models.appointment.v1.PetDetailModelR\npetDetails\x12\\\n\x0fpet_evaluations\x18\x02 \x03(\x0b\x32\x33.moego.models.appointment.v1.EvaluationServiceModelR\x0epetEvaluations\"\xb5\x11\n\x16UpdatePetDetailRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12-\n\x0bgrooming_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\ngroomingId\x88\x01\x01\x12#\n\x06pet_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\x05petId\x88\x01\x01\x12\'\n\x08staff_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x02R\x07staffId\x88\x01\x01\x12+\n\nservice_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x03R\tserviceId\x88\x01\x01\x12Y\n\x0cservice_type\x18\x06 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x04R\x0bserviceType\x88\x01\x01\x12/\n\x0cservice_time\x18\x07 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x05R\x0bserviceTime\x88\x01\x01\x12\x38\n\rservice_price\x18\x08 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00H\x06R\x0cservicePrice\x88\x01\x01\x12.\n\nstart_time\x18\t \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x07R\tstartTime\x88\x01\x01\x12*\n\x08\x65nd_time\x18\n \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x08R\x07\x65ndTime\x88\x01\x01\x12U\n\x06status\x18\x0b \x01(\x0e\x32,.moego.models.appointment.v1.PetDetailStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\tR\x06status\x88\x01\x01\x12\x65\n\x10scope_type_price\x18\x0c \x01(\x0e\x32*.moego.models.offering.v1.ServiceScopeTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\nR\x0escopeTypePrice\x88\x01\x01\x12\x63\n\x0fscope_type_time\x18\r \x01(\x0e\x32*.moego.models.offering.v1.ServiceScopeTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x0bR\rscopeTypeTime\x88\x01\x01\x12\x30\n\rstar_staff_id\x18\x0e \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x0cR\x0bstarStaffId\x88\x01\x01\x12:\n\x12package_service_id\x18\x0f \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\rR\x10packageServiceId\x88\x01\x01\x12.\n\x10\x65nable_operation\x18\x10 \x01(\x08H\x0eR\x0f\x65nableOperation\x88\x01\x01\x12Q\n\twork_mode\x18\x11 \x01(\x0e\x32%.moego.models.appointment.v1.WorkModeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x0fR\x08workMode\x88\x01\x01\x12<\n\x12service_color_code\x18\x12 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x64H\x10R\x10serviceColorCode\x88\x01\x01\x12>\n\nstart_date\x18\x13 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x11R\tstartDate\x88\x01\x01\x12:\n\x08\x65nd_date\x18\x14 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x12R\x07\x65ndDate\x88\x01\x01\x12\x66\n\x11service_item_type\x18\x1a \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x13R\x0fserviceItemType\x88\x01\x01\x12+\n\nlodging_id\x18\x15 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x14R\tlodgingId\x88\x01\x01\x12+\n\nprice_unit\x18\x16 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x15R\tpriceUnit\x88\x01\x01\x12\x34\n\x0especific_dates\x18\x17 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xe8\x07H\x16R\rspecificDates\x88\x01\x01\x12@\n\x15\x61ssociated_service_id\x18\x18 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x17R\x13\x61ssociatedServiceId\x88\x01\x01\x12l\n\x13price_override_type\x18\x19 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x18R\x11priceOverrideType\x88\x01\x01\x12r\n\x16\x64uration_override_type\x18\x1b \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x19R\x14\x64urationOverrideType\x88\x01\x01\x42\x0e\n\x0c_grooming_idB\t\n\x07_pet_idB\x0b\n\t_staff_idB\r\n\x0b_service_idB\x0f\n\r_service_typeB\x0f\n\r_service_timeB\x10\n\x0e_service_priceB\r\n\x0b_start_timeB\x0b\n\t_end_timeB\t\n\x07_statusB\x13\n\x11_scope_type_priceB\x12\n\x10_scope_type_timeB\x10\n\x0e_star_staff_idB\x15\n\x13_package_service_idB\x13\n\x11_enable_operationB\x0c\n\n_work_modeB\x15\n\x13_service_color_codeB\r\n\x0b_start_dateB\x0b\n\t_end_dateB\x14\n\x12_service_item_typeB\r\n\x0b_lodging_idB\r\n\x0b_price_unitB\x11\n\x0f_specific_datesB\x18\n\x16_associated_service_idB\x16\n\x14_price_override_typeB\x19\n\x17_duration_override_type\"@\n\x17UpdatePetDetailResponse\x12%\n\x0e\x61\x66\x66\x65\x63ted_count\x18\x01 \x01(\x03R\raffectedCount\"j\n\x13GetPetDetailRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12+\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x42\r\n\x0b_company_id\"[\n\x14GetPetDetailResponse\x12\x43\n\x06record\x18\x01 \x01(\x0b\x32+.moego.models.appointment.v1.PetDetailModelR\x06record\"R\n#DeletePetDetailForExtraOrderRequest\x12+\n\rpet_detail_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0bpetDetailId\">\n$DeletePetDetailForExtraOrderResponse\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\"\xa7\x01\n!UpdateUpcomingAppointmentsRequest\x12/\n\x0c\x62usiness_ids\x18\x01 \x03(\x03\x42\x0c\xfa\x42\t\x92\x01\x06\"\x04\"\x02 \x00R\x0b\x62usinessIds\x12Q\n\x0bold_service\x18\x02 \x01(\x0b\x32&.moego.models.offering.v1.ServiceModelB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\noldService\"$\n\"UpdateUpcomingAppointmentsResponse\"\x81\x07\n\x17GetLastPetDetailRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12/\n\x0b\x63ustomer_id\x18\x02 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x08\x01\"\x04\"\x02 \x00R\ncustomerId\x12%\n\x06pet_id\x18\x03 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x08\x01\"\x04\"\x02 \x00R\x05petId\x12Y\n\x06\x66ilter\x18\x05 \x01(\x0b\x32<.moego.service.appointment.v1.GetLastPetDetailRequest.FilterH\x00R\x06\x66ilter\x88\x01\x01\x1a\xff\x04\n\x06\x46ilter\x12-\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12W\n\x06status\x18\x02 \x03(\x0e\x32..moego.models.appointment.v1.AppointmentStatusB\x0f\xfa\x42\x0c\x92\x01\t\x18\x01\"\x05\x82\x01\x02\x10\x01R\x06status\x12\x44\n\x10start_time_range\x18\x03 \x01(\x0b\x32\x15.google.type.IntervalH\x01R\x0estartTimeRange\x88\x01\x01\x12@\n\x0e\x65nd_time_range\x18\x04 \x01(\x0b\x32\x15.google.type.IntervalH\x02R\x0c\x65ndTimeRange\x88\x01\x01\x12W\n\x12service_item_types\x18\x05 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypes\x12\x34\n\x14\x66ilter_no_start_time\x18\x06 \x01(\x08H\x03R\x11\x66ilterNoStartTime\x88\x01\x01\x12\x39\n\x16\x66ilter_booking_request\x18\x07 \x01(\x08H\x04R\x14\x66ilterBookingRequest\x88\x01\x01\x12/\n\x0bservice_ids\x18\x08 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\nserviceIdsB\x0e\n\x0c_business_idB\x13\n\x11_start_time_rangeB\x11\n\x0f_end_time_rangeB\x17\n\x15_filter_no_start_timeB\x19\n\x17_filter_booking_requestB\t\n\x07_filter\"h\n\x18GetLastPetDetailResponse\x12L\n\x0bpet_details\x18\x01 \x03(\x0b\x32+.moego.models.appointment.v1.PetDetailModelR\npetDetails\"\x9a\x02\n\x19GetStaffPetDetailsRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12-\n\tstaff_ids\x18\x03 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x01\x18\x01\"\x04\"\x02 \x00R\x08staffIds\x12?\n\x10start_time_range\x18\x04 \x01(\x0b\x32\x15.google.type.IntervalR\x0estartTimeRange\x12;\n\x0e\x65nd_time_range\x18\x05 \x01(\x0b\x32\x15.google.type.IntervalR\x0c\x65ndTimeRange\"u\n\x1aGetStaffPetDetailsResponse\x12W\n\x11staff_pet_details\x18\x01 \x03(\x0b\x32+.moego.models.appointment.v1.StaffPetDetailR\x0fstaffPetDetails2\xbd\r\n\x10PetDetailService\x12u\n\x0cGetPetDetail\x12\x31.moego.service.appointment.v1.GetPetDetailRequest\x1a\x32.moego.service.appointment.v1.GetPetDetailResponse\x12\x93\x01\n\x16SaveOrUpdatePetDetails\x12;.moego.service.appointment.v1.SaveOrUpdatePetDetailsRequest\x1a<.moego.service.appointment.v1.SaveOrUpdatePetDetailsResponse\x12\xa8\x01\n\x1d\x43reatePetDetailsForExtraOrder\x12\x42.moego.service.appointment.v1.CreatePetDetailsForExtraOrderRequest\x1a\x43.moego.service.appointment.v1.CreatePetDetailsForExtraOrderResponse\x12\xa5\x01\n\x1c\x44\x65letePetDetailForExtraOrder\x12\x41.moego.service.appointment.v1.DeletePetDetailForExtraOrderRequest\x1a\x42.moego.service.appointment.v1.DeletePetDetailForExtraOrderResponse\x12l\n\tDeletePet\x12..moego.service.appointment.v1.DeletePetRequest\x1a/.moego.service.appointment.v1.DeletePetResponse\x12\x8a\x01\n\x13\x44\x65letePetEvaluation\x12\x38.moego.service.appointment.v1.DeletePetEvaluationRequest\x1a\x39.moego.service.appointment.v1.DeletePetEvaluationResponse\x12\x99\x01\n\x18UpdateUpcomingPetDetails\x12=.moego.service.appointment.v1.UpdateUpcomingPetDetailsRequest\x1a>.moego.service.appointment.v1.UpdateUpcomingPetDetailsResponse\x12\x9f\x01\n\x1aUpdateUpcomingAppointments\x12?.moego.service.appointment.v1.UpdateUpcomingAppointmentsRequest\<EMAIL>\x12\x81\x01\n\x10GetPetDetailList\x12\x35.moego.service.appointment.v1.GetPetDetailListRequest\x1a\x36.moego.service.appointment.v1.GetPetDetailListResponse\x12~\n\x0fUpdatePetDetail\x12\x34.moego.service.appointment.v1.UpdatePetDetailRequest\x1a\x35.moego.service.appointment.v1.UpdatePetDetailResponse\x12\x81\x01\n\x10GetLastPetDetail\x12\x35.moego.service.appointment.v1.GetLastPetDetailRequest\x1a\x36.moego.service.appointment.v1.GetLastPetDetailResponse\x12\x87\x01\n\x12GetStaffPetDetails\x12\x37.moego.service.appointment.v1.GetStaffPetDetailsRequest\x1a\x38.moego.service.appointment.v1.GetStaffPetDetailsResponseB\x8c\x01\n$com.moego.idl.service.appointment.v1P\x01Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.appointment.v1.pet_detail_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.service.appointment.v1P\001Zbgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb'
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['pet_detail']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['pet_detail']._serialized_options = b'\030\001'
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['pet_details']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['pet_details']._serialized_options = b'\372B\005\222\001\002\020d'
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['repeat_appointment_modify_scope']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['repeat_appointment_modify_scope']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SAVEORUPDATEPETDETAILSREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST'].fields_by_name['extra_order_id']._loaded_options = None
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST'].fields_by_name['extra_order_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST'].fields_by_name['pet_detail']._loaded_options = None
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST'].fields_by_name['pet_detail']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEPETDETAILSFOREXTRAORDERRESPONSE'].fields_by_name['pet_detail_ids']._loaded_options = None
  _globals['_CREATEPETDETAILSFOREXTRAORDERRESPONSE'].fields_by_name['pet_detail_ids']._serialized_options = b'\372B\t\222\001\006\"\004\"\002 \000'
  _globals['_DELETEPETREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_DELETEPETREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_DELETEPETREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETREQUEST'].fields_by_name['repeat_appointment_modify_scope']._loaded_options = None
  _globals['_DELETEPETREQUEST'].fields_by_name['repeat_appointment_modify_scope']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_DELETEPETREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_DELETEPETREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_DELETEPETREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_DELETEPETREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETEVALUATIONREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_DELETEPETEVALUATIONREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETEVALUATIONREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_DELETEPETEVALUATIONREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETEVALUATIONREQUEST'].fields_by_name['evaluation_service_detail_id']._loaded_options = None
  _globals['_DELETEPETEVALUATIONREQUEST'].fields_by_name['evaluation_service_detail_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETEVALUATIONREQUEST'].fields_by_name['token_staff_id']._loaded_options = None
  _globals['_DELETEPETEVALUATIONREQUEST'].fields_by_name['token_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST'].fields_by_name['service_id']._loaded_options = None
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST'].fields_by_name['location_override_rule']._loaded_options = None
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST'].fields_by_name['location_override_rule']._serialized_options = b'\372B\n\222\001\007\"\005\212\001\002\020\001'
  _globals['_UPDATEUPCOMINGPETDETAILSRESPONSE'].fields_by_name['affected_appt_count']._loaded_options = None
  _globals['_UPDATEUPCOMINGPETDETAILSRESPONSE'].fields_by_name['affected_appt_count']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETPETDETAILLISTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETPETDETAILLISTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\006\"\004 \000@\001'
  _globals['_GETPETDETAILLISTREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_GETPETDETAILLISTREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\t\222\001\006\"\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['grooming_id']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['grooming_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_id']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_type']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_time']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_price']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_price']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['start_time']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['end_time']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['scope_type_price']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['scope_type_price']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['scope_type_time']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['scope_type_time']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['star_staff_id']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['star_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['package_service_id']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['package_service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['work_mode']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['work_mode']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_color_code']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_color_code']._serialized_options = b'\372B\006r\004\020\001\030d'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['start_date']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['end_date']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['service_item_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['lodging_id']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['lodging_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['price_unit']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['price_unit']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['specific_dates']._serialized_options = b'\372B\005r\003\030\350\007'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['associated_service_id']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['associated_service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['price_override_type']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['price_override_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['duration_override_type']._loaded_options = None
  _globals['_UPDATEPETDETAILREQUEST'].fields_by_name['duration_override_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_GETPETDETAILREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_GETPETDETAILREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETPETDETAILREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETPETDETAILREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEPETDETAILFOREXTRAORDERREQUEST'].fields_by_name['pet_detail_id']._loaded_options = None
  _globals['_DELETEPETDETAILFOREXTRAORDERREQUEST'].fields_by_name['pet_detail_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEUPCOMINGAPPOINTMENTSREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_UPDATEUPCOMINGAPPOINTMENTSREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\t\222\001\006\"\004\"\002 \000'
  _globals['_UPDATEUPCOMINGAPPOINTMENTSREQUEST'].fields_by_name['old_service']._loaded_options = None
  _globals['_UPDATEUPCOMINGAPPOINTMENTSREQUEST'].fields_by_name['old_service']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETLASTPETDETAILREQUEST_FILTER'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETLASTPETDETAILREQUEST_FILTER'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETLASTPETDETAILREQUEST_FILTER'].fields_by_name['status']._loaded_options = None
  _globals['_GETLASTPETDETAILREQUEST_FILTER'].fields_by_name['status']._serialized_options = b'\372B\014\222\001\t\030\001\"\005\202\001\002\020\001'
  _globals['_GETLASTPETDETAILREQUEST_FILTER'].fields_by_name['service_ids']._loaded_options = None
  _globals['_GETLASTPETDETAILREQUEST_FILTER'].fields_by_name['service_ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_GETLASTPETDETAILREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETLASTPETDETAILREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETLASTPETDETAILREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_GETLASTPETDETAILREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\013\222\001\010\010\001\"\004\"\002 \000'
  _globals['_GETLASTPETDETAILREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_GETLASTPETDETAILREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\013\222\001\010\010\001\"\004\"\002 \000'
  _globals['_GETSTAFFPETDETAILSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETSTAFFPETDETAILSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSTAFFPETDETAILSREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETSTAFFPETDETAILSREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSTAFFPETDETAILSREQUEST'].fields_by_name['staff_ids']._loaded_options = None
  _globals['_GETSTAFFPETDETAILSREQUEST'].fields_by_name['staff_ids']._serialized_options = b'\372B\r\222\001\n\010\001\030\001\"\004\"\002 \000'
  _globals['_SAVEORUPDATEPETDETAILSREQUEST']._serialized_start=503
  _globals['_SAVEORUPDATEPETDETAILSREQUEST']._serialized_end=1068
  _globals['_SAVEORUPDATEPETDETAILSRESPONSE']._serialized_start=1070
  _globals['_SAVEORUPDATEPETDETAILSRESPONSE']._serialized_end=1102
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST']._serialized_start=1105
  _globals['_CREATEPETDETAILSFOREXTRAORDERREQUEST']._serialized_end=1362
  _globals['_CREATEPETDETAILSFOREXTRAORDERRESPONSE']._serialized_start=1364
  _globals['_CREATEPETDETAILSFOREXTRAORDERRESPONSE']._serialized_end=1455
  _globals['_DELETEPETREQUEST']._serialized_start=1458
  _globals['_DELETEPETREQUEST']._serialized_end=1858
  _globals['_DELETEPETRESPONSE']._serialized_start=1860
  _globals['_DELETEPETRESPONSE']._serialized_end=1879
  _globals['_DELETEPETEVALUATIONREQUEST']._serialized_start=1882
  _globals['_DELETEPETEVALUATIONREQUEST']._serialized_end=2119
  _globals['_DELETEPETEVALUATIONRESPONSE']._serialized_start=2121
  _globals['_DELETEPETEVALUATIONRESPONSE']._serialized_end=2150
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST']._serialized_start=2153
  _globals['_UPDATEUPCOMINGPETDETAILSREQUEST']._serialized_end=2419
  _globals['_UPDATEUPCOMINGPETDETAILSRESPONSE']._serialized_start=2421
  _globals['_UPDATEUPCOMINGPETDETAILSRESPONSE']._serialized_end=2512
  _globals['_GETPETDETAILLISTREQUEST']._serialized_start=2515
  _globals['_GETPETDETAILLISTREQUEST']._serialized_end=2708
  _globals['_GETPETDETAILLISTRESPONSE']._serialized_start=2711
  _globals['_GETPETDETAILLISTRESPONSE']._serialized_end=2909
  _globals['_UPDATEPETDETAILREQUEST']._serialized_start=2912
  _globals['_UPDATEPETDETAILREQUEST']._serialized_end=5141
  _globals['_UPDATEPETDETAILRESPONSE']._serialized_start=5143
  _globals['_UPDATEPETDETAILRESPONSE']._serialized_end=5207
  _globals['_GETPETDETAILREQUEST']._serialized_start=5209
  _globals['_GETPETDETAILREQUEST']._serialized_end=5315
  _globals['_GETPETDETAILRESPONSE']._serialized_start=5317
  _globals['_GETPETDETAILRESPONSE']._serialized_end=5408
  _globals['_DELETEPETDETAILFOREXTRAORDERREQUEST']._serialized_start=5410
  _globals['_DELETEPETDETAILFOREXTRAORDERREQUEST']._serialized_end=5492
  _globals['_DELETEPETDETAILFOREXTRAORDERRESPONSE']._serialized_start=5494
  _globals['_DELETEPETDETAILFOREXTRAORDERRESPONSE']._serialized_end=5556
  _globals['_UPDATEUPCOMINGAPPOINTMENTSREQUEST']._serialized_start=5559
  _globals['_UPDATEUPCOMINGAPPOINTMENTSREQUEST']._serialized_end=5726
  _globals['_UPDATEUPCOMINGAPPOINTMENTSRESPONSE']._serialized_start=5728
  _globals['_UPDATEUPCOMINGAPPOINTMENTSRESPONSE']._serialized_end=5764
  _globals['_GETLASTPETDETAILREQUEST']._serialized_start=5767
  _globals['_GETLASTPETDETAILREQUEST']._serialized_end=6664
  _globals['_GETLASTPETDETAILREQUEST_FILTER']._serialized_start=6014
  _globals['_GETLASTPETDETAILREQUEST_FILTER']._serialized_end=6653
  _globals['_GETLASTPETDETAILRESPONSE']._serialized_start=6666
  _globals['_GETLASTPETDETAILRESPONSE']._serialized_end=6770
  _globals['_GETSTAFFPETDETAILSREQUEST']._serialized_start=6773
  _globals['_GETSTAFFPETDETAILSREQUEST']._serialized_end=7055
  _globals['_GETSTAFFPETDETAILSRESPONSE']._serialized_start=7057
  _globals['_GETSTAFFPETDETAILSRESPONSE']._serialized_end=7174
  _globals['_PETDETAILSERVICE']._serialized_start=7177
  _globals['_PETDETAILSERVICE']._serialized_end=8902
# @@protoc_insertion_point(module_scope)
