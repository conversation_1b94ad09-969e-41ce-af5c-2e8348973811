from google.type import interval_pb2 as _interval_pb2
from moego.models.appointment.v1 import appointment_enums_pb2 as _appointment_enums_pb2
from moego.models.appointment.v1 import evaluation_service_models_pb2 as _evaluation_service_models_pb2
from moego.models.appointment.v1 import pet_detail_defs_pb2 as _pet_detail_defs_pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as _pet_detail_enums_pb2
from moego.models.appointment.v1 import pet_detail_models_pb2 as _pet_detail_models_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.offering.v1 import service_models_pb2 as _service_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class SaveOrUpdatePetDetailsRequest(_message.Message):
    __slots__ = ("appointment_id", "pet_detail", "pet_details", "repeat_appointment_modify_scope", "business_id", "company_id", "staff_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_DETAIL_FIELD_NUMBER: _ClassVar[int]
    PET_DETAILS_FIELD_NUMBER: _ClassVar[int]
    REPEAT_APPOINTMENT_MODIFY_SCOPE_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_detail: _pet_detail_defs_pb2.PetDetailDef
    pet_details: _containers.RepeatedCompositeFieldContainer[_pet_detail_defs_pb2.PetDetailDef]
    repeat_appointment_modify_scope: _pet_detail_enums_pb2.RepeatAppointmentModifyScope
    business_id: int
    company_id: int
    staff_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., pet_detail: _Optional[_Union[_pet_detail_defs_pb2.PetDetailDef, _Mapping]] = ..., pet_details: _Optional[_Iterable[_Union[_pet_detail_defs_pb2.PetDetailDef, _Mapping]]] = ..., repeat_appointment_modify_scope: _Optional[_Union[_pet_detail_enums_pb2.RepeatAppointmentModifyScope, str]] = ..., business_id: _Optional[int] = ..., company_id: _Optional[int] = ..., staff_id: _Optional[int] = ...) -> None: ...

class SaveOrUpdatePetDetailsResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class CreatePetDetailsForExtraOrderRequest(_message.Message):
    __slots__ = ("extra_order_id", "appointment_id", "pet_detail", "company_id")
    EXTRA_ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_DETAIL_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    extra_order_id: int
    appointment_id: int
    pet_detail: _pet_detail_defs_pb2.PetDetailDef
    company_id: int
    def __init__(self, extra_order_id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., pet_detail: _Optional[_Union[_pet_detail_defs_pb2.PetDetailDef, _Mapping]] = ..., company_id: _Optional[int] = ...) -> None: ...

class CreatePetDetailsForExtraOrderResponse(_message.Message):
    __slots__ = ("pet_detail_ids",)
    PET_DETAIL_IDS_FIELD_NUMBER: _ClassVar[int]
    pet_detail_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, pet_detail_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class DeletePetRequest(_message.Message):
    __slots__ = ("appointment_id", "pet_id", "repeat_appointment_modify_scope", "business_id", "company_id", "staff_id")
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    REPEAT_APPOINTMENT_MODIFY_SCOPE_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: int
    pet_id: int
    repeat_appointment_modify_scope: _pet_detail_enums_pb2.RepeatAppointmentModifyScope
    business_id: int
    company_id: int
    staff_id: int
    def __init__(self, appointment_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., repeat_appointment_modify_scope: _Optional[_Union[_pet_detail_enums_pb2.RepeatAppointmentModifyScope, str]] = ..., business_id: _Optional[int] = ..., company_id: _Optional[int] = ..., staff_id: _Optional[int] = ...) -> None: ...

class DeletePetResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeletePetEvaluationRequest(_message.Message):
    __slots__ = ("company_id", "appointment_id", "evaluation_service_detail_id", "token_staff_id")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_SERVICE_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    TOKEN_STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    appointment_id: int
    evaluation_service_detail_id: int
    token_staff_id: int
    def __init__(self, company_id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., evaluation_service_detail_id: _Optional[int] = ..., token_staff_id: _Optional[int] = ...) -> None: ...

class DeletePetEvaluationResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class UpdateUpcomingPetDetailsRequest(_message.Message):
    __slots__ = ("service_id", "company_id", "staff_id", "location_override_rule")
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    LOCATION_OVERRIDE_RULE_FIELD_NUMBER: _ClassVar[int]
    service_id: int
    company_id: int
    staff_id: int
    location_override_rule: _containers.RepeatedCompositeFieldContainer[_service_models_pb2.LocationOverrideRule]
    def __init__(self, service_id: _Optional[int] = ..., company_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., location_override_rule: _Optional[_Iterable[_Union[_service_models_pb2.LocationOverrideRule, _Mapping]]] = ...) -> None: ...

class UpdateUpcomingPetDetailsResponse(_message.Message):
    __slots__ = ("affected_appt_count",)
    AFFECTED_APPT_COUNT_FIELD_NUMBER: _ClassVar[int]
    affected_appt_count: int
    def __init__(self, affected_appt_count: _Optional[int] = ...) -> None: ...

class GetPetDetailListRequest(_message.Message):
    __slots__ = ("company_id", "appointment_ids", "with_actual_dates")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    WITH_ACTUAL_DATES_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    with_actual_dates: bool
    def __init__(self, company_id: _Optional[int] = ..., appointment_ids: _Optional[_Iterable[int]] = ..., with_actual_dates: bool = ...) -> None: ...

class GetPetDetailListResponse(_message.Message):
    __slots__ = ("pet_details", "pet_evaluations")
    PET_DETAILS_FIELD_NUMBER: _ClassVar[int]
    PET_EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    pet_details: _containers.RepeatedCompositeFieldContainer[_pet_detail_models_pb2.PetDetailModel]
    pet_evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_service_models_pb2.EvaluationServiceModel]
    def __init__(self, pet_details: _Optional[_Iterable[_Union[_pet_detail_models_pb2.PetDetailModel, _Mapping]]] = ..., pet_evaluations: _Optional[_Iterable[_Union[_evaluation_service_models_pb2.EvaluationServiceModel, _Mapping]]] = ...) -> None: ...

class UpdatePetDetailRequest(_message.Message):
    __slots__ = ("id", "grooming_id", "pet_id", "staff_id", "service_id", "service_type", "service_time", "service_price", "start_time", "end_time", "status", "scope_type_price", "scope_type_time", "star_staff_id", "package_service_id", "enable_operation", "work_mode", "service_color_code", "start_date", "end_date", "service_item_type", "lodging_id", "price_unit", "specific_dates", "associated_service_id", "price_override_type", "duration_override_type")
    ID_FIELD_NUMBER: _ClassVar[int]
    GROOMING_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SCOPE_TYPE_PRICE_FIELD_NUMBER: _ClassVar[int]
    SCOPE_TYPE_TIME_FIELD_NUMBER: _ClassVar[int]
    STAR_STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    PACKAGE_SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    ENABLE_OPERATION_FIELD_NUMBER: _ClassVar[int]
    WORK_MODE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    LODGING_ID_FIELD_NUMBER: _ClassVar[int]
    PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    ASSOCIATED_SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    PRICE_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    DURATION_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    id: int
    grooming_id: int
    pet_id: int
    staff_id: int
    service_id: int
    service_type: _service_enum_pb2.ServiceType
    service_time: int
    service_price: float
    start_time: int
    end_time: int
    status: _pet_detail_enums_pb2.PetDetailStatus
    scope_type_price: _service_enum_pb2.ServiceScopeType
    scope_type_time: _service_enum_pb2.ServiceScopeType
    star_staff_id: int
    package_service_id: int
    enable_operation: bool
    work_mode: _pet_detail_enums_pb2.WorkMode
    service_color_code: str
    start_date: str
    end_date: str
    service_item_type: _service_enum_pb2.ServiceItemType
    lodging_id: int
    price_unit: int
    specific_dates: str
    associated_service_id: int
    price_override_type: _service_enum_pb2.ServiceOverrideType
    duration_override_type: _service_enum_pb2.ServiceOverrideType
    def __init__(self, id: _Optional[int] = ..., grooming_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., service_id: _Optional[int] = ..., service_type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., service_time: _Optional[int] = ..., service_price: _Optional[float] = ..., start_time: _Optional[int] = ..., end_time: _Optional[int] = ..., status: _Optional[_Union[_pet_detail_enums_pb2.PetDetailStatus, str]] = ..., scope_type_price: _Optional[_Union[_service_enum_pb2.ServiceScopeType, str]] = ..., scope_type_time: _Optional[_Union[_service_enum_pb2.ServiceScopeType, str]] = ..., star_staff_id: _Optional[int] = ..., package_service_id: _Optional[int] = ..., enable_operation: bool = ..., work_mode: _Optional[_Union[_pet_detail_enums_pb2.WorkMode, str]] = ..., service_color_code: _Optional[str] = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., lodging_id: _Optional[int] = ..., price_unit: _Optional[int] = ..., specific_dates: _Optional[str] = ..., associated_service_id: _Optional[int] = ..., price_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ..., duration_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ...) -> None: ...

class UpdatePetDetailResponse(_message.Message):
    __slots__ = ("affected_count",)
    AFFECTED_COUNT_FIELD_NUMBER: _ClassVar[int]
    affected_count: int
    def __init__(self, affected_count: _Optional[int] = ...) -> None: ...

class GetPetDetailRequest(_message.Message):
    __slots__ = ("id", "company_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    company_id: int
    def __init__(self, id: _Optional[int] = ..., company_id: _Optional[int] = ...) -> None: ...

class GetPetDetailResponse(_message.Message):
    __slots__ = ("record",)
    RECORD_FIELD_NUMBER: _ClassVar[int]
    record: _pet_detail_models_pb2.PetDetailModel
    def __init__(self, record: _Optional[_Union[_pet_detail_models_pb2.PetDetailModel, _Mapping]] = ...) -> None: ...

class DeletePetDetailForExtraOrderRequest(_message.Message):
    __slots__ = ("pet_detail_id",)
    PET_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    pet_detail_id: int
    def __init__(self, pet_detail_id: _Optional[int] = ...) -> None: ...

class DeletePetDetailForExtraOrderResponse(_message.Message):
    __slots__ = ("result",)
    RESULT_FIELD_NUMBER: _ClassVar[int]
    result: bool
    def __init__(self, result: bool = ...) -> None: ...

class UpdateUpcomingAppointmentsRequest(_message.Message):
    __slots__ = ("business_ids", "old_service")
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    OLD_SERVICE_FIELD_NUMBER: _ClassVar[int]
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    old_service: _service_models_pb2.ServiceModel
    def __init__(self, business_ids: _Optional[_Iterable[int]] = ..., old_service: _Optional[_Union[_service_models_pb2.ServiceModel, _Mapping]] = ...) -> None: ...

class UpdateUpcomingAppointmentsResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetLastPetDetailRequest(_message.Message):
    __slots__ = ("company_id", "customer_id", "pet_id", "filter")
    class Filter(_message.Message):
        __slots__ = ("business_id", "status", "start_time_range", "end_time_range", "service_item_types", "filter_no_start_time", "filter_booking_request", "service_ids")
        BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
        STATUS_FIELD_NUMBER: _ClassVar[int]
        START_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        END_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
        FILTER_NO_START_TIME_FIELD_NUMBER: _ClassVar[int]
        FILTER_BOOKING_REQUEST_FIELD_NUMBER: _ClassVar[int]
        SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
        business_id: int
        status: _containers.RepeatedScalarFieldContainer[_appointment_enums_pb2.AppointmentStatus]
        start_time_range: _interval_pb2.Interval
        end_time_range: _interval_pb2.Interval
        service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
        filter_no_start_time: bool
        filter_booking_request: bool
        service_ids: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, business_id: _Optional[int] = ..., status: _Optional[_Iterable[_Union[_appointment_enums_pb2.AppointmentStatus, str]]] = ..., start_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., end_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., filter_no_start_time: bool = ..., filter_booking_request: bool = ..., service_ids: _Optional[_Iterable[int]] = ...) -> None: ...
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    customer_id: _containers.RepeatedScalarFieldContainer[int]
    pet_id: _containers.RepeatedScalarFieldContainer[int]
    filter: GetLastPetDetailRequest.Filter
    def __init__(self, company_id: _Optional[int] = ..., customer_id: _Optional[_Iterable[int]] = ..., pet_id: _Optional[_Iterable[int]] = ..., filter: _Optional[_Union[GetLastPetDetailRequest.Filter, _Mapping]] = ...) -> None: ...

class GetLastPetDetailResponse(_message.Message):
    __slots__ = ("pet_details",)
    PET_DETAILS_FIELD_NUMBER: _ClassVar[int]
    pet_details: _containers.RepeatedCompositeFieldContainer[_pet_detail_models_pb2.PetDetailModel]
    def __init__(self, pet_details: _Optional[_Iterable[_Union[_pet_detail_models_pb2.PetDetailModel, _Mapping]]] = ...) -> None: ...

class GetStaffPetDetailsRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "staff_ids", "start_time_range", "end_time_range")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_IDS_FIELD_NUMBER: _ClassVar[int]
    START_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    staff_ids: _containers.RepeatedScalarFieldContainer[int]
    start_time_range: _interval_pb2.Interval
    end_time_range: _interval_pb2.Interval
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., staff_ids: _Optional[_Iterable[int]] = ..., start_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., end_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ...) -> None: ...

class GetStaffPetDetailsResponse(_message.Message):
    __slots__ = ("staff_pet_details",)
    STAFF_PET_DETAILS_FIELD_NUMBER: _ClassVar[int]
    staff_pet_details: _containers.RepeatedCompositeFieldContainer[_pet_detail_models_pb2.StaffPetDetail]
    def __init__(self, staff_pet_details: _Optional[_Iterable[_Union[_pet_detail_models_pb2.StaffPetDetail, _Mapping]]] = ...) -> None: ...
