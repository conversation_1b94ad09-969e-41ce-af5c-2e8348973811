# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.appointment.v1 import pet_detail_service_pb2 as moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2


class PetDetailServiceStub(object):
    """Appointment pet detail service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetPetDetail = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/GetPetDetail',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailResponse.FromString,
                _registered_method=True)
        self.SaveOrUpdatePetDetails = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/SaveOrUpdatePetDetails',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.SaveOrUpdatePetDetailsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.SaveOrUpdatePetDetailsResponse.FromString,
                _registered_method=True)
        self.CreatePetDetailsForExtraOrder = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/CreatePetDetailsForExtraOrder',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.CreatePetDetailsForExtraOrderRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.CreatePetDetailsForExtraOrderResponse.FromString,
                _registered_method=True)
        self.DeletePetDetailForExtraOrder = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/DeletePetDetailForExtraOrder',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetDetailForExtraOrderRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetDetailForExtraOrderResponse.FromString,
                _registered_method=True)
        self.DeletePet = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/DeletePet',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetResponse.FromString,
                _registered_method=True)
        self.DeletePetEvaluation = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/DeletePetEvaluation',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetEvaluationRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetEvaluationResponse.FromString,
                _registered_method=True)
        self.UpdateUpcomingPetDetails = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/UpdateUpcomingPetDetails',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingPetDetailsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingPetDetailsResponse.FromString,
                _registered_method=True)
        self.UpdateUpcomingAppointments = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/UpdateUpcomingAppointments',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingAppointmentsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingAppointmentsResponse.FromString,
                _registered_method=True)
        self.GetPetDetailList = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/GetPetDetailList',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailListRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailListResponse.FromString,
                _registered_method=True)
        self.UpdatePetDetail = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/UpdatePetDetail',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdatePetDetailRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdatePetDetailResponse.FromString,
                _registered_method=True)
        self.GetLastPetDetail = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/GetLastPetDetail',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetLastPetDetailRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetLastPetDetailResponse.FromString,
                _registered_method=True)
        self.GetStaffPetDetails = channel.unary_unary(
                '/moego.service.appointment.v1.PetDetailService/GetStaffPetDetails',
                request_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetStaffPetDetailsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetStaffPetDetailsResponse.FromString,
                _registered_method=True)


class PetDetailServiceServicer(object):
    """Appointment pet detail service
    """

    def GetPetDetail(self, request, context):
        """Get pet detail by id, not including deleted records.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveOrUpdatePetDetails(self, request, context):
        """Save or update pet's selected services
        If there is no pet, the selected services will be saved directly.
        If there is a pet, it will delete the original services selected for the pet, then save the newly selected services again.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePetDetailsForExtraOrder(self, request, context):
        """add new pet detail for extra order
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePetDetailForExtraOrder(self, request, context):
        """delete pet detail for extra order
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePet(self, request, context):
        """Delete selected pet
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePetEvaluation(self, request, context):
        """Delete selected pet evaluation service detail
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateUpcomingPetDetails(self, request, context):
        """Update upcoming appt pet details
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateUpcomingAppointments(self, request, context):
        """Update upcoming appointments
        NOTE: 这个接口调用很慢，业务代码不要尝试同步调用。
        这个接口的使用场景很独立，只有在修改 service 触发 apply to upcoming 时才会调用。
        不要尝试复用这个接口，除非你知道这个接口的真正目的。
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPetDetailList(self, request, context):
        """get pet detail list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdatePetDetail(self, request, context):
        """Selectively update a PetDetail, only the fields that are set in the request will be updated
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLastPetDetail(self, request, context):
        """get last pet detail
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStaffPetDetails(self, request, context):
        """get staff pet detail
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PetDetailServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetPetDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPetDetail,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailResponse.SerializeToString,
            ),
            'SaveOrUpdatePetDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveOrUpdatePetDetails,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.SaveOrUpdatePetDetailsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.SaveOrUpdatePetDetailsResponse.SerializeToString,
            ),
            'CreatePetDetailsForExtraOrder': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePetDetailsForExtraOrder,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.CreatePetDetailsForExtraOrderRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.CreatePetDetailsForExtraOrderResponse.SerializeToString,
            ),
            'DeletePetDetailForExtraOrder': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePetDetailForExtraOrder,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetDetailForExtraOrderRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetDetailForExtraOrderResponse.SerializeToString,
            ),
            'DeletePet': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePet,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetResponse.SerializeToString,
            ),
            'DeletePetEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePetEvaluation,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetEvaluationRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetEvaluationResponse.SerializeToString,
            ),
            'UpdateUpcomingPetDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateUpcomingPetDetails,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingPetDetailsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingPetDetailsResponse.SerializeToString,
            ),
            'UpdateUpcomingAppointments': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateUpcomingAppointments,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingAppointmentsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingAppointmentsResponse.SerializeToString,
            ),
            'GetPetDetailList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPetDetailList,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailListRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailListResponse.SerializeToString,
            ),
            'UpdatePetDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdatePetDetail,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdatePetDetailRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdatePetDetailResponse.SerializeToString,
            ),
            'GetLastPetDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLastPetDetail,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetLastPetDetailRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetLastPetDetailResponse.SerializeToString,
            ),
            'GetStaffPetDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStaffPetDetails,
                    request_deserializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetStaffPetDetailsRequest.FromString,
                    response_serializer=moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetStaffPetDetailsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.appointment.v1.PetDetailService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.appointment.v1.PetDetailService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PetDetailService(object):
    """Appointment pet detail service
    """

    @staticmethod
    def GetPetDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/GetPetDetail',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SaveOrUpdatePetDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/SaveOrUpdatePetDetails',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.SaveOrUpdatePetDetailsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.SaveOrUpdatePetDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePetDetailsForExtraOrder(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/CreatePetDetailsForExtraOrder',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.CreatePetDetailsForExtraOrderRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.CreatePetDetailsForExtraOrderResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePetDetailForExtraOrder(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/DeletePetDetailForExtraOrder',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetDetailForExtraOrderRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetDetailForExtraOrderResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/DeletePet',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePetEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/DeletePetEvaluation',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetEvaluationRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.DeletePetEvaluationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateUpcomingPetDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/UpdateUpcomingPetDetails',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingPetDetailsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingPetDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateUpcomingAppointments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/UpdateUpcomingAppointments',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingAppointmentsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdateUpcomingAppointmentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPetDetailList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/GetPetDetailList',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailListRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetPetDetailListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdatePetDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/UpdatePetDetail',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdatePetDetailRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.UpdatePetDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLastPetDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/GetLastPetDetail',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetLastPetDetailRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetLastPetDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetStaffPetDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.appointment.v1.PetDetailService/GetStaffPetDetails',
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetStaffPetDetailsRequest.SerializeToString,
            moego_dot_service_dot_appointment_dot_v1_dot_pet__detail__service__pb2.GetStaffPetDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
