# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/billing/v1/subscription_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/billing/v1/subscription_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import interval_pb2 as google_dot_type_dot_interval__pb2
from moego.models.billing.v1 import subscription_defs_pb2 as moego_dot_models_dot_billing_dot_v1_dot_subscription__defs__pb2
from moego.models.billing.v1 import subscription_models_pb2 as moego_dot_models_dot_billing_dot_v1_dot_subscription__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n3moego/service/billing/v1/subscription_service.proto\x12\x18moego.service.billing.v1\x1a\x1agoogle/type/interval.proto\x1a/moego/models/billing/v1/subscription_defs.proto\x1a\x31moego/models/billing/v1/subscription_models.proto\x1a\x17validate/validate.proto\"\xbc\x07\n\x19\x43reateSubscriptionRequest\x12@\n\nplan_units\x18\x01 \x03(\x0b\x32!.moego.models.billing.v1.PlanUnitR\tplanUnits\x12.\n\x0epayment_method\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x18@R\rpaymentMethod\x12 \n\tcoupon_id\x18\x03 \x01(\x03H\x00R\x08\x63ouponId\x88\x01\x01\x12T\n\x17\x61pplication_fee_percent\x18\x04 \x01(\x01\x42\x17\xfa\x42\x14\x12\x12\x19\x00\x00\x00\x00\x00\x00$@)\x00\x00\x00\x00\x00\x00\x00\x00H\x01R\x15\x61pplicationFeePercent\x88\x01\x01\x12.\n\x0con_behalf_of\x18\x05 \x01(\tB\x07\xfa\x42\x04r\x02\x18@H\x02R\nonBehalfOf\x88\x01\x01\x12O\n\rtransfer_data\x18\x06 \x01(\x0b\x32%.moego.models.billing.v1.TransferDataH\x03R\x0ctransferData\x88\x01\x01\x12$\n\x0b\x63ustomer_id\x18\x07 \x01(\x03H\x04R\ncustomerId\x88\x01\x01\x12\x65\n\x10payment_behavior\x18\x08 \x01(\x0e\x32:.moego.models.billing.v1.SubscriptionModel.PaymentBehaviorR\x0fpaymentBehavior\x12]\n\x08metadata\x18\n \x03(\x0b\x32\x41.moego.service.billing.v1.CreateSubscriptionRequest.MetadataEntryR\x08metadata\x12%\n\x0b\x64\x65scription\x18\x0b \x01(\tH\x05R\x0b\x64\x65scription\x88\x01\x01\x12\x35\n\x12vendor_customer_id\x18\x64 \x01(\tB\x07\xfa\x42\x04r\x02\x18@R\x10vendorCustomerId\x12,\n\x0fidempotency_key\x18\x0c \x01(\tH\x06R\x0eidempotencyKey\x88\x01\x01\x1a;\n\rMetadataEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\x42\x0c\n\n_coupon_idB\x1a\n\x18_application_fee_percentB\x0f\n\r_on_behalf_ofB\x10\n\x0e_transfer_dataB\x0e\n\x0c_customer_idB\x0e\n\x0c_descriptionB\x12\n\x10_idempotency_key\"\xec\x01\n\x1a\x43reateSubscriptionResponse\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12#\n\rclient_secret\x18\x02 \x01(\tR\x0c\x63lientSecret\x12\x34\n\x16vendor_subscription_id\x18\x03 \x01(\tR\x14vendorSubscriptionId\x12*\n\x11vendor_invoice_id\x18\x04 \x01(\tR\x0fvendorInvoiceId\x12\x37\n\x18vendor_payment_intent_id\x18\x05 \x01(\tR\x15vendorPaymentIntentId\"\xd3\x06\n\x19UpdateSubscriptionRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12@\n\nplan_units\x18\x02 \x03(\x0b\x32!.moego.models.billing.v1.PlanUnitR\tplanUnits\x12%\n\x0epayment_method\x18\x03 \x01(\tR\rpaymentMethod\x12t\n\x12proration_behavior\x18\x04 \x01(\x0e\x32\x45.moego.service.billing.v1.UpdateSubscriptionRequest.ProrationBehaviorR\x11prorationBehavior\x12T\n\x17\x61pplication_fee_percent\x18\x05 \x01(\x01\x42\x17\xfa\x42\x14\x12\x12\x19\x00\x00\x00\x00\x00\x00$@)\x00\x00\x00\x00\x00\x00\x00\x00H\x00R\x15\x61pplicationFeePercent\x88\x01\x01\x12\x65\n\x10payment_behavior\x18\x06 \x01(\x0e\x32:.moego.models.billing.v1.SubscriptionModel.PaymentBehaviorR\x0fpaymentBehavior\x12]\n\x08metadata\x18\n \x03(\x0b\x32\x41.moego.service.billing.v1.UpdateSubscriptionRequest.MetadataEntryR\x08metadata\x12S\n\x10pause_collection\x18\x0b \x01(\x0b\x32(.moego.models.billing.v1.PauseCollectionR\x0fpauseCollection\x1a;\n\rMetadataEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\"t\n\x11ProrationBehavior\x12\"\n\x1ePRORATION_BEHAVIOR_UNSPECIFIED\x10\x00\x12\x10\n\x0cNO_PRORATION\x10\x01\x12\x15\n\x11\x43REATE_PRORATIONS\x10\x02\x12\x12\n\x0e\x41LWAYS_INVOICE\x10\x03\x42\x1a\n\x18_application_fee_percent\"\xc7\x01\n\x1aUpdateSubscriptionResponse\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x34\n\x16vendor_subscription_id\x18\x02 \x01(\tR\x14vendorSubscriptionId\x12*\n\x11vendor_invoice_id\x18\x03 \x01(\tR\x0fvendorInvoiceId\x12\x37\n\x18vendor_payment_intent_id\x18\x04 \x01(\tR\x15vendorPaymentIntentId\"\xbf\x02\n\x19\x43\x61ncelSubscriptionRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12k\n\x0b\x63\x61ncel_type\x18\x02 \x01(\x0e\x32>.moego.service.billing.v1.CancelSubscriptionRequest.CancelTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\ncancelType\x12\x1b\n\x06revert\x18\x03 \x01(\x08H\x00R\x06revert\x88\x01\x01\x12\x1b\n\x06refund\x18\x04 \x01(\x08H\x01R\x06refund\x88\x01\x01\"L\n\nCancelType\x12\x1b\n\x17\x43\x41NCEL_TYPE_UNSPECIFIED\x10\x00\x12\x0f\n\x0bIMMEDIATELY\x10\x01\x12\x10\n\x0c\x45ND_OF_CYCLE\x10\x02\x42\t\n\x07_revertB\t\n\x07_refund\",\n\x1a\x43\x61ncelSubscriptionResponse\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\"]\n#ConvertVendorSubscriptionIdsRequest\x12\x36\n\x17vendor_subscription_ids\x18\x01 \x03(\tR\x15vendorSubscriptionIds\"Q\n$ConvertVendorSubscriptionIdsResponse\x12)\n\x10subscription_ids\x18\x01 \x03(\x03R\x0fsubscriptionIds\"(\n\x16GetSubscriptionRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\"i\n\x17GetSubscriptionResponse\x12N\n\x0csubscription\x18\x01 \x01(\x0b\x32*.moego.models.billing.v1.SubscriptionModelR\x0csubscription\"\xd7\x01\n\x1fScheduleNextBillingCycleRequest\x12\'\n\x0fsubscription_id\x18\x01 \x01(\x03R\x0esubscriptionId\x12@\n\nplan_units\x18\x02 \x03(\x0b\x32!.moego.models.billing.v1.PlanUnitR\tplanUnits\x12I\n\x15\x63urrent_billing_cycle\x18\x03 \x01(\x0b\x32\x15.google.type.IntervalR\x13\x63urrentBillingCycle\"\x8b\x01\n ScheduleNextBillingCycleResponse\x12g\n\x15subscription_schedule\x18\x01 \x01(\x0b\x32\x32.moego.models.billing.v1.SubscriptionScheduleModelR\x14subscriptionSchedule\"\xa7\x01\n\x18ListSubscriptionsRequest\x12Q\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x39.moego.service.billing.v1.ListSubscriptionsRequest.FilterR\x06\x66ilter\x1a\x38\n\x06\x46ilter\x12.\n\x13vendor_customer_ids\x18\x01 \x03(\tR\x11vendorCustomerIds\"m\n\x19ListSubscriptionsResponse\x12P\n\rsubscriptions\x18\x01 \x03(\x0b\x32*.moego.models.billing.v1.SubscriptionModelR\rsubscriptions2\xc2\x07\n\x13SubscriptionService\x12\x7f\n\x12\x43reateSubscription\x12\x33.moego.service.billing.v1.CreateSubscriptionRequest\x1a\x34.moego.service.billing.v1.CreateSubscriptionResponse\x12\x7f\n\x12UpdateSubscription\x12\x33.moego.service.billing.v1.UpdateSubscriptionRequest\x1a\x34.moego.service.billing.v1.UpdateSubscriptionResponse\x12\x7f\n\x12\x43\x61ncelSubscription\x12\x33.moego.service.billing.v1.CancelSubscriptionRequest\x1a\x34.moego.service.billing.v1.CancelSubscriptionResponse\x12\x9d\x01\n\x1c\x43onvertVendorSubscriptionIds\x12=.moego.service.billing.v1.ConvertVendorSubscriptionIdsRequest\x1a>.moego.service.billing.v1.ConvertVendorSubscriptionIdsResponse\x12v\n\x0fGetSubscription\x12\x30.moego.service.billing.v1.GetSubscriptionRequest\x1a\x31.moego.service.billing.v1.GetSubscriptionResponse\x12\x91\x01\n\x18ScheduleNextBillingCycle\x12\x39.moego.service.billing.v1.ScheduleNextBillingCycleRequest\x1a:.moego.service.billing.v1.ScheduleNextBillingCycleResponse\x12|\n\x11ListSubscriptions\x12\x32.moego.service.billing.v1.ListSubscriptionsRequest\x1a\x33.moego.service.billing.v1.ListSubscriptionsResponseB\x80\x01\n com.moego.idl.service.billing.v1P\x01ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/billing/v1;billingsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.billing.v1.subscription_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.service.billing.v1P\001ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/billing/v1;billingsvcpb'
  _globals['_CREATESUBSCRIPTIONREQUEST_METADATAENTRY']._loaded_options = None
  _globals['_CREATESUBSCRIPTIONREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_CREATESUBSCRIPTIONREQUEST'].fields_by_name['payment_method']._loaded_options = None
  _globals['_CREATESUBSCRIPTIONREQUEST'].fields_by_name['payment_method']._serialized_options = b'\372B\004r\002\030@'
  _globals['_CREATESUBSCRIPTIONREQUEST'].fields_by_name['application_fee_percent']._loaded_options = None
  _globals['_CREATESUBSCRIPTIONREQUEST'].fields_by_name['application_fee_percent']._serialized_options = b'\372B\024\022\022\031\000\000\000\000\000\000$@)\000\000\000\000\000\000\000\000'
  _globals['_CREATESUBSCRIPTIONREQUEST'].fields_by_name['on_behalf_of']._loaded_options = None
  _globals['_CREATESUBSCRIPTIONREQUEST'].fields_by_name['on_behalf_of']._serialized_options = b'\372B\004r\002\030@'
  _globals['_CREATESUBSCRIPTIONREQUEST'].fields_by_name['vendor_customer_id']._loaded_options = None
  _globals['_CREATESUBSCRIPTIONREQUEST'].fields_by_name['vendor_customer_id']._serialized_options = b'\372B\004r\002\030@'
  _globals['_UPDATESUBSCRIPTIONREQUEST_METADATAENTRY']._loaded_options = None
  _globals['_UPDATESUBSCRIPTIONREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_UPDATESUBSCRIPTIONREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATESUBSCRIPTIONREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATESUBSCRIPTIONREQUEST'].fields_by_name['application_fee_percent']._loaded_options = None
  _globals['_UPDATESUBSCRIPTIONREQUEST'].fields_by_name['application_fee_percent']._serialized_options = b'\372B\024\022\022\031\000\000\000\000\000\000$@)\000\000\000\000\000\000\000\000'
  _globals['_CANCELSUBSCRIPTIONREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_CANCELSUBSCRIPTIONREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CANCELSUBSCRIPTIONREQUEST'].fields_by_name['cancel_type']._loaded_options = None
  _globals['_CANCELSUBSCRIPTIONREQUEST'].fields_by_name['cancel_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_CREATESUBSCRIPTIONREQUEST']._serialized_start=235
  _globals['_CREATESUBSCRIPTIONREQUEST']._serialized_end=1191
  _globals['_CREATESUBSCRIPTIONREQUEST_METADATAENTRY']._serialized_start=1003
  _globals['_CREATESUBSCRIPTIONREQUEST_METADATAENTRY']._serialized_end=1062
  _globals['_CREATESUBSCRIPTIONRESPONSE']._serialized_start=1194
  _globals['_CREATESUBSCRIPTIONRESPONSE']._serialized_end=1430
  _globals['_UPDATESUBSCRIPTIONREQUEST']._serialized_start=1433
  _globals['_UPDATESUBSCRIPTIONREQUEST']._serialized_end=2284
  _globals['_UPDATESUBSCRIPTIONREQUEST_METADATAENTRY']._serialized_start=1003
  _globals['_UPDATESUBSCRIPTIONREQUEST_METADATAENTRY']._serialized_end=1062
  _globals['_UPDATESUBSCRIPTIONREQUEST_PRORATIONBEHAVIOR']._serialized_start=2140
  _globals['_UPDATESUBSCRIPTIONREQUEST_PRORATIONBEHAVIOR']._serialized_end=2256
  _globals['_UPDATESUBSCRIPTIONRESPONSE']._serialized_start=2287
  _globals['_UPDATESUBSCRIPTIONRESPONSE']._serialized_end=2486
  _globals['_CANCELSUBSCRIPTIONREQUEST']._serialized_start=2489
  _globals['_CANCELSUBSCRIPTIONREQUEST']._serialized_end=2808
  _globals['_CANCELSUBSCRIPTIONREQUEST_CANCELTYPE']._serialized_start=2710
  _globals['_CANCELSUBSCRIPTIONREQUEST_CANCELTYPE']._serialized_end=2786
  _globals['_CANCELSUBSCRIPTIONRESPONSE']._serialized_start=2810
  _globals['_CANCELSUBSCRIPTIONRESPONSE']._serialized_end=2854
  _globals['_CONVERTVENDORSUBSCRIPTIONIDSREQUEST']._serialized_start=2856
  _globals['_CONVERTVENDORSUBSCRIPTIONIDSREQUEST']._serialized_end=2949
  _globals['_CONVERTVENDORSUBSCRIPTIONIDSRESPONSE']._serialized_start=2951
  _globals['_CONVERTVENDORSUBSCRIPTIONIDSRESPONSE']._serialized_end=3032
  _globals['_GETSUBSCRIPTIONREQUEST']._serialized_start=3034
  _globals['_GETSUBSCRIPTIONREQUEST']._serialized_end=3074
  _globals['_GETSUBSCRIPTIONRESPONSE']._serialized_start=3076
  _globals['_GETSUBSCRIPTIONRESPONSE']._serialized_end=3181
  _globals['_SCHEDULENEXTBILLINGCYCLEREQUEST']._serialized_start=3184
  _globals['_SCHEDULENEXTBILLINGCYCLEREQUEST']._serialized_end=3399
  _globals['_SCHEDULENEXTBILLINGCYCLERESPONSE']._serialized_start=3402
  _globals['_SCHEDULENEXTBILLINGCYCLERESPONSE']._serialized_end=3541
  _globals['_LISTSUBSCRIPTIONSREQUEST']._serialized_start=3544
  _globals['_LISTSUBSCRIPTIONSREQUEST']._serialized_end=3711
  _globals['_LISTSUBSCRIPTIONSREQUEST_FILTER']._serialized_start=3655
  _globals['_LISTSUBSCRIPTIONSREQUEST_FILTER']._serialized_end=3711
  _globals['_LISTSUBSCRIPTIONSRESPONSE']._serialized_start=3713
  _globals['_LISTSUBSCRIPTIONSRESPONSE']._serialized_end=3822
  _globals['_SUBSCRIPTIONSERVICE']._serialized_start=3825
  _globals['_SUBSCRIPTIONSERVICE']._serialized_end=4787
# @@protoc_insertion_point(module_scope)
