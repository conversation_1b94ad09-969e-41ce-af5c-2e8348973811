from google.type import interval_pb2 as _interval_pb2
from moego.models.billing.v1 import subscription_defs_pb2 as _subscription_defs_pb2
from moego.models.billing.v1 import subscription_models_pb2 as _subscription_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateSubscriptionRequest(_message.Message):
    __slots__ = ("plan_units", "payment_method", "coupon_id", "application_fee_percent", "on_behalf_of", "transfer_data", "customer_id", "payment_behavior", "metadata", "description", "vendor_customer_id", "idempotency_key")
    class MetadataEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    PLAN_UNITS_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_METHOD_FIELD_NUMBER: _ClassVar[int]
    COUPON_ID_FIELD_NUMBER: _ClassVar[int]
    APPLICATION_FEE_PERCENT_FIELD_NUMBER: _ClassVar[int]
    ON_BEHALF_OF_FIELD_NUMBER: _ClassVar[int]
    TRANSFER_DATA_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_BEHAVIOR_FIELD_NUMBER: _ClassVar[int]
    METADATA_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    VENDOR_CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    IDEMPOTENCY_KEY_FIELD_NUMBER: _ClassVar[int]
    plan_units: _containers.RepeatedCompositeFieldContainer[_subscription_defs_pb2.PlanUnit]
    payment_method: str
    coupon_id: int
    application_fee_percent: float
    on_behalf_of: str
    transfer_data: _subscription_defs_pb2.TransferData
    customer_id: int
    payment_behavior: _subscription_models_pb2.SubscriptionModel.PaymentBehavior
    metadata: _containers.ScalarMap[str, str]
    description: str
    vendor_customer_id: str
    idempotency_key: str
    def __init__(self, plan_units: _Optional[_Iterable[_Union[_subscription_defs_pb2.PlanUnit, _Mapping]]] = ..., payment_method: _Optional[str] = ..., coupon_id: _Optional[int] = ..., application_fee_percent: _Optional[float] = ..., on_behalf_of: _Optional[str] = ..., transfer_data: _Optional[_Union[_subscription_defs_pb2.TransferData, _Mapping]] = ..., customer_id: _Optional[int] = ..., payment_behavior: _Optional[_Union[_subscription_models_pb2.SubscriptionModel.PaymentBehavior, str]] = ..., metadata: _Optional[_Mapping[str, str]] = ..., description: _Optional[str] = ..., vendor_customer_id: _Optional[str] = ..., idempotency_key: _Optional[str] = ...) -> None: ...

class CreateSubscriptionResponse(_message.Message):
    __slots__ = ("id", "client_secret", "vendor_subscription_id", "vendor_invoice_id", "vendor_payment_intent_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    CLIENT_SECRET_FIELD_NUMBER: _ClassVar[int]
    VENDOR_SUBSCRIPTION_ID_FIELD_NUMBER: _ClassVar[int]
    VENDOR_INVOICE_ID_FIELD_NUMBER: _ClassVar[int]
    VENDOR_PAYMENT_INTENT_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    client_secret: str
    vendor_subscription_id: str
    vendor_invoice_id: str
    vendor_payment_intent_id: str
    def __init__(self, id: _Optional[int] = ..., client_secret: _Optional[str] = ..., vendor_subscription_id: _Optional[str] = ..., vendor_invoice_id: _Optional[str] = ..., vendor_payment_intent_id: _Optional[str] = ...) -> None: ...

class UpdateSubscriptionRequest(_message.Message):
    __slots__ = ("id", "plan_units", "payment_method", "proration_behavior", "application_fee_percent", "payment_behavior", "metadata", "pause_collection")
    class ProrationBehavior(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        PRORATION_BEHAVIOR_UNSPECIFIED: _ClassVar[UpdateSubscriptionRequest.ProrationBehavior]
        NO_PRORATION: _ClassVar[UpdateSubscriptionRequest.ProrationBehavior]
        CREATE_PRORATIONS: _ClassVar[UpdateSubscriptionRequest.ProrationBehavior]
        ALWAYS_INVOICE: _ClassVar[UpdateSubscriptionRequest.ProrationBehavior]
    PRORATION_BEHAVIOR_UNSPECIFIED: UpdateSubscriptionRequest.ProrationBehavior
    NO_PRORATION: UpdateSubscriptionRequest.ProrationBehavior
    CREATE_PRORATIONS: UpdateSubscriptionRequest.ProrationBehavior
    ALWAYS_INVOICE: UpdateSubscriptionRequest.ProrationBehavior
    class MetadataEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    PLAN_UNITS_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_METHOD_FIELD_NUMBER: _ClassVar[int]
    PRORATION_BEHAVIOR_FIELD_NUMBER: _ClassVar[int]
    APPLICATION_FEE_PERCENT_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_BEHAVIOR_FIELD_NUMBER: _ClassVar[int]
    METADATA_FIELD_NUMBER: _ClassVar[int]
    PAUSE_COLLECTION_FIELD_NUMBER: _ClassVar[int]
    id: int
    plan_units: _containers.RepeatedCompositeFieldContainer[_subscription_defs_pb2.PlanUnit]
    payment_method: str
    proration_behavior: UpdateSubscriptionRequest.ProrationBehavior
    application_fee_percent: float
    payment_behavior: _subscription_models_pb2.SubscriptionModel.PaymentBehavior
    metadata: _containers.ScalarMap[str, str]
    pause_collection: _subscription_models_pb2.PauseCollection
    def __init__(self, id: _Optional[int] = ..., plan_units: _Optional[_Iterable[_Union[_subscription_defs_pb2.PlanUnit, _Mapping]]] = ..., payment_method: _Optional[str] = ..., proration_behavior: _Optional[_Union[UpdateSubscriptionRequest.ProrationBehavior, str]] = ..., application_fee_percent: _Optional[float] = ..., payment_behavior: _Optional[_Union[_subscription_models_pb2.SubscriptionModel.PaymentBehavior, str]] = ..., metadata: _Optional[_Mapping[str, str]] = ..., pause_collection: _Optional[_Union[_subscription_models_pb2.PauseCollection, _Mapping]] = ...) -> None: ...

class UpdateSubscriptionResponse(_message.Message):
    __slots__ = ("id", "vendor_subscription_id", "vendor_invoice_id", "vendor_payment_intent_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    VENDOR_SUBSCRIPTION_ID_FIELD_NUMBER: _ClassVar[int]
    VENDOR_INVOICE_ID_FIELD_NUMBER: _ClassVar[int]
    VENDOR_PAYMENT_INTENT_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    vendor_subscription_id: str
    vendor_invoice_id: str
    vendor_payment_intent_id: str
    def __init__(self, id: _Optional[int] = ..., vendor_subscription_id: _Optional[str] = ..., vendor_invoice_id: _Optional[str] = ..., vendor_payment_intent_id: _Optional[str] = ...) -> None: ...

class CancelSubscriptionRequest(_message.Message):
    __slots__ = ("id", "cancel_type", "revert", "refund")
    class CancelType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        CANCEL_TYPE_UNSPECIFIED: _ClassVar[CancelSubscriptionRequest.CancelType]
        IMMEDIATELY: _ClassVar[CancelSubscriptionRequest.CancelType]
        END_OF_CYCLE: _ClassVar[CancelSubscriptionRequest.CancelType]
    CANCEL_TYPE_UNSPECIFIED: CancelSubscriptionRequest.CancelType
    IMMEDIATELY: CancelSubscriptionRequest.CancelType
    END_OF_CYCLE: CancelSubscriptionRequest.CancelType
    ID_FIELD_NUMBER: _ClassVar[int]
    CANCEL_TYPE_FIELD_NUMBER: _ClassVar[int]
    REVERT_FIELD_NUMBER: _ClassVar[int]
    REFUND_FIELD_NUMBER: _ClassVar[int]
    id: int
    cancel_type: CancelSubscriptionRequest.CancelType
    revert: bool
    refund: bool
    def __init__(self, id: _Optional[int] = ..., cancel_type: _Optional[_Union[CancelSubscriptionRequest.CancelType, str]] = ..., revert: bool = ..., refund: bool = ...) -> None: ...

class CancelSubscriptionResponse(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class ConvertVendorSubscriptionIdsRequest(_message.Message):
    __slots__ = ("vendor_subscription_ids",)
    VENDOR_SUBSCRIPTION_IDS_FIELD_NUMBER: _ClassVar[int]
    vendor_subscription_ids: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, vendor_subscription_ids: _Optional[_Iterable[str]] = ...) -> None: ...

class ConvertVendorSubscriptionIdsResponse(_message.Message):
    __slots__ = ("subscription_ids",)
    SUBSCRIPTION_IDS_FIELD_NUMBER: _ClassVar[int]
    subscription_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, subscription_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class GetSubscriptionRequest(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class GetSubscriptionResponse(_message.Message):
    __slots__ = ("subscription",)
    SUBSCRIPTION_FIELD_NUMBER: _ClassVar[int]
    subscription: _subscription_models_pb2.SubscriptionModel
    def __init__(self, subscription: _Optional[_Union[_subscription_models_pb2.SubscriptionModel, _Mapping]] = ...) -> None: ...

class ScheduleNextBillingCycleRequest(_message.Message):
    __slots__ = ("subscription_id", "plan_units", "current_billing_cycle")
    SUBSCRIPTION_ID_FIELD_NUMBER: _ClassVar[int]
    PLAN_UNITS_FIELD_NUMBER: _ClassVar[int]
    CURRENT_BILLING_CYCLE_FIELD_NUMBER: _ClassVar[int]
    subscription_id: int
    plan_units: _containers.RepeatedCompositeFieldContainer[_subscription_defs_pb2.PlanUnit]
    current_billing_cycle: _interval_pb2.Interval
    def __init__(self, subscription_id: _Optional[int] = ..., plan_units: _Optional[_Iterable[_Union[_subscription_defs_pb2.PlanUnit, _Mapping]]] = ..., current_billing_cycle: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ...) -> None: ...

class ScheduleNextBillingCycleResponse(_message.Message):
    __slots__ = ("subscription_schedule",)
    SUBSCRIPTION_SCHEDULE_FIELD_NUMBER: _ClassVar[int]
    subscription_schedule: _subscription_models_pb2.SubscriptionScheduleModel
    def __init__(self, subscription_schedule: _Optional[_Union[_subscription_models_pb2.SubscriptionScheduleModel, _Mapping]] = ...) -> None: ...

class ListSubscriptionsRequest(_message.Message):
    __slots__ = ("filter",)
    class Filter(_message.Message):
        __slots__ = ("vendor_customer_ids",)
        VENDOR_CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
        vendor_customer_ids: _containers.RepeatedScalarFieldContainer[str]
        def __init__(self, vendor_customer_ids: _Optional[_Iterable[str]] = ...) -> None: ...
    FILTER_FIELD_NUMBER: _ClassVar[int]
    filter: ListSubscriptionsRequest.Filter
    def __init__(self, filter: _Optional[_Union[ListSubscriptionsRequest.Filter, _Mapping]] = ...) -> None: ...

class ListSubscriptionsResponse(_message.Message):
    __slots__ = ("subscriptions",)
    SUBSCRIPTIONS_FIELD_NUMBER: _ClassVar[int]
    subscriptions: _containers.RepeatedCompositeFieldContainer[_subscription_models_pb2.SubscriptionModel]
    def __init__(self, subscriptions: _Optional[_Iterable[_Union[_subscription_models_pb2.SubscriptionModel, _Mapping]]] = ...) -> None: ...
