# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/enterprise/v1/setting_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/enterprise/v1/setting_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1moego/service/enterprise/v1/setting_service.proto\x12\x1bmoego.service.enterprise.v1\x1a\x17validate/validate.proto\"\x8d\x01\n\x16\x43opyIntakeFormsRequest\x12\x33\n\x11source_company_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0fsourceCompanyId\x12>\n\x12target_company_ids\x18\x02 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x01\x18\x01\"\x04\"\x02 \x00R\x10targetCompanyIds\"\x19\n\x17\x43opyIntakeFormsResponse\"\x8f\x01\n\x18\x43opyDiscountCodesRequest\x12\x33\n\x11source_company_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0fsourceCompanyId\x12>\n\x12target_company_ids\x18\x02 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x01\x18\x01\"\x04\"\x02 \x00R\x10targetCompanyIds\"\x1b\n\x19\x43opyDiscountCodesResponse\"\x87\x01\n\x10\x43opyRolesRequest\x12\x33\n\x11source_company_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0fsourceCompanyId\x12>\n\x12target_company_ids\x18\x02 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x01\x18\x01\"\x04\"\x02 \x00R\x10targetCompanyIds\"\x13\n\x11\x43opyRolesResponse\"\x8a\x01\n\x13\x43opyPackagesRequest\x12\x33\n\x11source_company_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0fsourceCompanyId\x12>\n\x12target_company_ids\x18\x02 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x01\x18\x01\"\x04\"\x02 \x00R\x10targetCompanyIds\"\x16\n\x14\x43opyPackagesResponse\"\x8d\x01\n\x16\x43opyMembershipsRequest\x12\x33\n\x11source_company_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0fsourceCompanyId\x12>\n\x12target_company_ids\x18\x02 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x01\x18\x01\"\x04\"\x02 \x00R\x10targetCompanyIds\"\x19\n\x17\x43opyMembershipsResponse2\xf2\x04\n\x0eSettingService\x12|\n\x0f\x43opyIntakeForms\x12\x33.moego.service.enterprise.v1.CopyIntakeFormsRequest\x1a\x34.moego.service.enterprise.v1.CopyIntakeFormsResponse\x12\x82\x01\n\x11\x43opyDiscountCodes\x12\x35.moego.service.enterprise.v1.CopyDiscountCodesRequest\x1a\x36.moego.service.enterprise.v1.CopyDiscountCodesResponse\x12j\n\tCopyRoles\x12-.moego.service.enterprise.v1.CopyRolesRequest\x1a..moego.service.enterprise.v1.CopyRolesResponse\x12s\n\x0c\x43opyPackages\x12\x30.moego.service.enterprise.v1.CopyPackagesRequest\x1a\x31.moego.service.enterprise.v1.CopyPackagesResponse\x12|\n\x0f\x43opyMemberships\x12\x33.moego.service.enterprise.v1.CopyMembershipsRequest\x1a\x34.moego.service.enterprise.v1.CopyMembershipsResponseB\x89\x01\n#com.moego.idl.service.enterprise.v1P\x01Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.enterprise.v1.setting_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.service.enterprise.v1P\001Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb'
  _globals['_COPYINTAKEFORMSREQUEST'].fields_by_name['source_company_id']._loaded_options = None
  _globals['_COPYINTAKEFORMSREQUEST'].fields_by_name['source_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COPYINTAKEFORMSREQUEST'].fields_by_name['target_company_ids']._loaded_options = None
  _globals['_COPYINTAKEFORMSREQUEST'].fields_by_name['target_company_ids']._serialized_options = b'\372B\r\222\001\n\010\001\030\001\"\004\"\002 \000'
  _globals['_COPYDISCOUNTCODESREQUEST'].fields_by_name['source_company_id']._loaded_options = None
  _globals['_COPYDISCOUNTCODESREQUEST'].fields_by_name['source_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COPYDISCOUNTCODESREQUEST'].fields_by_name['target_company_ids']._loaded_options = None
  _globals['_COPYDISCOUNTCODESREQUEST'].fields_by_name['target_company_ids']._serialized_options = b'\372B\r\222\001\n\010\001\030\001\"\004\"\002 \000'
  _globals['_COPYROLESREQUEST'].fields_by_name['source_company_id']._loaded_options = None
  _globals['_COPYROLESREQUEST'].fields_by_name['source_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COPYROLESREQUEST'].fields_by_name['target_company_ids']._loaded_options = None
  _globals['_COPYROLESREQUEST'].fields_by_name['target_company_ids']._serialized_options = b'\372B\r\222\001\n\010\001\030\001\"\004\"\002 \000'
  _globals['_COPYPACKAGESREQUEST'].fields_by_name['source_company_id']._loaded_options = None
  _globals['_COPYPACKAGESREQUEST'].fields_by_name['source_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COPYPACKAGESREQUEST'].fields_by_name['target_company_ids']._loaded_options = None
  _globals['_COPYPACKAGESREQUEST'].fields_by_name['target_company_ids']._serialized_options = b'\372B\r\222\001\n\010\001\030\001\"\004\"\002 \000'
  _globals['_COPYMEMBERSHIPSREQUEST'].fields_by_name['source_company_id']._loaded_options = None
  _globals['_COPYMEMBERSHIPSREQUEST'].fields_by_name['source_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COPYMEMBERSHIPSREQUEST'].fields_by_name['target_company_ids']._loaded_options = None
  _globals['_COPYMEMBERSHIPSREQUEST'].fields_by_name['target_company_ids']._serialized_options = b'\372B\r\222\001\n\010\001\030\001\"\004\"\002 \000'
  _globals['_COPYINTAKEFORMSREQUEST']._serialized_start=108
  _globals['_COPYINTAKEFORMSREQUEST']._serialized_end=249
  _globals['_COPYINTAKEFORMSRESPONSE']._serialized_start=251
  _globals['_COPYINTAKEFORMSRESPONSE']._serialized_end=276
  _globals['_COPYDISCOUNTCODESREQUEST']._serialized_start=279
  _globals['_COPYDISCOUNTCODESREQUEST']._serialized_end=422
  _globals['_COPYDISCOUNTCODESRESPONSE']._serialized_start=424
  _globals['_COPYDISCOUNTCODESRESPONSE']._serialized_end=451
  _globals['_COPYROLESREQUEST']._serialized_start=454
  _globals['_COPYROLESREQUEST']._serialized_end=589
  _globals['_COPYROLESRESPONSE']._serialized_start=591
  _globals['_COPYROLESRESPONSE']._serialized_end=610
  _globals['_COPYPACKAGESREQUEST']._serialized_start=613
  _globals['_COPYPACKAGESREQUEST']._serialized_end=751
  _globals['_COPYPACKAGESRESPONSE']._serialized_start=753
  _globals['_COPYPACKAGESRESPONSE']._serialized_end=775
  _globals['_COPYMEMBERSHIPSREQUEST']._serialized_start=778
  _globals['_COPYMEMBERSHIPSREQUEST']._serialized_end=919
  _globals['_COPYMEMBERSHIPSRESPONSE']._serialized_start=921
  _globals['_COPYMEMBERSHIPSRESPONSE']._serialized_end=946
  _globals['_SETTINGSERVICE']._serialized_start=949
  _globals['_SETTINGSERVICE']._serialized_end=1575
# @@protoc_insertion_point(module_scope)
