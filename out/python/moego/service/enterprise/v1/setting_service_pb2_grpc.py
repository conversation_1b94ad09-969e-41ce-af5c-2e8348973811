# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.enterprise.v1 import setting_service_pb2 as moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2


class SettingServiceStub(object):
    """setting service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CopyIntakeForms = channel.unary_unary(
                '/moego.service.enterprise.v1.SettingService/CopyIntakeForms',
                request_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyIntakeFormsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyIntakeFormsResponse.FromString,
                _registered_method=True)
        self.CopyDiscountCodes = channel.unary_unary(
                '/moego.service.enterprise.v1.SettingService/CopyDiscountCodes',
                request_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyDiscountCodesRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyDiscountCodesResponse.FromString,
                _registered_method=True)
        self.CopyRoles = channel.unary_unary(
                '/moego.service.enterprise.v1.SettingService/CopyRoles',
                request_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyRolesRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyRolesResponse.FromString,
                _registered_method=True)
        self.CopyPackages = channel.unary_unary(
                '/moego.service.enterprise.v1.SettingService/CopyPackages',
                request_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyPackagesRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyPackagesResponse.FromString,
                _registered_method=True)
        self.CopyMemberships = channel.unary_unary(
                '/moego.service.enterprise.v1.SettingService/CopyMemberships',
                request_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyMembershipsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyMembershipsResponse.FromString,
                _registered_method=True)


class SettingServiceServicer(object):
    """setting service
    """

    def CopyIntakeForms(self, request, context):
        """copy intake form
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CopyDiscountCodes(self, request, context):
        """copy discount code
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CopyRoles(self, request, context):
        """copy roles
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CopyPackages(self, request, context):
        """copy packages
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CopyMemberships(self, request, context):
        """copy membership
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SettingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CopyIntakeForms': grpc.unary_unary_rpc_method_handler(
                    servicer.CopyIntakeForms,
                    request_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyIntakeFormsRequest.FromString,
                    response_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyIntakeFormsResponse.SerializeToString,
            ),
            'CopyDiscountCodes': grpc.unary_unary_rpc_method_handler(
                    servicer.CopyDiscountCodes,
                    request_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyDiscountCodesRequest.FromString,
                    response_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyDiscountCodesResponse.SerializeToString,
            ),
            'CopyRoles': grpc.unary_unary_rpc_method_handler(
                    servicer.CopyRoles,
                    request_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyRolesRequest.FromString,
                    response_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyRolesResponse.SerializeToString,
            ),
            'CopyPackages': grpc.unary_unary_rpc_method_handler(
                    servicer.CopyPackages,
                    request_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyPackagesRequest.FromString,
                    response_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyPackagesResponse.SerializeToString,
            ),
            'CopyMemberships': grpc.unary_unary_rpc_method_handler(
                    servicer.CopyMemberships,
                    request_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyMembershipsRequest.FromString,
                    response_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyMembershipsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.enterprise.v1.SettingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.enterprise.v1.SettingService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class SettingService(object):
    """setting service
    """

    @staticmethod
    def CopyIntakeForms(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.enterprise.v1.SettingService/CopyIntakeForms',
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyIntakeFormsRequest.SerializeToString,
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyIntakeFormsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CopyDiscountCodes(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.enterprise.v1.SettingService/CopyDiscountCodes',
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyDiscountCodesRequest.SerializeToString,
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyDiscountCodesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CopyRoles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.enterprise.v1.SettingService/CopyRoles',
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyRolesRequest.SerializeToString,
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyRolesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CopyPackages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.enterprise.v1.SettingService/CopyPackages',
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyPackagesRequest.SerializeToString,
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyPackagesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CopyMemberships(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.enterprise.v1.SettingService/CopyMemberships',
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyMembershipsRequest.SerializeToString,
            moego_dot_service_dot_enterprise_dot_v1_dot_setting__service__pb2.CopyMembershipsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
