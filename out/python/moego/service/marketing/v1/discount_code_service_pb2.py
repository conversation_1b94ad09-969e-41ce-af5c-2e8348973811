# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/marketing/v1/discount_code_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/marketing/v1/discount_code_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.models.marketing.v1 import discount_code_defs_pb2 as moego_dot_models_dot_marketing_dot_v1_dot_discount__code__defs__pb2
from moego.models.marketing.v1 import discount_code_enums_pb2 as moego_dot_models_dot_marketing_dot_v1_dot_discount__code__enums__pb2
from moego.models.marketing.v1 import discount_code_models_pb2 as moego_dot_models_dot_marketing_dot_v1_dot_discount__code__models__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n6moego/service/marketing/v1/discount_code_service.proto\x12\x1amoego.service.marketing.v1\x1a\x1bgoogle/protobuf/empty.proto\x1a\x32moego/models/marketing/v1/discount_code_defs.proto\x1a\x33moego/models/marketing/v1/discount_code_enums.proto\x1a\x34moego/models/marketing/v1/discount_code_models.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"[\n\x19GenerateDiscountCodeInput\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x02 \x01(\x03R\tcompanyId\"A\n\x1aGenerateDiscountCodeOutput\x12#\n\rdiscount_code\x18\x01 \x01(\tR\x0c\x64iscountCode\"\x88\x01\n\x16\x43heckDiscountCodeInput\x12.\n\rdiscount_code\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x04\x18\x14R\x0c\x64iscountCode\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x03 \x01(\x03R\tcompanyId\"<\n\x17\x43heckDiscountCodeOutput\x12!\n\x0cis_duplicate\x18\x01 \x01(\x08R\x0bisDuplicate\"\xe0\t\n\x17\x43reateDiscountCodeInput\x12.\n\rdiscount_code\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x04\x18\x64R\x0c\x64iscountCode\x12/\n\x0b\x64\x65scription\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xc8\x01H\x00R\x0b\x64\x65scription\x88\x01\x01\x12/\n\x06\x61mount\x18\x03 \x01(\x01\x42\x17\xfa\x42\x14\x12\x12\x11\x00\x00\x80\xff\x64\xcd\xcd\x41!\x00\x00\x00\x00\x00\x00\x00\x00R\x06\x61mount\x12K\n\x04type\x18\x04 \x01(\x0e\x32+.moego.models.marketing.v1.DiscountCodeTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x04type\x12\x39\n\nstart_date\x18\x05 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\tstartDate\x12:\n\x08\x65nd_date\x18\x06 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\x07\x65ndDate\x88\x01\x01\x12*\n\x11\x61llowed_all_thing\x18\x07 \x01(\x08R\x0f\x61llowedAllThing\x12\x30\n\x14\x61llowed_all_services\x18\x08 \x01(\x08R\x12\x61llowedAllServices\x12\x1f\n\x0bservice_ids\x18\t \x03(\x03R\nserviceIds\x12\x1c\n\nadd_on_ids\x18\n \x03(\x03R\x08\x61\x64\x64OnIds\x12\x30\n\x14\x61llowed_all_products\x18\x0b \x01(\x08R\x12\x61llowedAllProducts\x12\x1f\n\x0bproduct_ids\x18\x0c \x03(\x03R\nproductIds\x12.\n\x13\x61llowed_all_clients\x18\r \x01(\x08R\x11\x61llowedAllClients\x12.\n\x13\x61llowed_new_clients\x18\x0e \x01(\x08R\x11\x61llowedNewClients\x12/\n\rclients_group\x18\x0f \x01(\tB\n\xfa\x42\x07r\x05\x10\x00\x18\x90NR\x0c\x63lientsGroup\x12\x1d\n\nclient_ids\x18\x10 \x03(\x03R\tclientIds\x12,\n\x0blimit_usage\x18\x11 \x01(\x05\x42\x0b\xfa\x42\x08\x1a\x06\x10\xc0\x84=(\x00R\nlimitUsage\x12\x42\n\x17limit_number_per_client\x18\x12 \x01(\x05\x42\x0b\xfa\x42\x08\x1a\x06\x10\xc0\x84=(\x00R\x14limitNumberPerClient\x12.\n\x0climit_budget\x18\x13 \x01(\x05\x42\x0b\xfa\x42\x08\x1a\x06\x10\xc0\x84=(\x00R\x0blimitBudget\x12\x34\n\x16\x61uto_apply_association\x18\x14 \x01(\x08R\x14\x61utoApplyAssociation\x12\x32\n\x15\x65nable_online_booking\x18\x15 \x01(\x08R\x13\x65nableOnlineBooking\x12\x1f\n\x0b\x62usiness_id\x18\x16 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x17 \x01(\x03R\tcompanyId\x12!\n\x0clocation_ids\x18\x18 \x03(\x03R\x0blocationIds\x12\x43\n\nexpiry_def\x18\x19 \x01(\x0b\x32$.moego.models.marketing.v1.ExpiryDefR\texpiryDefB\x0e\n\x0c_descriptionB\x0b\n\t_end_date\"*\n\x18\x43reateDiscountCodeOutput\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\"\xbf\t\n\x15\x45\x64itDiscountCodeInput\x12.\n\rdiscount_code\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x04\x18\x14R\x0c\x64iscountCode\x12*\n\x0b\x64\x65scription\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xc8\x01R\x0b\x64\x65scription\x12/\n\x06\x61mount\x18\x03 \x01(\x01\x42\x17\xfa\x42\x14\x12\x12\x11\x00\x00\x80\xff\x64\xcd\xcd\x41!\x00\x00\x00\x00\x00\x00\x00\x00R\x06\x61mount\x12K\n\x04type\x18\x04 \x01(\x0e\x32+.moego.models.marketing.v1.DiscountCodeTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x04type\x12\x39\n\nstart_date\x18\x05 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\tstartDate\x12\x19\n\x08\x65nd_date\x18\x06 \x01(\tR\x07\x65ndDate\x12*\n\x11\x61llowed_all_thing\x18\x07 \x01(\x08R\x0f\x61llowedAllThing\x12\x30\n\x14\x61llowed_all_services\x18\x08 \x01(\x08R\x12\x61llowedAllServices\x12\x1f\n\x0bservice_ids\x18\t \x03(\x03R\nserviceIds\x12\x1c\n\nadd_on_ids\x18\n \x03(\x03R\x08\x61\x64\x64OnIds\x12\x30\n\x14\x61llowed_all_products\x18\x0b \x01(\x08R\x12\x61llowedAllProducts\x12\x1f\n\x0bproduct_ids\x18\x0c \x03(\x03R\nproductIds\x12.\n\x13\x61llowed_all_clients\x18\r \x01(\x08R\x11\x61llowedAllClients\x12.\n\x13\x61llowed_new_clients\x18\x0e \x01(\x08R\x11\x61llowedNewClients\x12/\n\rclients_group\x18\x0f \x01(\tB\n\xfa\x42\x07r\x05\x10\x00\x18\x90NR\x0c\x63lientsGroup\x12\x1d\n\nclient_ids\x18\x10 \x03(\x03R\tclientIds\x12,\n\x0blimit_usage\x18\x11 \x01(\x05\x42\x0b\xfa\x42\x08\x1a\x06\x10\xc0\x84=(\x00R\nlimitUsage\x12\x42\n\x17limit_number_per_client\x18\x12 \x01(\x05\x42\x0b\xfa\x42\x08\x1a\x06\x10\xc0\x84=(\x00R\x14limitNumberPerClient\x12.\n\x0climit_budget\x18\x13 \x01(\x05\x42\x0b\xfa\x42\x08\x1a\x06\x10\xc0\x84=(\x00R\x0blimitBudget\x12\x34\n\x16\x61uto_apply_association\x18\x14 \x01(\x08R\x14\x61utoApplyAssociation\x12\x32\n\x15\x65nable_online_booking\x18\x15 \x01(\x08R\x13\x65nableOnlineBooking\x12\x0e\n\x02id\x18\x16 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x17 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x18 \x01(\x03R\tcompanyId\x12!\n\x0clocation_ids\x18\x19 \x03(\x03R\x0blocationIds\x12H\n\nexpiry_def\x18\x1a \x01(\x0b\x32$.moego.models.marketing.v1.ExpiryDefH\x00R\texpiryDef\x88\x01\x01\x42\r\n\x0b_expiry_def\"(\n\x16\x45\x64itDiscountCodeOutput\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\"o\n\x14GetDiscountCodeInput\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x03 \x01(\x03R\tcompanyId\"\xe3\x01\n\x15GetDiscountCodeOutput\x12\\\n\x13\x64iscount_code_model\x18\x01 \x01(\x0b\x32,.moego.models.marketing.v1.DiscountCodeModelR\x11\x64iscountCodeModel\x12l\n\x19\x64iscount_code_summary_def\x18\x02 \x01(\x0b\x32\x31.moego.models.marketing.v1.DiscountCodeSummaryDefR\x16\x64iscountCodeSummaryDef\"\x8c\x01\n\x1aGetDiscountCodeByCodeInput\x12.\n\rdiscount_code\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04\x10\x04\x18\x14R\x0c\x64iscountCode\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x03 \x01(\x03R\tcompanyId\"{\n\x1bGetDiscountCodeByCodeOutput\x12\\\n\x13\x64iscount_code_model\x18\x01 \x01(\x0b\x32,.moego.models.marketing.v1.DiscountCodeModelR\x11\x64iscountCodeModel\"\xcf\x02\n\x18GetDiscountCodeListInput\x12K\n\npagination\x18\x01 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npagination\x12O\n\x06status\x18\x02 \x03(\x0e\x32-.moego.models.marketing.v1.DiscountCodeStatusB\x08\xfa\x42\x05\x92\x01\x02\x10\x05R\x06status\x12\x31\n\rdiscount_code\x18\x03 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x14H\x00R\x0c\x64iscountCode\x88\x01\x01\x12\x10\n\x03ids\x18\x04 \x03(\x03R\x03ids\x12\x1f\n\x0b\x62usiness_id\x18\x05 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x06 \x01(\x03R\tcompanyIdB\x10\n\x0e_discount_code\"\xd8\x01\n\x19GetDiscountCodeListOutput\x12\x42\n\npagination\x18\x01 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x12w\n\x1d\x64iscount_code_composite_views\x18\x02 \x03(\x0b\x32\x34.moego.models.marketing.v1.DiscountCodeCompositeViewR\x1a\x64iscountCodeCompositeViews\"\xc3\x01\n\x1bGetDiscountCodeLogListInput\x12K\n\npagination\x18\x01 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npagination\x12\x17\n\x02id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x03 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x04 \x01(\x03R\tcompanyId\"\xe6\x01\n\x1cGetDiscountCodeLogListOutput\x12\x42\n\npagination\x18\x01 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x12\x81\x01\n!discount_code_log_composite_views\x18\x02 \x03(\x0b\x32\x37.moego.models.marketing.v1.DiscountCodeLogCompositeViewR\x1d\x64iscountCodeLogCompositeViews\"z\n\x1fGetDiscountCodeLogOverviewInput\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12\x1d\n\ncompany_id\x18\x03 \x01(\x03R\tcompanyId\"\xb2\x01\n GetDiscountCodeLogOverviewOutput\x12\x1f\n\x0btotal_usage\x18\x01 \x01(\x05R\ntotalUsage\x12!\n\x0ctotal_client\x18\x02 \x01(\x05R\x0btotalClient\x12%\n\x0e\x64iscount_sales\x18\x03 \x01(\x01R\rdiscountSales\x12#\n\rinvoice_sales\x18\x04 \x01(\x01R\x0cinvoiceSales\"\xa7\x02\n\x11\x43hangeStatusInput\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x45\n\x06status\x18\x02 \x01(\x0e\x32-.moego.models.marketing.v1.DiscountCodeStatusR\x06status\x12\x1f\n\x0b\x62usiness_id\x18\x03 \x01(\x03R\nbusinessId\x12\x19\n\x08staff_id\x18\x04 \x01(\x03R\x07staffId\x12\x1d\n\ncompany_id\x18\x05 \x01(\x03R\tcompanyId\x12H\n\nexpiry_def\x18\x06 \x01(\x0b\x32$.moego.models.marketing.v1.ExpiryDefH\x00R\texpiryDef\x88\x01\x01\x42\r\n\x0b_expiry_def\"\xb9\x03\n&CheckDiscountCodeValidForCustomerInput\x12$\n\tcode_name\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x14R\x08\x63odeName\x12\x1f\n\x0bservice_ids\x18\x02 \x03(\x03R\nserviceIds\x12$\n\x0b\x63ustomer_id\x18\x03 \x01(\x03H\x00R\ncustomerId\x88\x01\x01\x12\x17\n\x04name\x18\x04 \x01(\tH\x01R\x04name\x88\x01\x01\x12\x1b\n\x06\x64omain\x18\x05 \x01(\tH\x02R\x06\x64omain\x88\x01\x01\x12$\n\x0b\x62usiness_id\x18\x06 \x01(\x03H\x03R\nbusinessId\x88\x01\x01\x12J\n\x10\x61ppointment_date\x18\x07 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x04R\x0f\x61ppointmentDate\x88\x01\x01\x12\"\n\ncompany_id\x18\x08 \x01(\x03H\x05R\tcompanyId\x88\x01\x01\x42\x0e\n\x0c_customer_idB\x07\n\x05_nameB\t\n\x07_domainB\x0e\n\x0c_business_idB\x13\n\x11_appointment_dateB\r\n\x0b_company_id\"\xbe\x01\n\'CheckDiscountCodeValidForCustomerOutput\x12\x92\x01\n\'discount_code_model_online_booking_view\x18\x01 \x01(\x0b\x32=.moego.models.marketing.v1.DiscountCodeModelOnlineBookingViewR\"discountCodeModelOnlineBookingView\".\n\x12\x43hangeStatusOutput\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success\"\x8f\x03\n\x1dGetAvailableDiscountListInput\x12K\n\npagination\x18\x01 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npagination\x12)\n\tcode_name\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x18\x14H\x00R\x08\x63odeName\x88\x01\x01\x12\x1f\n\x0b\x62usiness_id\x18\x03 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x04 \x01(\x03R\ncustomerId\x12\x35\n\x05items\x18\x05 \x03(\x0b\x32\x1f.moego.models.marketing.v1.ItemR\x05items\x12\x33\n\x16used_discount_code_ids\x18\x06 \x03(\x03R\x13usedDiscountCodeIds\x12\x1b\n\tsource_id\x18\x07 \x01(\x03R\x08sourceId\x12\x1d\n\ncompany_id\x18\x08 \x01(\x03R\tcompanyIdB\x0c\n\n_code_name\"\xdd\x01\n\x1eGetAvailableDiscountListOutput\x12\x42\n\npagination\x18\x01 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x12w\n\x1d\x64iscount_code_composite_views\x18\x02 \x03(\x0b\x32\x34.moego.models.marketing.v1.DiscountCodeCompositeViewR\x1a\x64iscountCodeCompositeViews\"\x9b\x03\n/GetAvailableDiscountListForExistingInvoiceInput\x12\x1f\n\x0b\x62usiness_id\x18\x03 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x04 \x01(\x03R\ncustomerId\x12\x35\n\x05items\x18\x05 \x03(\x0b\x32\x1f.moego.models.marketing.v1.ItemR\x05items\x12:\n\x1awill_use_discount_code_ids\x18\x06 \x03(\x03R\x16willUseDiscountCodeIds\x12\x33\n\x16used_discount_code_ids\x18\x07 \x03(\x03R\x13usedDiscountCodeIds\x12J\n\x10\x61ppointment_date\x18\x08 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x00R\x0f\x61ppointmentDate\x88\x01\x01\x12\x1d\n\ncompany_id\x18\t \x01(\x03R\tcompanyIdB\x13\n\x11_appointment_date\"\x92\x01\n0GetAvailableDiscountListForExistingInvoiceOutput\x12^\n\x14\x64iscount_code_models\x18\x01 \x03(\x0b\x32,.moego.models.marketing.v1.DiscountCodeModelR\x12\x64iscountCodeModels\"\xce\x02\n\x1a\x41utoApplyDiscountCodeInput\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12\x1b\n\tsource_id\x18\x03 \x01(\x03R\x08sourceId\x12\x19\n\x08order_id\x18\x04 \x01(\x03R\x07orderId\x12\x19\n\x08staff_id\x18\x05 \x01(\x03R\x07staffId\x12\x35\n\x05items\x18\x06 \x03(\x0b\x32\x1f.moego.models.marketing.v1.ItemR\x05items\x12\x45\n\x10\x61ppointment_date\x18\x07 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\x0f\x61ppointmentDate\x12\x1d\n\ncompany_id\x18\x08 \x01(\x03R\tcompanyId\"\x95\x01\n\x1b\x41utoApplyDiscountCodeOutput\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success\x12\\\n\x13\x64iscount_code_model\x18\x02 \x01(\x0b\x32,.moego.models.marketing.v1.DiscountCodeModelR\x11\x64iscountCodeModel\"\x98\x03\n\x14UseDiscountCodeInput\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12\x1b\n\tsource_id\x18\x03 \x01(\x03R\x08sourceId\x12\x19\n\x08order_id\x18\x04 \x01(\x03R\x07orderId\x12\x19\n\x08staff_id\x18\x05 \x01(\x03R\x07staffId\x12\x46\n\x0bredeem_type\x18\x06 \x01(\x0e\x32%.moego.models.marketing.v1.RedeemTypeR\nredeemType\x12_\n\x14\x64iscount_code_usages\x18\x07 \x03(\x0b\x32-.moego.service.marketing.v1.DiscountCodeUsageR\x12\x64iscountCodeUsages\x12#\n\rinvoice_sales\x18\x08 \x01(\x01R\x0cinvoiceSales\x12\x1d\n\ncompany_id\x18\t \x01(\x03R\tcompanyId\"\xa4\x01\n\x11\x44iscountCodeUsage\x12\x1d\n\nobject_ids\x18\x01 \x03(\x03R\tobjectIds\x12\x12\n\x04type\x18\x02 \x01(\tR\x04type\x12\x32\n\x15\x64iscount_sales_amount\x18\x03 \x01(\x01R\x13\x64iscountSalesAmount\x12(\n\x10\x64iscount_code_id\x18\x04 \x01(\x03R\x0e\x64iscountCodeId\"K\n\x1a\x44\x65leteDiscountCodeLogInput\x12\x1b\n\tredeem_id\x18\x01 \x01(\x03R\x08redeemId\x12\x10\n\x03ids\x18\x02 \x03(\x03R\x03ids\"\xf5\x01\n\"GetBusinessDiscountCodeConfigInput\x12\x1e\n\x04name\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x00R\x04name\x12\"\n\x06\x64omain\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x00R\x06\x64omain\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\nbusinessId\x88\x01\x01\x12+\n\ncompany_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x02R\tcompanyId\x88\x01\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x0e\n\x0c_business_idB\r\n\x0b_company_id\"\\\n#GetBusinessDiscountCodeConfigOutput\x12\x35\n\x17has_valid_discount_code\x18\x01 \x01(\x08R\x14hasValidDiscountCode\"\x9e\x01\n\x1aGetDiscountCodeConfigInput\x12\x1e\n\x04name\x18\x01 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x00R\x04name\x12\"\n\x06\x64omain\x18\x02 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x00R\x06\x64omain\x12*\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessIdB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"T\n\x1bGetDiscountCodeConfigOutput\x12\x35\n\x17has_valid_discount_code\x18\x01 \x01(\x08R\x14hasValidDiscountCode\"\xd9\x05\n\x18MigrateDiscountCodeInput\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12\x66\n\x0cstaff_id_map\x18\x02 \x03(\x0b\x32\x44.moego.service.marketing.v1.MigrateDiscountCodeInput.StaffIdMapEntryR\nstaffIdMap\x12l\n\x0eservice_id_map\x18\x03 \x03(\x0b\x32\x46.moego.service.marketing.v1.MigrateDiscountCodeInput.ServiceIdMapEntryR\x0cserviceIdMap\x12i\n\rclient_id_map\x18\x04 \x03(\x0b\x32\x45.moego.service.marketing.v1.MigrateDiscountCodeInput.ClientIdMapEntryR\x0b\x63lientIdMap\x12`\n\npet_id_map\x18\x05 \x03(\x0b\x32\x42.moego.service.marketing.v1.MigrateDiscountCodeInput.PetIdMapEntryR\x08petIdMap\x1a=\n\x0fStaffIdMapEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x03R\x05value:\x02\x38\x01\x1a?\n\x11ServiceIdMapEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x03R\x05value:\x02\x38\x01\x1a>\n\x10\x43lientIdMapEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x03R\x05value:\x02\x38\x01\x1a;\n\rPetIdMapEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x03R\x05value:\x02\x38\x01\" \n\x1eMigrateDiscountCodeInputOutput2\x99\x15\n\x13\x44iscountCodeService\x12\x85\x01\n\x14GenerateDiscountCode\x12\x35.moego.service.marketing.v1.GenerateDiscountCodeInput\x1a\x36.moego.service.marketing.v1.GenerateDiscountCodeOutput\x12|\n\x11\x43heckDiscountCode\x12\x32.moego.service.marketing.v1.CheckDiscountCodeInput\x1a\x33.moego.service.marketing.v1.CheckDiscountCodeOutput\x12\x7f\n\x12\x43reateDiscountCode\x12\x33.moego.service.marketing.v1.CreateDiscountCodeInput\x1a\x34.moego.service.marketing.v1.CreateDiscountCodeOutput\x12y\n\x10\x45\x64itDiscountCode\x12\x31.moego.service.marketing.v1.EditDiscountCodeInput\x1a\x32.moego.service.marketing.v1.EditDiscountCodeOutput\x12v\n\x0fGetDiscountCode\x12\x30.moego.service.marketing.v1.GetDiscountCodeInput\x1a\x31.moego.service.marketing.v1.GetDiscountCodeOutput\x12\x88\x01\n\x15GetDiscountCodeByCode\x12\x36.moego.service.marketing.v1.GetDiscountCodeByCodeInput\x1a\x37.moego.service.marketing.v1.GetDiscountCodeByCodeOutput\x12\x82\x01\n\x13GetDiscountCodeList\x12\x34.moego.service.marketing.v1.GetDiscountCodeListInput\x1a\x35.moego.service.marketing.v1.GetDiscountCodeListOutput\x12\x97\x01\n\x1aGetDiscountCodeLogOverview\x12;.moego.service.marketing.v1.GetDiscountCodeLogOverviewInput\x1a<.moego.service.marketing.v1.GetDiscountCodeLogOverviewOutput\x12\x8b\x01\n\x16GetDiscountCodeLogList\x12\x37.moego.service.marketing.v1.GetDiscountCodeLogListInput\x1a\x38.moego.service.marketing.v1.GetDiscountCodeLogListOutput\x12m\n\x0c\x43hangeStatus\x12-.moego.service.marketing.v1.ChangeStatusInput\x1a..moego.service.marketing.v1.ChangeStatusOutput\x12\xac\x01\n!CheckDiscountCodeValidForCustomer\x12\x42.moego.service.marketing.v1.CheckDiscountCodeValidForCustomerInput\x1a\x43.moego.service.marketing.v1.CheckDiscountCodeValidForCustomerOutput\x12\x91\x01\n\x18GetAvailableDiscountList\x12\x39.moego.service.marketing.v1.GetAvailableDiscountListInput\x1a:.moego.service.marketing.v1.GetAvailableDiscountListOutput\x12\xc7\x01\n*GetAvailableDiscountListForExistingInvoice\x12K.moego.service.marketing.v1.GetAvailableDiscountListForExistingInvoiceInput\x1aL.moego.service.marketing.v1.GetAvailableDiscountListForExistingInvoiceOutput\x12\x88\x01\n\x15\x41utoApplyDiscountCode\x12\x36.moego.service.marketing.v1.AutoApplyDiscountCodeInput\x1a\x37.moego.service.marketing.v1.AutoApplyDiscountCodeOutput\x12g\n\x15\x44\x65leteDiscountCodeLog\x12\x36.moego.service.marketing.v1.DeleteDiscountCodeLogInput\x1a\x16.google.protobuf.Empty\x12[\n\x0fUseDiscountCode\x12\x30.moego.service.marketing.v1.UseDiscountCodeInput\x1a\x16.google.protobuf.Empty\x12K\n\x19RefreshDiscountCodeStatus\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\x12\xa0\x01\n\x1dGetBusinessDiscountCodeConfig\x12>.moego.service.marketing.v1.GetBusinessDiscountCodeConfigInput\x1a?.moego.service.marketing.v1.GetBusinessDiscountCodeConfigOutput\x12\x88\x01\n\x15GetDiscountCodeConfig\x12\x36.moego.service.marketing.v1.GetDiscountCodeConfigInput\x1a\x37.moego.service.marketing.v1.GetDiscountCodeConfigOutput\x12\x87\x01\n\x13MigrateDiscountCode\x12\x34.moego.service.marketing.v1.MigrateDiscountCodeInput\x1a:.moego.service.marketing.v1.MigrateDiscountCodeInputOutputB\x86\x01\n\"com.moego.idl.service.marketing.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1;marketingsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.marketing.v1.discount_code_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.service.marketing.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/marketing/v1;marketingsvcpb'
  _globals['_CHECKDISCOUNTCODEINPUT'].fields_by_name['discount_code']._loaded_options = None
  _globals['_CHECKDISCOUNTCODEINPUT'].fields_by_name['discount_code']._serialized_options = b'\372B\006r\004\020\004\030\024'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['discount_code']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['discount_code']._serialized_options = b'\372B\006r\004\020\004\030d'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['description']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['description']._serialized_options = b'\372B\005r\003\030\310\001'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['amount']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['amount']._serialized_options = b'\372B\024\022\022\021\000\000\200\377d\315\315A!\000\000\000\000\000\000\000\000'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['type']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['start_date']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['end_date']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['clients_group']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['clients_group']._serialized_options = b'\372B\007r\005\020\000\030\220N'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['limit_usage']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['limit_usage']._serialized_options = b'\372B\010\032\006\020\300\204=(\000'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['limit_number_per_client']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['limit_number_per_client']._serialized_options = b'\372B\010\032\006\020\300\204=(\000'
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['limit_budget']._loaded_options = None
  _globals['_CREATEDISCOUNTCODEINPUT'].fields_by_name['limit_budget']._serialized_options = b'\372B\010\032\006\020\300\204=(\000'
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['discount_code']._loaded_options = None
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['discount_code']._serialized_options = b'\372B\006r\004\020\004\030\024'
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['description']._loaded_options = None
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['description']._serialized_options = b'\372B\005r\003\030\310\001'
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['amount']._loaded_options = None
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['amount']._serialized_options = b'\372B\024\022\022\021\000\000\200\377d\315\315A!\000\000\000\000\000\000\000\000'
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['type']._loaded_options = None
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['start_date']._loaded_options = None
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['clients_group']._loaded_options = None
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['clients_group']._serialized_options = b'\372B\007r\005\020\000\030\220N'
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['limit_usage']._loaded_options = None
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['limit_usage']._serialized_options = b'\372B\010\032\006\020\300\204=(\000'
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['limit_number_per_client']._loaded_options = None
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['limit_number_per_client']._serialized_options = b'\372B\010\032\006\020\300\204=(\000'
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['limit_budget']._loaded_options = None
  _globals['_EDITDISCOUNTCODEINPUT'].fields_by_name['limit_budget']._serialized_options = b'\372B\010\032\006\020\300\204=(\000'
  _globals['_GETDISCOUNTCODEINPUT'].fields_by_name['id']._loaded_options = None
  _globals['_GETDISCOUNTCODEINPUT'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDISCOUNTCODEBYCODEINPUT'].fields_by_name['discount_code']._loaded_options = None
  _globals['_GETDISCOUNTCODEBYCODEINPUT'].fields_by_name['discount_code']._serialized_options = b'\372B\006r\004\020\004\030\024'
  _globals['_GETDISCOUNTCODELISTINPUT'].fields_by_name['pagination']._loaded_options = None
  _globals['_GETDISCOUNTCODELISTINPUT'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETDISCOUNTCODELISTINPUT'].fields_by_name['status']._loaded_options = None
  _globals['_GETDISCOUNTCODELISTINPUT'].fields_by_name['status']._serialized_options = b'\372B\005\222\001\002\020\005'
  _globals['_GETDISCOUNTCODELISTINPUT'].fields_by_name['discount_code']._loaded_options = None
  _globals['_GETDISCOUNTCODELISTINPUT'].fields_by_name['discount_code']._serialized_options = b'\372B\004r\002\030\024'
  _globals['_GETDISCOUNTCODELOGLISTINPUT'].fields_by_name['pagination']._loaded_options = None
  _globals['_GETDISCOUNTCODELOGLISTINPUT'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETDISCOUNTCODELOGLISTINPUT'].fields_by_name['id']._loaded_options = None
  _globals['_GETDISCOUNTCODELOGLISTINPUT'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDISCOUNTCODELOGOVERVIEWINPUT'].fields_by_name['id']._loaded_options = None
  _globals['_GETDISCOUNTCODELOGOVERVIEWINPUT'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CHANGESTATUSINPUT'].fields_by_name['id']._loaded_options = None
  _globals['_CHANGESTATUSINPUT'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CHECKDISCOUNTCODEVALIDFORCUSTOMERINPUT'].fields_by_name['code_name']._loaded_options = None
  _globals['_CHECKDISCOUNTCODEVALIDFORCUSTOMERINPUT'].fields_by_name['code_name']._serialized_options = b'\372B\004r\002\030\024'
  _globals['_CHECKDISCOUNTCODEVALIDFORCUSTOMERINPUT'].fields_by_name['appointment_date']._loaded_options = None
  _globals['_CHECKDISCOUNTCODEVALIDFORCUSTOMERINPUT'].fields_by_name['appointment_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_GETAVAILABLEDISCOUNTLISTINPUT'].fields_by_name['pagination']._loaded_options = None
  _globals['_GETAVAILABLEDISCOUNTLISTINPUT'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETAVAILABLEDISCOUNTLISTINPUT'].fields_by_name['code_name']._loaded_options = None
  _globals['_GETAVAILABLEDISCOUNTLISTINPUT'].fields_by_name['code_name']._serialized_options = b'\372B\004r\002\030\024'
  _globals['_GETAVAILABLEDISCOUNTLISTFOREXISTINGINVOICEINPUT'].fields_by_name['appointment_date']._loaded_options = None
  _globals['_GETAVAILABLEDISCOUNTLISTFOREXISTINGINVOICEINPUT'].fields_by_name['appointment_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_AUTOAPPLYDISCOUNTCODEINPUT'].fields_by_name['appointment_date']._loaded_options = None
  _globals['_AUTOAPPLYDISCOUNTCODEINPUT'].fields_by_name['appointment_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].fields_by_name['name']._loaded_options = None
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].fields_by_name['name']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].fields_by_name['domain']._loaded_options = None
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].fields_by_name['domain']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETDISCOUNTCODECONFIGINPUT'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETDISCOUNTCODECONFIGINPUT'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETDISCOUNTCODECONFIGINPUT'].fields_by_name['name']._loaded_options = None
  _globals['_GETDISCOUNTCODECONFIGINPUT'].fields_by_name['name']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETDISCOUNTCODECONFIGINPUT'].fields_by_name['domain']._loaded_options = None
  _globals['_GETDISCOUNTCODECONFIGINPUT'].fields_by_name['domain']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETDISCOUNTCODECONFIGINPUT'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETDISCOUNTCODECONFIGINPUT'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_MIGRATEDISCOUNTCODEINPUT_STAFFIDMAPENTRY']._loaded_options = None
  _globals['_MIGRATEDISCOUNTCODEINPUT_STAFFIDMAPENTRY']._serialized_options = b'8\001'
  _globals['_MIGRATEDISCOUNTCODEINPUT_SERVICEIDMAPENTRY']._loaded_options = None
  _globals['_MIGRATEDISCOUNTCODEINPUT_SERVICEIDMAPENTRY']._serialized_options = b'8\001'
  _globals['_MIGRATEDISCOUNTCODEINPUT_CLIENTIDMAPENTRY']._loaded_options = None
  _globals['_MIGRATEDISCOUNTCODEINPUT_CLIENTIDMAPENTRY']._serialized_options = b'8\001'
  _globals['_MIGRATEDISCOUNTCODEINPUT_PETIDMAPENTRY']._loaded_options = None
  _globals['_MIGRATEDISCOUNTCODEINPUT_PETIDMAPENTRY']._serialized_options = b'8\001'
  _globals['_GENERATEDISCOUNTCODEINPUT']._serialized_start=341
  _globals['_GENERATEDISCOUNTCODEINPUT']._serialized_end=432
  _globals['_GENERATEDISCOUNTCODEOUTPUT']._serialized_start=434
  _globals['_GENERATEDISCOUNTCODEOUTPUT']._serialized_end=499
  _globals['_CHECKDISCOUNTCODEINPUT']._serialized_start=502
  _globals['_CHECKDISCOUNTCODEINPUT']._serialized_end=638
  _globals['_CHECKDISCOUNTCODEOUTPUT']._serialized_start=640
  _globals['_CHECKDISCOUNTCODEOUTPUT']._serialized_end=700
  _globals['_CREATEDISCOUNTCODEINPUT']._serialized_start=703
  _globals['_CREATEDISCOUNTCODEINPUT']._serialized_end=1951
  _globals['_CREATEDISCOUNTCODEOUTPUT']._serialized_start=1953
  _globals['_CREATEDISCOUNTCODEOUTPUT']._serialized_end=1995
  _globals['_EDITDISCOUNTCODEINPUT']._serialized_start=1998
  _globals['_EDITDISCOUNTCODEINPUT']._serialized_end=3213
  _globals['_EDITDISCOUNTCODEOUTPUT']._serialized_start=3215
  _globals['_EDITDISCOUNTCODEOUTPUT']._serialized_end=3255
  _globals['_GETDISCOUNTCODEINPUT']._serialized_start=3257
  _globals['_GETDISCOUNTCODEINPUT']._serialized_end=3368
  _globals['_GETDISCOUNTCODEOUTPUT']._serialized_start=3371
  _globals['_GETDISCOUNTCODEOUTPUT']._serialized_end=3598
  _globals['_GETDISCOUNTCODEBYCODEINPUT']._serialized_start=3601
  _globals['_GETDISCOUNTCODEBYCODEINPUT']._serialized_end=3741
  _globals['_GETDISCOUNTCODEBYCODEOUTPUT']._serialized_start=3743
  _globals['_GETDISCOUNTCODEBYCODEOUTPUT']._serialized_end=3866
  _globals['_GETDISCOUNTCODELISTINPUT']._serialized_start=3869
  _globals['_GETDISCOUNTCODELISTINPUT']._serialized_end=4204
  _globals['_GETDISCOUNTCODELISTOUTPUT']._serialized_start=4207
  _globals['_GETDISCOUNTCODELISTOUTPUT']._serialized_end=4423
  _globals['_GETDISCOUNTCODELOGLISTINPUT']._serialized_start=4426
  _globals['_GETDISCOUNTCODELOGLISTINPUT']._serialized_end=4621
  _globals['_GETDISCOUNTCODELOGLISTOUTPUT']._serialized_start=4624
  _globals['_GETDISCOUNTCODELOGLISTOUTPUT']._serialized_end=4854
  _globals['_GETDISCOUNTCODELOGOVERVIEWINPUT']._serialized_start=4856
  _globals['_GETDISCOUNTCODELOGOVERVIEWINPUT']._serialized_end=4978
  _globals['_GETDISCOUNTCODELOGOVERVIEWOUTPUT']._serialized_start=4981
  _globals['_GETDISCOUNTCODELOGOVERVIEWOUTPUT']._serialized_end=5159
  _globals['_CHANGESTATUSINPUT']._serialized_start=5162
  _globals['_CHANGESTATUSINPUT']._serialized_end=5457
  _globals['_CHECKDISCOUNTCODEVALIDFORCUSTOMERINPUT']._serialized_start=5460
  _globals['_CHECKDISCOUNTCODEVALIDFORCUSTOMERINPUT']._serialized_end=5901
  _globals['_CHECKDISCOUNTCODEVALIDFORCUSTOMEROUTPUT']._serialized_start=5904
  _globals['_CHECKDISCOUNTCODEVALIDFORCUSTOMEROUTPUT']._serialized_end=6094
  _globals['_CHANGESTATUSOUTPUT']._serialized_start=6096
  _globals['_CHANGESTATUSOUTPUT']._serialized_end=6142
  _globals['_GETAVAILABLEDISCOUNTLISTINPUT']._serialized_start=6145
  _globals['_GETAVAILABLEDISCOUNTLISTINPUT']._serialized_end=6544
  _globals['_GETAVAILABLEDISCOUNTLISTOUTPUT']._serialized_start=6547
  _globals['_GETAVAILABLEDISCOUNTLISTOUTPUT']._serialized_end=6768
  _globals['_GETAVAILABLEDISCOUNTLISTFOREXISTINGINVOICEINPUT']._serialized_start=6771
  _globals['_GETAVAILABLEDISCOUNTLISTFOREXISTINGINVOICEINPUT']._serialized_end=7182
  _globals['_GETAVAILABLEDISCOUNTLISTFOREXISTINGINVOICEOUTPUT']._serialized_start=7185
  _globals['_GETAVAILABLEDISCOUNTLISTFOREXISTINGINVOICEOUTPUT']._serialized_end=7331
  _globals['_AUTOAPPLYDISCOUNTCODEINPUT']._serialized_start=7334
  _globals['_AUTOAPPLYDISCOUNTCODEINPUT']._serialized_end=7668
  _globals['_AUTOAPPLYDISCOUNTCODEOUTPUT']._serialized_start=7671
  _globals['_AUTOAPPLYDISCOUNTCODEOUTPUT']._serialized_end=7820
  _globals['_USEDISCOUNTCODEINPUT']._serialized_start=7823
  _globals['_USEDISCOUNTCODEINPUT']._serialized_end=8231
  _globals['_DISCOUNTCODEUSAGE']._serialized_start=8234
  _globals['_DISCOUNTCODEUSAGE']._serialized_end=8398
  _globals['_DELETEDISCOUNTCODELOGINPUT']._serialized_start=8400
  _globals['_DELETEDISCOUNTCODELOGINPUT']._serialized_end=8475
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT']._serialized_start=8478
  _globals['_GETBUSINESSDISCOUNTCODECONFIGINPUT']._serialized_end=8723
  _globals['_GETBUSINESSDISCOUNTCODECONFIGOUTPUT']._serialized_start=8725
  _globals['_GETBUSINESSDISCOUNTCODECONFIGOUTPUT']._serialized_end=8817
  _globals['_GETDISCOUNTCODECONFIGINPUT']._serialized_start=8820
  _globals['_GETDISCOUNTCODECONFIGINPUT']._serialized_end=8978
  _globals['_GETDISCOUNTCODECONFIGOUTPUT']._serialized_start=8980
  _globals['_GETDISCOUNTCODECONFIGOUTPUT']._serialized_end=9064
  _globals['_MIGRATEDISCOUNTCODEINPUT']._serialized_start=9067
  _globals['_MIGRATEDISCOUNTCODEINPUT']._serialized_end=9796
  _globals['_MIGRATEDISCOUNTCODEINPUT_STAFFIDMAPENTRY']._serialized_start=9545
  _globals['_MIGRATEDISCOUNTCODEINPUT_STAFFIDMAPENTRY']._serialized_end=9606
  _globals['_MIGRATEDISCOUNTCODEINPUT_SERVICEIDMAPENTRY']._serialized_start=9608
  _globals['_MIGRATEDISCOUNTCODEINPUT_SERVICEIDMAPENTRY']._serialized_end=9671
  _globals['_MIGRATEDISCOUNTCODEINPUT_CLIENTIDMAPENTRY']._serialized_start=9673
  _globals['_MIGRATEDISCOUNTCODEINPUT_CLIENTIDMAPENTRY']._serialized_end=9735
  _globals['_MIGRATEDISCOUNTCODEINPUT_PETIDMAPENTRY']._serialized_start=9737
  _globals['_MIGRATEDISCOUNTCODEINPUT_PETIDMAPENTRY']._serialized_end=9796
  _globals['_MIGRATEDISCOUNTCODEINPUTOUTPUT']._serialized_start=9798
  _globals['_MIGRATEDISCOUNTCODEINPUTOUTPUT']._serialized_end=9830
  _globals['_DISCOUNTCODESERVICE']._serialized_start=9833
  _globals['_DISCOUNTCODESERVICE']._serialized_end=12546
# @@protoc_insertion_point(module_scope)
