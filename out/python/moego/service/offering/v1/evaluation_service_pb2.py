# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/offering/v1/evaluation_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/offering/v1/evaluation_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.offering.v1 import evaluation_defs_pb2 as moego_dot_models_dot_offering_dot_v1_dot_evaluation__defs__pb2
from moego.models.offering.v1 import evaluation_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_evaluation__models__pb2
from moego.models.offering.v1 import service_defs_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__defs__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.organization.v1 import tenant_pb2 as moego_dot_models_dot_organization_dot_v1_dot_tenant__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n2moego/service/offering/v1/evaluation_service.proto\x12\x19moego.service.offering.v1\x1a.moego/models/offering/v1/evaluation_defs.proto\x1a\x30moego/models/offering/v1/evaluation_models.proto\x1a+moego/models/offering/v1/service_defs.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a)moego/models/organization/v1/tenant.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\x9b\x01\n\x17\x43reateEvaluationRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12X\n\x0e\x65valuation_def\x18\x02 \x01(\x0b\x32\'.moego.models.offering.v1.EvaluationDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\revaluationDef\"z\n\x18\x43reateEvaluationResponse\x12^\n\x10\x65valuation_model\x18\x01 \x01(\x0b\x32).moego.models.offering.v1.EvaluationModelB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0f\x65valuationModel\"\xe4\x01\n\x17UpdateEvaluationRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12X\n\x0e\x65valuation_def\x18\x02 \x01(\x0b\x32\'.moego.models.offering.v1.EvaluationDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\revaluationDef\x12K\n\x06tenant\x18\x03 \x01(\x0b\x32$.moego.models.organization.v1.TenantB\x08\xfa\x42\x05\x8a\x01\x02\x10\x00H\x00R\x06tenant\x88\x01\x01\x42\t\n\x07_tenant\"z\n\x18UpdateEvaluationResponse\x12^\n\x10\x65valuation_model\x18\x01 \x01(\x0b\x32).moego.models.offering.v1.EvaluationModelB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0f\x65valuationModel\"B\n\x18GetEvaluationListRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\"h\n\x19GetEvaluationListResponse\x12K\n\x0b\x65valuations\x18\x01 \x03(\x0b\x32).moego.models.offering.v1.EvaluationModelR\x0b\x65valuations\"/\n\x14GetEvaluationRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"w\n\x15GetEvaluationResponse\x12^\n\x10\x65valuation_model\x18\x01 \x01(\x0b\x32).moego.models.offering.v1.EvaluationModelB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0f\x65valuationModel\"\x91\x03\n\"GetApplicableEvaluationListRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12Z\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeH\x01R\x0fserviceItemType\x88\x01\x01\x12U\n\rfilter_by_pet\x18\x04 \x01(\x0b\x32,.moego.models.offering.v1.ServiceFilterByPetH\x02R\x0b\x66ilterByPet\x88\x01\x01\x12)\n\x10include_inactive\x18\x05 \x01(\x08R\x0fincludeInactiveB\x0e\n\x0c_business_idB\x14\n\x12_service_item_typeB\x10\n\x0e_filter_by_pet\"v\n#GetApplicableEvaluationListResponse\x12O\n\x0b\x65valuations\x18\x01 \x03(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewR\x0b\x65valuations\"X\n.GetBusinessListWithApplicableEvaluationRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\"|\n/GetBusinessListWithApplicableEvaluationResponse\x12&\n\x0fis_all_location\x18\x01 \x01(\x08R\risAllLocation\x12!\n\x0c\x62usiness_ids\x18\x02 \x03(\x03R\x0b\x62usinessIds\"R\n)GetEvaluationListWithEvaluationIdsRequest\x12%\n\x0e\x65valuation_ids\x18\x01 \x03(\x03R\revaluationIds\"}\n*GetEvaluationListWithEvaluationIdsResponse\x12O\n\x0b\x65valuations\x18\x01 \x03(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewR\x0b\x65valuations\"\xc9\x02\n\x15ListEvaluationRequest\x12O\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x37.moego.service.offering.v1.ListEvaluationRequest.FilterR\x06\x66ilter\x12\x41\n\npagination\x18\x03 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npagination\x1a\x9b\x01\n\x06\x46ilter\x12(\n\ris_resettable\x18\x01 \x01(\x08H\x00R\x0cisResettable\x88\x01\x01\x12\x31\n\x0b\x63ompany_ids\x18\x02 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x00\x18\x01\"\x04\"\x02 \x00R\ncompanyIds\x12\"\n\x03ids\x18\x03 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x00\x18\x01\"\x04\"\x02 \x00R\x03idsB\x10\n\x0e_is_resettable\"\xa9\x01\n\x16ListEvaluationResponse\x12K\n\x0b\x65valuations\x18\x01 \x03(\x0b\x32).moego.models.offering.v1.EvaluationModelR\x0b\x65valuations\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"Z\n\x17\x44\x65leteEvaluationRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\"\x1a\n\x18\x44\x65leteEvaluationResponse2\x8b\n\n\x11\x45valuationService\x12{\n\x10\x43reateEvaluation\x12\x32.moego.service.offering.v1.CreateEvaluationRequest\x1a\x33.moego.service.offering.v1.CreateEvaluationResponse\x12{\n\x10UpdateEvaluation\x12\x32.moego.service.offering.v1.UpdateEvaluationRequest\x1a\x33.moego.service.offering.v1.UpdateEvaluationResponse\x12{\n\x10\x44\x65leteEvaluation\x12\x32.moego.service.offering.v1.DeleteEvaluationRequest\x1a\x33.moego.service.offering.v1.DeleteEvaluationResponse\x12r\n\rGetEvaluation\x12/.moego.service.offering.v1.GetEvaluationRequest\x1a\x30.moego.service.offering.v1.GetEvaluationResponse\x12~\n\x11GetEvaluationList\x12\x33.moego.service.offering.v1.GetEvaluationListRequest\x1a\x34.moego.service.offering.v1.GetEvaluationListResponse\x12\x9c\x01\n\x1bGetApplicableEvaluationList\x12=.moego.service.offering.v1.GetApplicableEvaluationListRequest\x1a>.moego.service.offering.v1.GetApplicableEvaluationListResponse\x12\xc0\x01\n\'GetBusinessListWithApplicableEvaluation\x12I.moego.service.offering.v1.GetBusinessListWithApplicableEvaluationRequest\x1aJ.moego.service.offering.v1.GetBusinessListWithApplicableEvaluationResponse\x12\xb1\x01\n\"GetEvaluationListWithEvaluationIds\x12\x44.moego.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest\x1a\x45.moego.service.offering.v1.GetEvaluationListWithEvaluationIdsResponse\x12u\n\x0eListEvaluation\x12\x30.moego.service.offering.v1.ListEvaluationRequest\x1a\x31.moego.service.offering.v1.ListEvaluationResponseB\x83\x01\n!com.moego.idl.service.offering.v1P\x01Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.offering.v1.evaluation_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.service.offering.v1P\001Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb'
  _globals['_CREATEEVALUATIONREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_CREATEEVALUATIONREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEEVALUATIONREQUEST'].fields_by_name['evaluation_def']._loaded_options = None
  _globals['_CREATEEVALUATIONREQUEST'].fields_by_name['evaluation_def']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEEVALUATIONRESPONSE'].fields_by_name['evaluation_model']._loaded_options = None
  _globals['_CREATEEVALUATIONRESPONSE'].fields_by_name['evaluation_model']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATEEVALUATIONREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEEVALUATIONREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEEVALUATIONREQUEST'].fields_by_name['evaluation_def']._loaded_options = None
  _globals['_UPDATEEVALUATIONREQUEST'].fields_by_name['evaluation_def']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATEEVALUATIONREQUEST'].fields_by_name['tenant']._loaded_options = None
  _globals['_UPDATEEVALUATIONREQUEST'].fields_by_name['tenant']._serialized_options = b'\372B\005\212\001\002\020\000'
  _globals['_UPDATEEVALUATIONRESPONSE'].fields_by_name['evaluation_model']._loaded_options = None
  _globals['_UPDATEEVALUATIONRESPONSE'].fields_by_name['evaluation_model']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETEVALUATIONLISTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETEVALUATIONLISTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETEVALUATIONREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_GETEVALUATIONREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETEVALUATIONRESPONSE'].fields_by_name['evaluation_model']._loaded_options = None
  _globals['_GETEVALUATIONRESPONSE'].fields_by_name['evaluation_model']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETAPPLICABLEEVALUATIONLISTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETAPPLICABLEEVALUATIONLISTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPLICABLEEVALUATIONLISTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETAPPLICABLEEVALUATIONLISTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTEVALUATIONREQUEST_FILTER'].fields_by_name['company_ids']._loaded_options = None
  _globals['_LISTEVALUATIONREQUEST_FILTER'].fields_by_name['company_ids']._serialized_options = b'\372B\r\222\001\n\010\000\030\001\"\004\"\002 \000'
  _globals['_LISTEVALUATIONREQUEST_FILTER'].fields_by_name['ids']._loaded_options = None
  _globals['_LISTEVALUATIONREQUEST_FILTER'].fields_by_name['ids']._serialized_options = b'\372B\r\222\001\n\010\000\030\001\"\004\"\002 \000'
  _globals['_DELETEEVALUATIONREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_DELETEEVALUATIONREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETEEVALUATIONREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_DELETEEVALUATIONREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEEVALUATIONREQUEST']._serialized_start=380
  _globals['_CREATEEVALUATIONREQUEST']._serialized_end=535
  _globals['_CREATEEVALUATIONRESPONSE']._serialized_start=537
  _globals['_CREATEEVALUATIONRESPONSE']._serialized_end=659
  _globals['_UPDATEEVALUATIONREQUEST']._serialized_start=662
  _globals['_UPDATEEVALUATIONREQUEST']._serialized_end=890
  _globals['_UPDATEEVALUATIONRESPONSE']._serialized_start=892
  _globals['_UPDATEEVALUATIONRESPONSE']._serialized_end=1014
  _globals['_GETEVALUATIONLISTREQUEST']._serialized_start=1016
  _globals['_GETEVALUATIONLISTREQUEST']._serialized_end=1082
  _globals['_GETEVALUATIONLISTRESPONSE']._serialized_start=1084
  _globals['_GETEVALUATIONLISTRESPONSE']._serialized_end=1188
  _globals['_GETEVALUATIONREQUEST']._serialized_start=1190
  _globals['_GETEVALUATIONREQUEST']._serialized_end=1237
  _globals['_GETEVALUATIONRESPONSE']._serialized_start=1239
  _globals['_GETEVALUATIONRESPONSE']._serialized_end=1358
  _globals['_GETAPPLICABLEEVALUATIONLISTREQUEST']._serialized_start=1361
  _globals['_GETAPPLICABLEEVALUATIONLISTREQUEST']._serialized_end=1762
  _globals['_GETAPPLICABLEEVALUATIONLISTRESPONSE']._serialized_start=1764
  _globals['_GETAPPLICABLEEVALUATIONLISTRESPONSE']._serialized_end=1882
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONREQUEST']._serialized_start=1884
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONREQUEST']._serialized_end=1972
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONRESPONSE']._serialized_start=1974
  _globals['_GETBUSINESSLISTWITHAPPLICABLEEVALUATIONRESPONSE']._serialized_end=2098
  _globals['_GETEVALUATIONLISTWITHEVALUATIONIDSREQUEST']._serialized_start=2100
  _globals['_GETEVALUATIONLISTWITHEVALUATIONIDSREQUEST']._serialized_end=2182
  _globals['_GETEVALUATIONLISTWITHEVALUATIONIDSRESPONSE']._serialized_start=2184
  _globals['_GETEVALUATIONLISTWITHEVALUATIONIDSRESPONSE']._serialized_end=2309
  _globals['_LISTEVALUATIONREQUEST']._serialized_start=2312
  _globals['_LISTEVALUATIONREQUEST']._serialized_end=2641
  _globals['_LISTEVALUATIONREQUEST_FILTER']._serialized_start=2486
  _globals['_LISTEVALUATIONREQUEST_FILTER']._serialized_end=2641
  _globals['_LISTEVALUATIONRESPONSE']._serialized_start=2644
  _globals['_LISTEVALUATIONRESPONSE']._serialized_end=2813
  _globals['_DELETEEVALUATIONREQUEST']._serialized_start=2815
  _globals['_DELETEEVALUATIONREQUEST']._serialized_end=2905
  _globals['_DELETEEVALUATIONRESPONSE']._serialized_start=2907
  _globals['_DELETEEVALUATIONRESPONSE']._serialized_end=2933
  _globals['_EVALUATIONSERVICE']._serialized_start=2936
  _globals['_EVALUATIONSERVICE']._serialized_end=4227
# @@protoc_insertion_point(module_scope)
