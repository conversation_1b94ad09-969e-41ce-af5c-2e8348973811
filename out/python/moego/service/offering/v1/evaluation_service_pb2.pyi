from moego.models.offering.v1 import evaluation_defs_pb2 as _evaluation_defs_pb2
from moego.models.offering.v1 import evaluation_models_pb2 as _evaluation_models_pb2
from moego.models.offering.v1 import service_defs_pb2 as _service_defs_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.organization.v1 import tenant_pb2 as _tenant_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateEvaluationRequest(_message.Message):
    __slots__ = ("company_id", "evaluation_def")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_DEF_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    evaluation_def: _evaluation_defs_pb2.EvaluationDef
    def __init__(self, company_id: _Optional[int] = ..., evaluation_def: _Optional[_Union[_evaluation_defs_pb2.EvaluationDef, _Mapping]] = ...) -> None: ...

class CreateEvaluationResponse(_message.Message):
    __slots__ = ("evaluation_model",)
    EVALUATION_MODEL_FIELD_NUMBER: _ClassVar[int]
    evaluation_model: _evaluation_models_pb2.EvaluationModel
    def __init__(self, evaluation_model: _Optional[_Union[_evaluation_models_pb2.EvaluationModel, _Mapping]] = ...) -> None: ...

class UpdateEvaluationRequest(_message.Message):
    __slots__ = ("id", "evaluation_def", "tenant")
    ID_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_DEF_FIELD_NUMBER: _ClassVar[int]
    TENANT_FIELD_NUMBER: _ClassVar[int]
    id: int
    evaluation_def: _evaluation_defs_pb2.EvaluationDef
    tenant: _tenant_pb2.Tenant
    def __init__(self, id: _Optional[int] = ..., evaluation_def: _Optional[_Union[_evaluation_defs_pb2.EvaluationDef, _Mapping]] = ..., tenant: _Optional[_Union[_tenant_pb2.Tenant, _Mapping]] = ...) -> None: ...

class UpdateEvaluationResponse(_message.Message):
    __slots__ = ("evaluation_model",)
    EVALUATION_MODEL_FIELD_NUMBER: _ClassVar[int]
    evaluation_model: _evaluation_models_pb2.EvaluationModel
    def __init__(self, evaluation_model: _Optional[_Union[_evaluation_models_pb2.EvaluationModel, _Mapping]] = ...) -> None: ...

class GetEvaluationListRequest(_message.Message):
    __slots__ = ("company_id",)
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    def __init__(self, company_id: _Optional[int] = ...) -> None: ...

class GetEvaluationListResponse(_message.Message):
    __slots__ = ("evaluations",)
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationModel]
    def __init__(self, evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationModel, _Mapping]]] = ...) -> None: ...

class GetEvaluationRequest(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class GetEvaluationResponse(_message.Message):
    __slots__ = ("evaluation_model",)
    EVALUATION_MODEL_FIELD_NUMBER: _ClassVar[int]
    evaluation_model: _evaluation_models_pb2.EvaluationModel
    def __init__(self, evaluation_model: _Optional[_Union[_evaluation_models_pb2.EvaluationModel, _Mapping]] = ...) -> None: ...

class GetApplicableEvaluationListRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "service_item_type", "filter_by_pet", "include_inactive")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    FILTER_BY_PET_FIELD_NUMBER: _ClassVar[int]
    INCLUDE_INACTIVE_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    service_item_type: _service_enum_pb2.ServiceItemType
    filter_by_pet: _service_defs_pb2.ServiceFilterByPet
    include_inactive: bool
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., filter_by_pet: _Optional[_Union[_service_defs_pb2.ServiceFilterByPet, _Mapping]] = ..., include_inactive: bool = ...) -> None: ...

class GetApplicableEvaluationListResponse(_message.Message):
    __slots__ = ("evaluations",)
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationBriefView]
    def __init__(self, evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]]] = ...) -> None: ...

class GetBusinessListWithApplicableEvaluationRequest(_message.Message):
    __slots__ = ("company_id",)
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    def __init__(self, company_id: _Optional[int] = ...) -> None: ...

class GetBusinessListWithApplicableEvaluationResponse(_message.Message):
    __slots__ = ("is_all_location", "business_ids")
    IS_ALL_LOCATION_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    is_all_location: bool
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, is_all_location: bool = ..., business_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class GetEvaluationListWithEvaluationIdsRequest(_message.Message):
    __slots__ = ("evaluation_ids",)
    EVALUATION_IDS_FIELD_NUMBER: _ClassVar[int]
    evaluation_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, evaluation_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class GetEvaluationListWithEvaluationIdsResponse(_message.Message):
    __slots__ = ("evaluations",)
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationBriefView]
    def __init__(self, evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]]] = ...) -> None: ...

class ListEvaluationRequest(_message.Message):
    __slots__ = ("filter", "pagination")
    class Filter(_message.Message):
        __slots__ = ("is_resettable", "company_ids", "ids")
        IS_RESETTABLE_FIELD_NUMBER: _ClassVar[int]
        COMPANY_IDS_FIELD_NUMBER: _ClassVar[int]
        IDS_FIELD_NUMBER: _ClassVar[int]
        is_resettable: bool
        company_ids: _containers.RepeatedScalarFieldContainer[int]
        ids: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, is_resettable: bool = ..., company_ids: _Optional[_Iterable[int]] = ..., ids: _Optional[_Iterable[int]] = ...) -> None: ...
    FILTER_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    filter: ListEvaluationRequest.Filter
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, filter: _Optional[_Union[ListEvaluationRequest.Filter, _Mapping]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class ListEvaluationResponse(_message.Message):
    __slots__ = ("evaluations", "pagination")
    EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationModel]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationModel, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class DeleteEvaluationRequest(_message.Message):
    __slots__ = ("id", "company_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    company_id: int
    def __init__(self, id: _Optional[int] = ..., company_id: _Optional[int] = ...) -> None: ...

class DeleteEvaluationResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
