# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.offering.v1 import evaluation_service_pb2 as moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2


class EvaluationServiceStub(object):
    """the evaluation service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateEvaluation = channel.unary_unary(
                '/moego.service.offering.v1.EvaluationService/CreateEvaluation',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.CreateEvaluationRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.CreateEvaluationResponse.FromString,
                _registered_method=True)
        self.UpdateEvaluation = channel.unary_unary(
                '/moego.service.offering.v1.EvaluationService/UpdateEvaluation',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.UpdateEvaluationRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.UpdateEvaluationResponse.FromString,
                _registered_method=True)
        self.DeleteEvaluation = channel.unary_unary(
                '/moego.service.offering.v1.EvaluationService/DeleteEvaluation',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.DeleteEvaluationRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.DeleteEvaluationResponse.FromString,
                _registered_method=True)
        self.GetEvaluation = channel.unary_unary(
                '/moego.service.offering.v1.EvaluationService/GetEvaluation',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationResponse.FromString,
                _registered_method=True)
        self.GetEvaluationList = channel.unary_unary(
                '/moego.service.offering.v1.EvaluationService/GetEvaluationList',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListResponse.FromString,
                _registered_method=True)
        self.GetApplicableEvaluationList = channel.unary_unary(
                '/moego.service.offering.v1.EvaluationService/GetApplicableEvaluationList',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetApplicableEvaluationListRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetApplicableEvaluationListResponse.FromString,
                _registered_method=True)
        self.GetBusinessListWithApplicableEvaluation = channel.unary_unary(
                '/moego.service.offering.v1.EvaluationService/GetBusinessListWithApplicableEvaluation',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetBusinessListWithApplicableEvaluationRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetBusinessListWithApplicableEvaluationResponse.FromString,
                _registered_method=True)
        self.GetEvaluationListWithEvaluationIds = channel.unary_unary(
                '/moego.service.offering.v1.EvaluationService/GetEvaluationListWithEvaluationIds',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListWithEvaluationIdsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListWithEvaluationIdsResponse.FromString,
                _registered_method=True)
        self.ListEvaluation = channel.unary_unary(
                '/moego.service.offering.v1.EvaluationService/ListEvaluation',
                request_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.ListEvaluationRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.ListEvaluationResponse.FromString,
                _registered_method=True)


class EvaluationServiceServicer(object):
    """the evaluation service
    """

    def CreateEvaluation(self, request, context):
        """create evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateEvaluation(self, request, context):
        """update evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteEvaluation(self, request, context):
        """delete evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetEvaluation(self, request, context):
        """get evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetEvaluationList(self, request, context):
        """get evaluation list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetApplicableEvaluationList(self, request, context):
        """get applicable evaluation list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBusinessListWithApplicableEvaluation(self, request, context):
        """get business list with applicable evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetEvaluationListWithEvaluationIds(self, request, context):
        """get evaluation list with evaluation ids
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListEvaluation(self, request, context):
        """list evaluation
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_EvaluationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateEvaluation,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.CreateEvaluationRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.CreateEvaluationResponse.SerializeToString,
            ),
            'UpdateEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateEvaluation,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.UpdateEvaluationRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.UpdateEvaluationResponse.SerializeToString,
            ),
            'DeleteEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteEvaluation,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.DeleteEvaluationRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.DeleteEvaluationResponse.SerializeToString,
            ),
            'GetEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEvaluation,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationResponse.SerializeToString,
            ),
            'GetEvaluationList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEvaluationList,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListResponse.SerializeToString,
            ),
            'GetApplicableEvaluationList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetApplicableEvaluationList,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetApplicableEvaluationListRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetApplicableEvaluationListResponse.SerializeToString,
            ),
            'GetBusinessListWithApplicableEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBusinessListWithApplicableEvaluation,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetBusinessListWithApplicableEvaluationRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetBusinessListWithApplicableEvaluationResponse.SerializeToString,
            ),
            'GetEvaluationListWithEvaluationIds': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEvaluationListWithEvaluationIds,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListWithEvaluationIdsRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListWithEvaluationIdsResponse.SerializeToString,
            ),
            'ListEvaluation': grpc.unary_unary_rpc_method_handler(
                    servicer.ListEvaluation,
                    request_deserializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.ListEvaluationRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.ListEvaluationResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.offering.v1.EvaluationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.offering.v1.EvaluationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class EvaluationService(object):
    """the evaluation service
    """

    @staticmethod
    def CreateEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.EvaluationService/CreateEvaluation',
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.CreateEvaluationRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.CreateEvaluationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.EvaluationService/UpdateEvaluation',
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.UpdateEvaluationRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.UpdateEvaluationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.EvaluationService/DeleteEvaluation',
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.DeleteEvaluationRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.DeleteEvaluationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.EvaluationService/GetEvaluation',
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetEvaluationList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.EvaluationService/GetEvaluationList',
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetApplicableEvaluationList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.EvaluationService/GetApplicableEvaluationList',
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetApplicableEvaluationListRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetApplicableEvaluationListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetBusinessListWithApplicableEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.EvaluationService/GetBusinessListWithApplicableEvaluation',
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetBusinessListWithApplicableEvaluationRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetBusinessListWithApplicableEvaluationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetEvaluationListWithEvaluationIds(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.EvaluationService/GetEvaluationListWithEvaluationIds',
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListWithEvaluationIdsRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.GetEvaluationListWithEvaluationIdsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListEvaluation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v1.EvaluationService/ListEvaluation',
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.ListEvaluationRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v1_dot_evaluation__service__pb2.ListEvaluationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
