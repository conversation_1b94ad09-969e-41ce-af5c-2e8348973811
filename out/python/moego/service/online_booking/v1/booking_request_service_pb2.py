# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/online_booking/v1/booking_request_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/online_booking/v1/booking_request_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from google.type import date_pb2 as google_dot_type_dot_date__pb2
from google.type import interval_pb2 as google_dot_type_dot_interval__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2
from moego.models.offering.v1 import evaluation_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_evaluation__models__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.offering.v1 import service_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__models__pb2
from moego.models.online_booking.v1 import booking_request_defs_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_booking__request__defs__pb2
from moego.models.online_booking.v1 import booking_request_enums_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_booking__request__enums__pb2
from moego.models.online_booking.v1 import booking_request_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_booking__request__models__pb2
from moego.models.online_booking.v1 import grooming_service_detail_defs_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_grooming__service__detail__defs__pb2
from moego.models.organization.v1 import tenant_pb2 as moego_dot_models_dot_organization_dot_v1_dot_tenant__pb2
from moego.service.online_booking.v1 import boarding_add_on_detail_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_boarding__add__on__detail__service__pb2
from moego.service.online_booking.v1 import boarding_service_detail_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_boarding__service__detail__service__pb2
from moego.service.online_booking.v1 import daycare_add_on_detail_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_daycare__add__on__detail__service__pb2
from moego.service.online_booking.v1 import daycare_service_detail_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_daycare__service__detail__service__pb2
from moego.service.online_booking.v1 import dog_walking_detail_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_dog__walking__detail__service__pb2
from moego.service.online_booking.v1 import evaluation_test_detail_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_evaluation__test__detail__service__pb2
from moego.service.online_booking.v1 import feeding_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_feeding__service__pb2
from moego.service.online_booking.v1 import grooming_add_on_detail_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_grooming__add__on__detail__service__pb2
from moego.service.online_booking.v1 import grooming_auto_assign_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_grooming__auto__assign__service__pb2
from moego.service.online_booking.v1 import grooming_service_detail_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_grooming__service__detail__service__pb2
from moego.service.online_booking.v1 import group_class_service_detail_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_group__class__service__detail__service__pb2
from moego.service.online_booking.v1 import medication_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_medication__service__pb2
from moego.utils.v2 import condition_messages_pb2 as moego_dot_utils_dot_v2_dot_condition__messages__pb2
from moego.utils.v2 import list_pb2 as moego_dot_utils_dot_v2_dot_list__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n=moego/service/online_booking/v1/booking_request_service.proto\x12\x1fmoego.service.online_booking.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x16google/type/date.proto\x1a\x1agoogle/type/interval.proto\x1a\x17google/type/money.proto\x1a\x30moego/models/offering/v1/evaluation_models.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a-moego/models/offering/v1/service_models.proto\x1a\x39moego/models/online_booking/v1/booking_request_defs.proto\x1a:moego/models/online_booking/v1/booking_request_enums.proto\x1a;moego/models/online_booking/v1/booking_request_models.proto\x1a\x41moego/models/online_booking/v1/grooming_service_detail_defs.proto\x1a)moego/models/organization/v1/tenant.proto\x1a\x44moego/service/online_booking/v1/boarding_add_on_detail_service.proto\x1a\x45moego/service/online_booking/v1/boarding_service_detail_service.proto\x1a\x43moego/service/online_booking/v1/daycare_add_on_detail_service.proto\x1a\x44moego/service/online_booking/v1/daycare_service_detail_service.proto\x1a@moego/service/online_booking/v1/dog_walking_detail_service.proto\x1a\x44moego/service/online_booking/v1/evaluation_test_detail_service.proto\x1a\x35moego/service/online_booking/v1/feeding_service.proto\x1a\x44moego/service/online_booking/v1/grooming_add_on_detail_service.proto\x1a\x42moego/service/online_booking/v1/grooming_auto_assign_service.proto\x1a\x45moego/service/online_booking/v1/grooming_service_detail_service.proto\x1aHmoego/service/online_booking/v1/group_class_service_detail_service.proto\x1a\x38moego/service/online_booking/v1/medication_service.proto\x1a\'moego/utils/v2/condition_messages.proto\x1a\x19moego/utils/v2/list.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xcf\"\n\x1b\x43reateBookingRequestRequest\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12*\n\x0e\x61ppointment_id\x18\x03 \x01(\x03H\x00R\rappointmentId\x88\x01\x01\x12>\n\nstart_date\x18\x04 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\tstartDate\x88\x01\x01\x12+\n\nstart_time\x18\x05 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x02R\tstartTime\x88\x01\x01\x12:\n\x08\x65nd_date\x18\x06 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x03R\x07\x65ndDate\x88\x01\x01\x12\x1e\n\x08\x65nd_time\x18\x07 \x01(\x05H\x04R\x07\x65ndTime\x88\x01\x01\x12\x1b\n\x06status\x18\x08 \x01(\x05H\x05R\x06status\x88\x01\x01\x12\"\n\nis_prepaid\x18\t \x01(\x08H\x06R\tisPrepaid\x88\x01\x01\x12\x36\n\x0f\x61\x64\x64itional_note\x18\n \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x07R\x0e\x61\x64\x64itionalNote\x88\x01\x01\x12\x36\n\x0fsource_platform\x18\x0b \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x08R\x0esourcePlatform\x88\x01\x01\x12>\n\ncreated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.TimestampH\tR\tcreatedAt\x88\x01\x01\x12>\n\nupdated_at\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.TimestampH\nR\tupdatedAt\x88\x01\x01\x12\"\n\ncompany_id\x18\x0f \x01(\x03H\x0bR\tcompanyId\x88\x01\x01\x12j\n\x08services\x18\x10 \x03(\x0b\x32\x44.moego.service.online_booking.v1.CreateBookingRequestRequest.ServiceB\x08\xfa\x42\x05\x92\x01\x02\x08\x01R\x08services\x12Q\n\x04\x61ttr\x18\x11 \x01(\x0b\x32\x38.moego.models.online_booking.v1.BookingRequestModel.AttrH\x0cR\x04\x61ttr\x88\x01\x01\x12y\n\x0epayment_status\x18\x12 \x01(\x0e\x32\x41.moego.models.online_booking.v1.BookingRequestModel.PaymentStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\rR\rpaymentStatus\x88\x01\x01\x12*\n\x11need_create_order\x18\x13 \x01(\x08R\x0fneedCreateOrder\x12l\n\nmembership\x18\x14 \x01(\x0b\x32G.moego.service.online_booking.v1.CreateBookingRequestRequest.MembershipH\x0eR\nmembership\x88\x01\x01\x12\'\n\x07\x63omment\x18\x15 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x0fR\x07\x63omment\x88\x01\x01\x12W\n\x06source\x18\x16 \x01(\x0e\x32:.moego.models.online_booking.v1.BookingRequestModel.SourceH\x10R\x06source\x88\x01\x01\x12 \n\tsource_id\x18\x17 \x01(\x03H\x11R\x08sourceId\x88\x01\x01\x1a\xad\x05\n\x07Service\x12j\n\x08grooming\x18\x01 \x01(\x0b\x32L.moego.service.online_booking.v1.CreateBookingRequestRequest.GroomingServiceH\x00R\x08grooming\x12j\n\x08\x62oarding\x18\x02 \x01(\x0b\x32L.moego.service.online_booking.v1.CreateBookingRequestRequest.BoardingServiceH\x00R\x08\x62oarding\x12g\n\x07\x64\x61ycare\x18\x03 \x01(\x0b\x32K.moego.service.online_booking.v1.CreateBookingRequestRequest.DaycareServiceH\x00R\x07\x64\x61ycare\x12p\n\nevaluation\x18\x04 \x01(\x0b\x32N.moego.service.online_booking.v1.CreateBookingRequestRequest.EvaluationServiceH\x00R\nevaluation\x12q\n\x0b\x64og_walking\x18\x05 \x01(\x0b\x32N.moego.service.online_booking.v1.CreateBookingRequestRequest.DogWalkingServiceH\x00R\ndogWalking\x12q\n\x0bgroup_class\x18\x06 \x01(\x0b\x32N.moego.service.online_booking.v1.CreateBookingRequestRequest.GroupClassServiceH\x00R\ngroupClassB\t\n\x07service\x1a\xb1\x02\n\x0fGroomingService\x12]\n\x07service\x18\x01 \x01(\x0b\x32\x43.moego.service.online_booking.v1.CreateGroomingServiceDetailRequestR\x07service\x12_\n\x06\x61\x64\x64ons\x18\x02 \x03(\x0b\x32\x43.moego.service.online_booking.v1.CreateGroomingServiceDetailRequestB\x02\x18\x01R\x06\x61\x64\x64ons\x12^\n\taddons_v2\x18\x03 \x03(\x0b\x32\x41.moego.service.online_booking.v1.CreateGroomingAddOnDetailRequestR\x08\x61\x64\x64onsV2\x1a\xc7\x05\n\x0f\x42oardingService\x12]\n\x07service\x18\x01 \x01(\x0b\x32\x43.moego.service.online_booking.v1.CreateBoardingServiceDetailRequestR\x07service\x12Y\n\x06\x61\x64\x64ons\x18\x02 \x03(\x0b\x32\x41.moego.service.online_booking.v1.CreateBoardingAddOnDetailRequestR\x06\x61\x64\x64ons\x12X\n\x07\x66\x65\x65\x64ing\x18\x03 \x01(\x0b\x32\x35.moego.service.online_booking.v1.CreateFeedingRequestB\x02\x18\x01H\x00R\x07\x66\x65\x65\x64ing\x88\x01\x01\x12\x61\n\nmedication\x18\x04 \x01(\x0b\x32\x38.moego.service.online_booking.v1.CreateMedicationRequestB\x02\x18\x01H\x01R\nmedication\x88\x01\x01\x12Q\n\x08\x66\x65\x65\x64ings\x18\x05 \x03(\x0b\x32\x35.moego.service.online_booking.v1.CreateFeedingRequestR\x08\x66\x65\x65\x64ings\x12Z\n\x0bmedications\x18\x06 \x03(\x0b\x32\x38.moego.service.online_booking.v1.CreateMedicationRequestR\x0bmedications\x12\x66\n\x08waitlist\x18\x07 \x01(\x0b\x32\x45.moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequestH\x02R\x08waitlist\x88\x01\x01\x42\n\n\x08_feedingB\r\n\x0b_medicationB\x0b\n\t_waitlist\x1a\xc3\x05\n\x0e\x44\x61ycareService\x12\\\n\x07service\x18\x01 \x01(\x0b\x32\x42.moego.service.online_booking.v1.CreateDaycareServiceDetailRequestR\x07service\x12X\n\x06\x61\x64\x64ons\x18\x02 \x03(\x0b\<EMAIL>.online_booking.v1.CreateDaycareAddOnDetailRequestR\x06\x61\x64\x64ons\x12X\n\x07\x66\x65\x65\x64ing\x18\x03 \x01(\x0b\x32\x35.moego.service.online_booking.v1.CreateFeedingRequestB\x02\x18\x01H\x00R\x07\x66\x65\x65\x64ing\x88\x01\x01\x12\x61\n\nmedication\x18\x04 \x01(\x0b\x32\x38.moego.service.online_booking.v1.CreateMedicationRequestB\x02\x18\x01H\x01R\nmedication\x88\x01\x01\x12Q\n\x08\x66\x65\x65\x64ings\x18\x05 \x03(\x0b\x32\x35.moego.service.online_booking.v1.CreateFeedingRequestR\x08\x66\x65\x65\x64ings\x12Z\n\x0bmedications\x18\x06 \x03(\x0b\x32\x38.moego.service.online_booking.v1.CreateMedicationRequestR\x0bmedications\x12\x65\n\x08waitlist\x18\x07 \x01(\x0b\x32\x44.moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequestH\x02R\x08waitlist\x88\x01\x01\x42\n\n\x08_feedingB\r\n\x0b_medicationB\x0b\n\t_waitlist\x1aq\n\x11\x45valuationService\x12\\\n\x07service\x18\x01 \x01(\x0b\x32\x42.moego.service.online_booking.v1.CreateEvaluationTestDetailRequestR\x07service\x1at\n\x11\x44ogWalkingService\x12_\n\x07service\x18\x01 \x01(\x0b\x32\x45.moego.service.online_booking.v1.CreateDogWalkingServiceDetailRequestR\x07service\x1at\n\x11GroupClassService\x12_\n\x07service\x18\x01 \x01(\x0b\x32\x45.moego.service.online_booking.v1.CreateGroupClassServiceDetailRequestR\x07service\x1a\x46\n\nMembership\x12\x38\n\x0emembership_ids\x18\x01 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x10\xe8\x07\x18\x01\"\x04\"\x02 \x00R\rmembershipIdsB\x11\n\x0f_appointment_idB\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x0b\n\t_end_dateB\x0b\n\t_end_timeB\t\n\x07_statusB\r\n\x0b_is_prepaidB\x12\n\x10_additional_noteB\x12\n\x10_source_platformB\r\n\x0b_created_atB\r\n\x0b_updated_atB\r\n\x0b_company_idB\x07\n\x05_attrB\x11\n\x0f_payment_statusB\r\n\x0b_membershipB\n\n\x08_commentB\t\n\x07_sourceB\x0c\n\n_source_id\"\xaf\x02\n\x18GetBookingRequestRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12{\n\x11\x61ssociated_models\x18\x02 \x03(\x0e\x32=.moego.models.online_booking.v1.BookingRequestAssociatedModelB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x10\x61ssociatedModels\x12}\n\x10payment_statuses\x18\x03 \x03(\x0e\x32\x41.moego.models.online_booking.v1.BookingRequestModel.PaymentStatusB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x0fpaymentStatuses\"\xd2\x01\n\x19GetBookingRequestResponse\x12\\\n\x0f\x62ooking_request\x18\x01 \x01(\x0b\x32\x33.moego.models.online_booking.v1.BookingRequestModelR\x0e\x62ookingRequest\x12W\n\x0fwaitlist_extras\x18\x02 \x01(\x0b\x32..moego.service.online_booking.v1.WaitlistExtraR\x0ewaitlistExtras\"\xe7\x0c\n\x1aListBookingRequestsRequest\x12*\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\t\xfa\x42\x06\"\x04 \x00@\x01R\nbusinessId\x12{\n\x11\x61ssociated_models\x18\x02 \x03(\x0e\x32=.moego.models.online_booking.v1.BookingRequestAssociatedModelB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x10\x61ssociatedModels\x12\x61\n\x08statuses\x18\x03 \x03(\x0e\x32\x34.moego.models.online_booking.v1.BookingRequestStatusB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x08statuses\x12\x35\n\x0c\x62usiness_ids\x18\x04 \x03(\x03\x42\x12\xfa\x42\x0f\x92\x01\x0c\x08\x00\x10\x64\x18\x01\"\x04\"\x02 \x00R\x0b\x62usinessIds\x12\x35\n\nstart_date\x18\x05 \x01(\x0b\x32\x11.google.type.DateH\x00R\tstartDate\x88\x01\x01\x12\x31\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x11.google.type.DateH\x01R\x07\x65ndDate\x88\x01\x01\x12\x34\n\torder_bys\x18\x07 \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBys\x12\x46\n\npagination\x18\x08 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x02R\npagination\x88\x01\x01\x12(\n\x08keywords\x18\t \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x03R\x08keywords\x88\x01\x01\x12\x63\n\rservice_items\x18\n \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x13\xfa\x42\x10\x92\x01\r\x08\x00\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x0cserviceItems\x12>\n\x15service_type_includes\x18\x0b \x03(\x05\x42\n\xfa\x42\x07\x92\x01\x04\x08\x00\x18\x01R\x13serviceTypeIncludes\x12+\n\ncompany_id\x18\x0c \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x04R\tcompanyId\x88\x01\x01\x12/\n\x0b\x63ustomer_id\x18\r \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\ncustomerId\x12\x39\n\x0f\x61ppointment_ids\x18\x0e \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\x12}\n\x10payment_statuses\x18\x0f \x03(\x0e\x32\x41.moego.models.online_booking.v1.BookingRequestModel.PaymentStatusB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x0fpaymentStatuses\x12W\n\x06source\x18\x10 \x01(\x0e\x32:.moego.models.online_booking.v1.BookingRequestModel.SourceH\x05R\x06source\x88\x01\x01\x12\x33\n\x13is_waitlist_expired\x18\x11 \x01(\x08H\x06R\x11isWaitlistExpired\x88\x01\x01\x12\x37\n\x15is_waitlist_available\x18\x12 \x01(\x08H\x07R\x13isWaitlistAvailable\x88\x01\x01\x12-\n\x10order_price_desc\x18\x13 \x01(\x08H\x08R\x0eorderPriceDesc\x88\x01\x01\x12>\n\x0flatest_end_date\x18\x14 \x01(\x0b\x32\x11.google.type.DateH\tR\rlatestEndDate\x88\x01\x01\x12T\n\x07sources\x18\x15 \x03(\x0e\x32:.moego.models.online_booking.v1.BookingRequestModel.SourceR\x07sourcesB\r\n\x0b_start_dateB\x0b\n\t_end_dateB\r\n\x0b_paginationB\x0b\n\t_keywordsB\r\n\x0b_company_idB\t\n\x07_sourceB\x16\n\x14_is_waitlist_expiredB\x18\n\x16_is_waitlist_availableB\x13\n\x11_order_price_descB\x12\n\x10_latest_end_date\"\xae\x02\n\x1bListBookingRequestsResponse\x12^\n\x10\x62ooking_requests\x18\x01 \x03(\x0b\x32\x33.moego.models.online_booking.v1.BookingRequestModelR\x0f\x62ookingRequests\x12W\n\x0fwaitlist_extras\x18\x03 \x03(\x0b\x32..moego.service.online_booking.v1.WaitlistExtraR\x0ewaitlistExtras\x12G\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseH\x00R\npagination\x88\x01\x01\x42\r\n\x0b_pagination\"\x98\n\n\x14ListWaitlistsRequest\x12{\n\x11\x61ssociated_models\x18\x02 \x03(\x0e\x32=.moego.models.online_booking.v1.BookingRequestAssociatedModelB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x10\x61ssociatedModels\x12\x35\n\x0c\x62usiness_ids\x18\x04 \x03(\x03\x42\x12\xfa\x42\x0f\x92\x01\x0c\x08\x00\x10\x64\x18\x01\"\x04\"\x02 \x00R\x0b\x62usinessIds\x12\x35\n\nstart_date\x18\x05 \x01(\x0b\x32\x11.google.type.DateH\x00R\tstartDate\x88\x01\x01\x12\x31\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x11.google.type.DateH\x01R\x07\x65ndDate\x88\x01\x01\x12\x34\n\torder_bys\x18\x07 \x03(\x0b\x32\x17.moego.utils.v2.OrderByR\x08orderBys\x12\x46\n\npagination\x18\x08 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x02R\npagination\x88\x01\x01\x12(\n\x08keywords\x18\t \x01(\tB\x07\xfa\x42\x04r\x02\x18\x32H\x03R\x08keywords\x88\x01\x01\x12\x63\n\rservice_items\x18\n \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x13\xfa\x42\x10\x92\x01\r\x08\x00\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x0cserviceItems\x12>\n\x15service_type_includes\x18\x0b \x03(\x05\x42\n\xfa\x42\x07\x92\x01\x04\x08\x00\x18\x01R\x13serviceTypeIncludes\x12+\n\ncompany_id\x18\x0c \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x04R\tcompanyId\x88\x01\x01\x12/\n\x0b\x63ustomer_id\x18\r \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\ncustomerId\x12W\n\x06source\x18\x10 \x01(\x0e\x32:.moego.models.online_booking.v1.BookingRequestModel.SourceH\x05R\x06source\x88\x01\x01\x12\x33\n\x13is_waitlist_expired\x18\x11 \x01(\x08H\x06R\x11isWaitlistExpired\x88\x01\x01\x12\x37\n\x15is_waitlist_available\x18\x12 \x01(\x08H\x07R\x13isWaitlistAvailable\x88\x01\x01\x12-\n\x10order_price_desc\x18\x13 \x01(\x08H\x08R\x0eorderPriceDesc\x88\x01\x01\x12>\n\x0flatest_end_date\x18\x14 \x01(\x0b\x32\x11.google.type.DateH\tR\rlatestEndDate\x88\x01\x01\x12T\n\x07sources\x18\x15 \x03(\x0e\x32:.moego.models.online_booking.v1.BookingRequestModel.SourceR\x07sourcesB\r\n\x0b_start_dateB\x0b\n\t_end_dateB\r\n\x0b_paginationB\x0b\n\t_keywordsB\r\n\x0b_company_idB\t\n\x07_sourceB\x16\n\x14_is_waitlist_expiredB\x18\n\x16_is_waitlist_availableB\x13\n\x11_order_price_descB\x12\n\x10_latest_end_date\"\xa8\x02\n\x15ListWaitlistsResponse\x12^\n\x10\x62ooking_requests\x18\x01 \x03(\x0b\x32\x33.moego.models.online_booking.v1.BookingRequestModelR\x0f\x62ookingRequests\x12W\n\x0fwaitlist_extras\x18\x03 \x03(\x0b\x32..moego.service.online_booking.v1.WaitlistExtraR\x0ewaitlistExtras\x12G\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseH\x00R\npagination\x88\x01\x01\x42\r\n\x0b_pagination\"a\n\rWaitlistExtra\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12!\n\x0cis_available\x18\x02 \x01(\x08R\x0bisAvailable\x12\x1d\n\nis_expired\x18\x03 \x01(\x08R\tisExpired\"\x87\x02\n\x19\x43reateGroomingOnlyRequest\x12\x64\n\x0f\x62ooking_request\x18\x01 \x01(\x0b\x32\x31.moego.models.online_booking.v1.BookingRequestDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0e\x62ookingRequest\x12\x83\x01\n\x18grooming_service_details\x18\x02 \x03(\x0b\x32\x38.moego.models.online_booking.v1.GroomingServiceDetailDefB\x0f\xfa\x42\x0c\x92\x01\t\x08\x01\"\x05\x8a\x01\x02\x10\x01R\x16groomingServiceDetails\"\x1c\n\x1a\x43reateGroomingOnlyResponse\"\x96\x01\n!UpdateBookingRequestStatusRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12X\n\x06status\x18\x02 \x01(\x0e\x32\x34.moego.models.online_booking.v1.BookingRequestStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x06status\"K\n\"UpdateBookingRequestStatusResponse\x12%\n\x0eupdated_result\x18\x01 \x01(\x08R\rupdatedResult\"\x1a\n\x18RetryFailedEventsRequest\"\x1b\n\x19RetryFailedEventsResponse\"\x9f\x15\n\x1bUpdateBookingRequestRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x33\n\x0e\x61ppointment_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\rappointmentId\x88\x01\x01\x12>\n\nstart_date\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\tstartDate\x88\x01\x01\x12+\n\nstart_time\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x02R\tstartTime\x88\x01\x01\x12:\n\x08\x65nd_date\x18\x05 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x03R\x07\x65ndDate\x88\x01\x01\x12\'\n\x08\x65nd_time\x18\x06 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x04R\x07\x65ndTime\x88\x01\x01\x12]\n\x06status\x18\t \x01(\x0e\x32\x34.moego.models.online_booking.v1.BookingRequestStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x05R\x06status\x88\x01\x01\x12`\n\x08services\x18\n \x03(\x0b\x32\x44.moego.service.online_booking.v1.UpdateBookingRequestRequest.ServiceR\x08services\x12Q\n\x04\x61ttr\x18\x0b \x01(\x0b\x32\x38.moego.models.online_booking.v1.BookingRequestModel.AttrH\x06R\x04\x61ttr\x88\x01\x01\x12y\n\x0epayment_status\x18\x0c \x01(\x0e\x32\x41.moego.models.online_booking.v1.BookingRequestModel.PaymentStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x07R\rpaymentStatus\x88\x01\x01\x12\'\n\x07\x63omment\x18\r \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x08R\x07\x63omment\x88\x01\x01\x1a\xd5\x02\n\x07Service\x12j\n\x08grooming\x18\x01 \x01(\x0b\x32L.moego.service.online_booking.v1.UpdateBookingRequestRequest.GroomingServiceH\x00R\x08grooming\x12j\n\x08\x62oarding\x18\x02 \x01(\x0b\x32L.moego.service.online_booking.v1.UpdateBookingRequestRequest.BoardingServiceH\x00R\x08\x62oarding\x12g\n\x07\x64\x61ycare\x18\x03 \x01(\x0b\x32K.moego.service.online_booking.v1.UpdateBookingRequestRequest.DaycareServiceH\x00R\x07\x64\x61ycareB\t\n\x07service\x1a\xc3\x02\n\x0fGroomingService\x12]\n\x07service\x18\x01 \x01(\x0b\x32\x43.moego.service.online_booking.v1.UpdateGroomingServiceDetailRequestR\x07service\x12Y\n\x06\x61\x64\x64ons\x18\x02 \x03(\x0b\x32\x41.moego.service.online_booking.v1.UpdateGroomingAddOnDetailRequestR\x06\x61\x64\x64ons\x12\x66\n\x0b\x61uto_assign\x18\x03 \x01(\x0b\<EMAIL>.online_booking.v1.UpsertGroomingAutoAssignRequestH\x00R\nautoAssign\x88\x01\x01\x42\x0e\n\x0c_auto_assign\x1a\xc6\x04\n\x0f\x42oardingService\x12]\n\x07service\x18\x01 \x01(\x0b\x32\x43.moego.service.online_booking.v1.UpdateBoardingServiceDetailRequestR\x07service\x12g\n\x0f\x66\x65\x65\x64ings_upsert\x18\x02 \x01(\x0b\x32\x39.moego.service.online_booking.v1.CreateFeedingRequestListH\x00R\x0e\x66\x65\x65\x64ingsUpsert\x88\x01\x01\x12p\n\x12medications_upsert\x18\x03 \x01(\x0b\x32<.moego.service.online_booking.v1.CreateMedicationRequestListH\x01R\x11medicationsUpsert\x88\x01\x01\x12Y\n\x06\x61\x64\x64ons\x18\x04 \x03(\x0b\x32\x41.moego.service.online_booking.v1.UpdateBoardingAddOnDetailRequestR\x06\x61\x64\x64ons\x12\x66\n\x08waitlist\x18\x07 \x01(\x0b\x32\x45.moego.service.online_booking.v1.UpdateBoardingServiceWaitlistRequestH\x02R\x08waitlist\x88\x01\x01\x42\x12\n\x10_feedings_upsertB\x15\n\x13_medications_upsertB\x0b\n\t_waitlist\x1a\xc2\x04\n\x0e\x44\x61ycareService\x12\\\n\x07service\x18\x01 \x01(\x0b\x32\x42.moego.service.online_booking.v1.UpdateDaycareServiceDetailRequestR\x07service\x12g\n\x0f\x66\x65\x65\x64ings_upsert\x18\x02 \x01(\x0b\x32\x39.moego.service.online_booking.v1.CreateFeedingRequestListH\x00R\x0e\x66\x65\x65\x64ingsUpsert\x88\x01\x01\x12p\n\x12medications_upsert\x18\x03 \x01(\x0b\x32<.moego.service.online_booking.v1.CreateMedicationRequestListH\x01R\x11medicationsUpsert\x88\x01\x01\x12X\n\x06\x61\x64\x64ons\x18\x04 \x03(\x0b\<EMAIL>.online_booking.v1.UpdateDaycareAddOnDetailRequestR\x06\x61\x64\x64ons\x12\x65\n\x08waitlist\x18\x07 \x01(\x0b\x32\x44.moego.service.online_booking.v1.UpdateDaycareServiceWaitlistRequestH\x02R\x08waitlist\x88\x01\x01\x42\x12\n\x10_feedings_upsertB\x15\n\x13_medications_upsertB\x0b\n\t_waitlistB\x11\n\x0f_appointment_idB\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x0b\n\t_end_dateB\x0b\n\t_end_timeB\t\n\x07_statusB\x07\n\x05_attrB\x11\n\x0f_payment_statusB\n\n\x08_comment\"\x1e\n\x1cUpdateBookingRequestResponse\"\xfe\t\n\x1cReplaceBookingRequestRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12>\n\nstart_date\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x00R\tstartDate\x88\x01\x01\x12+\n\nstart_time\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x01R\tstartTime\x88\x01\x01\x12:\n\x08\x65nd_date\x18\x05 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x02R\x07\x65ndDate\x88\x01\x01\x12\'\n\x08\x65nd_time\x18\x06 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x03R\x07\x65ndTime\x88\x01\x01\x12]\n\x06status\x18\t \x01(\x0e\x32\x34.moego.models.online_booking.v1.BookingRequestStatusB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x04R\x06status\x88\x01\x01\x12\x61\n\x08services\x18\n \x03(\x0b\x32\x45.moego.service.online_booking.v1.ReplaceBookingRequestRequest.ServiceR\x08services\x12\'\n\x07\x63omment\x18\r \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x05R\x07\x63omment\x88\x01\x01\x1a\xeb\x01\n\x07Service\x12k\n\x08\x62oarding\x18\x02 \x01(\x0b\x32M.moego.service.online_booking.v1.ReplaceBookingRequestRequest.BoardingServiceH\x00R\x08\x62oarding\x12h\n\x07\x64\x61ycare\x18\x03 \x01(\x0b\x32L.moego.service.online_booking.v1.ReplaceBookingRequestRequest.DaycareServiceH\x00R\x07\x64\x61ycareB\t\n\x07service\x1a\xe5\x01\n\x0f\x42oardingService\x12]\n\x07service\x18\x01 \x01(\x0b\x32\x43.moego.service.online_booking.v1.CreateBoardingServiceDetailRequestR\x07service\x12\x66\n\x08waitlist\x18\x07 \x01(\x0b\x32\x45.moego.service.online_booking.v1.CreateBoardingServiceWaitlistRequestH\x00R\x08waitlist\x88\x01\x01\x42\x0b\n\t_waitlist\x1a\xe2\x01\n\x0e\x44\x61ycareService\x12\\\n\x07service\x18\x01 \x01(\x0b\x32\x42.moego.service.online_booking.v1.CreateDaycareServiceDetailRequestR\x07service\x12\x65\n\x08waitlist\x18\x07 \x01(\x0b\x32\x44.moego.service.online_booking.v1.CreateDaycareServiceWaitlistRequestH\x00R\x08waitlist\x88\x01\x01\x42\x0b\n\t_waitlistB\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x0b\n\t_end_dateB\x0b\n\t_end_timeB\t\n\x07_statusB\n\n\x08_comment\"\x1f\n\x1dReplaceBookingRequestResponse\"\x81\x01\n\x14GetAutoAssignRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\"\x95\x08\n\x15GetAutoAssignResponse\x12~\n\x18\x62oarding_assign_requires\x18\x01 \x03(\x0b\x32\x44.moego.service.online_booking.v1.GetAutoAssignResponse.AssignRequireR\x16\x62oardingAssignRequires\x12W\n\x0fpet_to_lodgings\x18\x02 \x03(\x0b\x32/.moego.models.online_booking.v1.PetToLodgingDefR\rpetToLodgings\x12`\n\x08lodgings\x18\x03 \x03(\x0b\x32\x44.moego.service.online_booking.v1.GetAutoAssignResponse.LodgingDetailR\x08lodgings\x12\x82\x01\n\x1a\x65valuation_assign_requires\x18\x04 \x03(\x0b\x32\x44.moego.service.online_booking.v1.GetAutoAssignResponse.AssignRequireR\x18\x65valuationAssignRequires\x12\x66\n\x18\x65valuation_pet_to_staffs\x18\x05 \x03(\x0b\x32-.moego.models.online_booking.v1.PetToStaffDefR\x15\x65valuationPetToStaffs\x12|\n\x17\x64\x61ycare_assign_requires\x18\x06 \x03(\x0b\x32\x44.moego.service.online_booking.v1.GetAutoAssignResponse.AssignRequireR\x15\x64\x61ycareAssignRequires\x1a\xcc\x01\n\rAssignRequire\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x1d\n\nservice_id\x18\x02 \x01(\x03R\tserviceId\x12\"\n\nstart_date\x18\x03 \x01(\tH\x00R\tstartDate\x88\x01\x01\x12\x1e\n\x08\x65nd_date\x18\x04 \x01(\tH\x01R\x07\x65ndDate\x88\x01\x01\x12%\n\x0especific_dates\x18\x05 \x03(\tR\rspecificDatesB\r\n\x0b_start_dateB\x0b\n\t_end_date\x1a\x86\x01\n\rLodgingDetail\x12\x1d\n\nlodging_id\x18\x01 \x01(\x03R\tlodgingId\x12*\n\x11lodging_unit_name\x18\x02 \x01(\tR\x0flodgingUnitName\x12*\n\x11lodging_type_name\x18\x03 \x01(\tR\x0flodgingTypeName\"\xab\x04\n\x1b\x41\x63\x63\x65ptBookingRequestRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12W\n\x0fpet_to_lodgings\x18\x02 \x03(\x0b\x32/.moego.models.online_booking.v1.PetToLodgingDefR\rpetToLodgings\x12Q\n\rpet_to_staffs\x18\x03 \x03(\x0b\x32-.moego.models.online_booking.v1.PetToStaffDefR\x0bpetToStaffs\x12&\n\ncompany_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\'\n\x08staff_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x07staffId\x88\x01\x01\x12W\n\x0fpet_to_services\x18\x07 \x03(\x0b\x32/.moego.models.online_booking.v1.PetToServiceDefR\rpetToServices\x12\x66\n\x18\x65valuation_pet_to_staffs\x18\t \x03(\x0b\x32-.moego.models.online_booking.v1.PetToStaffDefR\x15\x65valuationPetToStaffsB\x0b\n\t_staff_id\"]\n\x1c\x41\x63\x63\x65ptBookingRequestResponse\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\"\xbf\x01\n\x1c\x44\x65\x63lineBookingRequestRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\'\n\x08staff_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x07staffId\x88\x01\x01\x42\x0b\n\t_staff_id\"^\n\x1d\x44\x65\x63lineBookingRequestResponse\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\"\xa5\x04\n\x1b\x43ountBookingRequestsRequest\x12\x46\n\x06tenant\x18\x01 \x01(\x0b\x32$.moego.models.organization.v1.TenantB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x06tenant\x12\x63\n\x07\x66ilters\x18\x02 \x01(\x0b\x32\x44.moego.service.online_booking.v1.CountBookingRequestsRequest.FiltersH\x00R\x07\x66ilters\x88\x01\x01\x1a\xcc\x02\n\x07\x46ilters\x12H\n\x12\x63reated_time_range\x18\x01 \x01(\x0b\x32\x15.google.type.IntervalH\x00R\x10\x63reatedTimeRange\x88\x01\x01\x12\x61\n\x08statuses\x18\x02 \x03(\x0e\x32\x34.moego.models.online_booking.v1.BookingRequestStatusB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x08statuses\x12}\n\x10payment_statuses\x18\x03 \x03(\x0e\x32\x41.moego.models.online_booking.v1.BookingRequestModel.PaymentStatusB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x0fpaymentStatusesB\x15\n\x13_created_time_rangeB\n\n\x08_filters\"4\n\x1c\x43ountBookingRequestsResponse\x12\x14\n\x05\x63ount\x18\x01 \x01(\x05R\x05\x63ount\"W\n\x1bListBookingRequestIdRequest\x12\x38\n\x0f\x61ppointment_ids\x18\x01 \x03(\x03\x42\x0f\xfa\x42\x0c\x92\x01\t\x10\x90N\"\x04\"\x02 \x00R\x0e\x61ppointmentIds\"\xa6\x02\n\x1cListBookingRequestIdResponse\x12\xb1\x01\n$appointment_id_to_booking_request_id\x18\x01 \x03(\x0b\x32\x62.moego.service.online_booking.v1.ListBookingRequestIdResponse.AppointmentIdToBookingRequestIdEntryR\x1f\x61ppointmentIdToBookingRequestId\x1aR\n$AppointmentIdToBookingRequestIdEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x03R\x05value:\x02\x38\x01\"b\n(SyncBookingRequestFromAppointmentRequest\x12\x36\n\x0e\x61ppointment_id\x18\x01 \x03(\x03\x42\x0f\xfa\x42\x0c\x92\x01\t\x10\xe8\x07\"\x04\"\x02 \x00R\rappointmentId\"+\n)SyncBookingRequestFromAppointmentResponse\"C\n(TriggerBookingRequestAutoAcceptedRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"U\n)TriggerBookingRequestAutoAcceptedResponse\x12(\n\x10is_auto_accepted\x18\x01 \x01(\x08R\x0eisAutoAccepted\"\x89\x12\n\x1d\x41\x63\x63\x65ptBookingRequestV2Request\x12+\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x12\x17\n\x02id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\'\n\x08staff_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\x07staffId\x88\x01\x01\x12{\n\x11grooming_services\x18\n \x03(\x0b\x32N.moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingServiceR\x10groomingServices\x12{\n\x11\x62oarding_services\x18\x0b \x03(\x0b\x32N.moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingServiceR\x10\x62oardingServices\x12x\n\x10\x64\x61ycare_services\x18\x0c \x03(\x0b\x32M.moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareServiceR\x0f\x64\x61ycareServices\x12\x81\x01\n\x13\x65valuation_services\x18\r \x03(\x0b\x32P.moego.service.online_booking.v1.AcceptBookingRequestV2Request.EvaluationServiceR\x12\x65valuationServices\x12u\n\x0fgrooming_addons\x18\x0e \x03(\x0b\x32L.moego.service.online_booking.v1.AcceptBookingRequestV2Request.GroomingAddonR\x0egroomingAddons\x12u\n\x0f\x62oarding_addons\x18\x0f \x03(\x0b\x32L.moego.service.online_booking.v1.AcceptBookingRequestV2Request.BoardingAddonR\x0e\x62oardingAddons\x12r\n\x0e\x64\x61ycare_addons\x18\x10 \x03(\x0b\x32K.moego.service.online_booking.v1.AcceptBookingRequestV2Request.DaycareAddonR\rdaycareAddons\x12\x94\x01\n\x1a\x63reate_evaluation_requests\x18\x11 \x03(\x0b\x32V.moego.service.online_booking.v1.AcceptBookingRequestV2Request.CreateEvaluationRequestR\x18\x63reateEvaluationRequests\x1a\x81\x01\n\x0fGroomingService\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n\x08staff_id\x18\x02 \x01(\x03H\x00R\x07staffId\x88\x01\x01\x12\"\n\nstart_time\x18\x03 \x01(\x05H\x01R\tstartTime\x88\x01\x01\x42\x0b\n\t_staff_idB\r\n\x0b_start_time\x1aT\n\x0f\x42oardingService\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\"\n\nlodging_id\x18\x04 \x01(\x03H\x00R\tlodgingId\x88\x01\x01\x42\r\n\x0b_lodging_id\x1aS\n\x0e\x44\x61ycareService\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\"\n\nlodging_id\x18\x04 \x01(\x03H\x00R\tlodgingId\x88\x01\x01\x42\r\n\x0b_lodging_id\x1a\x8c\x01\n\x11\x45valuationService\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n\x08staff_id\x18\x02 \x01(\x03H\x00R\x07staffId\x88\x01\x01\x12(\n\revaluation_id\x18\x03 \x01(\x03H\x01R\x0c\x65valuationId\x88\x01\x01\x42\x0b\n\t_staff_idB\x10\n\x0e_evaluation_id\x1a\x7f\n\rGroomingAddon\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n\x08staff_id\x18\x02 \x01(\x03H\x00R\x07staffId\x88\x01\x01\x12\"\n\nstart_time\x18\x03 \x01(\x05H\x01R\tstartTime\x88\x01\x01\x42\x0b\n\t_staff_idB\r\n\x0b_start_time\x1a\x7f\n\rBoardingAddon\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n\x08staff_id\x18\x02 \x01(\x03H\x00R\x07staffId\x88\x01\x01\x12\"\n\nstart_time\x18\x03 \x01(\x05H\x01R\tstartTime\x88\x01\x01\x42\x0b\n\t_staff_idB\r\n\x0b_start_time\x1a~\n\x0c\x44\x61ycareAddon\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n\x08staff_id\x18\x02 \x01(\x03H\x00R\x07staffId\x88\x01\x01\x12\"\n\nstart_time\x18\x03 \x01(\x05H\x01R\tstartTime\x88\x01\x01\x42\x0b\n\t_staff_idB\r\n\x0b_start_time\x1a\xab\x02\n\x17\x43reateEvaluationRequest\x12,\n\revaluation_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0c\x65valuationId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12:\n\nstart_date\x18\x03 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\tstartDate\x12&\n\nstart_time\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00R\tstartTime\x12\x1e\n\x08staff_id\x18\x05 \x01(\x03H\x00R\x07staffId\x88\x01\x01\x12\"\n\nlodging_id\x18\x06 \x01(\x03H\x01R\tlodgingId\x88\x01\x01\x42\x0b\n\t_staff_idB\r\n\x0b_lodging_idB\r\n\x0b_company_idB\x0b\n\t_staff_id\"_\n\x1e\x41\x63\x63\x65ptBookingRequestV2Response\x12\x16\n\x06result\x18\x01 \x01(\x08R\x06result\x12%\n\x0e\x61ppointment_id\x18\x02 \x01(\x03R\rappointmentId\"\x86\x01\n\x11\x41utoAssignRequest\x12\x35\n\x12\x62ooking_request_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x10\x62ookingRequestId\x12+\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x42\r\n\x0b_company_id\"\xe6\x07\n\x12\x41utoAssignResponse\x12p\n\x11\x62oarding_services\x18\x01 \x03(\x0b\x32\x43.moego.service.online_booking.v1.AutoAssignResponse.BoardingServiceR\x10\x62oardingServices\x12v\n\x13\x65valuation_services\x18\x02 \x03(\x0b\x32\x45.moego.service.online_booking.v1.AutoAssignResponse.EvaluationServiceR\x12\x65valuationServices\x12m\n\x10\x64\x61ycare_services\x18\x03 \x03(\x0b\x32\x42.moego.service.online_booking.v1.AutoAssignResponse.DaycareServiceR\x0f\x64\x61ycareServices\x1a\xce\x01\n\x0f\x42oardingService\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\"\n\nlodging_id\x18\x02 \x01(\x03H\x00R\tlodgingId\x88\x01\x01\x12\x61\n\x12missing_evaluation\x18\x03 \x01(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewH\x01R\x11missingEvaluation\x88\x01\x01\x42\r\n\x0b_lodging_idB\x15\n\x13_missing_evaluation\x1a\x88\x02\n\x11\x45valuationService\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n\x08staff_id\x18\x02 \x01(\x03H\x00R\x07staffId\x88\x01\x01\x12^\n\x13selected_evaluation\x18\x03 \x01(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewR\x12selectedEvaluation\x12V\n\x0f\x61ll_evaluations\x18\x04 \x03(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewR\x0e\x61llEvaluationsB\x0b\n\t_staff_id\x1a\x9a\x01\n\x0e\x44\x61ycareService\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x61\n\x12missing_evaluation\x18\x02 \x01(\x0b\x32-.moego.models.offering.v1.EvaluationBriefViewH\x00R\x11missingEvaluation\x88\x01\x01\x42\x15\n\x13_missing_evaluation\"\x9a\x01\n$CreateBoardingServiceWaitlistRequest\x12:\n\nstart_date\x18\x01 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\tstartDate\x12\x36\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07\x65ndDate\"i\n#CreateDaycareServiceWaitlistRequest\x12\x42\n\x0especific_dates\x18\x01 \x03(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x92\x01\x02\x08\x01R\rspecificDates\"\xac\x01\n$UpdateBoardingServiceWaitlistRequest\x12\x35\n\nstart_date\x18\x01 \x01(\x0b\x32\x11.google.type.DateH\x00R\tstartDate\x88\x01\x01\x12\x31\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x11.google.type.DateH\x01R\x07\x65ndDate\x88\x01\x01\x42\r\n\x0b_start_dateB\x0b\n\t_end_date\"~\n#UpdateDaycareServiceWaitlistRequest\x12\x44\n\x0especific_dates\x18\x01 \x01(\x0b\x32\x18.moego.utils.v2.DateListH\x00R\rspecificDates\x88\x01\x01\x42\x11\n\x0f_specific_dates\"#\n!CheckWaitlistAvailableTaskRequest\"$\n\"CheckWaitlistAvailableTaskResponse\"\xd7\x01\n#MoveBookingRequestToWaitlistRequest\x12+\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\nbusinessId\x88\x01\x01\x12\x35\n\x12\x62ooking_request_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x10\x62ookingRequestIdB\r\n\x0b_company_idB\x0e\n\x0c_business_id\"&\n$MoveBookingRequestToWaitlistResponse\"\x96\x02\n#PreviewBookingRequestPricingRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12-\n\x0b\x63ustomer_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\ncustomerId\x88\x01\x01\x12^\n\x0cpet_services\x18\x04 \x03(\x0b\x32\x31.moego.models.online_booking.v1.PetServiceDetailsB\x08\xfa\x42\x05\x92\x01\x02\x08\x01R\x0bpetServicesB\x0e\n\x0c_customer_id\"\x88\x03\n$PreviewBookingRequestPricingResponse\x12m\n\nline_items\x18\x01 \x03(\x0b\x32N.moego.service.online_booking.v1.PreviewBookingRequestPricingResponse.LineItemR\tlineItems\x1a\xf0\x01\n\x08LineItem\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12I\n\x07service\x18\x02 \x01(\x0b\x32/.moego.models.offering.v1.CustomizedServiceViewR\x07service\x12\x31\n\nunit_price\x18\x03 \x01(\x0b\x32\x12.google.type.MoneyR\tunitPrice\x12\x1a\n\x08quantity\x18\x04 \x01(\x05R\x08quantity\x12\x33\n\x0btotal_price\x18\x05 \x01(\x0b\x32\x12.google.type.MoneyR\ntotalPrice\"j\n\"CountBookingRequestByFilterRequest\x12\x1d\n\ncompany_id\x18\x01 \x01(\x03R\tcompanyId\x12%\n\x0e\x65valuation_ids\x18\x02 \x03(\x03R\revaluationIds\"\xf1\x01\n#CountBookingRequestByFilterResponse\x12\x85\x01\n\x11\x65valuation_in_use\x18\x01 \x03(\x0b\x32Y.moego.service.online_booking.v1.CountBookingRequestByFilterResponse.EvaluationInUseEntryR\x0f\x65valuationInUse\x1a\x42\n\x14\x45valuationInUseEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x14\n\x05value\x18\x02 \x01(\x05R\x05value:\x02\x38\x01\x32\xc9\x1a\n\x15\x42ookingRequestService\x12s\n\x14\x43reateBookingRequest\x12<.moego.service.online_booking.v1.CreateBookingRequestRequest\x1a\x1b.google.protobuf.Int64Value\"\x00\x12\x8c\x01\n\x11GetBookingRequest\x12\x39.moego.service.online_booking.v1.GetBookingRequestRequest\x1a:.moego.service.online_booking.v1.GetBookingRequestResponse\"\x00\x12\x92\x01\n\x13ListBookingRequests\x12;.moego.service.online_booking.v1.ListBookingRequestsRequest\x1a<.moego.service.online_booking.v1.ListBookingRequestsResponse\"\x00\x12\x80\x01\n\rListWaitlists\x12\x35.moego.service.online_booking.v1.ListWaitlistsRequest\x1a\x36.moego.service.online_booking.v1.ListWaitlistsResponse\"\x00\x12\x8d\x01\n\x12\x43reateGroomingOnly\x12:.moego.service.online_booking.v1.CreateGroomingOnlyRequest\x1a;.moego.service.online_booking.v1.CreateGroomingOnlyResponse\x12\xa5\x01\n\x1aUpdateBookingRequestStatus\x12\x42.moego.service.online_booking.v1.UpdateBookingRequestStatusRequest\x1a\x43.moego.service.online_booking.v1.UpdateBookingRequestStatusResponse\x12\x93\x01\n\x14UpdateBookingRequest\x12<.moego.service.online_booking.v1.UpdateBookingRequestRequest\x1a=.moego.service.online_booking.v1.UpdateBookingRequestResponse\x12\x96\x01\n\x15ReplaceBookingRequest\x12=.moego.service.online_booking.v1.ReplaceBookingRequestRequest\x1a>.moego.service.online_booking.v1.ReplaceBookingRequestResponse\x12\x8a\x01\n\x11RetryFailedEvents\x12\x39.moego.service.online_booking.v1.RetryFailedEventsRequest\x1a:.moego.service.online_booking.v1.RetryFailedEventsResponse\x12\x83\x01\n\rGetAutoAssign\x12\x35.moego.service.online_booking.v1.GetAutoAssignRequest\x1a\x36.moego.service.online_booking.v1.GetAutoAssignResponse\"\x03\x88\x02\x01\x12u\n\nAutoAssign\x12\x32.moego.service.online_booking.v1.AutoAssignRequest\x1a\x33.moego.service.online_booking.v1.AutoAssignResponse\x12\x98\x01\n\x14\x41\x63\x63\x65ptBookingRequest\x12<.moego.service.online_booking.v1.AcceptBookingRequestRequest\x1a=.moego.service.online_booking.v1.AcceptBookingRequestResponse\"\x03\x88\x02\x01\x12\x99\x01\n\x16\x41\x63\x63\x65ptBookingRequestV2\x12>.moego.service.online_booking.v1.AcceptBookingRequestV2Request\x1a?.moego.service.online_booking.v1.AcceptBookingRequestV2Response\x12\x96\x01\n\x15\x44\x65\x63lineBookingRequest\x12=.moego.service.online_booking.v1.DeclineBookingRequestRequest\x1a>.moego.service.online_booking.v1.DeclineBookingRequestResponse\x12\x93\x01\n\x14\x43ountBookingRequests\x12<.moego.service.online_booking.v1.CountBookingRequestsRequest\x1a=.moego.service.online_booking.v1.CountBookingRequestsResponse\x12\x93\x01\n\x14ListBookingRequestId\x12<.moego.service.online_booking.v1.ListBookingRequestIdRequest\x1a=.moego.service.online_booking.v1.ListBookingRequestIdResponse\x12\xba\x01\n!SyncBookingRequestFromAppointment\x12I.moego.service.online_booking.v1.SyncBookingRequestFromAppointmentRequest\x1aJ.moego.service.online_booking.v1.SyncBookingRequestFromAppointmentResponse\x12\xba\x01\n!TriggerBookingRequestAutoAccepted\x12I.moego.service.online_booking.v1.TriggerBookingRequestAutoAcceptedRequest\x1aJ.moego.service.online_booking.v1.TriggerBookingRequestAutoAcceptedResponse\x12\xa5\x01\n\x1a\x43heckWaitlistAvailableTask\x12\x42.moego.service.online_booking.v1.CheckWaitlistAvailableTaskRequest\x1a\x43.moego.service.online_booking.v1.CheckWaitlistAvailableTaskResponse\x12\xab\x01\n\x1cMoveBookingRequestToWaitlist\x12\x44.moego.service.online_booking.v1.MoveBookingRequestToWaitlistRequest\x1a\x45.moego.service.online_booking.v1.MoveBookingRequestToWaitlistResponse\x12\xa8\x01\n\x1b\x43ountBookingRequestByFilter\x12\x43.moego.service.online_booking.v1.CountBookingRequestByFilterRequest\x1a\x44.moego.service.online_booking.v1.CountBookingRequestByFilterResponse\x12\xab\x01\n\x1cPreviewBookingRequestPricing\x12\x44.moego.service.online_booking.v1.PreviewBookingRequestPricingRequest\x1a\x45.moego.service.online_booking.v1.PreviewBookingRequestPricingResponseB\x94\x01\n\'com.moego.idl.service.online_booking.v1P\x01Zggithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.online_booking.v1.booking_request_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\'com.moego.idl.service.online_booking.v1P\001Zggithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb'
  _globals['_CREATEBOOKINGREQUESTREQUEST_GROOMINGSERVICE'].fields_by_name['addons']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST_GROOMINGSERVICE'].fields_by_name['addons']._serialized_options = b'\030\001'
  _globals['_CREATEBOOKINGREQUESTREQUEST_BOARDINGSERVICE'].fields_by_name['feeding']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST_BOARDINGSERVICE'].fields_by_name['feeding']._serialized_options = b'\030\001'
  _globals['_CREATEBOOKINGREQUESTREQUEST_BOARDINGSERVICE'].fields_by_name['medication']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST_BOARDINGSERVICE'].fields_by_name['medication']._serialized_options = b'\030\001'
  _globals['_CREATEBOOKINGREQUESTREQUEST_DAYCARESERVICE'].fields_by_name['feeding']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST_DAYCARESERVICE'].fields_by_name['feeding']._serialized_options = b'\030\001'
  _globals['_CREATEBOOKINGREQUESTREQUEST_DAYCARESERVICE'].fields_by_name['medication']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST_DAYCARESERVICE'].fields_by_name['medication']._serialized_options = b'\030\001'
  _globals['_CREATEBOOKINGREQUESTREQUEST_MEMBERSHIP'].fields_by_name['membership_ids']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST_MEMBERSHIP'].fields_by_name['membership_ids']._serialized_options = b'\372B\016\222\001\013\020\350\007\030\001\"\004\"\002 \000'
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['start_date']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['start_time']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['start_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['end_date']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['additional_note']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['additional_note']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['source_platform']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['source_platform']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['services']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['services']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['payment_status']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['payment_status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['comment']._loaded_options = None
  _globals['_CREATEBOOKINGREQUESTREQUEST'].fields_by_name['comment']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_GETBOOKINGREQUESTREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_GETBOOKINGREQUESTREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETBOOKINGREQUESTREQUEST'].fields_by_name['associated_models']._loaded_options = None
  _globals['_GETBOOKINGREQUESTREQUEST'].fields_by_name['associated_models']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_GETBOOKINGREQUESTREQUEST'].fields_by_name['payment_statuses']._loaded_options = None
  _globals['_GETBOOKINGREQUESTREQUEST'].fields_by_name['payment_statuses']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\006\"\004 \000@\001'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['associated_models']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['associated_models']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['statuses']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['statuses']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\017\222\001\014\010\000\020d\030\001\"\004\"\002 \000'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['keywords']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['keywords']._serialized_options = b'\372B\004r\002\0302'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['service_items']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['service_items']._serialized_options = b'\372B\020\222\001\r\010\000\030\001\"\007\202\001\004\020\001 \000'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['service_type_includes']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['service_type_includes']._serialized_options = b'\372B\007\222\001\004\010\000\030\001'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002 \000'
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['payment_statuses']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTSREQUEST'].fields_by_name['payment_statuses']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['associated_models']._loaded_options = None
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['associated_models']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\017\222\001\014\010\000\020d\030\001\"\004\"\002 \000'
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['keywords']._loaded_options = None
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['keywords']._serialized_options = b'\372B\004r\002\0302'
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['service_items']._loaded_options = None
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['service_items']._serialized_options = b'\372B\020\222\001\r\010\000\030\001\"\007\202\001\004\020\001 \000'
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['service_type_includes']._loaded_options = None
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['service_type_includes']._serialized_options = b'\372B\007\222\001\004\010\000\030\001'
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_LISTWAITLISTSREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_CREATEGROOMINGONLYREQUEST'].fields_by_name['booking_request']._loaded_options = None
  _globals['_CREATEGROOMINGONLYREQUEST'].fields_by_name['booking_request']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEGROOMINGONLYREQUEST'].fields_by_name['grooming_service_details']._loaded_options = None
  _globals['_CREATEGROOMINGONLYREQUEST'].fields_by_name['grooming_service_details']._serialized_options = b'\372B\014\222\001\t\010\001\"\005\212\001\002\020\001'
  _globals['_UPDATEBOOKINGREQUESTSTATUSREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTSTATUSREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEBOOKINGREQUESTSTATUSREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTSTATUSREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['start_date']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['start_time']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['start_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['end_date']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['end_time']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['end_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['payment_status']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['payment_status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['comment']._loaded_options = None
  _globals['_UPDATEBOOKINGREQUESTREQUEST'].fields_by_name['comment']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['start_date']._loaded_options = None
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['start_time']._loaded_options = None
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['start_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['end_date']._loaded_options = None
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['end_time']._loaded_options = None
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['end_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['status']._loaded_options = None
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['status']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['comment']._loaded_options = None
  _globals['_REPLACEBOOKINGREQUESTREQUEST'].fields_by_name['comment']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_GETAUTOASSIGNREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_GETAUTOASSIGNREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAUTOASSIGNREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETAUTOASSIGNREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAUTOASSIGNREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETAUTOASSIGNREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_DECLINEBOOKINGREQUESTREQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COUNTBOOKINGREQUESTSREQUEST_FILTERS'].fields_by_name['statuses']._loaded_options = None
  _globals['_COUNTBOOKINGREQUESTSREQUEST_FILTERS'].fields_by_name['statuses']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_COUNTBOOKINGREQUESTSREQUEST_FILTERS'].fields_by_name['payment_statuses']._loaded_options = None
  _globals['_COUNTBOOKINGREQUESTSREQUEST_FILTERS'].fields_by_name['payment_statuses']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_COUNTBOOKINGREQUESTSREQUEST'].fields_by_name['tenant']._loaded_options = None
  _globals['_COUNTBOOKINGREQUESTSREQUEST'].fields_by_name['tenant']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LISTBOOKINGREQUESTIDREQUEST'].fields_by_name['appointment_ids']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTIDREQUEST'].fields_by_name['appointment_ids']._serialized_options = b'\372B\014\222\001\t\020\220N\"\004\"\002 \000'
  _globals['_LISTBOOKINGREQUESTIDRESPONSE_APPOINTMENTIDTOBOOKINGREQUESTIDENTRY']._loaded_options = None
  _globals['_LISTBOOKINGREQUESTIDRESPONSE_APPOINTMENTIDTOBOOKINGREQUESTIDENTRY']._serialized_options = b'8\001'
  _globals['_SYNCBOOKINGREQUESTFROMAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_SYNCBOOKINGREQUESTFROMAPPOINTMENTREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\014\222\001\t\020\350\007\"\004\"\002 \000'
  _globals['_TRIGGERBOOKINGREQUESTAUTOACCEPTEDREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_TRIGGERBOOKINGREQUESTAUTOACCEPTEDREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST'].fields_by_name['evaluation_id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST'].fields_by_name['evaluation_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST'].fields_by_name['start_date']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST'].fields_by_name['start_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST'].fields_by_name['start_time']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST'].fields_by_name['start_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST'].fields_by_name['staff_id']._loaded_options = None
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_AUTOASSIGNREQUEST'].fields_by_name['booking_request_id']._loaded_options = None
  _globals['_AUTOASSIGNREQUEST'].fields_by_name['booking_request_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_AUTOASSIGNREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_AUTOASSIGNREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEBOARDINGSERVICEWAITLISTREQUEST'].fields_by_name['start_date']._loaded_options = None
  _globals['_CREATEBOARDINGSERVICEWAITLISTREQUEST'].fields_by_name['start_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEBOARDINGSERVICEWAITLISTREQUEST'].fields_by_name['end_date']._loaded_options = None
  _globals['_CREATEBOARDINGSERVICEWAITLISTREQUEST'].fields_by_name['end_date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATEDAYCARESERVICEWAITLISTREQUEST'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_CREATEDAYCARESERVICEWAITLISTREQUEST'].fields_by_name['specific_dates']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTREQUEST'].fields_by_name['booking_request_id']._loaded_options = None
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTREQUEST'].fields_by_name['booking_request_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST'].fields_by_name['pet_services']._loaded_options = None
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST'].fields_by_name['pet_services']._serialized_options = b'\372B\005\222\001\002\010\001'
  _globals['_COUNTBOOKINGREQUESTBYFILTERRESPONSE_EVALUATIONINUSEENTRY']._loaded_options = None
  _globals['_COUNTBOOKINGREQUESTBYFILTERRESPONSE_EVALUATIONINUSEENTRY']._serialized_options = b'8\001'
  _globals['_BOOKINGREQUESTSERVICE'].methods_by_name['GetAutoAssign']._loaded_options = None
  _globals['_BOOKINGREQUESTSERVICE'].methods_by_name['GetAutoAssign']._serialized_options = b'\210\002\001'
  _globals['_BOOKINGREQUESTSERVICE'].methods_by_name['AcceptBookingRequest']._loaded_options = None
  _globals['_BOOKINGREQUESTSERVICE'].methods_by_name['AcceptBookingRequest']._serialized_options = b'\210\002\001'
  _globals['_CREATEBOOKINGREQUESTREQUEST']._serialized_start=1620
  _globals['_CREATEBOOKINGREQUESTREQUEST']._serialized_end=6051
  _globals['_CREATEBOOKINGREQUESTREQUEST_SERVICE']._serialized_start=2945
  _globals['_CREATEBOOKINGREQUESTREQUEST_SERVICE']._serialized_end=3630
  _globals['_CREATEBOOKINGREQUESTREQUEST_GROOMINGSERVICE']._serialized_start=3633
  _globals['_CREATEBOOKINGREQUESTREQUEST_GROOMINGSERVICE']._serialized_end=3938
  _globals['_CREATEBOOKINGREQUESTREQUEST_BOARDINGSERVICE']._serialized_start=3941
  _globals['_CREATEBOOKINGREQUESTREQUEST_BOARDINGSERVICE']._serialized_end=4652
  _globals['_CREATEBOOKINGREQUESTREQUEST_DAYCARESERVICE']._serialized_start=4655
  _globals['_CREATEBOOKINGREQUESTREQUEST_DAYCARESERVICE']._serialized_end=5362
  _globals['_CREATEBOOKINGREQUESTREQUEST_EVALUATIONSERVICE']._serialized_start=5364
  _globals['_CREATEBOOKINGREQUESTREQUEST_EVALUATIONSERVICE']._serialized_end=5477
  _globals['_CREATEBOOKINGREQUESTREQUEST_DOGWALKINGSERVICE']._serialized_start=5479
  _globals['_CREATEBOOKINGREQUESTREQUEST_DOGWALKINGSERVICE']._serialized_end=5595
  _globals['_CREATEBOOKINGREQUESTREQUEST_GROUPCLASSSERVICE']._serialized_start=5597
  _globals['_CREATEBOOKINGREQUESTREQUEST_GROUPCLASSSERVICE']._serialized_end=5713
  _globals['_CREATEBOOKINGREQUESTREQUEST_MEMBERSHIP']._serialized_start=5715
  _globals['_CREATEBOOKINGREQUESTREQUEST_MEMBERSHIP']._serialized_end=5785
  _globals['_GETBOOKINGREQUESTREQUEST']._serialized_start=6054
  _globals['_GETBOOKINGREQUESTREQUEST']._serialized_end=6357
  _globals['_GETBOOKINGREQUESTRESPONSE']._serialized_start=6360
  _globals['_GETBOOKINGREQUESTRESPONSE']._serialized_end=6570
  _globals['_LISTBOOKINGREQUESTSREQUEST']._serialized_start=6573
  _globals['_LISTBOOKINGREQUESTSREQUEST']._serialized_end=8212
  _globals['_LISTBOOKINGREQUESTSRESPONSE']._serialized_start=8215
  _globals['_LISTBOOKINGREQUESTSRESPONSE']._serialized_end=8517
  _globals['_LISTWAITLISTSREQUEST']._serialized_start=8520
  _globals['_LISTWAITLISTSREQUEST']._serialized_end=9824
  _globals['_LISTWAITLISTSRESPONSE']._serialized_start=9827
  _globals['_LISTWAITLISTSRESPONSE']._serialized_end=10123
  _globals['_WAITLISTEXTRA']._serialized_start=10125
  _globals['_WAITLISTEXTRA']._serialized_end=10222
  _globals['_CREATEGROOMINGONLYREQUEST']._serialized_start=10225
  _globals['_CREATEGROOMINGONLYREQUEST']._serialized_end=10488
  _globals['_CREATEGROOMINGONLYRESPONSE']._serialized_start=10490
  _globals['_CREATEGROOMINGONLYRESPONSE']._serialized_end=10518
  _globals['_UPDATEBOOKINGREQUESTSTATUSREQUEST']._serialized_start=10521
  _globals['_UPDATEBOOKINGREQUESTSTATUSREQUEST']._serialized_end=10671
  _globals['_UPDATEBOOKINGREQUESTSTATUSRESPONSE']._serialized_start=10673
  _globals['_UPDATEBOOKINGREQUESTSTATUSRESPONSE']._serialized_end=10748
  _globals['_RETRYFAILEDEVENTSREQUEST']._serialized_start=10750
  _globals['_RETRYFAILEDEVENTSREQUEST']._serialized_end=10776
  _globals['_RETRYFAILEDEVENTSRESPONSE']._serialized_start=10778
  _globals['_RETRYFAILEDEVENTSRESPONSE']._serialized_end=10805
  _globals['_UPDATEBOOKINGREQUESTREQUEST']._serialized_start=10808
  _globals['_UPDATEBOOKINGREQUESTREQUEST']._serialized_end=13527
  _globals['_UPDATEBOOKINGREQUESTREQUEST_SERVICE']._serialized_start=11568
  _globals['_UPDATEBOOKINGREQUESTREQUEST_SERVICE']._serialized_end=11909
  _globals['_UPDATEBOOKINGREQUESTREQUEST_GROOMINGSERVICE']._serialized_start=11912
  _globals['_UPDATEBOOKINGREQUESTREQUEST_GROOMINGSERVICE']._serialized_end=12235
  _globals['_UPDATEBOOKINGREQUESTREQUEST_BOARDINGSERVICE']._serialized_start=12238
  _globals['_UPDATEBOOKINGREQUESTREQUEST_BOARDINGSERVICE']._serialized_end=12820
  _globals['_UPDATEBOOKINGREQUESTREQUEST_DAYCARESERVICE']._serialized_start=12823
  _globals['_UPDATEBOOKINGREQUESTREQUEST_DAYCARESERVICE']._serialized_end=13401
  _globals['_UPDATEBOOKINGREQUESTRESPONSE']._serialized_start=13529
  _globals['_UPDATEBOOKINGREQUESTRESPONSE']._serialized_end=13559
  _globals['_REPLACEBOOKINGREQUESTREQUEST']._serialized_start=13562
  _globals['_REPLACEBOOKINGREQUESTREQUEST']._serialized_end=14840
  _globals['_REPLACEBOOKINGREQUESTREQUEST_SERVICE']._serialized_start=14065
  _globals['_REPLACEBOOKINGREQUESTREQUEST_SERVICE']._serialized_end=14300
  _globals['_REPLACEBOOKINGREQUESTREQUEST_BOARDINGSERVICE']._serialized_start=14303
  _globals['_REPLACEBOOKINGREQUESTREQUEST_BOARDINGSERVICE']._serialized_end=14532
  _globals['_REPLACEBOOKINGREQUESTREQUEST_DAYCARESERVICE']._serialized_start=14535
  _globals['_REPLACEBOOKINGREQUESTREQUEST_DAYCARESERVICE']._serialized_end=14761
  _globals['_REPLACEBOOKINGREQUESTRESPONSE']._serialized_start=14842
  _globals['_REPLACEBOOKINGREQUESTRESPONSE']._serialized_end=14873
  _globals['_GETAUTOASSIGNREQUEST']._serialized_start=14876
  _globals['_GETAUTOASSIGNREQUEST']._serialized_end=15005
  _globals['_GETAUTOASSIGNRESPONSE']._serialized_start=15008
  _globals['_GETAUTOASSIGNRESPONSE']._serialized_end=16053
  _globals['_GETAUTOASSIGNRESPONSE_ASSIGNREQUIRE']._serialized_start=15712
  _globals['_GETAUTOASSIGNRESPONSE_ASSIGNREQUIRE']._serialized_end=15916
  _globals['_GETAUTOASSIGNRESPONSE_LODGINGDETAIL']._serialized_start=15919
  _globals['_GETAUTOASSIGNRESPONSE_LODGINGDETAIL']._serialized_end=16053
  _globals['_ACCEPTBOOKINGREQUESTREQUEST']._serialized_start=16056
  _globals['_ACCEPTBOOKINGREQUESTREQUEST']._serialized_end=16611
  _globals['_ACCEPTBOOKINGREQUESTRESPONSE']._serialized_start=16613
  _globals['_ACCEPTBOOKINGREQUESTRESPONSE']._serialized_end=16706
  _globals['_DECLINEBOOKINGREQUESTREQUEST']._serialized_start=16709
  _globals['_DECLINEBOOKINGREQUESTREQUEST']._serialized_end=16900
  _globals['_DECLINEBOOKINGREQUESTRESPONSE']._serialized_start=16902
  _globals['_DECLINEBOOKINGREQUESTRESPONSE']._serialized_end=16996
  _globals['_COUNTBOOKINGREQUESTSREQUEST']._serialized_start=16999
  _globals['_COUNTBOOKINGREQUESTSREQUEST']._serialized_end=17548
  _globals['_COUNTBOOKINGREQUESTSREQUEST_FILTERS']._serialized_start=17204
  _globals['_COUNTBOOKINGREQUESTSREQUEST_FILTERS']._serialized_end=17536
  _globals['_COUNTBOOKINGREQUESTSRESPONSE']._serialized_start=17550
  _globals['_COUNTBOOKINGREQUESTSRESPONSE']._serialized_end=17602
  _globals['_LISTBOOKINGREQUESTIDREQUEST']._serialized_start=17604
  _globals['_LISTBOOKINGREQUESTIDREQUEST']._serialized_end=17691
  _globals['_LISTBOOKINGREQUESTIDRESPONSE']._serialized_start=17694
  _globals['_LISTBOOKINGREQUESTIDRESPONSE']._serialized_end=17988
  _globals['_LISTBOOKINGREQUESTIDRESPONSE_APPOINTMENTIDTOBOOKINGREQUESTIDENTRY']._serialized_start=17906
  _globals['_LISTBOOKINGREQUESTIDRESPONSE_APPOINTMENTIDTOBOOKINGREQUESTIDENTRY']._serialized_end=17988
  _globals['_SYNCBOOKINGREQUESTFROMAPPOINTMENTREQUEST']._serialized_start=17990
  _globals['_SYNCBOOKINGREQUESTFROMAPPOINTMENTREQUEST']._serialized_end=18088
  _globals['_SYNCBOOKINGREQUESTFROMAPPOINTMENTRESPONSE']._serialized_start=18090
  _globals['_SYNCBOOKINGREQUESTFROMAPPOINTMENTRESPONSE']._serialized_end=18133
  _globals['_TRIGGERBOOKINGREQUESTAUTOACCEPTEDREQUEST']._serialized_start=18135
  _globals['_TRIGGERBOOKINGREQUESTAUTOACCEPTEDREQUEST']._serialized_end=18202
  _globals['_TRIGGERBOOKINGREQUESTAUTOACCEPTEDRESPONSE']._serialized_start=18204
  _globals['_TRIGGERBOOKINGREQUESTAUTOACCEPTEDRESPONSE']._serialized_end=18289
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST']._serialized_start=18292
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST']._serialized_end=20605
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_GROOMINGSERVICE']._serialized_start=19446
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_GROOMINGSERVICE']._serialized_end=19575
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_BOARDINGSERVICE']._serialized_start=19577
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_BOARDINGSERVICE']._serialized_end=19661
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_DAYCARESERVICE']._serialized_start=19663
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_DAYCARESERVICE']._serialized_end=19746
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_EVALUATIONSERVICE']._serialized_start=19749
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_EVALUATIONSERVICE']._serialized_end=19889
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_GROOMINGADDON']._serialized_start=19891
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_GROOMINGADDON']._serialized_end=20018
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_BOARDINGADDON']._serialized_start=20020
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_BOARDINGADDON']._serialized_end=20147
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_DAYCAREADDON']._serialized_start=20149
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_DAYCAREADDON']._serialized_end=20275
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST']._serialized_start=20278
  _globals['_ACCEPTBOOKINGREQUESTV2REQUEST_CREATEEVALUATIONREQUEST']._serialized_end=20577
  _globals['_ACCEPTBOOKINGREQUESTV2RESPONSE']._serialized_start=20607
  _globals['_ACCEPTBOOKINGREQUESTV2RESPONSE']._serialized_end=20702
  _globals['_AUTOASSIGNREQUEST']._serialized_start=20705
  _globals['_AUTOASSIGNREQUEST']._serialized_end=20839
  _globals['_AUTOASSIGNRESPONSE']._serialized_start=20842
  _globals['_AUTOASSIGNRESPONSE']._serialized_end=21840
  _globals['_AUTOASSIGNRESPONSE_BOARDINGSERVICE']._serialized_start=21210
  _globals['_AUTOASSIGNRESPONSE_BOARDINGSERVICE']._serialized_end=21416
  _globals['_AUTOASSIGNRESPONSE_EVALUATIONSERVICE']._serialized_start=21419
  _globals['_AUTOASSIGNRESPONSE_EVALUATIONSERVICE']._serialized_end=21683
  _globals['_AUTOASSIGNRESPONSE_DAYCARESERVICE']._serialized_start=21686
  _globals['_AUTOASSIGNRESPONSE_DAYCARESERVICE']._serialized_end=21840
  _globals['_CREATEBOARDINGSERVICEWAITLISTREQUEST']._serialized_start=21843
  _globals['_CREATEBOARDINGSERVICEWAITLISTREQUEST']._serialized_end=21997
  _globals['_CREATEDAYCARESERVICEWAITLISTREQUEST']._serialized_start=21999
  _globals['_CREATEDAYCARESERVICEWAITLISTREQUEST']._serialized_end=22104
  _globals['_UPDATEBOARDINGSERVICEWAITLISTREQUEST']._serialized_start=22107
  _globals['_UPDATEBOARDINGSERVICEWAITLISTREQUEST']._serialized_end=22279
  _globals['_UPDATEDAYCARESERVICEWAITLISTREQUEST']._serialized_start=22281
  _globals['_UPDATEDAYCARESERVICEWAITLISTREQUEST']._serialized_end=22407
  _globals['_CHECKWAITLISTAVAILABLETASKREQUEST']._serialized_start=22409
  _globals['_CHECKWAITLISTAVAILABLETASKREQUEST']._serialized_end=22444
  _globals['_CHECKWAITLISTAVAILABLETASKRESPONSE']._serialized_start=22446
  _globals['_CHECKWAITLISTAVAILABLETASKRESPONSE']._serialized_end=22482
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTREQUEST']._serialized_start=22485
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTREQUEST']._serialized_end=22700
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTRESPONSE']._serialized_start=22702
  _globals['_MOVEBOOKINGREQUESTTOWAITLISTRESPONSE']._serialized_end=22740
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST']._serialized_start=22743
  _globals['_PREVIEWBOOKINGREQUESTPRICINGREQUEST']._serialized_end=23021
  _globals['_PREVIEWBOOKINGREQUESTPRICINGRESPONSE']._serialized_start=23024
  _globals['_PREVIEWBOOKINGREQUESTPRICINGRESPONSE']._serialized_end=23416
  _globals['_PREVIEWBOOKINGREQUESTPRICINGRESPONSE_LINEITEM']._serialized_start=23176
  _globals['_PREVIEWBOOKINGREQUESTPRICINGRESPONSE_LINEITEM']._serialized_end=23416
  _globals['_COUNTBOOKINGREQUESTBYFILTERREQUEST']._serialized_start=23418
  _globals['_COUNTBOOKINGREQUESTBYFILTERREQUEST']._serialized_end=23524
  _globals['_COUNTBOOKINGREQUESTBYFILTERRESPONSE']._serialized_start=23527
  _globals['_COUNTBOOKINGREQUESTBYFILTERRESPONSE']._serialized_end=23768
  _globals['_COUNTBOOKINGREQUESTBYFILTERRESPONSE_EVALUATIONINUSEENTRY']._serialized_start=23702
  _globals['_COUNTBOOKINGREQUESTBYFILTERRESPONSE_EVALUATIONINUSEENTRY']._serialized_end=23768
  _globals['_BOOKINGREQUESTSERVICE']._serialized_start=23771
  _globals['_BOOKINGREQUESTSERVICE']._serialized_end=27172
# @@protoc_insertion_point(module_scope)
