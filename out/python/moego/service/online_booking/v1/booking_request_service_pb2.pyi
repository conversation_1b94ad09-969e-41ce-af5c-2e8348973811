from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf import wrappers_pb2 as _wrappers_pb2
from google.type import date_pb2 as _date_pb2
from google.type import interval_pb2 as _interval_pb2
from google.type import money_pb2 as _money_pb2
from moego.models.offering.v1 import evaluation_models_pb2 as _evaluation_models_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.offering.v1 import service_models_pb2 as _service_models_pb2
from moego.models.online_booking.v1 import booking_request_defs_pb2 as _booking_request_defs_pb2
from moego.models.online_booking.v1 import booking_request_enums_pb2 as _booking_request_enums_pb2
from moego.models.online_booking.v1 import booking_request_models_pb2 as _booking_request_models_pb2
from moego.models.online_booking.v1 import grooming_service_detail_defs_pb2 as _grooming_service_detail_defs_pb2
from moego.models.organization.v1 import tenant_pb2 as _tenant_pb2
from moego.service.online_booking.v1 import boarding_add_on_detail_service_pb2 as _boarding_add_on_detail_service_pb2
from moego.service.online_booking.v1 import boarding_service_detail_service_pb2 as _boarding_service_detail_service_pb2
from moego.service.online_booking.v1 import daycare_add_on_detail_service_pb2 as _daycare_add_on_detail_service_pb2
from moego.service.online_booking.v1 import daycare_service_detail_service_pb2 as _daycare_service_detail_service_pb2
from moego.service.online_booking.v1 import dog_walking_detail_service_pb2 as _dog_walking_detail_service_pb2
from moego.service.online_booking.v1 import evaluation_test_detail_service_pb2 as _evaluation_test_detail_service_pb2
from moego.service.online_booking.v1 import feeding_service_pb2 as _feeding_service_pb2
from moego.service.online_booking.v1 import grooming_add_on_detail_service_pb2 as _grooming_add_on_detail_service_pb2
from moego.service.online_booking.v1 import grooming_auto_assign_service_pb2 as _grooming_auto_assign_service_pb2
from moego.service.online_booking.v1 import grooming_service_detail_service_pb2 as _grooming_service_detail_service_pb2
from moego.service.online_booking.v1 import group_class_service_detail_service_pb2 as _group_class_service_detail_service_pb2
from moego.service.online_booking.v1 import medication_service_pb2 as _medication_service_pb2
from moego.utils.v2 import condition_messages_pb2 as _condition_messages_pb2
from moego.utils.v2 import list_pb2 as _list_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateBookingRequestRequest(_message.Message):
    __slots__ = ("business_id", "customer_id", "appointment_id", "start_date", "start_time", "end_date", "end_time", "status", "is_prepaid", "additional_note", "source_platform", "created_at", "updated_at", "company_id", "services", "attr", "payment_status", "need_create_order", "membership", "comment", "source", "source_id")
    class Service(_message.Message):
        __slots__ = ("grooming", "boarding", "daycare", "evaluation", "dog_walking", "group_class")
        GROOMING_FIELD_NUMBER: _ClassVar[int]
        BOARDING_FIELD_NUMBER: _ClassVar[int]
        DAYCARE_FIELD_NUMBER: _ClassVar[int]
        EVALUATION_FIELD_NUMBER: _ClassVar[int]
        DOG_WALKING_FIELD_NUMBER: _ClassVar[int]
        GROUP_CLASS_FIELD_NUMBER: _ClassVar[int]
        grooming: CreateBookingRequestRequest.GroomingService
        boarding: CreateBookingRequestRequest.BoardingService
        daycare: CreateBookingRequestRequest.DaycareService
        evaluation: CreateBookingRequestRequest.EvaluationService
        dog_walking: CreateBookingRequestRequest.DogWalkingService
        group_class: CreateBookingRequestRequest.GroupClassService
        def __init__(self, grooming: _Optional[_Union[CreateBookingRequestRequest.GroomingService, _Mapping]] = ..., boarding: _Optional[_Union[CreateBookingRequestRequest.BoardingService, _Mapping]] = ..., daycare: _Optional[_Union[CreateBookingRequestRequest.DaycareService, _Mapping]] = ..., evaluation: _Optional[_Union[CreateBookingRequestRequest.EvaluationService, _Mapping]] = ..., dog_walking: _Optional[_Union[CreateBookingRequestRequest.DogWalkingService, _Mapping]] = ..., group_class: _Optional[_Union[CreateBookingRequestRequest.GroupClassService, _Mapping]] = ...) -> None: ...
    class GroomingService(_message.Message):
        __slots__ = ("service", "addons", "addons_v2")
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        ADDONS_FIELD_NUMBER: _ClassVar[int]
        ADDONS_V2_FIELD_NUMBER: _ClassVar[int]
        service: _grooming_service_detail_service_pb2.CreateGroomingServiceDetailRequest
        addons: _containers.RepeatedCompositeFieldContainer[_grooming_service_detail_service_pb2.CreateGroomingServiceDetailRequest]
        addons_v2: _containers.RepeatedCompositeFieldContainer[_grooming_add_on_detail_service_pb2.CreateGroomingAddOnDetailRequest]
        def __init__(self, service: _Optional[_Union[_grooming_service_detail_service_pb2.CreateGroomingServiceDetailRequest, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[_grooming_service_detail_service_pb2.CreateGroomingServiceDetailRequest, _Mapping]]] = ..., addons_v2: _Optional[_Iterable[_Union[_grooming_add_on_detail_service_pb2.CreateGroomingAddOnDetailRequest, _Mapping]]] = ...) -> None: ...
    class BoardingService(_message.Message):
        __slots__ = ("service", "addons", "feeding", "medication", "feedings", "medications", "waitlist")
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        ADDONS_FIELD_NUMBER: _ClassVar[int]
        FEEDING_FIELD_NUMBER: _ClassVar[int]
        MEDICATION_FIELD_NUMBER: _ClassVar[int]
        FEEDINGS_FIELD_NUMBER: _ClassVar[int]
        MEDICATIONS_FIELD_NUMBER: _ClassVar[int]
        WAITLIST_FIELD_NUMBER: _ClassVar[int]
        service: _boarding_service_detail_service_pb2.CreateBoardingServiceDetailRequest
        addons: _containers.RepeatedCompositeFieldContainer[_boarding_add_on_detail_service_pb2.CreateBoardingAddOnDetailRequest]
        feeding: _feeding_service_pb2.CreateFeedingRequest
        medication: _medication_service_pb2.CreateMedicationRequest
        feedings: _containers.RepeatedCompositeFieldContainer[_feeding_service_pb2.CreateFeedingRequest]
        medications: _containers.RepeatedCompositeFieldContainer[_medication_service_pb2.CreateMedicationRequest]
        waitlist: CreateBoardingServiceWaitlistRequest
        def __init__(self, service: _Optional[_Union[_boarding_service_detail_service_pb2.CreateBoardingServiceDetailRequest, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[_boarding_add_on_detail_service_pb2.CreateBoardingAddOnDetailRequest, _Mapping]]] = ..., feeding: _Optional[_Union[_feeding_service_pb2.CreateFeedingRequest, _Mapping]] = ..., medication: _Optional[_Union[_medication_service_pb2.CreateMedicationRequest, _Mapping]] = ..., feedings: _Optional[_Iterable[_Union[_feeding_service_pb2.CreateFeedingRequest, _Mapping]]] = ..., medications: _Optional[_Iterable[_Union[_medication_service_pb2.CreateMedicationRequest, _Mapping]]] = ..., waitlist: _Optional[_Union[CreateBoardingServiceWaitlistRequest, _Mapping]] = ...) -> None: ...
    class DaycareService(_message.Message):
        __slots__ = ("service", "addons", "feeding", "medication", "feedings", "medications", "waitlist")
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        ADDONS_FIELD_NUMBER: _ClassVar[int]
        FEEDING_FIELD_NUMBER: _ClassVar[int]
        MEDICATION_FIELD_NUMBER: _ClassVar[int]
        FEEDINGS_FIELD_NUMBER: _ClassVar[int]
        MEDICATIONS_FIELD_NUMBER: _ClassVar[int]
        WAITLIST_FIELD_NUMBER: _ClassVar[int]
        service: _daycare_service_detail_service_pb2.CreateDaycareServiceDetailRequest
        addons: _containers.RepeatedCompositeFieldContainer[_daycare_add_on_detail_service_pb2.CreateDaycareAddOnDetailRequest]
        feeding: _feeding_service_pb2.CreateFeedingRequest
        medication: _medication_service_pb2.CreateMedicationRequest
        feedings: _containers.RepeatedCompositeFieldContainer[_feeding_service_pb2.CreateFeedingRequest]
        medications: _containers.RepeatedCompositeFieldContainer[_medication_service_pb2.CreateMedicationRequest]
        waitlist: CreateDaycareServiceWaitlistRequest
        def __init__(self, service: _Optional[_Union[_daycare_service_detail_service_pb2.CreateDaycareServiceDetailRequest, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[_daycare_add_on_detail_service_pb2.CreateDaycareAddOnDetailRequest, _Mapping]]] = ..., feeding: _Optional[_Union[_feeding_service_pb2.CreateFeedingRequest, _Mapping]] = ..., medication: _Optional[_Union[_medication_service_pb2.CreateMedicationRequest, _Mapping]] = ..., feedings: _Optional[_Iterable[_Union[_feeding_service_pb2.CreateFeedingRequest, _Mapping]]] = ..., medications: _Optional[_Iterable[_Union[_medication_service_pb2.CreateMedicationRequest, _Mapping]]] = ..., waitlist: _Optional[_Union[CreateDaycareServiceWaitlistRequest, _Mapping]] = ...) -> None: ...
    class EvaluationService(_message.Message):
        __slots__ = ("service",)
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        service: _evaluation_test_detail_service_pb2.CreateEvaluationTestDetailRequest
        def __init__(self, service: _Optional[_Union[_evaluation_test_detail_service_pb2.CreateEvaluationTestDetailRequest, _Mapping]] = ...) -> None: ...
    class DogWalkingService(_message.Message):
        __slots__ = ("service",)
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        service: _dog_walking_detail_service_pb2.CreateDogWalkingServiceDetailRequest
        def __init__(self, service: _Optional[_Union[_dog_walking_detail_service_pb2.CreateDogWalkingServiceDetailRequest, _Mapping]] = ...) -> None: ...
    class GroupClassService(_message.Message):
        __slots__ = ("service",)
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        service: _group_class_service_detail_service_pb2.CreateGroupClassServiceDetailRequest
        def __init__(self, service: _Optional[_Union[_group_class_service_detail_service_pb2.CreateGroupClassServiceDetailRequest, _Mapping]] = ...) -> None: ...
    class Membership(_message.Message):
        __slots__ = ("membership_ids",)
        MEMBERSHIP_IDS_FIELD_NUMBER: _ClassVar[int]
        membership_ids: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, membership_ids: _Optional[_Iterable[int]] = ...) -> None: ...
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    IS_PREPAID_FIELD_NUMBER: _ClassVar[int]
    ADDITIONAL_NOTE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_PLATFORM_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    ATTR_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_STATUS_FIELD_NUMBER: _ClassVar[int]
    NEED_CREATE_ORDER_FIELD_NUMBER: _ClassVar[int]
    MEMBERSHIP_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    SOURCE_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_id: int
    appointment_id: int
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    status: int
    is_prepaid: bool
    additional_note: str
    source_platform: str
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    company_id: int
    services: _containers.RepeatedCompositeFieldContainer[CreateBookingRequestRequest.Service]
    attr: _booking_request_models_pb2.BookingRequestModel.Attr
    payment_status: _booking_request_models_pb2.BookingRequestModel.PaymentStatus
    need_create_order: bool
    membership: CreateBookingRequestRequest.Membership
    comment: str
    source: _booking_request_models_pb2.BookingRequestModel.Source
    source_id: int
    def __init__(self, business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., status: _Optional[int] = ..., is_prepaid: bool = ..., additional_note: _Optional[str] = ..., source_platform: _Optional[str] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., company_id: _Optional[int] = ..., services: _Optional[_Iterable[_Union[CreateBookingRequestRequest.Service, _Mapping]]] = ..., attr: _Optional[_Union[_booking_request_models_pb2.BookingRequestModel.Attr, _Mapping]] = ..., payment_status: _Optional[_Union[_booking_request_models_pb2.BookingRequestModel.PaymentStatus, str]] = ..., need_create_order: bool = ..., membership: _Optional[_Union[CreateBookingRequestRequest.Membership, _Mapping]] = ..., comment: _Optional[str] = ..., source: _Optional[_Union[_booking_request_models_pb2.BookingRequestModel.Source, str]] = ..., source_id: _Optional[int] = ...) -> None: ...

class GetBookingRequestRequest(_message.Message):
    __slots__ = ("id", "associated_models", "payment_statuses")
    ID_FIELD_NUMBER: _ClassVar[int]
    ASSOCIATED_MODELS_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_STATUSES_FIELD_NUMBER: _ClassVar[int]
    id: int
    associated_models: _containers.RepeatedScalarFieldContainer[_booking_request_enums_pb2.BookingRequestAssociatedModel]
    payment_statuses: _containers.RepeatedScalarFieldContainer[_booking_request_models_pb2.BookingRequestModel.PaymentStatus]
    def __init__(self, id: _Optional[int] = ..., associated_models: _Optional[_Iterable[_Union[_booking_request_enums_pb2.BookingRequestAssociatedModel, str]]] = ..., payment_statuses: _Optional[_Iterable[_Union[_booking_request_models_pb2.BookingRequestModel.PaymentStatus, str]]] = ...) -> None: ...

class GetBookingRequestResponse(_message.Message):
    __slots__ = ("booking_request", "waitlist_extras")
    BOOKING_REQUEST_FIELD_NUMBER: _ClassVar[int]
    WAITLIST_EXTRAS_FIELD_NUMBER: _ClassVar[int]
    booking_request: _booking_request_models_pb2.BookingRequestModel
    waitlist_extras: WaitlistExtra
    def __init__(self, booking_request: _Optional[_Union[_booking_request_models_pb2.BookingRequestModel, _Mapping]] = ..., waitlist_extras: _Optional[_Union[WaitlistExtra, _Mapping]] = ...) -> None: ...

class ListBookingRequestsRequest(_message.Message):
    __slots__ = ("business_id", "associated_models", "statuses", "business_ids", "start_date", "end_date", "order_bys", "pagination", "keywords", "service_items", "service_type_includes", "company_id", "customer_id", "appointment_ids", "payment_statuses", "source", "is_waitlist_expired", "is_waitlist_available", "order_price_desc", "latest_end_date", "sources")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    ASSOCIATED_MODELS_FIELD_NUMBER: _ClassVar[int]
    STATUSES_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    KEYWORDS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEMS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_INCLUDES_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_STATUSES_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    IS_WAITLIST_EXPIRED_FIELD_NUMBER: _ClassVar[int]
    IS_WAITLIST_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    ORDER_PRICE_DESC_FIELD_NUMBER: _ClassVar[int]
    LATEST_END_DATE_FIELD_NUMBER: _ClassVar[int]
    SOURCES_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    associated_models: _containers.RepeatedScalarFieldContainer[_booking_request_enums_pb2.BookingRequestAssociatedModel]
    statuses: _containers.RepeatedScalarFieldContainer[_booking_request_enums_pb2.BookingRequestStatus]
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2.OrderBy]
    pagination: _pagination_messages_pb2.PaginationRequest
    keywords: str
    service_items: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    service_type_includes: _containers.RepeatedScalarFieldContainer[int]
    company_id: int
    customer_id: _containers.RepeatedScalarFieldContainer[int]
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    payment_statuses: _containers.RepeatedScalarFieldContainer[_booking_request_models_pb2.BookingRequestModel.PaymentStatus]
    source: _booking_request_models_pb2.BookingRequestModel.Source
    is_waitlist_expired: bool
    is_waitlist_available: bool
    order_price_desc: bool
    latest_end_date: _date_pb2.Date
    sources: _containers.RepeatedScalarFieldContainer[_booking_request_models_pb2.BookingRequestModel.Source]
    def __init__(self, business_id: _Optional[int] = ..., associated_models: _Optional[_Iterable[_Union[_booking_request_enums_pb2.BookingRequestAssociatedModel, str]]] = ..., statuses: _Optional[_Iterable[_Union[_booking_request_enums_pb2.BookingRequestStatus, str]]] = ..., business_ids: _Optional[_Iterable[int]] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2.OrderBy, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., keywords: _Optional[str] = ..., service_items: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., service_type_includes: _Optional[_Iterable[int]] = ..., company_id: _Optional[int] = ..., customer_id: _Optional[_Iterable[int]] = ..., appointment_ids: _Optional[_Iterable[int]] = ..., payment_statuses: _Optional[_Iterable[_Union[_booking_request_models_pb2.BookingRequestModel.PaymentStatus, str]]] = ..., source: _Optional[_Union[_booking_request_models_pb2.BookingRequestModel.Source, str]] = ..., is_waitlist_expired: bool = ..., is_waitlist_available: bool = ..., order_price_desc: bool = ..., latest_end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., sources: _Optional[_Iterable[_Union[_booking_request_models_pb2.BookingRequestModel.Source, str]]] = ...) -> None: ...

class ListBookingRequestsResponse(_message.Message):
    __slots__ = ("booking_requests", "waitlist_extras", "pagination")
    BOOKING_REQUESTS_FIELD_NUMBER: _ClassVar[int]
    WAITLIST_EXTRAS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    booking_requests: _containers.RepeatedCompositeFieldContainer[_booking_request_models_pb2.BookingRequestModel]
    waitlist_extras: _containers.RepeatedCompositeFieldContainer[WaitlistExtra]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, booking_requests: _Optional[_Iterable[_Union[_booking_request_models_pb2.BookingRequestModel, _Mapping]]] = ..., waitlist_extras: _Optional[_Iterable[_Union[WaitlistExtra, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class ListWaitlistsRequest(_message.Message):
    __slots__ = ("associated_models", "business_ids", "start_date", "end_date", "order_bys", "pagination", "keywords", "service_items", "service_type_includes", "company_id", "customer_id", "source", "is_waitlist_expired", "is_waitlist_available", "order_price_desc", "latest_end_date", "sources")
    ASSOCIATED_MODELS_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    KEYWORDS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEMS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_INCLUDES_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    IS_WAITLIST_EXPIRED_FIELD_NUMBER: _ClassVar[int]
    IS_WAITLIST_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    ORDER_PRICE_DESC_FIELD_NUMBER: _ClassVar[int]
    LATEST_END_DATE_FIELD_NUMBER: _ClassVar[int]
    SOURCES_FIELD_NUMBER: _ClassVar[int]
    associated_models: _containers.RepeatedScalarFieldContainer[_booking_request_enums_pb2.BookingRequestAssociatedModel]
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2.OrderBy]
    pagination: _pagination_messages_pb2.PaginationRequest
    keywords: str
    service_items: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    service_type_includes: _containers.RepeatedScalarFieldContainer[int]
    company_id: int
    customer_id: _containers.RepeatedScalarFieldContainer[int]
    source: _booking_request_models_pb2.BookingRequestModel.Source
    is_waitlist_expired: bool
    is_waitlist_available: bool
    order_price_desc: bool
    latest_end_date: _date_pb2.Date
    sources: _containers.RepeatedScalarFieldContainer[_booking_request_models_pb2.BookingRequestModel.Source]
    def __init__(self, associated_models: _Optional[_Iterable[_Union[_booking_request_enums_pb2.BookingRequestAssociatedModel, str]]] = ..., business_ids: _Optional[_Iterable[int]] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2.OrderBy, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., keywords: _Optional[str] = ..., service_items: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., service_type_includes: _Optional[_Iterable[int]] = ..., company_id: _Optional[int] = ..., customer_id: _Optional[_Iterable[int]] = ..., source: _Optional[_Union[_booking_request_models_pb2.BookingRequestModel.Source, str]] = ..., is_waitlist_expired: bool = ..., is_waitlist_available: bool = ..., order_price_desc: bool = ..., latest_end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., sources: _Optional[_Iterable[_Union[_booking_request_models_pb2.BookingRequestModel.Source, str]]] = ...) -> None: ...

class ListWaitlistsResponse(_message.Message):
    __slots__ = ("booking_requests", "waitlist_extras", "pagination")
    BOOKING_REQUESTS_FIELD_NUMBER: _ClassVar[int]
    WAITLIST_EXTRAS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    booking_requests: _containers.RepeatedCompositeFieldContainer[_booking_request_models_pb2.BookingRequestModel]
    waitlist_extras: _containers.RepeatedCompositeFieldContainer[WaitlistExtra]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, booking_requests: _Optional[_Iterable[_Union[_booking_request_models_pb2.BookingRequestModel, _Mapping]]] = ..., waitlist_extras: _Optional[_Iterable[_Union[WaitlistExtra, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class WaitlistExtra(_message.Message):
    __slots__ = ("id", "is_available", "is_expired")
    ID_FIELD_NUMBER: _ClassVar[int]
    IS_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    IS_EXPIRED_FIELD_NUMBER: _ClassVar[int]
    id: int
    is_available: bool
    is_expired: bool
    def __init__(self, id: _Optional[int] = ..., is_available: bool = ..., is_expired: bool = ...) -> None: ...

class CreateGroomingOnlyRequest(_message.Message):
    __slots__ = ("booking_request", "grooming_service_details")
    BOOKING_REQUEST_FIELD_NUMBER: _ClassVar[int]
    GROOMING_SERVICE_DETAILS_FIELD_NUMBER: _ClassVar[int]
    booking_request: _booking_request_defs_pb2.BookingRequestDef
    grooming_service_details: _containers.RepeatedCompositeFieldContainer[_grooming_service_detail_defs_pb2.GroomingServiceDetailDef]
    def __init__(self, booking_request: _Optional[_Union[_booking_request_defs_pb2.BookingRequestDef, _Mapping]] = ..., grooming_service_details: _Optional[_Iterable[_Union[_grooming_service_detail_defs_pb2.GroomingServiceDetailDef, _Mapping]]] = ...) -> None: ...

class CreateGroomingOnlyResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class UpdateBookingRequestStatusRequest(_message.Message):
    __slots__ = ("id", "status")
    ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    id: int
    status: _booking_request_enums_pb2.BookingRequestStatus
    def __init__(self, id: _Optional[int] = ..., status: _Optional[_Union[_booking_request_enums_pb2.BookingRequestStatus, str]] = ...) -> None: ...

class UpdateBookingRequestStatusResponse(_message.Message):
    __slots__ = ("updated_result",)
    UPDATED_RESULT_FIELD_NUMBER: _ClassVar[int]
    updated_result: bool
    def __init__(self, updated_result: bool = ...) -> None: ...

class RetryFailedEventsRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class RetryFailedEventsResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class UpdateBookingRequestRequest(_message.Message):
    __slots__ = ("id", "appointment_id", "start_date", "start_time", "end_date", "end_time", "status", "services", "attr", "payment_status", "comment")
    class Service(_message.Message):
        __slots__ = ("grooming", "boarding", "daycare")
        GROOMING_FIELD_NUMBER: _ClassVar[int]
        BOARDING_FIELD_NUMBER: _ClassVar[int]
        DAYCARE_FIELD_NUMBER: _ClassVar[int]
        grooming: UpdateBookingRequestRequest.GroomingService
        boarding: UpdateBookingRequestRequest.BoardingService
        daycare: UpdateBookingRequestRequest.DaycareService
        def __init__(self, grooming: _Optional[_Union[UpdateBookingRequestRequest.GroomingService, _Mapping]] = ..., boarding: _Optional[_Union[UpdateBookingRequestRequest.BoardingService, _Mapping]] = ..., daycare: _Optional[_Union[UpdateBookingRequestRequest.DaycareService, _Mapping]] = ...) -> None: ...
    class GroomingService(_message.Message):
        __slots__ = ("service", "addons", "auto_assign")
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        ADDONS_FIELD_NUMBER: _ClassVar[int]
        AUTO_ASSIGN_FIELD_NUMBER: _ClassVar[int]
        service: _grooming_service_detail_service_pb2.UpdateGroomingServiceDetailRequest
        addons: _containers.RepeatedCompositeFieldContainer[_grooming_add_on_detail_service_pb2.UpdateGroomingAddOnDetailRequest]
        auto_assign: _grooming_auto_assign_service_pb2.UpsertGroomingAutoAssignRequest
        def __init__(self, service: _Optional[_Union[_grooming_service_detail_service_pb2.UpdateGroomingServiceDetailRequest, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[_grooming_add_on_detail_service_pb2.UpdateGroomingAddOnDetailRequest, _Mapping]]] = ..., auto_assign: _Optional[_Union[_grooming_auto_assign_service_pb2.UpsertGroomingAutoAssignRequest, _Mapping]] = ...) -> None: ...
    class BoardingService(_message.Message):
        __slots__ = ("service", "feedings_upsert", "medications_upsert", "addons", "waitlist")
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        FEEDINGS_UPSERT_FIELD_NUMBER: _ClassVar[int]
        MEDICATIONS_UPSERT_FIELD_NUMBER: _ClassVar[int]
        ADDONS_FIELD_NUMBER: _ClassVar[int]
        WAITLIST_FIELD_NUMBER: _ClassVar[int]
        service: _boarding_service_detail_service_pb2.UpdateBoardingServiceDetailRequest
        feedings_upsert: _feeding_service_pb2.CreateFeedingRequestList
        medications_upsert: _medication_service_pb2.CreateMedicationRequestList
        addons: _containers.RepeatedCompositeFieldContainer[_boarding_add_on_detail_service_pb2.UpdateBoardingAddOnDetailRequest]
        waitlist: UpdateBoardingServiceWaitlistRequest
        def __init__(self, service: _Optional[_Union[_boarding_service_detail_service_pb2.UpdateBoardingServiceDetailRequest, _Mapping]] = ..., feedings_upsert: _Optional[_Union[_feeding_service_pb2.CreateFeedingRequestList, _Mapping]] = ..., medications_upsert: _Optional[_Union[_medication_service_pb2.CreateMedicationRequestList, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[_boarding_add_on_detail_service_pb2.UpdateBoardingAddOnDetailRequest, _Mapping]]] = ..., waitlist: _Optional[_Union[UpdateBoardingServiceWaitlistRequest, _Mapping]] = ...) -> None: ...
    class DaycareService(_message.Message):
        __slots__ = ("service", "feedings_upsert", "medications_upsert", "addons", "waitlist")
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        FEEDINGS_UPSERT_FIELD_NUMBER: _ClassVar[int]
        MEDICATIONS_UPSERT_FIELD_NUMBER: _ClassVar[int]
        ADDONS_FIELD_NUMBER: _ClassVar[int]
        WAITLIST_FIELD_NUMBER: _ClassVar[int]
        service: _daycare_service_detail_service_pb2.UpdateDaycareServiceDetailRequest
        feedings_upsert: _feeding_service_pb2.CreateFeedingRequestList
        medications_upsert: _medication_service_pb2.CreateMedicationRequestList
        addons: _containers.RepeatedCompositeFieldContainer[_daycare_add_on_detail_service_pb2.UpdateDaycareAddOnDetailRequest]
        waitlist: UpdateDaycareServiceWaitlistRequest
        def __init__(self, service: _Optional[_Union[_daycare_service_detail_service_pb2.UpdateDaycareServiceDetailRequest, _Mapping]] = ..., feedings_upsert: _Optional[_Union[_feeding_service_pb2.CreateFeedingRequestList, _Mapping]] = ..., medications_upsert: _Optional[_Union[_medication_service_pb2.CreateMedicationRequestList, _Mapping]] = ..., addons: _Optional[_Iterable[_Union[_daycare_add_on_detail_service_pb2.UpdateDaycareAddOnDetailRequest, _Mapping]]] = ..., waitlist: _Optional[_Union[UpdateDaycareServiceWaitlistRequest, _Mapping]] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    ATTR_FIELD_NUMBER: _ClassVar[int]
    PAYMENT_STATUS_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    id: int
    appointment_id: int
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    status: _booking_request_enums_pb2.BookingRequestStatus
    services: _containers.RepeatedCompositeFieldContainer[UpdateBookingRequestRequest.Service]
    attr: _booking_request_models_pb2.BookingRequestModel.Attr
    payment_status: _booking_request_models_pb2.BookingRequestModel.PaymentStatus
    comment: str
    def __init__(self, id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., status: _Optional[_Union[_booking_request_enums_pb2.BookingRequestStatus, str]] = ..., services: _Optional[_Iterable[_Union[UpdateBookingRequestRequest.Service, _Mapping]]] = ..., attr: _Optional[_Union[_booking_request_models_pb2.BookingRequestModel.Attr, _Mapping]] = ..., payment_status: _Optional[_Union[_booking_request_models_pb2.BookingRequestModel.PaymentStatus, str]] = ..., comment: _Optional[str] = ...) -> None: ...

class UpdateBookingRequestResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ReplaceBookingRequestRequest(_message.Message):
    __slots__ = ("id", "start_date", "start_time", "end_date", "end_time", "status", "services", "comment")
    class Service(_message.Message):
        __slots__ = ("boarding", "daycare")
        BOARDING_FIELD_NUMBER: _ClassVar[int]
        DAYCARE_FIELD_NUMBER: _ClassVar[int]
        boarding: ReplaceBookingRequestRequest.BoardingService
        daycare: ReplaceBookingRequestRequest.DaycareService
        def __init__(self, boarding: _Optional[_Union[ReplaceBookingRequestRequest.BoardingService, _Mapping]] = ..., daycare: _Optional[_Union[ReplaceBookingRequestRequest.DaycareService, _Mapping]] = ...) -> None: ...
    class BoardingService(_message.Message):
        __slots__ = ("service", "waitlist")
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        WAITLIST_FIELD_NUMBER: _ClassVar[int]
        service: _boarding_service_detail_service_pb2.CreateBoardingServiceDetailRequest
        waitlist: CreateBoardingServiceWaitlistRequest
        def __init__(self, service: _Optional[_Union[_boarding_service_detail_service_pb2.CreateBoardingServiceDetailRequest, _Mapping]] = ..., waitlist: _Optional[_Union[CreateBoardingServiceWaitlistRequest, _Mapping]] = ...) -> None: ...
    class DaycareService(_message.Message):
        __slots__ = ("service", "waitlist")
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        WAITLIST_FIELD_NUMBER: _ClassVar[int]
        service: _daycare_service_detail_service_pb2.CreateDaycareServiceDetailRequest
        waitlist: CreateDaycareServiceWaitlistRequest
        def __init__(self, service: _Optional[_Union[_daycare_service_detail_service_pb2.CreateDaycareServiceDetailRequest, _Mapping]] = ..., waitlist: _Optional[_Union[CreateDaycareServiceWaitlistRequest, _Mapping]] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    COMMENT_FIELD_NUMBER: _ClassVar[int]
    id: int
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    status: _booking_request_enums_pb2.BookingRequestStatus
    services: _containers.RepeatedCompositeFieldContainer[ReplaceBookingRequestRequest.Service]
    comment: str
    def __init__(self, id: _Optional[int] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., status: _Optional[_Union[_booking_request_enums_pb2.BookingRequestStatus, str]] = ..., services: _Optional[_Iterable[_Union[ReplaceBookingRequestRequest.Service, _Mapping]]] = ..., comment: _Optional[str] = ...) -> None: ...

class ReplaceBookingRequestResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetAutoAssignRequest(_message.Message):
    __slots__ = ("id", "company_id", "business_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    company_id: int
    business_id: int
    def __init__(self, id: _Optional[int] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class GetAutoAssignResponse(_message.Message):
    __slots__ = ("boarding_assign_requires", "pet_to_lodgings", "lodgings", "evaluation_assign_requires", "evaluation_pet_to_staffs", "daycare_assign_requires")
    class AssignRequire(_message.Message):
        __slots__ = ("pet_id", "service_id", "start_date", "end_date", "specific_dates")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        END_DATE_FIELD_NUMBER: _ClassVar[int]
        SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        service_id: int
        start_date: str
        end_date: str
        specific_dates: _containers.RepeatedScalarFieldContainer[str]
        def __init__(self, pet_id: _Optional[int] = ..., service_id: _Optional[int] = ..., start_date: _Optional[str] = ..., end_date: _Optional[str] = ..., specific_dates: _Optional[_Iterable[str]] = ...) -> None: ...
    class LodgingDetail(_message.Message):
        __slots__ = ("lodging_id", "lodging_unit_name", "lodging_type_name")
        LODGING_ID_FIELD_NUMBER: _ClassVar[int]
        LODGING_UNIT_NAME_FIELD_NUMBER: _ClassVar[int]
        LODGING_TYPE_NAME_FIELD_NUMBER: _ClassVar[int]
        lodging_id: int
        lodging_unit_name: str
        lodging_type_name: str
        def __init__(self, lodging_id: _Optional[int] = ..., lodging_unit_name: _Optional[str] = ..., lodging_type_name: _Optional[str] = ...) -> None: ...
    BOARDING_ASSIGN_REQUIRES_FIELD_NUMBER: _ClassVar[int]
    PET_TO_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    LODGINGS_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_ASSIGN_REQUIRES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_PET_TO_STAFFS_FIELD_NUMBER: _ClassVar[int]
    DAYCARE_ASSIGN_REQUIRES_FIELD_NUMBER: _ClassVar[int]
    boarding_assign_requires: _containers.RepeatedCompositeFieldContainer[GetAutoAssignResponse.AssignRequire]
    pet_to_lodgings: _containers.RepeatedCompositeFieldContainer[_booking_request_defs_pb2.PetToLodgingDef]
    lodgings: _containers.RepeatedCompositeFieldContainer[GetAutoAssignResponse.LodgingDetail]
    evaluation_assign_requires: _containers.RepeatedCompositeFieldContainer[GetAutoAssignResponse.AssignRequire]
    evaluation_pet_to_staffs: _containers.RepeatedCompositeFieldContainer[_booking_request_defs_pb2.PetToStaffDef]
    daycare_assign_requires: _containers.RepeatedCompositeFieldContainer[GetAutoAssignResponse.AssignRequire]
    def __init__(self, boarding_assign_requires: _Optional[_Iterable[_Union[GetAutoAssignResponse.AssignRequire, _Mapping]]] = ..., pet_to_lodgings: _Optional[_Iterable[_Union[_booking_request_defs_pb2.PetToLodgingDef, _Mapping]]] = ..., lodgings: _Optional[_Iterable[_Union[GetAutoAssignResponse.LodgingDetail, _Mapping]]] = ..., evaluation_assign_requires: _Optional[_Iterable[_Union[GetAutoAssignResponse.AssignRequire, _Mapping]]] = ..., evaluation_pet_to_staffs: _Optional[_Iterable[_Union[_booking_request_defs_pb2.PetToStaffDef, _Mapping]]] = ..., daycare_assign_requires: _Optional[_Iterable[_Union[GetAutoAssignResponse.AssignRequire, _Mapping]]] = ...) -> None: ...

class AcceptBookingRequestRequest(_message.Message):
    __slots__ = ("id", "pet_to_lodgings", "pet_to_staffs", "company_id", "business_id", "staff_id", "pet_to_services", "evaluation_pet_to_staffs")
    ID_FIELD_NUMBER: _ClassVar[int]
    PET_TO_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    PET_TO_STAFFS_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    PET_TO_SERVICES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_PET_TO_STAFFS_FIELD_NUMBER: _ClassVar[int]
    id: int
    pet_to_lodgings: _containers.RepeatedCompositeFieldContainer[_booking_request_defs_pb2.PetToLodgingDef]
    pet_to_staffs: _containers.RepeatedCompositeFieldContainer[_booking_request_defs_pb2.PetToStaffDef]
    company_id: int
    business_id: int
    staff_id: int
    pet_to_services: _containers.RepeatedCompositeFieldContainer[_booking_request_defs_pb2.PetToServiceDef]
    evaluation_pet_to_staffs: _containers.RepeatedCompositeFieldContainer[_booking_request_defs_pb2.PetToStaffDef]
    def __init__(self, id: _Optional[int] = ..., pet_to_lodgings: _Optional[_Iterable[_Union[_booking_request_defs_pb2.PetToLodgingDef, _Mapping]]] = ..., pet_to_staffs: _Optional[_Iterable[_Union[_booking_request_defs_pb2.PetToStaffDef, _Mapping]]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., pet_to_services: _Optional[_Iterable[_Union[_booking_request_defs_pb2.PetToServiceDef, _Mapping]]] = ..., evaluation_pet_to_staffs: _Optional[_Iterable[_Union[_booking_request_defs_pb2.PetToStaffDef, _Mapping]]] = ...) -> None: ...

class AcceptBookingRequestResponse(_message.Message):
    __slots__ = ("result", "appointment_id")
    RESULT_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    result: bool
    appointment_id: int
    def __init__(self, result: bool = ..., appointment_id: _Optional[int] = ...) -> None: ...

class DeclineBookingRequestRequest(_message.Message):
    __slots__ = ("id", "company_id", "business_id", "staff_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    company_id: int
    business_id: int
    staff_id: int
    def __init__(self, id: _Optional[int] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., staff_id: _Optional[int] = ...) -> None: ...

class DeclineBookingRequestResponse(_message.Message):
    __slots__ = ("result", "appointment_id")
    RESULT_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    result: bool
    appointment_id: int
    def __init__(self, result: bool = ..., appointment_id: _Optional[int] = ...) -> None: ...

class CountBookingRequestsRequest(_message.Message):
    __slots__ = ("tenant", "filters")
    class Filters(_message.Message):
        __slots__ = ("created_time_range", "statuses", "payment_statuses")
        CREATED_TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
        STATUSES_FIELD_NUMBER: _ClassVar[int]
        PAYMENT_STATUSES_FIELD_NUMBER: _ClassVar[int]
        created_time_range: _interval_pb2.Interval
        statuses: _containers.RepeatedScalarFieldContainer[_booking_request_enums_pb2.BookingRequestStatus]
        payment_statuses: _containers.RepeatedScalarFieldContainer[_booking_request_models_pb2.BookingRequestModel.PaymentStatus]
        def __init__(self, created_time_range: _Optional[_Union[_interval_pb2.Interval, _Mapping]] = ..., statuses: _Optional[_Iterable[_Union[_booking_request_enums_pb2.BookingRequestStatus, str]]] = ..., payment_statuses: _Optional[_Iterable[_Union[_booking_request_models_pb2.BookingRequestModel.PaymentStatus, str]]] = ...) -> None: ...
    TENANT_FIELD_NUMBER: _ClassVar[int]
    FILTERS_FIELD_NUMBER: _ClassVar[int]
    tenant: _tenant_pb2.Tenant
    filters: CountBookingRequestsRequest.Filters
    def __init__(self, tenant: _Optional[_Union[_tenant_pb2.Tenant, _Mapping]] = ..., filters: _Optional[_Union[CountBookingRequestsRequest.Filters, _Mapping]] = ...) -> None: ...

class CountBookingRequestsResponse(_message.Message):
    __slots__ = ("count",)
    COUNT_FIELD_NUMBER: _ClassVar[int]
    count: int
    def __init__(self, count: _Optional[int] = ...) -> None: ...

class ListBookingRequestIdRequest(_message.Message):
    __slots__ = ("appointment_ids",)
    APPOINTMENT_IDS_FIELD_NUMBER: _ClassVar[int]
    appointment_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, appointment_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class ListBookingRequestIdResponse(_message.Message):
    __slots__ = ("appointment_id_to_booking_request_id",)
    class AppointmentIdToBookingRequestIdEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: int
        def __init__(self, key: _Optional[int] = ..., value: _Optional[int] = ...) -> None: ...
    APPOINTMENT_ID_TO_BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id_to_booking_request_id: _containers.ScalarMap[int, int]
    def __init__(self, appointment_id_to_booking_request_id: _Optional[_Mapping[int, int]] = ...) -> None: ...

class SyncBookingRequestFromAppointmentRequest(_message.Message):
    __slots__ = ("appointment_id",)
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    appointment_id: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, appointment_id: _Optional[_Iterable[int]] = ...) -> None: ...

class SyncBookingRequestFromAppointmentResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class TriggerBookingRequestAutoAcceptedRequest(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class TriggerBookingRequestAutoAcceptedResponse(_message.Message):
    __slots__ = ("is_auto_accepted",)
    IS_AUTO_ACCEPTED_FIELD_NUMBER: _ClassVar[int]
    is_auto_accepted: bool
    def __init__(self, is_auto_accepted: bool = ...) -> None: ...

class AcceptBookingRequestV2Request(_message.Message):
    __slots__ = ("company_id", "id", "staff_id", "grooming_services", "boarding_services", "daycare_services", "evaluation_services", "grooming_addons", "boarding_addons", "daycare_addons", "create_evaluation_requests")
    class GroomingService(_message.Message):
        __slots__ = ("id", "staff_id", "start_time")
        ID_FIELD_NUMBER: _ClassVar[int]
        STAFF_ID_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        id: int
        staff_id: int
        start_time: int
        def __init__(self, id: _Optional[int] = ..., staff_id: _Optional[int] = ..., start_time: _Optional[int] = ...) -> None: ...
    class BoardingService(_message.Message):
        __slots__ = ("id", "lodging_id")
        ID_FIELD_NUMBER: _ClassVar[int]
        LODGING_ID_FIELD_NUMBER: _ClassVar[int]
        id: int
        lodging_id: int
        def __init__(self, id: _Optional[int] = ..., lodging_id: _Optional[int] = ...) -> None: ...
    class DaycareService(_message.Message):
        __slots__ = ("id", "lodging_id")
        ID_FIELD_NUMBER: _ClassVar[int]
        LODGING_ID_FIELD_NUMBER: _ClassVar[int]
        id: int
        lodging_id: int
        def __init__(self, id: _Optional[int] = ..., lodging_id: _Optional[int] = ...) -> None: ...
    class EvaluationService(_message.Message):
        __slots__ = ("id", "staff_id", "evaluation_id")
        ID_FIELD_NUMBER: _ClassVar[int]
        STAFF_ID_FIELD_NUMBER: _ClassVar[int]
        EVALUATION_ID_FIELD_NUMBER: _ClassVar[int]
        id: int
        staff_id: int
        evaluation_id: int
        def __init__(self, id: _Optional[int] = ..., staff_id: _Optional[int] = ..., evaluation_id: _Optional[int] = ...) -> None: ...
    class GroomingAddon(_message.Message):
        __slots__ = ("id", "staff_id", "start_time")
        ID_FIELD_NUMBER: _ClassVar[int]
        STAFF_ID_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        id: int
        staff_id: int
        start_time: int
        def __init__(self, id: _Optional[int] = ..., staff_id: _Optional[int] = ..., start_time: _Optional[int] = ...) -> None: ...
    class BoardingAddon(_message.Message):
        __slots__ = ("id", "staff_id", "start_time")
        ID_FIELD_NUMBER: _ClassVar[int]
        STAFF_ID_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        id: int
        staff_id: int
        start_time: int
        def __init__(self, id: _Optional[int] = ..., staff_id: _Optional[int] = ..., start_time: _Optional[int] = ...) -> None: ...
    class DaycareAddon(_message.Message):
        __slots__ = ("id", "staff_id", "start_time")
        ID_FIELD_NUMBER: _ClassVar[int]
        STAFF_ID_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        id: int
        staff_id: int
        start_time: int
        def __init__(self, id: _Optional[int] = ..., staff_id: _Optional[int] = ..., start_time: _Optional[int] = ...) -> None: ...
    class CreateEvaluationRequest(_message.Message):
        __slots__ = ("evaluation_id", "pet_id", "start_date", "start_time", "staff_id", "lodging_id")
        EVALUATION_ID_FIELD_NUMBER: _ClassVar[int]
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        START_DATE_FIELD_NUMBER: _ClassVar[int]
        START_TIME_FIELD_NUMBER: _ClassVar[int]
        STAFF_ID_FIELD_NUMBER: _ClassVar[int]
        LODGING_ID_FIELD_NUMBER: _ClassVar[int]
        evaluation_id: int
        pet_id: int
        start_date: _date_pb2.Date
        start_time: int
        staff_id: int
        lodging_id: int
        def __init__(self, evaluation_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., start_time: _Optional[int] = ..., staff_id: _Optional[int] = ..., lodging_id: _Optional[int] = ...) -> None: ...
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    GROOMING_SERVICES_FIELD_NUMBER: _ClassVar[int]
    BOARDING_SERVICES_FIELD_NUMBER: _ClassVar[int]
    DAYCARE_SERVICES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_SERVICES_FIELD_NUMBER: _ClassVar[int]
    GROOMING_ADDONS_FIELD_NUMBER: _ClassVar[int]
    BOARDING_ADDONS_FIELD_NUMBER: _ClassVar[int]
    DAYCARE_ADDONS_FIELD_NUMBER: _ClassVar[int]
    CREATE_EVALUATION_REQUESTS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    id: int
    staff_id: int
    grooming_services: _containers.RepeatedCompositeFieldContainer[AcceptBookingRequestV2Request.GroomingService]
    boarding_services: _containers.RepeatedCompositeFieldContainer[AcceptBookingRequestV2Request.BoardingService]
    daycare_services: _containers.RepeatedCompositeFieldContainer[AcceptBookingRequestV2Request.DaycareService]
    evaluation_services: _containers.RepeatedCompositeFieldContainer[AcceptBookingRequestV2Request.EvaluationService]
    grooming_addons: _containers.RepeatedCompositeFieldContainer[AcceptBookingRequestV2Request.GroomingAddon]
    boarding_addons: _containers.RepeatedCompositeFieldContainer[AcceptBookingRequestV2Request.BoardingAddon]
    daycare_addons: _containers.RepeatedCompositeFieldContainer[AcceptBookingRequestV2Request.DaycareAddon]
    create_evaluation_requests: _containers.RepeatedCompositeFieldContainer[AcceptBookingRequestV2Request.CreateEvaluationRequest]
    def __init__(self, company_id: _Optional[int] = ..., id: _Optional[int] = ..., staff_id: _Optional[int] = ..., grooming_services: _Optional[_Iterable[_Union[AcceptBookingRequestV2Request.GroomingService, _Mapping]]] = ..., boarding_services: _Optional[_Iterable[_Union[AcceptBookingRequestV2Request.BoardingService, _Mapping]]] = ..., daycare_services: _Optional[_Iterable[_Union[AcceptBookingRequestV2Request.DaycareService, _Mapping]]] = ..., evaluation_services: _Optional[_Iterable[_Union[AcceptBookingRequestV2Request.EvaluationService, _Mapping]]] = ..., grooming_addons: _Optional[_Iterable[_Union[AcceptBookingRequestV2Request.GroomingAddon, _Mapping]]] = ..., boarding_addons: _Optional[_Iterable[_Union[AcceptBookingRequestV2Request.BoardingAddon, _Mapping]]] = ..., daycare_addons: _Optional[_Iterable[_Union[AcceptBookingRequestV2Request.DaycareAddon, _Mapping]]] = ..., create_evaluation_requests: _Optional[_Iterable[_Union[AcceptBookingRequestV2Request.CreateEvaluationRequest, _Mapping]]] = ...) -> None: ...

class AcceptBookingRequestV2Response(_message.Message):
    __slots__ = ("result", "appointment_id")
    RESULT_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    result: bool
    appointment_id: int
    def __init__(self, result: bool = ..., appointment_id: _Optional[int] = ...) -> None: ...

class AutoAssignRequest(_message.Message):
    __slots__ = ("booking_request_id", "company_id")
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    booking_request_id: int
    company_id: int
    def __init__(self, booking_request_id: _Optional[int] = ..., company_id: _Optional[int] = ...) -> None: ...

class AutoAssignResponse(_message.Message):
    __slots__ = ("boarding_services", "evaluation_services", "daycare_services")
    class BoardingService(_message.Message):
        __slots__ = ("id", "lodging_id", "missing_evaluation")
        ID_FIELD_NUMBER: _ClassVar[int]
        LODGING_ID_FIELD_NUMBER: _ClassVar[int]
        MISSING_EVALUATION_FIELD_NUMBER: _ClassVar[int]
        id: int
        lodging_id: int
        missing_evaluation: _evaluation_models_pb2.EvaluationBriefView
        def __init__(self, id: _Optional[int] = ..., lodging_id: _Optional[int] = ..., missing_evaluation: _Optional[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]] = ...) -> None: ...
    class EvaluationService(_message.Message):
        __slots__ = ("id", "staff_id", "selected_evaluation", "all_evaluations")
        ID_FIELD_NUMBER: _ClassVar[int]
        STAFF_ID_FIELD_NUMBER: _ClassVar[int]
        SELECTED_EVALUATION_FIELD_NUMBER: _ClassVar[int]
        ALL_EVALUATIONS_FIELD_NUMBER: _ClassVar[int]
        id: int
        staff_id: int
        selected_evaluation: _evaluation_models_pb2.EvaluationBriefView
        all_evaluations: _containers.RepeatedCompositeFieldContainer[_evaluation_models_pb2.EvaluationBriefView]
        def __init__(self, id: _Optional[int] = ..., staff_id: _Optional[int] = ..., selected_evaluation: _Optional[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]] = ..., all_evaluations: _Optional[_Iterable[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]]] = ...) -> None: ...
    class DaycareService(_message.Message):
        __slots__ = ("id", "missing_evaluation")
        ID_FIELD_NUMBER: _ClassVar[int]
        MISSING_EVALUATION_FIELD_NUMBER: _ClassVar[int]
        id: int
        missing_evaluation: _evaluation_models_pb2.EvaluationBriefView
        def __init__(self, id: _Optional[int] = ..., missing_evaluation: _Optional[_Union[_evaluation_models_pb2.EvaluationBriefView, _Mapping]] = ...) -> None: ...
    BOARDING_SERVICES_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_SERVICES_FIELD_NUMBER: _ClassVar[int]
    DAYCARE_SERVICES_FIELD_NUMBER: _ClassVar[int]
    boarding_services: _containers.RepeatedCompositeFieldContainer[AutoAssignResponse.BoardingService]
    evaluation_services: _containers.RepeatedCompositeFieldContainer[AutoAssignResponse.EvaluationService]
    daycare_services: _containers.RepeatedCompositeFieldContainer[AutoAssignResponse.DaycareService]
    def __init__(self, boarding_services: _Optional[_Iterable[_Union[AutoAssignResponse.BoardingService, _Mapping]]] = ..., evaluation_services: _Optional[_Iterable[_Union[AutoAssignResponse.EvaluationService, _Mapping]]] = ..., daycare_services: _Optional[_Iterable[_Union[AutoAssignResponse.DaycareService, _Mapping]]] = ...) -> None: ...

class CreateBoardingServiceWaitlistRequest(_message.Message):
    __slots__ = ("start_date", "end_date")
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    def __init__(self, start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ...) -> None: ...

class CreateDaycareServiceWaitlistRequest(_message.Message):
    __slots__ = ("specific_dates",)
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    specific_dates: _containers.RepeatedCompositeFieldContainer[_date_pb2.Date]
    def __init__(self, specific_dates: _Optional[_Iterable[_Union[_date_pb2.Date, _Mapping]]] = ...) -> None: ...

class UpdateBoardingServiceWaitlistRequest(_message.Message):
    __slots__ = ("start_date", "end_date")
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    start_date: _date_pb2.Date
    end_date: _date_pb2.Date
    def __init__(self, start_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., end_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ...) -> None: ...

class UpdateDaycareServiceWaitlistRequest(_message.Message):
    __slots__ = ("specific_dates",)
    SPECIFIC_DATES_FIELD_NUMBER: _ClassVar[int]
    specific_dates: _list_pb2.DateList
    def __init__(self, specific_dates: _Optional[_Union[_list_pb2.DateList, _Mapping]] = ...) -> None: ...

class CheckWaitlistAvailableTaskRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class CheckWaitlistAvailableTaskResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class MoveBookingRequestToWaitlistRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "booking_request_id")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    booking_request_id: int
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., booking_request_id: _Optional[int] = ...) -> None: ...

class MoveBookingRequestToWaitlistResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class PreviewBookingRequestPricingRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "customer_id", "pet_services")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    PET_SERVICES_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    customer_id: int
    pet_services: _containers.RepeatedCompositeFieldContainer[_booking_request_defs_pb2.PetServiceDetails]
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., customer_id: _Optional[int] = ..., pet_services: _Optional[_Iterable[_Union[_booking_request_defs_pb2.PetServiceDetails, _Mapping]]] = ...) -> None: ...

class PreviewBookingRequestPricingResponse(_message.Message):
    __slots__ = ("line_items",)
    class LineItem(_message.Message):
        __slots__ = ("pet_id", "service", "unit_price", "quantity", "total_price")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        SERVICE_FIELD_NUMBER: _ClassVar[int]
        UNIT_PRICE_FIELD_NUMBER: _ClassVar[int]
        QUANTITY_FIELD_NUMBER: _ClassVar[int]
        TOTAL_PRICE_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        service: _service_models_pb2.CustomizedServiceView
        unit_price: _money_pb2.Money
        quantity: int
        total_price: _money_pb2.Money
        def __init__(self, pet_id: _Optional[int] = ..., service: _Optional[_Union[_service_models_pb2.CustomizedServiceView, _Mapping]] = ..., unit_price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., quantity: _Optional[int] = ..., total_price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ...) -> None: ...
    LINE_ITEMS_FIELD_NUMBER: _ClassVar[int]
    line_items: _containers.RepeatedCompositeFieldContainer[PreviewBookingRequestPricingResponse.LineItem]
    def __init__(self, line_items: _Optional[_Iterable[_Union[PreviewBookingRequestPricingResponse.LineItem, _Mapping]]] = ...) -> None: ...

class CountBookingRequestByFilterRequest(_message.Message):
    __slots__ = ("company_id", "evaluation_ids")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    EVALUATION_IDS_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    evaluation_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, company_id: _Optional[int] = ..., evaluation_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class CountBookingRequestByFilterResponse(_message.Message):
    __slots__ = ("evaluation_in_use",)
    class EvaluationInUseEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: int
        def __init__(self, key: _Optional[int] = ..., value: _Optional[int] = ...) -> None: ...
    EVALUATION_IN_USE_FIELD_NUMBER: _ClassVar[int]
    evaluation_in_use: _containers.ScalarMap[int, int]
    def __init__(self, evaluation_in_use: _Optional[_Mapping[int, int]] = ...) -> None: ...
