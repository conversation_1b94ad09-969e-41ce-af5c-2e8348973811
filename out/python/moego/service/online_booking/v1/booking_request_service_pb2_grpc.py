# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from moego.service.online_booking.v1 import booking_request_service_pb2 as moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2


class BookingRequestServiceStub(object):
    """the booking_request service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateBookingRequest = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/CreateBookingRequest',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CreateBookingRequestRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_wrappers__pb2.Int64Value.FromString,
                _registered_method=True)
        self.GetBookingRequest = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/GetBookingRequest',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetBookingRequestRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetBookingRequestResponse.FromString,
                _registered_method=True)
        self.ListBookingRequests = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/ListBookingRequests',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestsResponse.FromString,
                _registered_method=True)
        self.ListWaitlists = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/ListWaitlists',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListWaitlistsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListWaitlistsResponse.FromString,
                _registered_method=True)
        self.CreateGroomingOnly = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/CreateGroomingOnly',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CreateGroomingOnlyRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CreateGroomingOnlyResponse.FromString,
                _registered_method=True)
        self.UpdateBookingRequestStatus = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/UpdateBookingRequestStatus',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestStatusRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestStatusResponse.FromString,
                _registered_method=True)
        self.UpdateBookingRequest = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/UpdateBookingRequest',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestResponse.FromString,
                _registered_method=True)
        self.ReplaceBookingRequest = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/ReplaceBookingRequest',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ReplaceBookingRequestRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ReplaceBookingRequestResponse.FromString,
                _registered_method=True)
        self.RetryFailedEvents = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/RetryFailedEvents',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.RetryFailedEventsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.RetryFailedEventsResponse.FromString,
                _registered_method=True)
        self.GetAutoAssign = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/GetAutoAssign',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetAutoAssignRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetAutoAssignResponse.FromString,
                _registered_method=True)
        self.AutoAssign = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/AutoAssign',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AutoAssignRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AutoAssignResponse.FromString,
                _registered_method=True)
        self.AcceptBookingRequest = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/AcceptBookingRequest',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestResponse.FromString,
                _registered_method=True)
        self.AcceptBookingRequestV2 = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/AcceptBookingRequestV2',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestV2Request.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestV2Response.FromString,
                _registered_method=True)
        self.DeclineBookingRequest = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/DeclineBookingRequest',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.DeclineBookingRequestRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.DeclineBookingRequestResponse.FromString,
                _registered_method=True)
        self.CountBookingRequests = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/CountBookingRequests',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestsResponse.FromString,
                _registered_method=True)
        self.ListBookingRequestId = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/ListBookingRequestId',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestIdRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestIdResponse.FromString,
                _registered_method=True)
        self.SyncBookingRequestFromAppointment = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/SyncBookingRequestFromAppointment',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.SyncBookingRequestFromAppointmentRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.SyncBookingRequestFromAppointmentResponse.FromString,
                _registered_method=True)
        self.TriggerBookingRequestAutoAccepted = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/TriggerBookingRequestAutoAccepted',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.TriggerBookingRequestAutoAcceptedRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.TriggerBookingRequestAutoAcceptedResponse.FromString,
                _registered_method=True)
        self.CheckWaitlistAvailableTask = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/CheckWaitlistAvailableTask',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CheckWaitlistAvailableTaskRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CheckWaitlistAvailableTaskResponse.FromString,
                _registered_method=True)
        self.MoveBookingRequestToWaitlist = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/MoveBookingRequestToWaitlist',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.MoveBookingRequestToWaitlistRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.MoveBookingRequestToWaitlistResponse.FromString,
                _registered_method=True)
        self.CountBookingRequestByFilter = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/CountBookingRequestByFilter',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestByFilterRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestByFilterResponse.FromString,
                _registered_method=True)
        self.PreviewBookingRequestPricing = channel.unary_unary(
                '/moego.service.online_booking.v1.BookingRequestService/PreviewBookingRequestPricing',
                request_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.PreviewBookingRequestPricingRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.PreviewBookingRequestPricingResponse.FromString,
                _registered_method=True)


class BookingRequestServiceServicer(object):
    """the booking_request service
    """

    def CreateBookingRequest(self, request, context):
        """Create a record, return inserted id.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBookingRequest(self, request, context):
        """Get a record by id, not include deleted record.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListBookingRequests(self, request, context):
        """List booking requests<br>
        Supports filtering, sorting, paging, and returning data by associated model array<br>
        filters: business_id, customer_id, status, start_date, end_date, created_at<br>
        sorts: id, start_date, created_at<br>
        associated model array: services, add-on, feeding, medication
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListWaitlists(self, request, context):
        """list waitlists
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateGroomingOnly(self, request, context):
        """Create a grooming only booking request
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateBookingRequestStatus(self, request, context):
        """Update booking request status
        submitted -> wait_list / scheduled / declined / payment_failed
        wait_list -> scheduled / deleted
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateBookingRequest(self, request, context):
        """Update booking request
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReplaceBookingRequest(self, request, context):
        """replace booking reqeust
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RetryFailedEvents(self, request, context):
        """Retry failed events
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAutoAssign(self, request, context):
        """Get auto assign room
        deprecated by Freeman since 2025/3/6, use AutoAssign instead
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AutoAssign(self, request, context):
        """Auto assign
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AcceptBookingRequest(self, request, context):
        """Accept booking request
        deprecated by Freeman since 2025/3/5, 这个接口的参数设计和实现都很难看懂，难以扩展，use AcceptBookingRequestV2 instead
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AcceptBookingRequestV2(self, request, context):
        """Accept booking request v2
        没放在 v2 包下面的原因是这个接口会大量复用 AcceptBookingRequest 的逻辑，需要在一个类里面
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeclineBookingRequest(self, request, context):
        """Decline booking request
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CountBookingRequests(self, request, context):
        """Count booking requests
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListBookingRequestId(self, request, context):
        """List booking request ids by appointment id
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SyncBookingRequestFromAppointment(self, request, context):
        """Sync BookingRequest from appointment
        这个接口用于同步老的 BookingRequest 数据（保存在 moe_grooming_appointment 表）到新的 booking_request 表
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TriggerBookingRequestAutoAccepted(self, request, context):
        """online booking 的 auto accept 逻辑，这个接口会处理 accept BookingRequest，capture PaymentIntent，send notification 等逻辑。
        这个接口的调用场景有两个：
        1. 在不需要 payment 的 submit BookingRequest 场景里，在 client-api-v1 直接调用。
        2. 在需要 payment 的 submit BookingRequest 场景里，在 confirm PaymentIntent 的回调里调用。
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckWaitlistAvailableTask(self, request, context):
        """waitlist available check task
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MoveBookingRequestToWaitlist(self, request, context):
        """Move booking request to waitlist
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CountBookingRequestByFilter(self, request, context):
        """check service id is use
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PreviewBookingRequestPricing(self, request, context):
        """Preview pricing of booking request
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BookingRequestServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateBookingRequest': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateBookingRequest,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CreateBookingRequestRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_wrappers__pb2.Int64Value.SerializeToString,
            ),
            'GetBookingRequest': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBookingRequest,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetBookingRequestRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetBookingRequestResponse.SerializeToString,
            ),
            'ListBookingRequests': grpc.unary_unary_rpc_method_handler(
                    servicer.ListBookingRequests,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestsRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestsResponse.SerializeToString,
            ),
            'ListWaitlists': grpc.unary_unary_rpc_method_handler(
                    servicer.ListWaitlists,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListWaitlistsRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListWaitlistsResponse.SerializeToString,
            ),
            'CreateGroomingOnly': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateGroomingOnly,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CreateGroomingOnlyRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CreateGroomingOnlyResponse.SerializeToString,
            ),
            'UpdateBookingRequestStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateBookingRequestStatus,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestStatusRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestStatusResponse.SerializeToString,
            ),
            'UpdateBookingRequest': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateBookingRequest,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestResponse.SerializeToString,
            ),
            'ReplaceBookingRequest': grpc.unary_unary_rpc_method_handler(
                    servicer.ReplaceBookingRequest,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ReplaceBookingRequestRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ReplaceBookingRequestResponse.SerializeToString,
            ),
            'RetryFailedEvents': grpc.unary_unary_rpc_method_handler(
                    servicer.RetryFailedEvents,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.RetryFailedEventsRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.RetryFailedEventsResponse.SerializeToString,
            ),
            'GetAutoAssign': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAutoAssign,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetAutoAssignRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetAutoAssignResponse.SerializeToString,
            ),
            'AutoAssign': grpc.unary_unary_rpc_method_handler(
                    servicer.AutoAssign,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AutoAssignRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AutoAssignResponse.SerializeToString,
            ),
            'AcceptBookingRequest': grpc.unary_unary_rpc_method_handler(
                    servicer.AcceptBookingRequest,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestResponse.SerializeToString,
            ),
            'AcceptBookingRequestV2': grpc.unary_unary_rpc_method_handler(
                    servicer.AcceptBookingRequestV2,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestV2Request.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestV2Response.SerializeToString,
            ),
            'DeclineBookingRequest': grpc.unary_unary_rpc_method_handler(
                    servicer.DeclineBookingRequest,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.DeclineBookingRequestRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.DeclineBookingRequestResponse.SerializeToString,
            ),
            'CountBookingRequests': grpc.unary_unary_rpc_method_handler(
                    servicer.CountBookingRequests,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestsRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestsResponse.SerializeToString,
            ),
            'ListBookingRequestId': grpc.unary_unary_rpc_method_handler(
                    servicer.ListBookingRequestId,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestIdRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestIdResponse.SerializeToString,
            ),
            'SyncBookingRequestFromAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.SyncBookingRequestFromAppointment,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.SyncBookingRequestFromAppointmentRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.SyncBookingRequestFromAppointmentResponse.SerializeToString,
            ),
            'TriggerBookingRequestAutoAccepted': grpc.unary_unary_rpc_method_handler(
                    servicer.TriggerBookingRequestAutoAccepted,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.TriggerBookingRequestAutoAcceptedRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.TriggerBookingRequestAutoAcceptedResponse.SerializeToString,
            ),
            'CheckWaitlistAvailableTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckWaitlistAvailableTask,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CheckWaitlistAvailableTaskRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CheckWaitlistAvailableTaskResponse.SerializeToString,
            ),
            'MoveBookingRequestToWaitlist': grpc.unary_unary_rpc_method_handler(
                    servicer.MoveBookingRequestToWaitlist,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.MoveBookingRequestToWaitlistRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.MoveBookingRequestToWaitlistResponse.SerializeToString,
            ),
            'CountBookingRequestByFilter': grpc.unary_unary_rpc_method_handler(
                    servicer.CountBookingRequestByFilter,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestByFilterRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestByFilterResponse.SerializeToString,
            ),
            'PreviewBookingRequestPricing': grpc.unary_unary_rpc_method_handler(
                    servicer.PreviewBookingRequestPricing,
                    request_deserializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.PreviewBookingRequestPricingRequest.FromString,
                    response_serializer=moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.PreviewBookingRequestPricingResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.online_booking.v1.BookingRequestService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.online_booking.v1.BookingRequestService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class BookingRequestService(object):
    """the booking_request service
    """

    @staticmethod
    def CreateBookingRequest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/CreateBookingRequest',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CreateBookingRequestRequest.SerializeToString,
            google_dot_protobuf_dot_wrappers__pb2.Int64Value.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetBookingRequest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/GetBookingRequest',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetBookingRequestRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetBookingRequestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListBookingRequests(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/ListBookingRequests',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestsRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListWaitlists(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/ListWaitlists',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListWaitlistsRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListWaitlistsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateGroomingOnly(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/CreateGroomingOnly',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CreateGroomingOnlyRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CreateGroomingOnlyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateBookingRequestStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/UpdateBookingRequestStatus',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestStatusRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateBookingRequest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/UpdateBookingRequest',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.UpdateBookingRequestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReplaceBookingRequest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/ReplaceBookingRequest',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ReplaceBookingRequestRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ReplaceBookingRequestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RetryFailedEvents(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/RetryFailedEvents',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.RetryFailedEventsRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.RetryFailedEventsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAutoAssign(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/GetAutoAssign',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetAutoAssignRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.GetAutoAssignResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AutoAssign(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/AutoAssign',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AutoAssignRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AutoAssignResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AcceptBookingRequest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/AcceptBookingRequest',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AcceptBookingRequestV2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/AcceptBookingRequestV2',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestV2Request.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.AcceptBookingRequestV2Response.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeclineBookingRequest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/DeclineBookingRequest',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.DeclineBookingRequestRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.DeclineBookingRequestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CountBookingRequests(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/CountBookingRequests',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestsRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListBookingRequestId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/ListBookingRequestId',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestIdRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.ListBookingRequestIdResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SyncBookingRequestFromAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/SyncBookingRequestFromAppointment',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.SyncBookingRequestFromAppointmentRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.SyncBookingRequestFromAppointmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TriggerBookingRequestAutoAccepted(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/TriggerBookingRequestAutoAccepted',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.TriggerBookingRequestAutoAcceptedRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.TriggerBookingRequestAutoAcceptedResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CheckWaitlistAvailableTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/CheckWaitlistAvailableTask',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CheckWaitlistAvailableTaskRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CheckWaitlistAvailableTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MoveBookingRequestToWaitlist(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/MoveBookingRequestToWaitlist',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.MoveBookingRequestToWaitlistRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.MoveBookingRequestToWaitlistResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CountBookingRequestByFilter(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/CountBookingRequestByFilter',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestByFilterRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.CountBookingRequestByFilterResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PreviewBookingRequestPricing(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.online_booking.v1.BookingRequestService/PreviewBookingRequestPricing',
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.PreviewBookingRequestPricingRequest.SerializeToString,
            moego_dot_service_dot_online__booking_dot_v1_dot_booking__request__service__pb2.PreviewBookingRequestPricingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
