package com.moego.api.v3.appointment.controller;

import com.moego.idl.api.appointment.v1.BatchDeleteDailyReportConfigParams;
import com.moego.idl.api.appointment.v1.BatchDeleteDailyReportConfigResult;
import com.moego.idl.api.appointment.v1.BatchSendDailyDraftReportParams;
import com.moego.idl.api.appointment.v1.BatchSendDailyDraftReportResult;
import com.moego.idl.api.appointment.v1.DailyReportServiceGrpc;
import com.moego.idl.api.appointment.v1.GenerateMessageContentParams;
import com.moego.idl.api.appointment.v1.GenerateMessageContentResult;
import com.moego.idl.api.appointment.v1.GetDailyReportConfigParams;
import com.moego.idl.api.appointment.v1.GetDailyReportConfigResult;
import com.moego.idl.api.appointment.v1.GetDailyReportForCustomerParams;
import com.moego.idl.api.appointment.v1.GetDailyReportForCustomerResult;
import com.moego.idl.api.appointment.v1.GetDailyReportSentHistoryParams;
import com.moego.idl.api.appointment.v1.GetDailyReportSentHistoryResult;
import com.moego.idl.api.appointment.v1.GetDailyReportSentResultParams;
import com.moego.idl.api.appointment.v1.GetDailyReportSentResultResult;
import com.moego.idl.api.appointment.v1.ListDailyReportConfigParams;
import com.moego.idl.api.appointment.v1.ListDailyReportConfigResult;
import com.moego.idl.api.appointment.v1.SendMessageParams;
import com.moego.idl.api.appointment.v1.SendMessageResult;
import com.moego.idl.api.appointment.v1.UpsertDailyReportConfigParams;
import com.moego.idl.api.appointment.v1.UpsertDailyReportConfigResult;
import com.moego.idl.models.appointment.v1.DailyReportConfigDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.BatchDeleteDailyReportConfigRequest;
import com.moego.idl.service.appointment.v1.BatchSendDailyDraftReportRequest;
import com.moego.idl.service.appointment.v1.GenerateMessageContentRequest;
import com.moego.idl.service.appointment.v1.GetDailyReportConfigRequest;
import com.moego.idl.service.appointment.v1.GetDailyReportForCustomerRequest;
import com.moego.idl.service.appointment.v1.GetDailyReportSentHistoryRequest;
import com.moego.idl.service.appointment.v1.GetDailyReportSentResultRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByFilterRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigByFilterResponse;
import com.moego.idl.service.appointment.v1.SendMessageRequest;
import com.moego.idl.service.appointment.v1.UpsertDailyReportConfigRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetPetRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class DailyReportController extends DailyReportServiceGrpc.DailyReportServiceImplBase {
    private final com.moego.idl.service.appointment.v1.DailyReportServiceGrpc.DailyReportServiceBlockingStub
            dailyReportService;
    private final IBusinessBusinessClient businessClient;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;

    @Override
    @Auth(AuthType.COMPANY)
    public void getDailyReportConfig(
            GetDailyReportConfigParams request, StreamObserver<GetDailyReportConfigResult> responseObserver) {
        var response = dailyReportService.getDailyReportConfig(GetDailyReportConfigRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .setPetId(request.getPetId())
                .setServiceDate(request.getServiceDate())
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .build());

        responseObserver.onNext(GetDailyReportConfigResult.newBuilder()
                .setId(response.getId())
                .setReport(response.getReport())
                .setStatus(response.getStatus())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getDailyReportSentResult(
            GetDailyReportSentResultParams request, StreamObserver<GetDailyReportSentResultResult> responseObserver) {
        var response = dailyReportService.getDailyReportSentResult(GetDailyReportSentResultRequest.newBuilder()
                .addAppointmentIds(request.getAppointmentId())
                .setServiceDate(request.getServiceDate())
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .build());

        responseObserver.onNext(GetDailyReportSentResultResult.newBuilder()
                .addAllSentResults(response.getSentResultsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void upsertDailyReportConfig(
            UpsertDailyReportConfigParams request, StreamObserver<UpsertDailyReportConfigResult> responseObserver) {
        var response = dailyReportService.upsertDailyReportConfig(UpsertDailyReportConfigRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .setPetId(request.getPetId())
                .setCustomerId(request.getCustomerId())
                .setServiceDate(request.getServiceDate())
                .setReport(request.getReport())
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setStaffId(AuthContext.get().staffId())
                .build());

        responseObserver.onNext(UpsertDailyReportConfigResult.newBuilder()
                .setId(response.getId())
                .setUuid(response.getUuid())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getDailyReportSentHistory(
            GetDailyReportSentHistoryParams request, StreamObserver<GetDailyReportSentHistoryResult> responseObserver) {
        var response = dailyReportService.getDailyReportSentHistory(GetDailyReportSentHistoryRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .setPetId(request.getPetId())
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .build());

        responseObserver.onNext(GetDailyReportSentHistoryResult.newBuilder()
                .addAllSentHistoryRecords(response.getSentHistoryRecordsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void getDailyReportForCustomer(
            GetDailyReportForCustomerParams request, StreamObserver<GetDailyReportForCustomerResult> responseObserver) {
        var response = dailyReportService.getDailyReportForCustomer(GetDailyReportForCustomerRequest.newBuilder()
                .setUuid(request.getUuid())
                .build());
        if (!response.hasReport()) {
            responseObserver.onNext(GetDailyReportForCustomerResult.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        MoeBusinessDto businessInfo = businessClient.getBusinessInfo(InfoIdParams.builder()
                .infoId(Math.toIntExact(response.getBusinessId()))
                .build());
        BusinessCustomerPetModel petInfo = businessCustomerPetService
                .getPet(GetPetRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(response.getCompanyId())
                                .build())
                        .setId(response.getPetId())
                        .build())
                .getPet();

        responseObserver.onNext(GetDailyReportForCustomerResult.newBuilder()
                .setReport(response.getReport())
                .setServiceDate(response.getServiceDate())
                .setBusiness(GetDailyReportForCustomerResult.Business.newBuilder()
                        .setBusinessId(businessInfo.getId())
                        .setBusinessName(businessInfo.getBusinessName())
                        .setAvatarPath(businessInfo.getAvatarPath())
                        .build())
                .setPet(GetDailyReportForCustomerResult.Pet.newBuilder()
                        .setPetId(petInfo.getId())
                        .setPetName(petInfo.getPetName())
                        .setPetType(petInfo.getPetType())
                        .setAvatarPath(petInfo.getAvatarPath())
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void generateMessageContent(
            GenerateMessageContentParams request, StreamObserver<GenerateMessageContentResult> responseObserver) {
        var response = dailyReportService.generateMessageContent(GenerateMessageContentRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .setPetId(request.getPetId())
                .setServiceDate(request.getServiceDate())
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .build());

        responseObserver.onNext(GenerateMessageContentResult.newBuilder()
                .setMessage(response.getMessage())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void sendMessage(SendMessageParams request, StreamObserver<SendMessageResult> responseObserver) {
        var response = dailyReportService.sendMessage(SendMessageRequest.newBuilder()
                .setId(request.getId())
                .setBusinessId(AuthContext.get().businessId())
                .setCompanyId(AuthContext.get().companyId())
                .setStaffId(AuthContext.get().staffId())
                .build());

        responseObserver.onNext(
                SendMessageResult.newBuilder().setResult(response.getResult()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listDailyReportConfig(
            ListDailyReportConfigParams request, StreamObserver<ListDailyReportConfigResult> responseObserver) {
        ListDailyReportConfigByFilterResponse listDailyReportConfigByFilterResponse =
                dailyReportService.listDailyReportConfigByFilter(ListDailyReportConfigByFilterRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .setFilter(request.getFilter())
                        .setPagination(request.getPagination())
                        .build());

        List<DailyReportConfigDef> reportConfigsList = listDailyReportConfigByFilterResponse.getReportConfigsList();
        List<Long> petIds = reportConfigsList.stream()
                .map(DailyReportConfigDef::getPetId)
                .distinct()
                .toList();
        List<BusinessCustomerPetInfoModel> petInfoList = businessCustomerPetService
                .batchGetPetInfo(
                        BatchGetPetInfoRequest.newBuilder().addAllIds(petIds).build())
                .getPetsList();
        List<ListDailyReportConfigResult.Pet> pets = petInfoList.stream()
                .map(pet -> ListDailyReportConfigResult.Pet.newBuilder()
                        .setPetId(pet.getId())
                        .setPetName(pet.getPetName())
                        .setAvatarPath(pet.getAvatarPath())
                        .setPetType(pet.getPetType())
                        .build())
                .toList();

        ListDailyReportConfigResult result = ListDailyReportConfigResult.newBuilder()
                .addAllReportConfigs(reportConfigsList)
                .setPagination(listDailyReportConfigByFilterResponse.getPagination())
                .addAllPets(pets)
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void batchSendDailyDraftReport(
            BatchSendDailyDraftReportParams request, StreamObserver<BatchSendDailyDraftReportResult> responseObserver) {
        dailyReportService.batchSendDailyDraftReport(BatchSendDailyDraftReportRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setSendMethod(request.getSendMethod())
                .addAllDailyReportIds(request.getDailyReportIdsList())
                .build());
        responseObserver.onNext(BatchSendDailyDraftReportResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void batchDeleteDailyReportConfig(
            BatchDeleteDailyReportConfigParams request,
            StreamObserver<BatchDeleteDailyReportConfigResult> responseObserver) {
        dailyReportService.batchDeleteDailyReportConfig(BatchDeleteDailyReportConfigRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .addAllDailyReportIds(request.getDailyReportIdsList())
                .build());
        responseObserver.onNext(BatchDeleteDailyReportConfigResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
