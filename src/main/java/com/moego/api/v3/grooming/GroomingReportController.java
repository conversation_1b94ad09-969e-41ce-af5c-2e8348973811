package com.moego.api.v3.grooming;

import com.google.common.collect.Lists;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.idl.api.grooming.v1.BatchDeleteGroomingReportCardParams;
import com.moego.idl.api.grooming.v1.BatchDeleteGroomingReportCardResult;
import com.moego.idl.api.grooming.v1.BatchSendGroomingReportCardParams;
import com.moego.idl.api.grooming.v1.BatchSendGroomingReportCardResult;
import com.moego.idl.api.grooming.v1.GroomingReportCardDef;
import com.moego.idl.api.grooming.v1.GroomingReportServiceGrpc;
import com.moego.idl.api.grooming.v1.ListGroomingReportCardParams;
import com.moego.idl.api.grooming.v1.ListGroomingReportCardResult;
import com.moego.idl.api.grooming.v1.ReportCardPetOverview;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.grooming.v1.GroomingReportSendMethod;
import com.moego.idl.models.grooming.v1.GroomingReportStatus;
import com.google.protobuf.Timestamp;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.utils.DateTimeUtils;
import com.moego.server.grooming.api.IGroomingGroomingReportService;
import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.grooming.params.groomingreport.BatchSendGroomingReportParams;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportCardListParams;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.message.api.IGroomingReportSendService;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class GroomingReportController extends GroomingReportServiceGrpc.GroomingReportServiceImplBase {

    private final IGroomingGroomingReportService groomingGroomingReportService;
    private final IGroomingReportSendService groomingReportSendService;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;

    @Override
    @Auth(AuthType.COMPANY)
    public void listGroomingReportCard(
            ListGroomingReportCardParams request, StreamObserver<ListGroomingReportCardResult> responseObserver) {

        // 查询report list
        var listGroomingReportCardParamsBuilder = new GetGroomingReportCardListParams().toBuilder()
                .companyId(request.getCompanyId())
                .businessId(request.getBusinessId());
        if (request.hasStartDate() && request.hasEndDate()) {
            String startDateStr =
                    DateTimeConverter.INSTANCE.fromGoogleDate(request.getStartDate()).toString();
            String endDateStr =
                    DateTimeConverter.INSTANCE.fromGoogleDate(request.getEndDate()).toString();
            listGroomingReportCardParamsBuilder.startDate(startDateStr)
                    .endDate(endDateStr);
        }
        List<GroomingReportDTO> groomingReportDTOS =
                groomingGroomingReportService.listGroomingReportCardByFilter(listGroomingReportCardParamsBuilder.build());
        if (groomingReportDTOS.isEmpty()) {
            responseObserver.onNext(ListGroomingReportCardResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // 查询pet list
        List<Long> petIds = groomingReportDTOS.stream()
                .map(groomingReportDTO -> groomingReportDTO.getPetId().longValue())
                .distinct()
                .toList();
        List<BusinessCustomerPetInfoModel> petInfoList = businessCustomerPetService
                .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder().addAllIds(petIds).build())
                .getPetsList();

        // 构建petInfoMap，方便查找
        Map<Long, BusinessCustomerPetInfoModel> petInfoMap = petInfoList.stream()
                .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity()));

        List<GroomingReportCardDef> reportConfigs;
        if (request.hasStatus() && request.getStatus().equals(GroomingReportStatus.GROOMING_REPORT_STATUS_SENT)) {
            // 查询report last send log，key 为 groomingId
            Map<Integer, List<GroomingReportSendLogDTO>> groomingLastReportSendLogsMap =
                    groomingReportSendService.getGroomingLastReportSendLogsMap(
                            new GroomingIdListParams(Math.toIntExact(request.getBusinessId()),
                                    groomingReportDTOS.stream()
                                            .map(GroomingReportDTO::getGroomingId)
                                            .distinct()
                                            .toList()));
            // 构建report config
            reportConfigs = groomingReportDTOS.stream()
                    .map(groomingReportDTO -> {
                        // 获取对应的pet信息
                        BusinessCustomerPetInfoModel petInfo = petInfoMap.get(groomingReportDTO.getPetId().longValue());

                        // 构建pet overview
                        ReportCardPetOverview.Builder petOverviewBuilder = buildReportCardPetOverview(petInfo);

                        // 构建GroomingReportCardDef
                        GroomingReportCardDef.Builder reportCardBuilder = GroomingReportCardDef.newBuilder()
                                .setReportCardId(groomingReportDTO.getId().longValue())
                                .setAppointmentId(groomingReportDTO.getGroomingId().longValue())
                                .setPetOverview(petOverviewBuilder.build());

                        // 设置updateTime
                        if (groomingReportDTO.getUpdateTime() != null) {
                            reportCardBuilder.setUpdateTime(
                                    Timestamp.newBuilder().setSeconds(groomingReportDTO.getUpdateTime()).build());
                        }

                        // 获取发送记录
                        List<GroomingReportSendLogDTO> sendLogs = groomingLastReportSendLogsMap.get(groomingReportDTO.getGroomingId());
                        if (sendLogs != null && !sendLogs.isEmpty()) {
                            // 取最新的发送记录
                            GroomingReportSendLogDTO latestSendLog = sendLogs.stream()
                                    .filter(log -> log.getReportId().equals(groomingReportDTO.getId()))
                                    .toList().get(0);

                            // 设置sendTime
                            if (latestSendLog.getSentTime() != null) {
                                long sentTimeMillis = latestSendLog.getSentTime().getTime();
                                reportCardBuilder.setSendTime(
                                        Timestamp.newBuilder()
                                        .setSeconds(sentTimeMillis / 1000)
                                        .setNanos((int) ((sentTimeMillis % 1000) * 1000000))
                                        .build());
                            }

                            // 设置sendMethod
                            if (latestSendLog.getSendingMethod() != null) {
                                GroomingReportSendMethod sendMethod = GroomingReportSendMethod.forNumber(latestSendLog.getSendingMethod().intValue());
                                if (sendMethod != null) {
                                    reportCardBuilder.setSendMethod(sendMethod);
                                }
                            }
                        }

                        return reportCardBuilder.build();
                    })
                    .toList();
        } else {
            // 构建report config
            reportConfigs = groomingReportDTOS.stream()
                    .map(groomingReportDTO -> {
                        // 获取对应的pet信息
                        BusinessCustomerPetInfoModel petInfo = petInfoMap.get(groomingReportDTO.getPetId().longValue());

                        // 构建pet overview
                        ReportCardPetOverview.Builder petOverviewBuilder = buildReportCardPetOverview(petInfo);

                        // 构建GroomingReportCardDef
                        GroomingReportCardDef.Builder reportCardBuilder = GroomingReportCardDef.newBuilder()
                                .setReportCardId(groomingReportDTO.getId().longValue())
                                .setAppointmentId(groomingReportDTO.getGroomingId().longValue())
                                .setPetOverview(petOverviewBuilder.build());

                        // 设置updateTime
                        if (groomingReportDTO.getUpdateTime() != null) {
                            reportCardBuilder.setUpdateTime(
                                    Timestamp.newBuilder().setSeconds(groomingReportDTO.getUpdateTime()).build());
                        }

                        return reportCardBuilder.build();
                    })
                    .toList();
        }

        int pageSize = request.getPagination().getPageSize();
        int pageNum = request.getPagination().getPageNum();

        List<List<GroomingReportCardDef>> partition = Lists.partition(reportConfigs, pageSize);
        List<GroomingReportCardDef> itemsPage =
                (pageNum > 0 && pageNum <= partition.size()) ? partition.get(pageNum - 1) : List.of();

        responseObserver.onNext(ListGroomingReportCardResult.newBuilder()
                .addAllGroomingReportCards(itemsPage)
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .setTotal(partition.size())
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void batchDeleteGroomingReportCard(
            BatchDeleteGroomingReportCardParams request, StreamObserver<BatchDeleteGroomingReportCardResult> responseObserver) {
        groomingGroomingReportService.batchDeleteGroomingReportCard(
                Math.toIntExact(request.getBusinessId()), request.getReportCardIdsList().stream()
                        .map(Long::intValue)
                        .toList());
        responseObserver.onNext(BatchDeleteGroomingReportCardResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void batchSendGroomingReportCard(
            BatchSendGroomingReportCardParams request, StreamObserver<BatchSendGroomingReportCardResult> responseObserver) {
        var batchSendRequest = BatchSendGroomingReportParams.builder()
                .companyId(Math.toIntExact(request.getCompanyId()))
                .businessId(Math.toIntExact(request.getBusinessId()))
                .reportIds(request.getReportCardIdsList().stream()
                        .map(Long::intValue)
                        .toList())
                .staffId(Math.toIntExact(request.getStaffId()))
                .build();
        groomingReportSendService.batchSendGroomingReportCard(batchSendRequest);
        responseObserver.onNext( BatchSendGroomingReportCardResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private ReportCardPetOverview.Builder buildReportCardPetOverview(BusinessCustomerPetInfoModel petInfo) {
        ReportCardPetOverview.Builder petOverviewBuilder = ReportCardPetOverview.newBuilder();
        if (petInfo != null) {
            petOverviewBuilder
                    .setPetId(petInfo.getId())
                    .setPetName(petInfo.getPetName())
                    .setPetType(petInfo.getPetType())
                    .setAvatarPath(petInfo.getAvatarPath());
        }
        return petOverviewBuilder;
    }
}
