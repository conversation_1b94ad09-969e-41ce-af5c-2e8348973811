package com.moego.api.v3.grooming.converter;

import com.google.protobuf.Timestamp;
import com.google.type.Date;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.idl.api.grooming.v1.GroomingReportCardDef;
import com.moego.idl.api.grooming.v1.ReportCardPetOverview;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.grooming.v1.GroomingReportSendMethod;
import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface GroomingReportConverter {

    GroomingReportConverter INSTANCE = Mappers.getMapper(GroomingReportConverter.class);

    /**
     * 转换 GroomingReportDTO 列表为 GroomingReportCardDef 列表（包含发送日志）
     */
    default List<GroomingReportCardDef> convertToGroomingReportCardDefList(
            List<GroomingReportDTO> groomingReportDTOList,
            Map<Long, BusinessCustomerPetInfoModel> petInfoMap,
            List<GroomingReportSendLogDTO> groomingReportSendLogs) {

        Map<Integer, GroomingReportSendLogDTO> sendLogMap = groomingReportSendLogs.stream()
                .collect(Collectors.toMap(GroomingReportSendLogDTO::getReportId, Function.identity()));

        return groomingReportDTOList.stream()
                .map(groomingReportDTO -> {
                    GroomingReportSendLogDTO sendLog = sendLogMap.get(groomingReportDTO.getId());
                    if (sendLog != null) {
                        return toGroomingReportCardDef(groomingReportDTO, petInfoMap, sendLog);
                    } else {
                        return toGroomingReportCardDef(groomingReportDTO, petInfoMap);
                    }
                })
                .toList();
    }

    /**
     * 转换 GroomingReportDTO 列表为 GroomingReportCardDef 列表（不包含发送日志）
     */
    default List<GroomingReportCardDef> convertToGroomingReportCardDefList(
            List<GroomingReportDTO> groomingReportDTOList,
            Map<Long, BusinessCustomerPetInfoModel> petInfoMap) {

        return groomingReportDTOList.stream()
                .map(groomingReportDTO -> toGroomingReportCardDef(groomingReportDTO, petInfoMap))
                .toList();
    }

    /**
     * 转换单个 GroomingReportDTO 为 GroomingReportCardDef（包含发送日志）
     */
    default GroomingReportCardDef toGroomingReportCardDef(
            GroomingReportDTO groomingReportDTO,
            Map<Long, BusinessCustomerPetInfoModel> petInfoMap,
            GroomingReportSendLogDTO sendLog) {

        GroomingReportCardDef baseReportCard = toGroomingReportCardDef(groomingReportDTO, petInfoMap);

        GroomingReportCardDef.Builder builder = baseReportCard.toBuilder();

        // 设置发送时间
        if (sendLog.getSentTime() != null) {
            long sentTimeMillis = sendLog.getSentTime().getTime();
            builder.setSendTime(Timestamp.newBuilder()
                    .setSeconds(sentTimeMillis / 1000)
                    .setNanos((int) ((sentTimeMillis % 1000) * 1000000))
                    .build());
        }

        // 设置发送方法
        if (sendLog.getSendingMethod() != null) {
            GroomingReportSendMethod sendMethod = GroomingReportSendMethod.forNumber(sendLog.getSendingMethod().intValue());
            if (sendMethod != null) {
                builder.setSendMethod(sendMethod);
            }
        }

        return builder.build();
    }

    /**
     * 转换单个 GroomingReportDTO 为 GroomingReportCardDef（不包含发送日志）
     */
    default GroomingReportCardDef toGroomingReportCardDef(
            GroomingReportDTO groomingReportDTO,
            Map<Long, BusinessCustomerPetInfoModel> petInfoMap) {

        // 获取对应的pet信息
        BusinessCustomerPetInfoModel petInfo = petInfoMap.get(groomingReportDTO.getPetId().longValue());

        // 构建pet overview
        ReportCardPetOverview petOverview = buildReportCardPetOverview(petInfo);

        // 构建GroomingReportCardDef
        GroomingReportCardDef.Builder reportCardBuilder = GroomingReportCardDef.newBuilder()
                .setReportCardId(groomingReportDTO.getId().longValue())
                .setAppointmentId(groomingReportDTO.getGroomingId().longValue())
                .setPetOverview(petOverview);

        // 设置updateTime
        if (groomingReportDTO.getUpdateTime() != null) {
            reportCardBuilder.setUpdateTime(Timestamp.newBuilder()
                    .setSeconds(groomingReportDTO.getUpdateTime())
                    .build());
        }

        return reportCardBuilder.build();
    }

    /**
     * 构建 ReportCardPetOverview
     */
    default ReportCardPetOverview buildReportCardPetOverview(BusinessCustomerPetInfoModel petInfo) {
        ReportCardPetOverview.Builder petOverviewBuilder = ReportCardPetOverview.newBuilder();

        if (petInfo != null) {
            petOverviewBuilder
                    .setPetId(petInfo.getId())
                    .setPetName(petInfo.getPetName())
                    .setPetType(petInfo.getPetType())
                    .setAvatarPath(petInfo.getAvatarPath());
        }

        return petOverviewBuilder.build();
    }
}
