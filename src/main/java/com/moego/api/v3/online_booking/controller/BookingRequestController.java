package com.moego.api.v3.online_booking.controller;

import static com.moego.idl.api.online_booking.v1.BookingRequestServiceGrpc.BookingRequestServiceImplBase;

import com.moego.api.v3.online_booking.converter.AutoAssignConverter;
import com.moego.api.v3.online_booking.converter.BookingRequestConverter;
import com.moego.api.v3.online_booking.service.BookingRequestQueryService;
import com.moego.api.v3.organization.service.StaffService;
import com.moego.idl.api.online_booking.v1.AcceptBookingRequestRequest;
import com.moego.idl.api.online_booking.v1.AcceptBookingRequestResponse;
import com.moego.idl.api.online_booking.v1.AcceptBookingRequestV2Request;
import com.moego.idl.api.online_booking.v1.AcceptBookingRequestV2Response;
import com.moego.idl.api.online_booking.v1.AutoAssignRequest;
import com.moego.idl.api.online_booking.v1.AutoAssignResponse;
import com.moego.idl.api.online_booking.v1.AutoAssignV2Request;
import com.moego.idl.api.online_booking.v1.AutoAssignV2Response;
import com.moego.idl.api.online_booking.v1.DeclineBookingRequestRequest;
import com.moego.idl.api.online_booking.v1.DeclineBookingRequestResponse;
import com.moego.idl.api.online_booking.v1.GetBookingRequestItem;
import com.moego.idl.api.online_booking.v1.GetBookingRequestListRequest;
import com.moego.idl.api.online_booking.v1.GetBookingRequestListResponse;
import com.moego.idl.api.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.api.online_booking.v1.GetBookingRequestResponse;
import com.moego.idl.api.online_booking.v1.MoveBookingRequestToWaitlistParams;
import com.moego.idl.api.online_booking.v1.MoveBookingRequestToWaitlistResult;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.PetToStaffDef;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetAutoAssignRequest;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsResponse;
import com.moego.idl.service.online_booking.v1.MoveBookingRequestToWaitlistRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingRequestController extends BookingRequestServiceImplBase {

    private final StaffService staffService;
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestService;
    private final BookingRequestQueryService bookingRequestQueryService;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_BOOKING_REQUEST_AND_WAITING_LIST})
    public void getBookingRequestList(
            GetBookingRequestListRequest request, StreamObserver<GetBookingRequestListResponse> responseObserver) {
        long businessId = request.getBusinessId();
        Long companyId = AuthContext.get().companyId();
        Long tokenStaffId = AuthContext.get().staffId();

        // get booking request
        ListBookingRequestsResponse response =
                bookingRequestService.listBookingRequests(ListBookingRequestsRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addAllAssociatedModels(
                                List.of(BookingRequestAssociatedModel.SERVICE, BookingRequestAssociatedModel.ADD_ON))
                        .addAllStatuses(List.of(BookingRequestStatus.SUBMITTED))
                        .setPagination(request.getPagination())
                        .addAllOrderBys(request.getOrderBysList())
                        .addAllPaymentStatuses(List.of(
                                BookingRequestModel.PaymentStatus.NO_PAYMENT,
                                BookingRequestModel.PaymentStatus.PROCESSING, // confirmed PaymentIntent
                                BookingRequestModel.PaymentStatus.SUCCESS))
                        .build());
        List<BookingRequestModel> bookingRequests = response.getBookingRequestsList();
        if (CollectionUtils.isEmpty(bookingRequests)) {
            responseObserver.onNext(GetBookingRequestListResponse.newBuilder()
                    .addAllBookingRequestItems(List.of())
                    .setPagination(response.getPagination())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        List<GetBookingRequestItem> bookingRequestItems =
                bookingRequestQueryService.getBookingRequestItems(companyId, businessId, bookingRequests, tokenStaffId);

        responseObserver.onNext(GetBookingRequestListResponse.newBuilder()
                .addAllBookingRequestItems(bookingRequestItems)
                .setPagination(response.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_BOOKING_REQUEST_AND_WAITING_LIST})
    public void getBookingRequest(
            GetBookingRequestRequest request, StreamObserver<GetBookingRequestResponse> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        Long businessId = AuthContext.get().businessId();
        Long staffId = AuthContext.get().staffId();

        BookingRequestModel bookingRequest = bookingRequestService
                .getBookingRequest(com.moego.idl.service.online_booking.v1.GetBookingRequestRequest.newBuilder()
                        .setId(request.getId())
                        .addAllAssociatedModels(List.of(
                                BookingRequestAssociatedModel.SERVICE,
                                BookingRequestAssociatedModel.ADD_ON,
                                BookingRequestAssociatedModel.FEEDING,
                                BookingRequestAssociatedModel.MEDICATION))
                        .build())
                .getBookingRequest();

        if (bookingRequest.getId() <= 0 || !Objects.equals(companyId, bookingRequest.getCompanyId())) {
            responseObserver.onNext(GetBookingRequestResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        GetBookingRequestResponse bookingRequestResponse =
                bookingRequestQueryService.getBookingRequestResponse(companyId, businessId, bookingRequest, staffId);

        responseObserver.onNext(bookingRequestResponse);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void autoAssign(AutoAssignRequest request, StreamObserver<AutoAssignResponse> responseObserver) {
        GetAutoAssignResponse response = bookingRequestService.getAutoAssign(GetAutoAssignRequest.newBuilder()
                .setId(request.getBookingRequestId())
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .build());

        var staffIds = response.getEvaluationPetToStaffsList().stream()
                .map(PetToStaffDef::getStaffId)
                .toList();
        var staffMap = staffService.getStaffMap(staffIds);

        responseObserver.onNext(AutoAssignResponse.newBuilder()
                .addAllBoardingAssignRequires(response.getBoardingAssignRequiresList().stream()
                        .map(AutoAssignConverter.INSTANCE::toAssignRequire)
                        .toList())
                .addAllEvaluationAssignRequires(response.getEvaluationAssignRequiresList().stream()
                        .map(AutoAssignConverter.INSTANCE::toAssignRequire)
                        .toList())
                .addAllPetToLodgings(response.getPetToLodgingsList().stream()
                        .map(BookingRequestConverter.INSTANCE::toPetToLodging)
                        .toList())
                .addAllEvaluationPetToStaffs(response.getEvaluationPetToStaffsList().stream()
                        .map(BookingRequestConverter.INSTANCE::toPetToStaff)
                        .toList())
                .addAllLodgings(response.getLodgingsList().stream()
                        .map(AutoAssignConverter.INSTANCE::toLodgingDetail)
                        .toList())
                .addAllStaffs(staffIds.stream()
                        .map(staffMap::get)
                        .filter(Objects::nonNull)
                        .map(AutoAssignConverter::toAutoAssignResponseStaffDetail)
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_BOOKING_REQUEST_AND_WAITING_LIST})
    public void acceptBookingRequest(
            AcceptBookingRequestRequest request, StreamObserver<AcceptBookingRequestResponse> responseObserver) {
        com.moego.idl.service.online_booking.v1.AcceptBookingRequestResponse response =
                bookingRequestService.acceptBookingRequest(
                        com.moego.idl.service.online_booking.v1.AcceptBookingRequestRequest.newBuilder()
                                .setId(request.getId())
                                .addAllPetToLodgings(request.getPetToLodgingsList().stream()
                                        .map(BookingRequestConverter.INSTANCE::toPetToLodgingDef)
                                        .toList())
                                .addAllPetToStaffs(request.getPetToStaffsList().stream()
                                        .map(BookingRequestConverter.INSTANCE::toPetToStaffDef)
                                        .toList())
                                .addAllPetToServices(request.getPetToServicesList().stream()
                                        .map(BookingRequestConverter.INSTANCE::toPetToServiceDef)
                                        .toList())
                                .addAllEvaluationPetToStaffs(request.getEvaluationPetToStaffsList().stream()
                                        .map(BookingRequestConverter.INSTANCE::toPetToStaffDef)
                                        .toList())
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .setStaffId(AuthContext.get().staffId())
                                .build());

        responseObserver.onNext(AcceptBookingRequestResponse.newBuilder()
                .setResult(response.getResult())
                .setAppointmentId(response.getAppointmentId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_BOOKING_REQUEST_AND_WAITING_LIST})
    public void acceptBookingRequestV2(
            AcceptBookingRequestV2Request request, StreamObserver<AcceptBookingRequestV2Response> responseObserver) {
        var response = bookingRequestService.acceptBookingRequestV2(
                com.moego.idl.service.online_booking.v1.AcceptBookingRequestV2Request.newBuilder()
                        .setId(request.getId())
                        .setCompanyId(AuthContext.get().companyId())
                        .addAllGroomingServices(request.getGroomingServicesList())
                        .addAllBoardingServices(request.getBoardingServicesList())
                        .addAllDaycareServices(request.getDaycareServicesList())
                        .addAllEvaluationServices(request.getEvaluationServicesList())
                        .addAllGroomingAddons(request.getGroomingAddonsList())
                        .addAllBoardingAddons(request.getBoardingAddonsList())
                        .addAllDaycareAddons(request.getDaycareAddonsList())
                        .addAllCreateEvaluationRequests(request.getCreateEvaluationRequestsList())
                        .build());

        responseObserver.onNext(AcceptBookingRequestV2Response.newBuilder()
                .setResult(response.getResult())
                .setAppointmentId(response.getAppointmentId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_BOOKING_REQUEST_AND_WAITING_LIST})
    public void declineBookingRequest(
            DeclineBookingRequestRequest request, StreamObserver<DeclineBookingRequestResponse> responseObserver) {
        com.moego.idl.service.online_booking.v1.DeclineBookingRequestResponse response =
                bookingRequestService.declineBookingRequest(
                        com.moego.idl.service.online_booking.v1.DeclineBookingRequestRequest.newBuilder()
                                .setId(request.getId())
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .setStaffId(AuthContext.get().staffId())
                                .build());

        responseObserver.onNext(DeclineBookingRequestResponse.newBuilder()
                .setResult(response.getResult())
                .setAppointmentId(response.getAppointmentId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void autoAssignV2(AutoAssignV2Request request, StreamObserver<AutoAssignV2Response> responseObserver) {

        var response =
                bookingRequestService.autoAssign(com.moego.idl.service.online_booking.v1.AutoAssignRequest.newBuilder()
                        .setBookingRequestId(request.getBookingRequestId())
                        .setCompanyId(AuthContext.get().companyId())
                        .build());

        responseObserver.onNext(AutoAssignV2Response.newBuilder()
                .addAllBoardingServices(response.getBoardingServicesList())
                .addAllEvaluationServices(response.getEvaluationServicesList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void moveBookingRequestToWaitlist(
            MoveBookingRequestToWaitlistParams request,
            StreamObserver<MoveBookingRequestToWaitlistResult> responseObserver) {

        bookingRequestService.moveBookingRequestToWaitlist(MoveBookingRequestToWaitlistRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setBookingRequestId(request.getBookingRequestId())
                .build());

        responseObserver.onNext(MoveBookingRequestToWaitlistResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
