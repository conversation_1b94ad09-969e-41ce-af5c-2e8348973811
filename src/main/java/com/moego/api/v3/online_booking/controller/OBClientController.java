package com.moego.api.v3.online_booking.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.google.protobuf.Timestamp;
import com.moego.api.v3.online_booking.converter.ClientConverter;
import com.moego.api.v3.online_booking.converter.CreditCardConverter;
import com.moego.api.v3.online_booking.converter.QuestionConverter;
import com.moego.api.v3.online_booking.service.BusinessCustomerService;
import com.moego.api.v3.online_booking.service.OBLoginService;
import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.idl.api.online_booking.v1.GetOBClientRequest;
import com.moego.idl.api.online_booking.v1.OBClientInfoResponse;
import com.moego.idl.api.online_booking.v1.OBClientPaymentTypeResponse;
import com.moego.idl.api.online_booking.v1.OBClientServiceGrpc;
import com.moego.idl.api.online_booking.v1.UpdateOBClientRequest;
import com.moego.idl.api.online_booking.v1.UpdateOBClientRequest.UpdateOBClientRequestAddress;
import com.moego.idl.api.online_booking.v1.UpdateOBClientResponse;
import com.moego.idl.api.online_booking.v1.UpdateOBClientResponse.UpdateOBClientAddressResponse;
import com.moego.idl.models.customer.v1.CustomerDef;
import com.moego.idl.models.customer.v1.CustomerModelOnlineBookingView;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerContactServiceGrpc;
import com.moego.idl.service.business_customer.v1.CreateCustomerAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.ListCustomerContactRequest;
import com.moego.idl.service.business_customer.v1.UpdateCustomerAddressRequest;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerRequest;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerResponse;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.customer.api.ICustomerContactService;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.customer.api.IProfileRequestAddressService;
import com.moego.server.customer.dto.AdditionalContactDTO;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerBasicDTO;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.OBMainSessionDTO;
import com.moego.server.customer.dto.ProfileRequestAddressDTO;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.grooming.api.IBookOnlineQuestionService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentGroupSettingDTO;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.enums.OBRequestSubmittedAutoTypeEnum;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.payment.api.IPaymentCreditCardService;
import com.moego.server.payment.dto.CardDTO;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/9
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class OBClientController extends OBClientServiceGrpc.OBClientServiceImplBase {

    private final ICustomerCustomerService customerService;
    private final IBookOnlineQuestionService questionService;
    private final IGroomingOnlineBookingService onlineBookingService;
    private final ICustomerProfileRequestService profileRequestService;
    private final ICustomerContactService contactService;
    private final IPaymentCreditCardService creditCardService;
    private final OBLoginService obLoginService;
    private final IProfileRequestAddressService profileRequestAddressApi;

    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub
            businessCustomerAddressApi;
    private final BusinessCustomerContactServiceGrpc.BusinessCustomerContactServiceBlockingStub
            businessCustomerContactApi;
    private final CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceBlockingStub
            customerAvailabilityServiceBlockingStub;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void updateOBClient(UpdateOBClientRequest request, StreamObserver<UpdateOBClientResponse> responseObserver) {
        OBBusinessDTO businessDTO = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));
        var businessId = businessDTO.getBusinessId();
        var companyId = businessDTO.getCompanyId();
        Integer customerId = AuthContext.get().getCustomerId();
        if (Objects.isNull(customerId)) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }

        BookOnlineDTO obSetting = Optional.ofNullable(onlineBookingService.getOBSetting(businessId))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "OB setting not found, businessId: " + businessId));
        boolean autoAcceptConflict =
                OBRequestSubmittedAutoTypeEnum.isAutoAcceptConflict(obSetting.getRequestSubmittedAutoType());

        var futures = new ArrayList<CompletableFuture<?>>();

        if (request.hasCustomer()) {
            futures.add(CompletableFuture.runAsync(
                    () -> updateCustomerInfo(request, companyId, customerId, businessId, autoAcceptConflict)));
        }

        if (!CollectionUtils.isEmpty(request.getCustomQuestionsMap())) {
            futures.add(CompletableFuture.runAsync(
                    () -> updateQuestionInfo(request, businessId, customerId, autoAcceptConflict)));
        }

        UpdateOBClientResponse.Builder builder = UpdateOBClientResponse.newBuilder();
        if (hasAddress(request)) {
            futures.add(CompletableFuture.runAsync(
                    () -> builder.setAddress(updateAddressInfo(customerId, request.getAddress(), businessId))));
        }

        CompletableFuture.allOf(futures.toArray(CompletableFuture[]::new)).join();

        responseObserver.onNext(builder.setId(customerId).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void getOBClientInfo(GetOBClientRequest request, StreamObserver<OBClientInfoResponse> responseObserver) {
        var customerId = AuthContext.get().getCustomerId();
        if (Objects.isNull(customerId)) {
            responseObserver.onNext(OBClientInfoResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        OBAnonymousParams anonymousParams =
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName());
        var businessDTO = onlineBookingService.getBusinessDTOByOBNameOrDomain(anonymousParams);
        if (businessDTO == null) {
            responseObserver.onNext(OBClientInfoResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        var businessId = businessDTO.getBusinessId();
        var companyId = businessDTO.getCompanyId();
        OBMainSessionDTO obMainSession = obLoginService.checkOBSession(anonymousParams);
        CustomerInfoIdParams params =
                new CustomerInfoIdParams().setCompanyId(companyId).setCustomerId(customerId);
        CustomerInfoDto customer = customerService.getCustomerWithDeletedHasCompanyId(params);
        if (Objects.isNull(customer) || Objects.equals(customer.getStatus(), CommonConstant.DELETED)) {
            obLoginService.logoutOBSubSession(obMainSession.mainSessionId(), obMainSession.obName());
            responseObserver.onNext(OBClientInfoResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        CustomerProfileRequestDTO.ClientProfileDTO client =
                profileRequestService.getClientWithProfileRequest(businessId, customerId);
        CustomerContactDto ownerPhone = contactService.getCustomerOwnerPhone(businessId, customerId);
        List<CardDTO> creditCardList = creditCardService.getCreditCardList(businessId, customerId);
        Map<String, Object> clientCustomQuestionMap = questionService
                .getCustomerLatestQuestionSave(businessId, AuthContext.get().getCustomerId())
                .getClientCustomQuestionMap();
        List<GroomingQuestionDTO> clientQuestions = questionService
                .listByCondition(IBookOnlineQuestionService.ListByConditionParam.builder()
                        .businessId(businessId)
                        .build())
                .stream()
                .filter(question -> Objects.equals(question.getType(), GroomingQuestionDTO.Type.PET_OWNER))
                .toList();
        CustomerModelOnlineBookingView.Builder builder = ClientConverter.INSTANCE.dto2view(client).toBuilder();
        if (client.getPreferredDay() != null && client.getPreferredDay().length != 0) {
            builder.addAllPreferredDay(List.of(client.getPreferredDay()));
        }
        if (client.getPreferredTime() != null && client.getPreferredTime().length != 0) {
            builder.addAllPreferredTime(List.of(client.getPreferredTime()));
        }
        if (Objects.nonNull(customer.getBirthday())) {
            var birthday = customer.getBirthday();
            var instant = birthday.atZone(ZoneId.systemDefault()).toInstant();
            builder.setBirthday(Timestamp.newBuilder()
                    .setSeconds(instant.getEpochSecond())
                    .setNanos(instant.getNano())
                    .build());
        }
        Map<String, String> customAnswers =
                QuestionConverter.INSTANCE.mergeCustomQuestion(clientCustomQuestionMap, client.getCustomQuestions());
        // customer info, phone number, address
        String phoneNumber = Objects.nonNull(ownerPhone) ? ownerPhone.getPhoneNumber() : null;
        CustomerAddressDto primaryAddress =
                profileRequestService.getAddressWithProfileRequest(businessId, customerId).stream()
                        .filter(address -> Objects.equals(address.getIsPrimary(), CommonConstant.ENABLE))
                        .findAny()
                        .orElse(null);
        Map<String, Object> answerMap = buildStandardAnswerMap(customer, phoneNumber, primaryAddress);
        answerMap.putAll(customAnswers);

        // get blocked service item types
        ListBlockedCustomerResponse listBlockCustomerResponse =
                customerAvailabilityServiceBlockingStub.listBlockedCustomer(ListBlockedCustomerRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllServiceItemTypes(List.of(
                                ServiceItemType.GROOMING, ServiceItemType.BOARDING,
                                ServiceItemType.DAYCARE, ServiceItemType.DOG_WALKING))
                        .addAllCustomerIds(List.of(customerId.longValue()))
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(10)
                                .build())
                        .build());
        List<ServiceItemType> blockedServiceItemTypes = new ArrayList<>();
        listBlockCustomerResponse.getCustomerBlockInfosList().stream()
                .filter(info -> Objects.equals(info.getCustomerId(), customerId.longValue()))
                .findFirst()
                .ifPresent(customerBlockInfo ->
                        blockedServiceItemTypes.addAll(customerBlockInfo.getServiceItemTypesList()));
        Byte isBlockOnlineBooking = blockedServiceItemTypes.isEmpty()
                ? CustomerContactEnum.CUSTOMER_IS_OK
                : CustomerContactEnum.CUSTOMER_IS_BLOCK;

        var customerContact = businessCustomerContactApi.listCustomerContact(ListCustomerContactRequest.newBuilder()
                .setCustomerId(customerId)
                .build());

        var emergencyContact = customerContact.getContactsList().stream()
                .filter(Objects::nonNull)
                .filter(c -> Objects.equals(CustomerContactEnum.TYPE_EMERGENCY.intValue(), c.getType()))
                .findFirst()
                .map(c -> CustomerModelOnlineBookingView.EmergencyContact.newBuilder()
                        .setFirstName(c.getFirstName())
                        .setLastName(c.getLastName())
                        .setPhoneNumber(c.getPhoneNumber())
                        .build())
                .orElse(CustomerModelOnlineBookingView.EmergencyContact.getDefaultInstance());

        responseObserver.onNext(OBClientInfoResponse.newBuilder()
                .setCustomer(builder.setAvatarPath(customer.getAvatarPath())
                        .setId(customerId)
                        .setBusinessId(businessId)
                        .setPhoneNumber(phoneNumber)
                        .setIsBlockOnlineBooking(isBlockOnlineBooking)
                        .setCustomerType(Optional.of(customer)
                                .map(CustomerBasicDTO::getType)
                                .orElse(""))
                        .setEmergencyContact(emergencyContact)
                        .build())
                .addAllCreditCardList(CreditCardConverter.INSTANCE.dto2model(creditCardList))
                .putAllCustomQuestions(customAnswers)
                .setRequiredUpdate(hasRequiredQuestion(answerMap, clientQuestions))
                .addAllBlockedServiceItemTypes(blockedServiceItemTypes)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB)
    public void getOnlineBookingClientPaymentType(
            GetOBClientRequest request, StreamObserver<OBClientPaymentTypeResponse> responseObserver) {
        var customerId = AuthContext.get().getCustomerId();
        OBAnonymousParams anonymousParams =
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName());
        Integer businessId = onlineBookingService
                .mustGetBusinessDTOByOBNameOrDomain(anonymousParams)
                .getBusinessId();
        BookOnlinePaymentGroupSettingDTO setting =
                onlineBookingService.getOBClientPaymentSetting(businessId, customerId);
        if (setting == null) {
            responseObserver.onNext(OBClientPaymentTypeResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        responseObserver.onNext(ClientConverter.INSTANCE.toPaymentSettingResponse(setting));
        responseObserver.onCompleted();
    }

    private void updateQuestionInfo(
            UpdateOBClientRequest request, Integer businessId, Integer customerId, boolean autoAcceptConflict) {
        questionService.upsertCustomerQuestionSave(
                new BookOnlineQuestionSaveDTO()
                        .setBusinessId(businessId)
                        .setCustomerId(customerId)
                        .setClientCustomQuestionMap(
                                QuestionConverter.INSTANCE.string2obj(request.getCustomQuestionsMap())),
                autoAcceptConflict);
    }

    private void updateCustomerInfo(
            UpdateOBClientRequest request,
            Long companyId,
            Integer customerId,
            Integer businessId,
            boolean autoAcceptConflict) {
        CustomerInfoIdParams params =
                new CustomerInfoIdParams().setCompanyId(companyId).setCustomerId(customerId);
        CustomerInfoDto customer = customerService.getCustomerWithDeletedHasCompanyId(params);
        if (Objects.isNull(customer)) {
            throw bizException(Code.CODE_CUSTOMER_NOT_FOUND_FOR_OB);
        }
        CustomerDef customerDef = request.getCustomer();
        CustomerProfileRequestDTO.ClientProfileDTO client = ClientConverter.INSTANCE.def2ClientProfileDTO(customerDef);
        client.setEmergencyContact(convertToAdditionalContact(customerDef.getEmergencyContact()));
        client.setPickupContact(convertToAdditionalContact(customerDef.getPickupContact()));
        profileRequestService.updateCustomerAndProfileRequest(
                CustomerProfileRequestDTO.builder()
                        .businessId(businessId)
                        .companyId(companyId)
                        .customerId(customerId)
                        .client(client)
                        .build(),
                autoAcceptConflict);
    }

    private AdditionalContactDTO convertToAdditionalContact(CustomerDef.Contact contact) {
        AdditionalContactDTO additionalContact = new AdditionalContactDTO();
        if (Objects.isNull(contact)) return additionalContact;

        additionalContact.setFirstName(contact.getFirstName());
        additionalContact.setLastName(contact.getLastName());
        additionalContact.setPhone(contact.getPhoneNumber());
        return additionalContact;
    }

    private UpdateOBClientAddressResponse updateAddressInfo(
            Integer customerId, UpdateOBClientRequestAddress address, Integer businessId) {
        var r = handleNoPrimaryAddress(customerId, address);
        if (r != null) {
            return r;
        }

        r = handleAutomation(address, businessId, customerId);
        if (r != null) {
            return r;
        }

        ProfileRequestAddressDTO upsertBean = upsertParamToDTO(address, customerId);
        r = handleModifyBAddress(upsertBean, customerId);
        if (r != null) {
            return r;
        }

        int id = upsertProfileRequestAddress(upsertBean);
        return dtoToUpsertResult(profileRequestAddressApi.get(id));
    }

    private boolean hasRequiredQuestion(Map<String, Object> answerMap, List<GroomingQuestionDTO> questions) {
        boolean allHave = questions.stream().allMatch(question -> {
            if (!Objects.equals(question.getIsRequired(), CommonConstant.ENABLE)) {
                return true;
            }
            if (!isForExistingClient(question)) {
                return true;
            }
            Object answer = answerMap.get(question.getKey());
            if (answer instanceof String obj) {
                return StringUtils.hasText(obj);
            } else if (answer instanceof Integer[] obj) {
                return obj.length != 0;
            }
            return answer != null;
        });
        return !allHave;
    }

    private boolean isForExistingClient(GroomingQuestionDTO question) {
        return question.getAcceptCustomerType() == AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE
                || question.getAcceptCustomerType() == AcceptCustomerType.EXISTING_CUSTOMER_VALUE;
    }

    private Map<String, Object> buildStandardAnswerMap(
            CustomerInfoDto customer, String phoneNumber, CustomerAddressDto primaryAddress) {
        Map<String, Object> answerMap = new HashMap<>();
        answerMap.put("First_name", customer.getFirstName());
        answerMap.put("Last_name", customer.getLastName());
        answerMap.put("Phone_number", phoneNumber);
        answerMap.put("Email", customer.getEmail());
        answerMap.put("Address", primaryAddress);
        answerMap.put("Referral_source", customer.getReferralSourceId());
        answerMap.put("Preferred_groomer", customer.getPreferredGroomerId());
        answerMap.put("Preferred_frequency", customer.getPreferredFrequencyDay());
        answerMap.put("Preferred_day_of_the_week", customer.getPreferredDay());
        answerMap.put("Preferred_time_of_the_day", customer.getPreferredTime());
        return answerMap;
    }

    @Nullable
    private UpdateOBClientAddressResponse handleNoPrimaryAddress(
            Integer customerId, UpdateOBClientRequestAddress address) {
        var request = GetCustomerPrimaryAddressRequest.newBuilder()
                .setCustomerId(customerId)
                .build();
        var response = businessCustomerAddressApi.getCustomerPrimaryAddress(request);
        if (!response.hasAddress()) {
            return insertCustomerAddress(address, customerId);
        }
        return null;
    }

    @Nullable
    private UpdateOBClientAddressResponse handleModifyBAddress(
            ProfileRequestAddressDTO upsertBean, Integer customerId) {
        if (upsertBean.getCustomerAddressId() == null) {
            return null;
        }
        var getRequest = GetCustomerAddressRequest.newBuilder()
                .setId(upsertBean.getCustomerAddressId())
                .build();
        var addressModel =
                businessCustomerAddressApi.getCustomerAddress(getRequest).getAddress();

        if (!BusinessCustomerService.hasAddressUpdate(upsertBean, addressModel) && noCAddress(customerId)) {
            return ClientConverter.INSTANCE.toUpdateOBClientAddressResponse(addressModel);
        }
        return null;
    }

    @Nullable
    private UpdateOBClientAddressResponse handleAutomation(
            UpdateOBClientRequestAddress request, Integer businessId, Integer customerId) {
        if (!isAutoAcceptAll(businessId)) {
            return null;
        }

        CustomerAddressDto customerAddress = upsertParamToCustomerAddressDTO(request, customerId);

        // 对于 C 端 address 的操作，直接写入 B 端 address
        Integer bAddressId = !Boolean.TRUE.equals(customerAddress.getIsProfileRequestAddress())
                ? customerAddress.getCustomerAddressId()
                : null;

        var result = bAddressId == null
                ? insertCustomerAddress(request, customerId)
                : updateCustomerAddress(request, bAddressId);

        // 写入 B 端 address 后，删除 C 端 profile request address
        profileRequestAddressApi.deleteByCustomerIds(List.of(customerId));

        return result;
    }

    private UpdateOBClientAddressResponse insertCustomerAddress(
            UpdateOBClientRequestAddress request, Integer customerId) {
        var createDef = ClientConverter.INSTANCE.toAddressCreateDef(request);
        var createRequest = CreateCustomerAddressRequest.newBuilder()
                .setCustomerId(customerId)
                .setAddress(createDef)
                .build();
        var addressModel =
                businessCustomerAddressApi.createCustomerAddress(createRequest).getAddress();
        return ClientConverter.INSTANCE.toUpdateOBClientAddressResponse(addressModel);
    }

    private UpdateOBClientAddressResponse updateCustomerAddress(
            UpdateOBClientRequestAddress request, Integer addressId) {
        var updateDef = ClientConverter.INSTANCE.toAddressUpdateDef(request);
        var updateRequest = UpdateCustomerAddressRequest.newBuilder()
                .setId(addressId)
                .setAddress(updateDef)
                .build();
        var updateResponse = businessCustomerAddressApi.updateCustomerAddress(updateRequest);
        return ClientConverter.INSTANCE.toUpdateOBClientAddressResponse(updateResponse.getAddress());
    }

    private boolean isAutoAcceptAll(Integer businessId) {
        BookOnlineDTO obSetting = Optional.ofNullable(onlineBookingService.getOBSetting(businessId))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "OB setting not found, businessId: " + businessId));
        return OBRequestSubmittedAutoTypeEnum.isAutoAcceptConflict(obSetting.getRequestSubmittedAutoType());
    }

    private boolean noCAddress(Integer customerId) {
        Map<Integer, List<ProfileRequestAddressDTO>> customerIdToProfileRequestAddresses =
                profileRequestAddressApi.listByCustomerIds(List.of(customerId));
        return ObjectUtils.isEmpty(customerIdToProfileRequestAddresses.get(customerId));
    }

    private int upsertProfileRequestAddress(ProfileRequestAddressDTO bean) {
        if (bean.getId() == null) {
            return profileRequestAddressApi.insert(bean);
        }
        profileRequestAddressApi.update(bean);
        return bean.getId();
    }

    private static UpdateOBClientAddressResponse dtoToUpsertResult(ProfileRequestAddressDTO dto) {
        var builder = UpdateOBClientAddressResponse.newBuilder();
        if (dto.getCustomerId() != null) {
            builder.setCustomerId(dto.getCustomerId());
        }
        if (dto.getAddress1() != null) {
            builder.setAddress1(dto.getAddress1());
        }
        if (dto.getAddress2() != null) {
            builder.setAddress2(dto.getAddress2());
        }
        if (dto.getCity() != null) {
            builder.setCity(dto.getCity());
        }
        if (dto.getCountry() != null) {
            builder.setCountry(dto.getCountry());
        }
        if (dto.getLat() != null) {
            builder.setLat(dto.getLat());
        }
        if (dto.getLng() != null) {
            builder.setLng(dto.getLng());
        }
        if (dto.getState() != null) {
            builder.setState(dto.getState());
        }
        if (dto.getZipcode() != null) {
            builder.setZipcode(dto.getZipcode());
        }
        builder.setIsPrimary(
                Boolean.TRUE.equals(dto.getIsPrimary())
                        ? CustomerAddressDto.IsPrimary.TRUE.getValue()
                        : CustomerAddressDto.IsPrimary.FALSE.getValue());

        // 这里前端需要一个 addressId，如果是 C 端新增 address，此时还没有真正添加到 B 端，还没有 addressId。
        // 因此如果是 C 端新增的 address，我们使用 profile request address 的 id 作为 addressId。
        // 并标记这个 address 是 profile request address（不是 B 端的 address）
        if (dto.getCustomerAddressId() == null) {
            builder.setId(dto.getId());
            builder.setIsProfileRequestAddress(true);
        } else {
            builder.setId(dto.getCustomerAddressId());
            builder.setIsProfileRequestAddress(false);
        }
        return builder.build();
    }

    private static ProfileRequestAddressDTO upsertParamToDTO(UpdateOBClientRequestAddress request, Integer customerId) {
        ProfileRequestAddressDTO profileRequestAddress = new ProfileRequestAddressDTO();
        if (request.hasAddress1()) {
            profileRequestAddress.setAddress1(request.getAddress1());
        }
        if (request.hasAddress2()) {
            profileRequestAddress.setAddress2(request.getAddress2());
        }
        if (request.hasCity()) {
            profileRequestAddress.setCity(request.getCity());
        }
        if (request.hasState()) {
            profileRequestAddress.setState(request.getState());
        }
        if (request.hasZipcode()) {
            profileRequestAddress.setZipcode(request.getZipcode());
        }
        if (request.hasCountry()) {
            profileRequestAddress.setCountry(request.getCountry());
        }
        if (request.hasLat()) {
            profileRequestAddress.setLat(request.getLat());
        }
        if (request.hasLng()) {
            profileRequestAddress.setLng(request.getLng());
        }
        profileRequestAddress.setIsPrimary(Objects.equals(
                request.getIsPrimary(),
                CustomerAddressDto.IsPrimary.TRUE.getValue().intValue()));
        profileRequestAddress.setCustomerId(customerId);
        if (request.hasId()) {
            if (request.getIsProfileRequestAddress()) {
                profileRequestAddress.setId((int) request.getId());
            } else {
                profileRequestAddress.setCustomerAddressId((int) request.getId());
            }
        }
        return profileRequestAddress;
    }

    private static CustomerAddressDto upsertParamToCustomerAddressDTO(
            UpdateOBClientRequestAddress request, Integer customerId) {
        CustomerAddressDto customerAddress = new CustomerAddressDto();
        if (request.hasId()) {
            customerAddress.setCustomerAddressId((int) request.getId());
        }
        if (request.hasAddress1()) {
            customerAddress.setAddress1(request.getAddress1());
        }
        if (request.hasAddress2()) {
            customerAddress.setAddress2(request.getAddress2());
        }
        if (request.hasCity()) {
            customerAddress.setCity(request.getCity());
        }
        if (request.hasCountry()) {
            customerAddress.setCountry(request.getCountry());
        }
        if (request.hasLat()) {
            customerAddress.setLat(request.getLat());
        }
        if (request.hasLng()) {
            customerAddress.setLng(request.getLng());
        }
        if (request.hasState()) {
            customerAddress.setState(request.getState());
        }
        if (request.hasZipcode()) {
            customerAddress.setZipcode(request.getZipcode());
        }
        if (request.hasIsPrimary()) {
            customerAddress.setIsPrimary((byte) request.getIsPrimary());
        }
        customerAddress.setIsProfileRequestAddress(request.getIsProfileRequestAddress());
        customerAddress.setCustomerId(customerId);
        return customerAddress;
    }

    private static boolean hasAddress(UpdateOBClientRequest request) {
        return request.hasAddress()
                && StringUtils.hasText(request.getAddress().getLat())
                && StringUtils.hasText(request.getAddress().getLng());
    }
}
